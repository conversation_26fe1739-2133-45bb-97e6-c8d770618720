#!/usr/bin/env python3

import json
import os
import time
from logging.config import dictConfig
from typing import Any

import boto3
import redis
import structlog
from structlog.processors import Exception<PERSON>enderer
from structlog.tracebacks import ExceptionDictTransformer

logging_config = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json_formatter": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.processors.JSONRenderer(),
        },
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "json_formatter",
        }
    },
    "loggers": {
        "django_structlog": {
            "handlers": ["console"],
            "level": "INFO",
        },
        "root": {
            "handlers": ["console"],
            "level": "INFO",
        },
    },
}
processors = [
    ExceptionRenderer(ExceptionDictTransformer(show_locals=False)),
    structlog.contextvars.merge_contextvars,
    structlog.stdlib.filter_by_level,
    structlog.processors.TimeStamper(fmt="iso"),
    structlog.stdlib.add_logger_name,
    structlog.stdlib.add_log_level,
    structlog.dev.set_exc_info,
    structlog.stdlib.PositionalArgumentsFormatter(),
    structlog.processors.StackInfoRenderer(),
    structlog.processors.UnicodeDecoder(),
    structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
]

logger = structlog.get_logger(__name__)
dictConfig(logging_config)


def get_redis_secret() -> dict[str, Any]:
    """Retrieve Redis credentials from AWS Secrets Manager."""
    secret_arn = os.environ.get("REDIS_SECRET_ARN")
    if not secret_arn:
        msg = "REDIS_SECRET_ARN environment variable not set"
        raise ValueError(msg)

    try:
        region_name = os.environ.get("AWS_REGION", "us-east-1")
        session = boto3.session.Session()
        secrets_client = session.client(
            service_name="secretsmanager",
            region_name=region_name,
        )
        response = secrets_client.get_secret_value(SecretId=secret_arn)
        return json.loads(response["SecretString"])
    except Exception as e:
        logger.exception("failed_to_retrieve_redis_secret", error=str(e))
        raise


def get_queue_depths_and_publish_to_cloudwatch(namespace: str) -> None:
    """Get Redis queue depths and publish metrics to CloudWatch."""
    host = os.environ.get("REDIS_HOST")
    port = os.environ.get("REDIS_PORT")
    redis_secret = get_redis_secret()
    password = redis_secret.get("password")
    db = os.environ.get("REDIS_DB", "0")

    if not host:
        msg = "Redis host not found in secret"
        raise ValueError(msg)

    depths = get_queue_depths(host, port, password, db)
    publish_depths_to_cloudwatch(depths, namespace)


def get_queue_depths(host: str, port: int, password: str | None, db: int = 0) -> dict:
    """Connect to Redis and get queue depths for all list-type keys."""
    try:
        # Connect to Redis with SSL/TLS (required for transit encryption)
        r = redis.Redis(
            host=host,
            port=int(port),
            password=password,
            db=int(db),
            decode_responses=True,
            ssl=True,
            ssl_cert_reqs=None,
            ssl_check_hostname=False,
            socket_timeout=30,
            socket_connect_timeout=30,
        )
        r.ping()
    except Exception:
        logger.exception("redis_connection_failed")
        raise

    depths = {}
    total = 0

    try:
        all_keys = r.keys("*")
        logger.info("redis_scan_start", total_keys=len(all_keys), all_keys=all_keys)
        skipped_keys = []
        for queue_key in all_keys:
            try:
                key_type = r.type(queue_key)
                if key_type == "list":
                    queue_depth = r.llen(queue_key)
                    log_msg = f"key: {queue_key} has depth {queue_depth}"
                    logger.info(log_msg, members=r.lrange(queue_key, 0, queue_depth))
                    # we can add more types here if needed
                    depths[queue_key] = queue_depth
                    total += queue_depth
                    logger.info("queue_depth_success", queue_key=queue_key, queue_depth=queue_depth)
                else:
                    skipped_key = {"queue_key": queue_key, "key_type": key_type}
                    skipped_keys.append(skipped_key)
            except redis.RedisError as e:
                logger.exception("queue_depth_failure", queue_key=queue_key, type=key_type, error=str(e))

        depths["total"] = total

        logger.info("redis_queue_scan_complete", skipped_keys=skipped_keys)

    except redis.RedisError:
        logger.exception("redis_scan_failure")

    return depths


def publish_depths_to_cloudwatch(depths: dict, namespace: str) -> None:
    """Publish queue depth metrics to CloudWatch."""
    try:
        cloudwatch = boto3.client("cloudwatch", region_name=os.environ.get("AWS_REGION", "us-east-1"))
        for queue_key, queue_depth in depths.items():
            try:
                logger.info("cloudwatch_put_metric_start", queue_key=queue_key, queue_depth=queue_depth)
                cloudwatch.put_metric_data(
                    Namespace=namespace,
                    MetricData=[
                        {
                            "MetricName": str(queue_key),
                            "Timestamp": time.time(),
                            "Value": int(queue_depth),
                            "Unit": "Count",
                        }
                    ],
                )
                logger.info("cloudwatch_put_metric_success", queue_key=queue_key)
            except Exception as e:
                logger.exception("cloudwatch_put_metric_error", queue_key=queue_key, e=e)
    except Exception as e:
        logger.exception("cloudwatch_client_initialization_error", e=e)


def lambda_handler(_event: Any, _context: Any) -> None:  # noqa: ANN401
    """Main Lambda handler function."""
    try:
        get_queue_depths_and_publish_to_cloudwatch(namespace="celery_queue_depths")
        return {"statusCode": 200, "body": json.dumps({"message": "Queue monitoring completed successfully"})}
    except Exception as e:
        logger.exception("lambda_execution_failed", error=str(e))
        return {"statusCode": 500, "body": json.dumps({"error": str(e)})}


if __name__ == "__main__":
    lambda_handler(event=None, context=None)
