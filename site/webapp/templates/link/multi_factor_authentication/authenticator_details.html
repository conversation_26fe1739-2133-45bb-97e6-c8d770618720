{% load static customtags %}
<div id="modal-container-{{ merged_portal_credential.pk }}">
    <div class="relative">
        <!-- Modal body -->
        <div class="flex justify-between p-4 md:p-5 rounded-t dark:border-gray-600">
            <div class="FeaturedIcon  p-2 rounded-[10px] justify-center items-center inline-flex">
                <div class="Frame  relative flex-col justify-start items-start flex">
                    <div class="Group  relative">
                        <img src="data:image/png;base64, {{ otp_qr_code_base64 }}"
                             alt="qr code"
                             height="200px"
                             width="200px">
                    </div>
                </div>
            </div>
            <button _="on click trigger closeModal"
                    type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                <img src="{% static 'assets/images/close.svg' %}"
                     width="16"
                     height="19"
                     alt="closeButton">
                <span class="sr-only">Close modal</span>
            </button>
        </div>
        <div class="p-4">
            <!-- Label -->
            <label class="block text-sm font-medium text-gray-700 mb-2">Secret Key</label>
            <!-- Secret Key Input -->
            <input type="text"
                   value="{{ otp.secret }}"
                   readonly=""
                   class="w-full bg-gray-100 border border-gray-300 rounded-lg px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <!-- Copy Button -->
            <div class="flex justify-end mt-2">
                <button class=" border border-[#924f34] text-[#924f34] py-2 px-4 rounded-lg shadow hover:bg-primary-darker-olive transition"
                        onclick="navigator.clipboard.writeText('{{ otp.secret }}')">Copy to Clipboard</button>
            </div>
        </div>
        <div class="p-5 rounded-lg max-w-md mx-auto">
            <!-- Account Name Field -->
            <div class="mb-4">
                <label for="accountName"
                       class="block text-sm font-medium text-gray-700 mb-2">Account Name</label>
                <input type="text"
                       id="accountName"
                       value="{{ otp.name }}"
                       readonly=""
                       class="w-full bg-gray-100 border border-gray-300 rounded-lg px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <!-- Issuer Field -->
            <div class="mb-4">
                <label for="issuer" class="block text-sm font-medium text-gray-700 mb-2">Issuer</label>
                <input type="text"
                       id="issuer"
                       value="{{ otp.issuer }}"
                       readonly=""
                       class="w-full bg-gray-100 border border-gray-300 rounded-lg px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <!-- Authenticate Button -->
            <div class="mb-4">
                <button hx-get="{% url 'multi_factor_authentication_end_screen' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
                        hx-swap="outerHTML"
                        hx-target="#modal-container-{{ merged_portal_credential.pk }}"
                        class="w-full bg-[#924f34] text-white py-3 px-4 rounded-lg shadow-md hover:bg-primary-darker-olive transition">
                    Authenticate
                </button>
            </div>
            <div class="w-[600px]"></div>
        </div>
    </div>
</div>
