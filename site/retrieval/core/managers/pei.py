import asyncio
import datetime
import hashlib
import json
import urllib.parse
from collections.abc import AsyncGenerator

import structlog
from playwright.async_api import Locator
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class PEIPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "peifunds.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [("<EMAIL>", "PEI")]

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError

        email_input_locator = self.page.locator("input[id='modlgn-username']")
        password_input_locator = self.page.locator("input[id='modlgn-passwd']")
        login_button_locator = self.page.locator("button[type='submit']")
        if await email_input_locator.count() == 1 and await email_input_locator.is_visible():
            await email_input_locator.click()
            await email_input_locator.fill(username)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        if await password_input_locator.count() == 1 and await password_input_locator.is_visible():
            await password_input_locator.click()
            await password_input_locator.fill(password)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        if await login_button_locator.count() == 1 and await login_button_locator.is_visible():
            await login_button_locator.click()
            await asyncio.sleep(2)
        else:
            raise CouldNotFindHTMLElementError

    async def _check_has_otp(self) -> bool:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        if self.page is None:
            raise ManagerNotInitializedError

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return await self.page.get_by_text("Log out").count() > 0

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        doc_tree_page_locator = self.page.get_by_text("Document Tree")

        if await doc_tree_page_locator.count() == 1:
            await doc_tree_page_locator.click()
            await asyncio.sleep(10)
        else:
            raise CouldNotFindHTMLElementError

        doc_tree_locator = self.page.locator("ul.jqtree-tree[role='tree']")
        doc_branches_locator = doc_tree_locator.locator("> li.jqtree_common[role='presentation']")
        doc_branches_count = await doc_branches_locator.count()

        if doc_branches_count == 0:
            logger.info("PEI portal has no documents.")
            return

        for i in range(doc_branches_count):
            doc_branch = doc_branches_locator.nth(i)
            await doc_branch.click()
            await asyncio.sleep(10)
            async for doc in self._download_page():
                yield doc

    async def _download_page(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: C901, PLR0912, PLR0915
        page_category_locator = self.page.locator("div.docman_category")
        header_content_span = page_category_locator.locator("span.koowa_wrapped_content")
        if await header_content_span.count() == 1:
            page_title = await header_content_span.inner_text()
        else:
            raise CouldNotFindHTMLElementError
        docs_form = self.page.locator("form")
        categories_table = docs_form.locator("table.koowa_table--categories")
        categories_table_body = categories_table.locator("tbody")
        categories_table_rows = categories_table_body.locator("tr")
        categories_table_rows_count = await categories_table_rows.count()
        docs_table = docs_form.locator("table.koowa_table--documents")
        docs_table_body = docs_table.locator("tbody")
        docs_table_rows = docs_table_body.locator("tr")
        docs_table_rows_count = await docs_table_rows.count()

        if categories_table_rows_count > 0:
            for i in range(categories_table_rows_count):
                category_row = categories_table_rows.nth(i)
                category_row_inner_span = category_row.locator("span.koowa_wrapped_content")
                category_row_inner_a_tag = category_row_inner_span.locator("a")
                if await category_row_inner_a_tag.count() == 1:
                    await category_row_inner_a_tag.click()
                else:
                    raise CouldNotFindHTMLElementError
                await asyncio.sleep(10)
                async for doc in self._download_page():
                    yield doc

        if docs_table_rows_count == 0:
            return

        for i in range(docs_table_rows_count):
            row = docs_table_rows.nth(i)
            row_metadata = await self._get_row_metadata(row, page_title)
            metadata = json.dumps(row_metadata)
            if await self.should_download_document(metadata):
                row_table_data_locators = row.locator("td")
                row_content_cell = row_table_data_locators.nth(1)
                row_content_cell_a_tag = row_content_cell.locator("a")
                href = await row_content_cell_a_tag.get_attribute("href")
                base_url = "https://docs.peifunds.com"
                full_url = urllib.parse.urljoin(base_url, href)
                response = await self.page.request.get(full_url)
                if response.ok:
                    doc_content = await response.body()
                    datetime_date = datetime.datetime.strptime(row_metadata["date"], "%d %b %Y")  # noqa: DTZ007
                    doc_type = self._document_mapper(row_metadata["category"], row_metadata["data-title"])
                    doc = RawDocumentTuple(
                        name=row_metadata["title"],
                        date=datetime_date,
                        doc_type=doc_type,
                        content=doc_content,
                        raw_metadata=metadata,
                        doc_hash=self._doc_hash(metadata),
                    )
                    if doc is not None:
                        yield doc
                else:
                    logger.error("Could not download document contents", row=row)

        if await self.page.evaluate("window.history.length") > 1:
            await self.page.go_back()
        else:
            logger.warning("Cannot go back, no previous page in history.")
            raise CouldNotFindHTMLElementError

    async def _get_row_metadata(self, row: Locator, page_title: str) -> dict[str, str]:
        row_metadata = {}
        row_table_data_locators = row.locator("td")
        row_content_cell = row_table_data_locators.nth(1)
        row_content_cell_a_tag = row_content_cell.locator("a")
        row_metadata["data-title"] = await row_content_cell_a_tag.get_attribute("data-title")
        row_metadata["title"] = await row_content_cell_a_tag.get_attribute("title")
        row_date_cell = row_table_data_locators.nth(2)
        row_metadata["date"] = await row_date_cell.inner_text()
        row_metadata["category"] = page_title
        return row_metadata

    def _document_mapper(self, document_category: str | None, document_data_title: str | None) -> DocumentType | str:
        mapping = {
            "Capital Call": DocumentType.CAPITAL_CALL,
            "Distribution": DocumentType.DISTRIBUTION_NOTICE,
            "Communications": DocumentType.INVESTMENT_UPDATE,
            "Estimated Fair Value Report": DocumentType.LEGAL,
            "Financial Statement": DocumentType.FINANCIAL_STATEMENTS,
            "Investment Summar": DocumentType.ACCOUNT_STATEMENT,
        }

        for key, value in mapping.items():
            if document_category and key in document_category:
                return value
            if document_data_title and key in document_data_title:
                return value
        return DocumentType.UNKNOWN
