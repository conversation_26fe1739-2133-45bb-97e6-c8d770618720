import * as esbuild from 'esbuild-wasm'

let minify = false
let sourcemap = true
let watch = true

if (process.env.NODE_ENV === 'production') {
  minify = true
  sourcemap = false
  watch = false
}
import { copyFileSync } from 'fs';

const parent_dir = process.cwd().split('/').slice(0, -1).join('/');
console.log(process.cwd(), parent_dir);
copyFileSync(
  `${process.cwd()}/node_modules/pdfjs-dist/build/pdf.worker.min.mjs`,
  `${parent_dir}/static/js/pdf.worker.min.js`
);

const config = {
  entryPoints: ['./js/app.js'],
  outdir: '../static/js',
  bundle: true,
  minify: minify,
  sourcemap: sourcemap,
  external: ['pdf.worker.min.js']
}

console.log(config)

if (watch) {
  let context = await esbuild.context({...config, logLevel: 'info'})
  await context.watch()
} else {
  esbuild.build(config)
}