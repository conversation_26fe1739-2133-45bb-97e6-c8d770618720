{% load static customtags %}
<div id="modal-container-export-line-items" class="p-3">
    <div class="flex items-center justify-between p-3 rounded-t dark:border-gray-600">
        <div class="p-2 bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
            <div class="relative flex-col justify-start items-start flex">
                <div class="relative">
                    <img src="{% static 'assets/images/download_icon.svg' %}"
                         width="38"
                         height="20"
                         alt="closeButton">
                </div>
            </div>
        </div>
        <button _="on click trigger closeModal"
                type="button"
                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <img src="{% static 'assets/images/close.svg' %}"
                 width="16"
                 height="19"
                 alt="closeButton">
            <span class="sr-only">Close modal</span>
        </button>
    </div>
    <div class="opacity-75 text-[#343d36] px-3 pb-3 pt-4 text-sm font-normal font-inter leading-tight">
        Please select the line-item(s) detail you want to download.
    </div>
    <div class="relative inline-block" id="checkboxDropdown">
        <div class="flex items-center space-x-4">
            <div class="px-3 pb-3 pt-4 text-sm font-normal font-inter leading-tight">Line Item(s):</div>
            <button type="button"
                    class="dropdown-btn flex justify-between items-center py-3 px-6 text-sm w-[275px] font-semibold text-base-darker-olive-muted font-inter rounded border border-gray-300"
                    id="dropdownMenuButton"
                    data-dropdown-toggle="dropdownMenuContainer"
                    data-dropdown-placement="bottom-start">
                <span id="dropdownSelectedCount" class="flex-grow text-left">Select</span>
                <img class="ml-2"
                     width="16"
                     height="16"
                     alt="down_arrow"
                     src="{% static 'assets/images/down_arrow.svg' %}">
            </button>
            <div id="dropdownMenuContainer"
                 class="absolute top-full left-0 mt-1 w-full max-h-90 hidden overflow-y-auto bg-white border rounded shadow-lg z-50 dropdown-menu">
                <label class="flex items-center px-4 py-2 hover:bg-gray-100 text-[#924f34]">
                    <input type="checkbox" id="select-all-checkbox" class="mr-2 text-[#924f34]">
                    Select All
                </label>
                <div class="border-t border-[#924f34] mr-2 ml-2"></div>
                {% for line_item in line_items %}
                    <label class="flex items-center px-4 py-2 hover:bg-gray-100">
                        <input type="checkbox"
                               name="selected_line_items"
                               value="{{ line_item.pk }}"
                               class="item-checkbox mr-2">
                        <span class="truncate overflow-hidden whitespace-nowrap"
                              title="{{ line_item.investing_entity.legal_name }}: {{ line_item.investment.legal_name }}">
                            {{ line_item.investing_entity.legal_name }}: {{ line_item.investment.legal_name }}
                        </span>
                    </label>
                {% endfor %}
            </div>
        </div>
    </div>
    <div class="border-t border-[#eceee8] mt-4"></div>
    {% include "vault/zip_files/zip_button.html" with page="dashboard_export" include_download_icon=False button_class="h-11 px-3 mt-4 w-full py-2.5 bg-[#924f34] rounded-lg border border-[#924f34] justify-center items-center gap-1.5 inline-flex opacity-50" %}
</div>
<script>
    var dropdownBtn = document.getElementById('dropdownMenuButton');
    var dropdownMenu = document.getElementById('dropdownMenuContainer');
    var selectAllCheckbox = document.getElementById('select-all-checkbox');

    function getItemCheckboxes() {
        return dropdownMenu ? dropdownMenu.querySelectorAll('input.item-checkbox') : [];
    }

    function updateSelectedCountLabel() {
        const dropdownLabel = document.getElementById('dropdownSelectedCount');
        const selectedCount = Array.from(getItemCheckboxes()).filter(cb => cb.checked).length;
        dropdownLabel.textContent = selectedCount > 0 ? `${selectedCount} items selected` : 'Select';
    }

    function onSelectAllChange(e) {
        getItemCheckboxes().forEach(cb => cb.checked = e.target.checked);
        updateSelectedCountLabel();
    }

    function onDropdownMenuChange(e) {
        if (e.target.classList.contains('item-checkbox')) {
            const checkboxes = getItemCheckboxes();
            const allChecked = checkboxes.length > 0 && Array.from(checkboxes).every(cb => cb.checked);
            if (selectAllCheckbox) selectAllCheckbox.checked = allChecked;
        }
        updateSelectedCountLabel();
    }

    var listenersAttached = false;

    dropdownBtn?.addEventListener('click', function() {
        dropdownMenu.classList.toggle('hidden');
        const isVisible = !dropdownMenu.classList.contains('hidden');
        if (isVisible && !listenersAttached) {
            selectAllCheckbox?.addEventListener('change', onSelectAllChange);
            dropdownMenu.addEventListener('change', onDropdownMenuChange);
            listenersAttached = true;
        } else if (!isVisible && listenersAttached) {
            selectAllCheckbox?.removeEventListener('change', onSelectAllChange);
            dropdownMenu.removeEventListener('change', onDropdownMenuChange);
            listenersAttached = false;
        }
    });
</script>
