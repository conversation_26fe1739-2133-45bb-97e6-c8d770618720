"""
Django settings for bridge_project project.

Generated by 'django-admin startproject' using Django 5.1.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

import os
import typing
from pathlib import Path

from kombu import Queue

if typing.TYPE_CHECKING:
    import django_stubs_ext

    django_stubs_ext.monkeypatch()


BASE_DIR = Path(__file__).resolve().parent.parent.parent

INSTALLED_APPS = [
    # Default Django apps...
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Third-party apps
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "axes",
    # "csp",
    "django_htmx",
    "django_celery_results",
    "widget_tweaks",
    "tailwind",
    "ninja",
    "django_extensions",
    "health_check",  # required
    "health_check.db",  # stock Django health checkers
    "health_check.cache",
    "health_check.storage",
    "health_check.contrib.migrations",
    "health_check.contrib.celery",  # requires celery
    "health_check.contrib.celery_ping",  # requires celery
    "debug_toolbar",
    "django_celery_beat",
    "django_browser_reload",
    # local apps
    "webapp",
    "email_webhook",
    "retrieval",
    "ml_app",
    "public_api",
    "integrations",
    "django.contrib.admin",
    "hijack",
    "hijack.contrib.admin",
    "django_structlog",
    "phonenumber_field",
]
TAILWIND_APP_NAME = "webapp"

INTERNAL_IPS = ["localhost", "127.0.0.1"]  # , "0.0.0.0"]
MIDDLEWARE = [
    # default
    "django.middleware.security.SecurityMiddleware",
    # Axes middleware should be placed as high as possible, before any middleware that can create a user session
    "axes.middleware.AxesMiddleware",  # Axes Middleware
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    # additional middleware
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "allauth.account.middleware.AccountMiddleware",
    "django_htmx.middleware.HtmxMiddleware",
    "hijack.middleware.HijackUserMiddleware",
    "debug_toolbar.middleware.DebugToolbarMiddleware",
    "webapp.middleware.TimezoneMiddleware",
    "webapp.middleware.HtmxMessageMiddleware",
    "webapp.middleware.RedirectAuthenticatedUserMiddleware",
    "webapp.middleware.TermsOfServiceMiddleware",
    # TODO: figure out how this works
    # 'csp.middleware.CSPMiddleware',
]

DJANGO_STRUCTLOG_CELERY_ENABLED = True
DJANGO_STRUCTLOG_COMMAND_LOGGING_ENABLED = True

DEBUG_TOOLBAR_CONFIG = {
    # Toolbar options
    "SHOW_TOOLBAR_CALLBACK": "bridge_project.settings.utils.show_debug_toolbar",
    "SHOW_COLLAPSED": True,
    "SQL_WARNING_THRESHOLD": 70,
}

AUTHENTICATION_BACKENDS = (
    "axes.backends.AxesBackend",
    "django.contrib.auth.backends.ModelBackend",
    "allauth.account.auth_backends.AuthenticationBackend",
)

ACCOUNT_FORMS = {
    "signup": "webapp.forms.CustomSignupForm",
    "reset_password": "webapp.forms.CustomResetPasswordForm",
}

AUTH_USER_MODEL = "webapp.BridgeUser"
ACCOUNT_ADAPTER = "webapp.adapter.CustomAccountAdapter"
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework.authentication.TokenAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.IsAuthenticated",),
}
ACCOUNT_AUTHENTICATION_METHODS = {"email"}
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_EMAIL_VERIFICATION = "optional"
ACCOUNT_UNIQUE_EMAIL = True

PASSWORD_RESET_TIMEOUT = 60 * 15  # fifteen minutes
LOGIN_REDIRECT_URL = "/"

# If you change this please update the password validate_password() in views/account.py
# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
    {
        "NAME": "webapp.utils.password_validator.ContainsNumberValidator",
    },
    {
        "NAME": "webapp.utils.password_validator.ContainsSpecialCharacterValidator",
    },
]


EMAIL_BACKEND = "django_ses.SESBackend"
ROOT_URLCONF = "bridge_project.urls"
TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            BASE_DIR / "webapp" / "templates",
        ],
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
            "loaders": [
                (
                    "django.template.loaders.cached.Loader",
                    [
                        # Default Django loader
                        "django.template.loaders.filesystem.Loader",
                        # Inluding this is the same as APP_DIRS=True
                        "django.template.loaders.app_directories.Loader",
                    ],
                ),
            ],
        },
    },
]

WSGI_APPLICATION = "bridge_project.wsgi.application"

# SECURITY SETTINGS
# CSRF and Session Settings
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_AGE = 30 * 24 * 60 * 60  # 30 days in seconds

# Clickjacking Protection
X_FRAME_OPTIONS = "DENY"

# Content Security Policy (CSP)
# You can use django-csp package for this
# pip install django-csp
CSP_DEFAULT_SRC = ("'self'",)

# Axes Email Configuration
AXES_FAILURE_LIMIT = 10
AXES_COOLOFF_TIME = 1  # In hours
AXES_LOCKOUT_PARAMETERS = ["ip_address", ["username", "user_agent"]]

# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/
LANGUAGE_CODE = "en-us"
TIME_ZONE = "UTC"
USE_I18N = True
USE_L10N = True
USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/
STATIC_URL = "/static/"
STATIC_ROOT = BASE_DIR / "static"
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
]

STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        # Using whitenoise for static file serving: https://whitenoise.readthedocs.io/en/stable/django.html
        # TODO: configure CDN
        "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
    },
}

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"
DATA_UPLOAD_MAX_NUMBER_FIELDS = None

# Configure Celery
# https://docs.celeryq.dev/en/stable/django/first-steps-with-django.html

# Celery
redis_host = "localhost" if os.getenv("USE_LOCALHOST", None) is not None else "redis"
CELERY_BROKER_URL = f"redis://{redis_host}:6379/0"
CELERY_RESULT_BACKEND = "django-db"
CELERY_CACHE_BACKEND = "default"
CELERY_TASK_ACKS_LATE = True
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_TIME_LIMIT = 60 * 60 * 6  # 6 hours in seconds
CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"
CELERY_RESULT_EXTENDED = True
CELERY_RESULT_EXPIRES = 60 * 60 * 36  # 36 hours in seconds


CELERY_QUEUES = (
    Queue("high", exchange="high", routing_key="high"),
    Queue("medium", exchange="medium", routing_key="medium"),
    Queue("low", exchange="low", routing_key="low"),
)

# Catch all incase someone forgets to set a priority, on new tasks.
# All tasks have task-specific defaults defined below.
CELERY_TASK_DEFAULT_QUEUE = "medium"
CELERY_CREATE_MISSING_QUEUES = False

CELERY_TASK_ROUTES = {
    # note: try to keep the "high" queue to all tasks that don't run frequently but are user facing
    # try to explicitly throw everything else in the "low" queue
    # have the "medium" queue as the safe smart default for new tasks we don't know yet.
    # One day we might tie celery queues to different worker instances with different concurrency settings
    # or different machines or different containers or different requirements/docker images.
    # Scheduled tasks are high priority so the train runs on time.
    "retrieval.tasks.send_mail.send_internal_daily_digest": {"queue": "high"},
    "retrieval.tasks.login_portal_task.execute_portal_extractions": {"queue": "high"},
    "retrieval.tasks.login_portal_task.schedule_all_portal_extractions": {"queue": "high"},
    "integrations.tasks.orion.create_orion_files": {"queue": "high"},
    "retrieval.tasks.send_mail.notify_new_processed_documents_v2": {"queue": "high"},
    # System tasks are high priority.
    "celery.backend_cleanup": {"queue": "high"},
    "health_check.contrib.celery.tasks.add": {"queue": "high"},
    # While the scheduling of logins are high priority, the actual work is low priority.
    # When running the demo, we override that at the call site to be "high" priority.
    # TODO: what about the run retrieval button? Should that be medium/high?
    "retrieval.tasks.login_portal_task.login_portal_task": {"queue": "low"},
    # Our cute no-op tasks for testing is low priority.
    "retrieval.tasks.login_portal_task.noop_task": {"queue": "low"},
    "retrieval.tasks.login_portal_task.singleton_noop_task": {"queue": "low"},
    # Creation is high priority, deletion is low priority.
    "retrieval.tasks.create_zip.schedule_s3_deletion_task": {"queue": "low"},
    "retrieval.tasks.create_zip.process_zip_job_task": {"queue": "high"},
    # Make sure important user interactions are in the high priority queue by default.
    "email_webhook.tasks.email_parse.route_email_async": {"queue": "high"},
    "retrieval.tasks.send_mail.send_mfa_notification_email": {"queue": "high"},
    "retrieval.tasks.send_mail.send_invitation_email": {"queue": "high"},
    "retrieval.tasks.send_mail.notify_admins_email_generic": {"queue": "high"},
    "retrieval.tasks.universal_login_task.universal_login_task": {"queue": "high"},
    # Right now all the ML tasks take a backseat
    "ml_app.tasks.doc_vault_tasks.multi_process_doc_vault": {"queue": "low"},
    "ml_app.tasks.doc_vault_tasks.stateful_process_doc_vault": {"queue": "low"},
    "ml_app.tasks.doc_vault_tasks.stateful_process_doc_vault_singleton_backfill": {"queue": "low"},
    "ml_app.tasks.doc_vault_tasks.predict_line_items": {"queue": "low"},
    # Demo stuff is high priority by default.
    "retrieval.tasks.demo_tasks.demo_process_document": {"queue": "high"},
}


# Use redis as a cache, currently use redis as a cache to use locking features.
CACHES = {
    "default": {
        "BACKEND": "redis_lock.django_cache.RedisCache",
        "LOCATION": f"redis://{redis_host}:6379/0",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
        "KEY_PREFIX": "django_cache",
        "TIMEOUT": 600,
    }
}

PHONENUMBER_DEFAULT_REGION = "US"
# Allowed Hosts
DOMAIN_NAME = "bridgeinvest.co"
ALLOWED_HOSTS = ["localhost", "127.0.0.1", ".ngrok-free.app"]
CSRF_TRUSTED_ORIGINS = [f"https://{host}" for host in ALLOWED_HOSTS]

# Secret Key
SECRET_KEY = "django-insecure-9@09z6^%5=^00xs11q!jig0_x9$_v250p-e=*+-n3px4)nkii@"  # nosec # noqa: S105

# Database
host = "localhost" if os.getenv("USE_LOCALHOST", None) is not None else "db"
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "mydatabase",
        "USER": "myuser",
        "PASSWORD": "mypassword",
        "HOST": host,
        "PORT": 5432,
    },
}

OAUTH_SECRET = {}
