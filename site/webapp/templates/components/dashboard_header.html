{% load static %}
<div class="border-b border-gray-300 w-full">
    <div class="Padding4xl h-auto lg:h-24 p-4 lg:p-8 flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 lg:gap-0">
        <!-- Title Section -->
        <div class="DisplayXl text-[#343d36] font-nanum text-xl lg:text-[28px] font-extrabold leading-[39.20px]">{{ title }}</div>
        <!-- User Info Section -->
        <div class="relative w-full lg:w-auto">
            <!-- Dropdown Header -->
            <div id="dropdownButton"
                 onclick="toggleDropdown()"
                 class="flex items-center justify-between px-3 lg:px-4 py-2 lg:py-3 bg-white rounded-lg shadow border border-gray-200 cursor-pointer w-full lg:w-[250px]">
                <div class="flex items-center gap-2 lg:gap-3">
                    <div class="relative">
                        <img class="w-8 lg:w-10 h-8 lg:h-10 rounded-full"
                             src="{% static 'assets/images/avatar_default.svg' %}"
                             width="40"
                             height="40"
                             alt="User Avatar" />
                        <span class="absolute bottom-0 right-0 w-2 lg:w-3 h-2 lg:h-3 bg-green-500 rounded-full border border-white"></span>
                    </div>
                    <div class="text-gray-800 font-medium text-sm lg:text-base">{{ user.get_full_name }}</div>
                </div>
                <img id="arrowIcon"
                     src="{% static 'assets/images/dropdown/arrow.svg' %}"
                     class="rotate-90 transform transition-transform duration-300 w-4 lg:w-5 h-4 lg:h-5"
                     width="20"
                     height="20"
                     alt="dropImage" />
            </div>
            <!-- Dropdown Menu -->
            <div id="dropdownMenu"
                 class="hidden absolute right-0 mt-2 w-full lg:w-[250px] bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                <ul class="py-2 text-sm text-gray-800">
                    <li>
                        <a href="{% url 'settings' %}"
                           class="flex items-center gap-3 px-4 py-2 hover:bg-gray-100">
                            <img src="{% static 'assets/images/dropdown/settings.svg' %}"
                                 width="20"
                                 height="20"
                                 alt="dropImage" />
                            Settings
                        </a>
                    </li>
                    <li>
                        <a href="mailto:<EMAIL>"
                           class="flex items-center gap-3 px-4 py-2 hover:bg-gray-100">
                            <img src="{% static 'assets/images/dropdown/support.svg' %}"
                                 width="20"
                                 height="20"
                                 alt="dropImage" />
                            Message Support
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'api-1.0.0:openapi-view' %}"
                           hx-boost="false"
                           class="flex items-center gap-3 px-4 py-2 hover:bg-gray-100">
                            <img src="{% static 'assets/images/filter_black.svg' %}"
                                 width="20"
                                 height="20"
                                 alt="API docs" />
                            API Docs
                        </a>
                    </li>
                    {% if user.is_staff and user.is_superuser %}
                        <li>
                            <a href="{% url 'admin:index' %}"
                               hx-boost="false"
                               class="flex items-center gap-3 px-4 py-2 hover:bg-gray-100">
                                <img src="{% static 'assets/images/secure.svg' %}"
                                     width="20"
                                     height="20"
                                     alt="dropImage" />
                                Admin Panel
                            </a>
                        </li>
                    {% endif %}
                </ul>
                <ul class="py-2 text-sm text-gray-800">
                    <li>
                        <a href="{% url 'account_logout' %}"
                           class="flex items-center gap-3 px-4 py-2 text-red-600 hover:bg-gray-100">
                            <img src="{% static 'assets/images/dropdown/logout.svg' %}"
                                 width="20"
                                 height="20"
                                 alt="Logout Icon" />
                            Log Out
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
<script>
    function toggleDropdown() {
        const dropdown = document.getElementById("dropdownMenu");
        const arrowIcon = document.getElementById("arrowIcon");

        // Toggle dropdown visibility
        dropdown.classList.toggle("hidden");

        // Rotate the arrow
        if (dropdown.classList.contains("hidden")) {
            arrowIcon.classList.remove("rotate-0");
            arrowIcon.classList.add("rotate-90");
        } else {
            arrowIcon.classList.remove("rotate-90");
            arrowIcon.classList.add("rotate-0");
        }
    }

    // Close dropdown when clicking outside
    document.addEventListener("click", (event) => {
        const dropdown = document.getElementById("dropdownMenu");
        const button = document.getElementById("dropdownButton");
        const arrowIcon = document.getElementById("arrowIcon");

        if (!button.contains(event.target) && !dropdown.contains(event.target)) {
            dropdown.classList.add("hidden");
            arrowIcon.classList.remove("rotate-0");
            arrowIcon.classList.add("rotate-90"); // Reset the arrow
        }
    });
</script>
