# Generated by Django 5.1.6 on 2025-03-05 16:13

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0016_processeddocument_has_extracted_numbers'),
    ]

    operations = [
        migrations.CreateModel(
            name='ZipJob',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pr', 'Processing'), ('co', 'Completed'), ('fa', 'Failed')], default='pr', max_length=2)),
                ('progress', models.IntegerField(default=0)),
                ('initial_document_count', models.IntegerField(default=0)),
                ('successful_document_count', models.IntegerField(default=0)),
                ('failed_document_count', models.IntegerField(default=0)),
                ('s3_key', models.CharField(blank=True, default='', max_length=255)),
                ('s3_bucket', models.CharField(blank=True, max_length=255)),
                ('exists_in_s3', models.BooleanField(default=False)),
                ('task_id', models.CharField(blank=True, default='', max_length=255)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s', to='webapp.organization')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
                'default_manager_name': 'objects',
            },
        ),
    ]
