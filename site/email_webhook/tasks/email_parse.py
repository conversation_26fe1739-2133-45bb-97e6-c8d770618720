import datetime
import email
import hashlib
import re
import uuid
from pathlib import Path
from typing import NamedTuple

import boto3
import structlog
from bs4 import BeautifulSoup
from celery import Task, shared_task
from django.conf import settings
from django.utils import timezone
from playwright.sync_api import sync_playwright
from retrieval.tasks import demo_process_document
from webapp.models.documents import DocumentType, RawDocument
from webapp.models.emails import EmailIntake, UserForwardingRule
from webapp.models.line_item import LineItem
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser, Organization
from webapp.utils.slack import send_message_to_slack

CLIENT_ID = settings.OAUTH_SECRET.get("CLIENT_ID", None)
AUTHORITY = "https://login.microsoftonline.com/common"  # Use to control the account that the user authenticates with
CLIENT_SECRET = settings.OAUTH_SECRET.get("CLIENT_SECRET", None)
BUCKET = settings.AWS_STORAGE_BUCKET_NAME
# TODO: do a refactor of all constant variables to settings
EMAIL_DOCUMENTS_PATH = Path("email_documents")

logger = structlog.get_logger(__name__)


class RetrievalNotRunningError(Exception):
    pass


class RetrievalRunningNoSecurityCodeError(Exception):
    pass


class NoCredentialsFoundError(Exception):
    pass


class TooManyLineItemsFoundError(Exception):
    pass


class EmailParseError(Exception):
    pass


class S3BucketNotFoundError(Exception):
    pass


def extract_email_addresses(text: str) -> list[str]:
    # Regular expression pattern for matching email addresses (RFC 5322 compliant)
    email_pattern = r"""(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])"""  # noqa: E501
    email_addresses = re.findall(email_pattern, text, re.VERBOSE | re.MULTILINE | re.IGNORECASE)
    return list(set(email_addresses))  # Remove duplicates


def extract_emails_by_domain(text: str, domain: str) -> list[str]:
    """
    Extract email addresses from text that match a specific domain.

    Args:
        text: The text to search for email addresses
        domain: The domain to filter by (e.g. 'example.com')

    Returns:
        A list of email addresses matching the specified domain

    """
    all_emails = extract_email_addresses(text)
    # Ensure domain starts with @ for matching
    if not domain.startswith("@"):
        domain = "@" + domain

    # Filter emails that end with the specified domain
    return [email for email in all_emails if email.lower().endswith(domain.lower())]


def extract_all_emails(email_msg: email.message.EmailMessage) -> list[str]:
    text = get_text_from_email_message(email_msg, soup_seperator=" ")
    return list(set(extract_email_addresses(str(email_msg)) + extract_email_addresses(text)))


def get_email_from_s3(bucket: str, key: str) -> email.message.EmailMessage:  # type: ignore[attr-defined]
    # TODO: logger.bind
    logger.info("Processing email", bucket=bucket, key=key)
    sts = boto3.client("sts")
    logger.info("STS client created", identity=sts.get_caller_identity())
    s3 = boto3.client("s3")
    email_msg = None
    obj = s3.get_object(Bucket=bucket, Key=key)
    file_bytes: bytes = obj["Body"].read()
    email_msg = email.message_from_bytes(file_bytes, policy=email.policy.default)  # type: ignore[attr-defined]
    if email_msg is None:
        logger.error("Email message was not found", bucket=bucket, key=key)
        raise EmailParseError
    logger.info("Email message found and parsed", bucket=bucket, key=key)
    return email_msg


def get_text_from_email_message(msg: email.message.EmailMessage, soup_seperator: str = "\n") -> str:  # type: ignore[attr-defined]
    text = ""
    if msg.is_multipart():
        for part in msg.walk():
            content_type = part.get_content_type()
            content_disposition = str(part.get_content_disposition())
            text_part = ""
            if content_type == "text/plain" and "attachment" not in content_disposition:
                text_part = part.get_payload(decode=True).decode(errors="ignore")
                text += text_part + "\n"
            elif content_type == "text/html" and "attachment" not in content_disposition:
                html_part = part.get_payload(decode=True).decode(errors="ignore")
                soup = BeautifulSoup(html_part, "html.parser")
                text_part = soup.get_text(separator=soup_seperator)
                text += text_part + "\n"
    else:
        content_type = msg.get_content_type()
        if content_type == "text/plain":
            text = msg.get_payload(decode=True).decode(errors="ignore")
        elif content_type == "text/html":
            html_part = msg.get_payload(decode=True).decode(errors="ignore")
            soup = BeautifulSoup(html_part, "html.parser")
            text = soup.get_text(separator=soup_seperator)
    return text


def extract_security_code(text: str) -> str | None:
    # Adjust patterns to be more flexible in handling spaces and different wording
    # TODO: create a chatgpt test suite for keeping this up to date. (was generated by chatgpt)
    patterns = [
        r"(?:Security Code|Verification Code|Code)[:\s]*\*?(\d[\d\s]{3,14}\d)\*?",
        r"(\d[\d\s]{3,14}\d)\s+is\s+your\s+(?:security|verification)\s+code",
        r"Your\s+(?:security|verification)\s+code\s+is\s+(\d[\d\s]{3,14}\d)",
        r"Your\s+code\s+is:?\s+(\d[\d\s]{3,14}\d)",
        r"Your\s+security\s+code\s+is:?\s+(\d[\d\s]{3,14}\d)",
        r"Please\s+use\s+the\s+verification\s+code\s+below\s+to\s+log\s+in\s+to\s+your\s+account:[\s\S]*?(\d{4,8})",
        r"This\s+is\s+your\s+PIN[:\s]+([A-Za-z0-9]{4,8})",
    ]
    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(1).replace(" ", "")

    logger.info("No security code found")
    return None


def extract_attachments(msg: email.message.EmailMessage) -> dict[str, dict[str, any]]:
    attachments = {}
    full_html_string = ""
    for part in msg.walk():
        content_disposition = part.get("Content-Disposition", None)
        if content_disposition and "attachment" in content_disposition:
            filename = part.get_filename()
            attachment_bytes = part.get_payload(decode=True)
            attachments[filename] = {
                "bytes": attachment_bytes,
                "content_type": part.get_content_type(),
                "content_disposition": content_disposition,
            }
        else:
            # TODO: this html parse doesn't work for a email from gmail. need to iterate on parsing the email as a raw doc  # noqa: E501
            content_type = part.get_content_type()
            if content_type == "text/html":
                html = part.get_payload(decode=True).decode(part.get_content_charset())
                full_html_string += html
    if full_html_string:
        try:
            pdf_bytes = convert_html_to_pdf_bytes(full_html_string)
            attachments[msg.get("Subject") + "(Email).pdf"] = {
                "bytes": pdf_bytes,
                "content_type": "application/pdf",
            }
        except Exception as e:
            logger.exception("Error converting HTML to PDF", error=str(e))
    return attachments


def demo_get_line_item_from_email() -> LineItem:
    li = LineItem.objects.filter(
        organization__name="Demo",
        investing_entity__client__legal_name="Ayo Ekhator",
        investing_entity__legal_name="Ayodele Ekhator",
        investment__legal_name="Apax XI Fund, L.P.",
    ).first()
    if li is None:
        raise ValueError
    return li


def convert_html_to_pdf_bytes(html_string: str) -> bytes:
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        page.set_content(html_string)
        pdf_bytes = page.pdf()
        browser.close()
        return pdf_bytes


class EmailDocument(NamedTuple):
    name: str
    date: datetime.datetime
    doc_type: str
    content: bytes
    content_type: str
    content_disposition: str
    s3_key: str | None = None
    s3_bucket: str | None = None


def get_email_documents(msg: email.message.EmailMessage) -> dict[str, EmailDocument]:
    # TODO: what if the email itself is a document? What if there are links to follow to documents?
    attachments = {}
    if msg.is_multipart():
        for part in msg.walk():
            content_type = part.get_content_type()
            content_disposition = part.get("Content-Disposition", None)
            if content_disposition and "attachment" in content_disposition:
                filename = part.get_filename()
                attachment_bytes = part.get_payload(decode=True)
                # TODO: DEMO, This is a hack... Not everything is an Investment Update, need to extract posted_date
                attachments[filename] = EmailDocument(
                    filename,
                    timezone.now(),
                    DocumentType.ACCOUNT_STATEMENT,
                    attachment_bytes,
                    content_type,
                    content_disposition,
                )
    return attachments


def demo_save_document(user: BridgeUser, document: EmailDocument) -> str | None:
    from retrieval.core.managers.demo.demo_generic import get_demo_data

    lookup_dict, _ = get_demo_data()
    md5 = None
    if document.content is not None:
        hash_object = hashlib.md5(document.content)  # nosec # noqa: S324
        md5 = hash_object.hexdigest()
    if document.s3_bucket is not None and document.s3_key is not None:
        obj = boto3.client("s3").head_object(Bucket=document.s3_bucket, Key=document.s3_key)
        md5 = obj["ETag"].strip('"')
    metadata_values = lookup_dict[document.name.replace(".pdf", "")]
    if RawDocument.objects.filter(organization__name="Demo", md5=md5).exists():
        logger.info("Document already exists, skipping", md5=md5)
        return None

    raw_doc = RawDocument.objects.create(
        user=user,
        name=document.name,
        posted_date=document.date,
        original_posted_date=document.date,
        document_type=document.doc_type,
        original_document_type=document.doc_type,
        metadata=metadata_values,
        md5=md5,
    )
    # TODO: Check if we need to save, or dedup

    if document.content is None and document.s3_bucket is not None and document.s3_key is not None:
        raw_doc.s3_bucket = document.s3_bucket
        raw_doc.s3_key = document.s3_key
        raw_doc.exists_in_s3 = True
        raw_doc.save()
        return str(raw_doc.pk)

    s3_path = str(EMAIL_DOCUMENTS_PATH / "demo_raw_documents" / str(uuid.uuid4()) / document.name)
    boto3.client("s3").put_object(Bucket=BUCKET, Key=s3_path, Body=document.content)

    raw_doc.s3_bucket = BUCKET
    raw_doc.s3_key = s3_path
    raw_doc.exists_in_s3 = True
    raw_doc.save()
    return str(raw_doc.pk)


def run_msft_rule_manually(retrieval: Retrieval, text_content: str) -> bool:
    from retrieval.core.registry import RetrievalRegistry

    manager = RetrievalRegistry.get_retrieval_manager_for_retrieval(retrieval.created_by, retrieval)
    rules = manager.email_otp_rules()
    all_emails = extract_email_addresses(text_content)

    res = False
    if "senderContains" in rules:
        for email in all_emails:
            for sender in rules["senderContains"]:
                if sender in email:
                    res = True
    logger.info(
        "Result checking msft rules manually",
        all_emails=all_emails,
        rules=rules,
        manager=manager,
        res=res,
        retrieval_pk=retrieval.pk,
    )
    return res


def demo_email_logic(email_msg: email.message.EmailMessage) -> bool:
    line_item = demo_get_line_item_from_email()
    if line_item.created_by.is_demo and line_item.investing_entity.client.legal_name == "Ayo Ekhator":
        get_documents = get_email_documents(email_msg)
        if len(get_documents) > 0:
            for document in get_documents.values():
                raw_document_id = demo_save_document(line_item.created_by, document)
                if raw_document_id is not None:
                    demo_process_document.apply_async(
                        kwargs={
                            "user_id": line_item.created_by.pk,
                            "raw_document_id": raw_document_id,
                            "mark_as_read": False,
                        }
                    )  # type: ignore[attr-defined]
                    return True
    return False


def extract_mfa(retrieval_pk: str, text_content: str) -> bool:
    retrieval = Retrieval.objects.get(pk=retrieval_pk)
    if retrieval.retrieval_status not in [
        Retrieval.RetrievalStatus.PENDING_LOGIN,
        Retrieval.RetrievalStatus.BLOCKED_LOGIN_OTP,
    ]:
        logger.error(
            "Retrieval is not in BLOCKED_LOGIN_OTP or PENDING_LOGIN status",
            retrieval_pk=retrieval_pk,
            retrieval_status=retrieval.retrieval_status,
        )
        return False
    security_code = extract_security_code(text_content)
    if security_code is None:
        logger.error(
            "Security code not found in email, but a retrieval is running",
            retrieval_pk=retrieval_pk,
            retrieval_status=retrieval.retrieval_status,
        )
        return False
    # This ensures if theres 2 running retrievals, we pick the right one
    if not run_msft_rule_manually(retrieval, text_content):
        logger.error(
            "Wrong manager for retrieval", retrieval_pk=retrieval_pk, retrieval_status=retrieval.retrieval_status
        )
        return False
    if not retrieval.is_email_mfa:
        logger.error(
            "Retrieval is not an email MFA ",
            retrieval_pk=retrieval_pk,
            merged_portal_credential=retrieval.merged_portal_credential,
        )
        return False
    retrieval.update_token_otp(security_code)
    logger.info(
        "extract_mfa has successfully updated the token",
        retrieval_pk=retrieval_pk,
        retrieval_status=retrieval.retrieval_status,
    )
    return True


def route_email_organization(
    email_s3_key: str,
    text_content: str,
    organization: Organization,
) -> None:
    # A retrieval could be hanging on waiting for the OTP screen to load (still PENDING_LOGIN)
    # or it could be waiting for the OTP code (BLOCKED_LOGIN_OTP)
    in_flight_retrievals = Retrieval.objects.filter(
        organization=organization,
        retrieval_status__in=[
            Retrieval.RetrievalStatus.BLOCKED_LOGIN_OTP,
            Retrieval.RetrievalStatus.PENDING_LOGIN,
        ],
    )
    logger.info("Retrievals Found", length_in_flight_retrievals=len(in_flight_retrievals))
    was_email_for_mfa = False
    # TODO: should I check for MFA codes earlier to log the case where there are no retrievals but there is an MFA code?
    # if there's an MFA code, but no running retrievals, just log and ignore.
    for retrieval in in_flight_retrievals:
        if extract_mfa(retrieval.pk, text_content):
            logger.info("Extracted MFA for retrieval", retrieval_pk=retrieval.pk)
            was_email_for_mfa = True
            return
        logger.error("Could not extract MFA", retrieval_pk=retrieval.pk)
    if not was_email_for_mfa:
        raw_docs_created = extract_documents_from_email(email_s3_key)
        logger.info("Raw documents created", raw_docs_created=raw_docs_created)
        # TODO: trigger a notification to admin for doc labelling
    # TODO: if not a document and not a mfa code, log an error?


def route_email(email_s3_key: str, email_s3_bucket: str) -> None:
    email_msg = get_email_from_s3(email_s3_bucket, email_s3_key)
    text_content = get_text_from_email_message(email_msg)
    to_emails = extract_email_addresses(email_msg.get("To"))
    from_emails = extract_email_addresses(email_msg.get("From"))
    all_emails = extract_email_addresses(text_content)
    if len(to_emails) != 1:
        logger.error("Too many or too few to emails", to_emails=to_emails)
        raise ValueError
    to_email = to_emails[0]
    # Remove the intended email from the list of other emails to check
    if to_email in all_emails:
        all_emails.remove(to_email)

    logger.info("Emails", to_emails=to_emails, from_emails=from_emails, all_emails=all_emails)

    to_email_location, _ = to_email.split("@", 1)
    to_email_split = to_email_location.split("+")

    if to_email.startswith("mfa+") and not any("bridgeinvest.io" in e for e in all_emails):
        # Do the MFA Logic
        strategy, retrieval_pk = to_email_split
        logger.info("Doing the mfa logic for bridge managed email", strategy=strategy, retrieval_pk=retrieval_pk)
        res = extract_mfa(retrieval_pk, text_content)
        if not res:
            logger.error("Retrieval ran into no security code error", strategy=strategy, retrieval_pk=retrieval_pk)
            raise RetrievalRunningNoSecurityCodeError
        return

    if to_email.startswith("ayo-ekhator+bridge"):
        logger.info("Doing the demo logic")
        result = demo_email_logic(email_msg)
        if not result:
            logger.error("Demo email logic failed")
        return

    # TODO: Should we use "+" w/ organization?
    # What would some of the extra actions be?
    organization_string = to_email_split[0]
    try:
        organization = Organization.objects.get(name__iexact=organization_string)
    except Organization.DoesNotExist:
        organization = None
    if organization is not None:
        logger.info("Has an organization", organization=organization)
        # TODO: actually test this organization logic
        route_email_organization(email_s3_key, text_content, organization)
        return

    logger.info("Skipping email, no logic implemented", email_s3_key=email_s3_key)
    return


@shared_task(bind=True, track_started=True)
def route_email_async(self: Task, *, email_s3_key: str, email_s3_bucket: str) -> None:  # noqa: ARG001
    route_email(email_s3_key, email_s3_bucket)


def get_email_data(
    email_s3_key: str,
) -> tuple[email.message.EmailMessage, str, str, str, list[str], datetime.datetime, str]:
    r"""
    Get email data from S3 and parse it.

    Args:
        email_s3_key: The S3 key of the email to parse
    Returns:
        A tuple containing the parsed email data:

        email_msg: The email message object

        email_subject: The subject of the email

        to_email: The receiving @app.bridgeinvest.io email address

        from_email: The email address of the sender

        all_emails: A list of all email addresses found in the email

        parsed_date: The parsed date of the email

        text: The text content of the email

    """
    email_msg = get_email_from_s3(BUCKET, email_s3_key)
    to_emails = extract_emails_by_domain(email_msg.get("To"), "@app.bridgeinvest.io")
    if len(to_emails) != 1:
        logger.info("Too many or too few bridgeinvest.io to_emails", to_emails=to_emails)
    to_email = to_emails[0] if len(to_emails) >= 1 else ""
    from_emails = extract_email_addresses(email_msg.get("From"))
    if len(from_emails) != 1:
        logger.info("Too many or too few from_emails", from_emails=from_emails)
    from_email = from_emails[0] if len(from_emails) >= 1 else ""
    email_subject = email_msg.get("Subject")
    all_emails = extract_all_emails(email_msg)
    received_date = email_msg.get("Date")
    parsed_date = datetime.datetime.strptime(received_date, "%a, %d %b %Y %H:%M:%S %z")
    text = get_text_from_email_message(email_msg, soup_seperator=" ")
    return (email_msg, email_subject, to_email, from_email, all_emails, parsed_date, text)


def extract_documents_from_email(email_s3_key: str) -> int:
    if EmailIntake.objects.filter(s3_key=email_s3_key, s3_bucket=BUCKET).exists():
        logger.debug("Email intake already exists, skipping", s3_key=email_s3_key, s3_bucket=BUCKET)
        return 0
    (email_msg, email_subject, to_email, from_email, all_emails, parsed_date, text) = get_email_data(email_s3_key)
    try:
        user_forwarding_rule = UserForwardingRule.objects.get(receiving_email__email=from_email)
        logger.info("found User Forwarding Rule", pk=user_forwarding_rule.pk)
        user = user_forwarding_rule.created_by
    except Exception:
        # TODO: get an admin user from to_email ?
        msg = f"User Forwarding Rule with from_email {from_email} not found"
        logger.exception(msg)
        return 0
    logger.info("creating EmailIntake user", user=user)
    email_intake = EmailIntake.objects.create(
        user=user,
        email_subject=email_subject,
        to_email=to_email,
        from_email=from_email,
        all_emails=all_emails,
        received_date=parsed_date,
        email_text_content=text,
        s3_key=email_s3_key,
        s3_bucket=BUCKET,
    )
    logger.info(
        "Created EmailIntake",
        email_intake_pk=email_intake.pk,
        to_email=to_email,
        from_email=from_email,
    )

    raw_docs_created = 0
    raw_doc_ids = []
    attachments = extract_attachments(email_msg)
    for doc_name, document in attachments.items():
        md5 = None
        if document["bytes"] is not None and document["content_type"] in [
            "application/pdf",
            "application/octet-stream",
        ]:
            hash_object = hashlib.md5(document["bytes"])  # nosec # noqa: S324
            md5 = hash_object.hexdigest()
        if md5 is None:
            logger.info("Document is not a valid pdf or has no bytes, skipping", doc_name=doc_name, md5=md5)
            continue
        if md5 is not None and RawDocument.objects.filter(md5=md5).exists():
            logger.info("Document already exists, skipping", md5=md5)
            continue
        raw_doc = RawDocument.objects.create(
            user=user,
            doc_hash=md5,
            doc_hash_version=1,
            doc_hash_source="email_intake",
            email_intake=email_intake,
            md5=md5,
            name=doc_name,
            posted_date=parsed_date,
            original_posted_date=parsed_date,
        )
        logger.info(
            "Created RawDocument",
            raw_doc_pk=raw_doc.id,
            email_intake_pk=email_intake.id,
            doc_name=doc_name,
            md5=md5,
        )
        raw_doc_s3_path = str(EMAIL_DOCUMENTS_PATH / str(email_intake.pk) / "raw_documents" / str(raw_doc.pk))
        logger.debug("Putting new document to S3", bucket=BUCKET, s3_path=raw_doc_s3_path)
        boto3.client("s3").put_object(Bucket=BUCKET, Key=raw_doc_s3_path, Body=document["bytes"])
        head_obj = boto3.client("s3").head_object(Bucket=BUCKET, Key=raw_doc_s3_path)
        s3_md5 = head_obj["ETag"].strip('"')
        logger.debug("Put document in s3", s3_md5=s3_md5, raw_doc_md5=raw_doc.md5, calced_md5=md5)
        if s3_md5 != raw_doc.md5:
            logger.exception("MD5s do not match")
            raise ValueError
        raw_doc.s3_bucket = BUCKET
        raw_doc.s3_key = raw_doc_s3_path
        raw_doc.exists_in_s3 = True
        raw_doc.save()
        raw_docs_created += 1
        raw_doc_ids.append(raw_doc.id)
        logger.debug("Finished putting new document to S3")
    message = f"{raw_docs_created} raw docs generated from a new email intake: `{email_intake.pk}`"
    for uploaded_id in raw_doc_ids:
        label_url = f"https://{settings.DOMAIN_NAME}/admin/webapp/rawdocument/{uploaded_id}/label"
        message += f"- `{uploaded_id}`: <{label_url}|label>\n"
    send_message_to_slack(
        channel="#user-actions",
        message=message,
    )
    return raw_docs_created
