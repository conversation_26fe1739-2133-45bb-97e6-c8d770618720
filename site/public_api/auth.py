from typing import <PERSON><PERSON>uple

from django.contrib.auth.hashers import check_password, make_password
from django.http import HttpRequest
from django.utils.crypto import get_random_string
from ninja.security import APIKeyHeader
from webapp.models.user import Bridge<PERSON>ser

from .models import APIKey


class KeyData(NamedTuple):
    prefix: str
    key: str
    hashed_key: str


def generate_key() -> KeyData:
    prefix = get_random_string(8)
    key = get_random_string(56)
    hashed_key = make_password(key)
    return KeyData(prefix, key, hashed_key)


class APIKeyAuth(APIKeyHeader):
    param_name = "X-Bridge-API-Key"

    def authenticate(self, request: HttpRequest, key: str | None) -> BridgeUser | bool:
        if key and "." in key:
            data = key.split(".")
            prefix = data[0]
            key = data[1]

            persistent_key = APIKey.objects.filter(prefix=prefix).first()

            if persistent_key and check_password(key, persistent_key.hashed_key) and persistent_key.is_valid:
                user = persistent_key.user

                if user and user.is_active:
                    request.user = user
                    return user

        if request.user.is_authenticated and request.user.is_active:
            return request.user

        return False
