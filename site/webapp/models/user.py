import uuid
from abc import <PERSON><PERSON><PERSON>
from typing import Any, ClassVar

import structlog
from asgiref.sync import sync_to_async
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
from django.db.models.base import ModelBase
from django.http import HttpRequest
from phonenumber_field.modelfields import PhoneNumberField

logger = structlog.get_logger(__name__)


# https://stackoverflow.com/questions/50085658/inheriting-from-both-abc-and-django-db-models-model-raises-metaclass-exception
# https://gist.github.com/gavinwahl/7778717
class ModelABCMeta(ABCMeta, ModelBase):
    pass


# https://dipkumar.dev/posts/essential-db-design-1/
class SimpleBaseModel(models.Model, metaclass=ModelABCMeta):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        abstract = True


# TODO: Lower case role names and have standardized descriptions for each role.
# TODO: Get rid of admin role type, just have manager and reader.
# TODO: Backfill data for this migration to be successful.
# TODO: Seed descriptions for each role.
class Role(SimpleBaseModel):
    class RoleName(models.TextChoices):
        MANAGER = "manager", "Manager"
        VIEWER = "viewer", "Viewer"

    STANDARD_DESCRIPTIONS: ClassVar[dict[RoleName, str]] = {
        RoleName.MANAGER: "Full access to all objects and settings within an organization.",
        RoleName.VIEWER: "Gated line item access to view data within an organization.",
    }

    name = models.CharField(max_length=255, unique=True, choices=RoleName.choices)
    description = models.TextField(blank=True)
    users: "BridgeUser"

    def __str__(self) -> str:
        return self.get_name_display()

    def save(self, *args: list[Any], **kwargs: dict[str, Any]) -> None:
        self.full_clean()
        if not self.description.strip() and self.name in self.STANDARD_DESCRIPTIONS:
            self.description = self.STANDARD_DESCRIPTIONS[self.name]
        super().save(*args, **kwargs)


class Organization(SimpleBaseModel):
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True)
    users: "BridgeUser"

    def __str__(self) -> str:
        return self.name


class BridgeUserManager(BaseUserManager):
    def create_user(  # noqa: PLR0913
        self,
        username: str,
        email: str,
        first_name: str,
        last_name: str,
        organization: str,
        roles: list[str],
        password: str,
        contact_number: str = "",
    ) -> "BridgeUser":
        """
        Creates and saves a User with the given email, date of
        birth and password.
        """
        if not email:
            msg = "Users must have an email address"
            raise ValueError(msg)

        o_organization = Organization.objects.get(id=organization)
        o_roles = Role.objects.filter(id__in=roles)
        user = self.model(
            username=username,
            email=self.normalize_email(email),
            organization=o_organization,
            first_name=first_name,
            last_name=last_name,
            invitation_token=uuid.uuid4(),  # Automatically generate the token
            contact_number=contact_number,
        )
        user.set_password(password)
        user.save(using=self._db)
        for role in o_roles:
            user.roles.add(role)
        user.save(using=self._db)
        return user

    def create_superuser(  # noqa: PLR0913 [too many args]
        self,
        username: str,
        email: str,
        first_name: str,
        last_name: str,
        organization: str,
        roles: list[str],
        password: str,
        contact_number: str = "",
    ) -> "BridgeUser":
        user = self.create_user(
            username,
            email,
            password=password,
            first_name=first_name,
            last_name=last_name,
            organization=organization,
            roles=roles,
            contact_number=contact_number,
        )
        user.is_admin = True
        user.is_superuser = True
        user.is_staff = True
        user.save(using=self._db)
        return user


class BridgeUser(AbstractUser, SimpleBaseModel):  # type: ignore[misc]
    roles = models.ManyToManyField("Role", related_name="%(class)s")
    organization = models.ForeignKey("Organization", on_delete=models.CASCADE, related_name="%(class)s")
    is_active = models.BooleanField(default=True)
    is_admin = models.BooleanField(default=False)
    invitation_token = models.UUIDField(null=True, blank=True, default=None)  # New field
    tos_accepted_at = models.DateField(null=True, blank=True)  # Date when user accepted TOS
    tos_last_updated_at = models.DateField(default="2025-02-23")  # Last TOS update date
    contact_number = PhoneNumberField(blank=True)
    user_view_preferences = models.JSONField(default=dict, blank=True)  # dashboard view sort
    objects = BridgeUserManager()  # type: ignore[attr-defined]
    REQUIRED_FIELDS = ["roles", "organization", "email", "first_name", "last_name", "contact_number"]  # noqa: RUF012

    def __str__(self) -> str:
        return self.username

    @property
    def is_demo(self) -> bool:
        return self.organization.name.startswith("Demo ") or self.organization.name == "Demo"

    @sync_to_async
    def ais_demo(self) -> bool:
        return self.organization.name.startswith("Demo ") or self.organization.name == "Demo"

    @property
    def is_demo_post_email(self) -> bool:
        from webapp.models import ProcessedDocument

        demo_email_doc_name = (
            "Apax XI USD L.P._Bridge Buyout AP Fund I, LP_Quarterly Financial Summary Q4-2024 - Apax XI USD.pdf"
        )

        return ProcessedDocument.objects.for_user(self).filter(name=demo_email_doc_name).exists()

    @property
    def is_demo_has_docs(self) -> bool:
        from webapp.models import ProcessedDocument

        return ProcessedDocument.objects.for_user(self).exists()

    @property
    def is_manager(self) -> bool:
        manager_role = {Role.RoleName.MANAGER}
        user_roles = set(self.roles.values_list("name", flat=True))
        return any(role in manager_role for role in user_roles)


from django.contrib.auth.signals import user_logged_out
from django.dispatch import receiver


@receiver(user_logged_out)
def cleanup_demo(sender: models.Model, user: BridgeUser, request: HttpRequest, **kwargs) -> None:  # noqa: ANN003, ARG001
    from webapp.models import (
        BridgeEmail,
        Client,
        CustomerEmailCredential,
        InvestingEntity,
        Investment,
        LineItem,
        MergedPortalCredential,
        MultiFactorAuthentication,
        Portal,
        PortalCredential,
        ProcessedDocument,
        RawDocument,
        Retrieval,
    )

    # WARNING: I am very paranoid about this code in production,
    # idgaf if a customer accidentally see's demo shit, but we cannot delete customer data.
    if (
        user.is_demo
        and user.email == "<EMAIL>"
        and user.first_name == "Tom"
        and user.last_name == "Jones"
    ):
        models_to_clean = [
            LineItem,
            CustomerEmailCredential,
            Client,
            BridgeEmail,
            InvestingEntity,
            Investment,
            Portal,
            Retrieval,
            MergedPortalCredential,
            ProcessedDocument,
            RawDocument,
            PortalCredential,
            MultiFactorAuthentication,
        ]

        for model in models_to_clean:
            model.objects.for_user(user=user).delete()
