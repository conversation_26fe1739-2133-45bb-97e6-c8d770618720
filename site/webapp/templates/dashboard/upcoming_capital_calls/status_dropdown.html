<!-- DEBUG: {{ capital_call_status_choices|length }} choices - {{ capital_call_status_choices }} -->
<td class="px-3 py-3 text-[#343D36] font-inter text-[14px]">
    <input type="hidden"
           name="capital_call_document_pk"
           value="{{ capital_call_document.pk }}">
    <div class="relative flex items-center w-full">
        <select class="rounded-md border px-2 py-1 text-[14px] text-[#343D36] font-medium font-sans border-1 {% if capital_call_document.status == 'executed' %} border-[#689757] bg-[#EAF4EA] {% else %} border-[#3B82F6] bg-[#EAF1FB] {% endif %} focus:outline-none focus:ring-0"
                data-capital-call-id="{{ capital_call_document.pk }}"
                data-current-status="{{ capital_call_document.status }}"
                hx-post="{% url 'update_capital_call_status' %}"
                hx-target="closest td"
                hx-swap="outerHTML"
                hx-trigger="change"
                hx-include="closest td"
                name="status">
            {% for value, label in capital_call_status_choices %}
                <option value="{{ value }}"
                        {% if capital_call_document.status == value %}selected{% endif %}>{{ label }}</option>
            {% endfor %}
        </select>
    </div>
</td>
