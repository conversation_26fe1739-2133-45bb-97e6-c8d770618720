import datetime

from aenum import Enum
from pydantic import BaseModel, Field
from pydantic.json_schema import GetJsonSchemaHandler, JsonSchemaValue
from pydantic_core import core_schema
from webapp.models import DocumentType, SubDocumentType


class EnumSchemaDoc(Enum):
    @classmethod
    def __get_pydantic_json_schema__(
        cls, core_schema: core_schema.CoreSchema, handler: GetJsonSchemaHandler
    ) -> JsonSchemaValue:
        schema = handler(core_schema)
        schema["documentation"] = {e.value: e.__doc__ for e in cls}
        return schema


# https://stackoverflow.com/questions/********/how-to-provide-additional-information-on-enum-values-to-an-llm-in-langchain
DOC_TYPES_DOCUMENTATION = {
    DocumentType.CAPITAL_CALL: """
    capital calls notices, where the effective date is their due date.
     For Capital calls, the Effective date is always greater than posted date.
     """,
    DocumentType.DISTRIBUTION_NOTICE: """
    distribution notices, where the effective date is the date money is transferred.
    """,
    DocumentType.ACCOUNT_STATEMENT: """
    account statements where the effective date is the date the statement was generated.
    """,
    DocumentType.INVESTMENT_UPDATE: """
    investment updates, where the effective date is the reporting date of the financial data.
    """,
    DocumentType.FINANCIAL_STATEMENTS: """
    financial statements, where the effective date is the reporting date of the financial data.
    """,
    DocumentType.TAX: """
    tax documents, where the effective date is the reporting date of the data.
    """,
    DocumentType.LEGAL: """
    legal documents, where the effective date is typically the same date as the posted date.
    """,
    DocumentType.OTHER: """
    general notices, where the effective date is the reporting date of the financial data.
    """,
    DocumentType.UNKNOWN: """
    unknown documents, NEVER choose this option.
    """,
}
doc_choices_dict = {"_init_": "value __doc__"}
for k, v in DocumentType.choices:
    doc_choices_dict[k] = str(v), DOC_TYPES_DOCUMENTATION.get(k, "no documentation")
DocumentTypeEnum = EnumSchemaDoc("DocumentTypeEnum", doc_choices_dict)

SUB_DOCUMENT_TYPES_DOCUMENTATION = {
    SubDocumentType.AUDITED_FINANCIAL_STATEMENTS: """
    audited financial statements, is always a sub document type of financial statements.
    """,
    SubDocumentType.UNAUDITED_FINANCIAL_STATEMENTS: """
    unaudited financial statements, is always a sub document type of financial statements.
    """,
    SubDocumentType.QUARTERLY_LETTER: """
    quarterly letters, is always a sub document type of investment updates.
    """,
    SubDocumentType.ANNUAL_LETTER: """
    annual letters, is always a sub document type of investment updates.
    """,
    SubDocumentType.INVESTMENT_MEMO: """
    investment memos, is always a sub document type of investment updates.
    """,
    SubDocumentType.TAX_ESTIMATES: """
    tax estimates, is always a sub document type of tax documents.
    """,
    SubDocumentType.TAX_FINAL: """
    tax final, is always a sub document type of tax documents.
    """,
    SubDocumentType.OTHER: """
    other documents, is always a sub document type of financial statements,
    investment updates, or tax documents that do not fit into the other
    categories. This is a catch all for documents that do not fit into
    the other categories.
    """,
}
sub_doc_choices_dict = {"_init_": "value __doc__"}
for k, v in SubDocumentType.choices:
    sub_doc_choices_dict[k] = str(v), SUB_DOCUMENT_TYPES_DOCUMENTATION.get(k, "no documentation")
SubDocumentTypeEnum = EnumSchemaDoc("SubDocumentTypeEnum", sub_doc_choices_dict)


class ConfidenceBracketsEnum(Enum):
    """
    Enum for confidence brackets.
    """

    NO = "no_confidence"
    LOW = "low_confidence"
    MEDIUM = "medium_confidence"
    HIGH = "high_confidence"
    REALLY_HIGH = "really_high_confidence"


class LineItemModel(BaseModel):
    investing_entity_name: str = Field(
        description="""
The name of the investing entity, could be an individual, a company, or a trust
EVERY SINGLE BIT OF PUNCTIONATION MATTERS!!!
"LLC" is not the same as "L.L.C." or "LLC."
"Entity, L.P." is not the same as "Entity L.P." which is not the same as "Entity, LP"
This is likely not at all similar to the investment_name
"""
    )
    client_investor_name: str = Field(
        description="""The name of the investor, owner of the investing entity,
this will be a person with a normal name, like Tom Jones or a couple with a normal name like Tom and Mary Jones.
This is not the name of the investing entity, this is the name of the person or couple that owns the investing entity.
MAKE SURE THAT THIS IS A HUMAN NAME NOT A COMPANY NAME.
If this information is not available, use the investing entity name."""
    )
    investment_name: str = Field(
        description="""
The name of the investment that the investing entity is investing in.
This would typically be the name of the fund. Funds can make investments,
so don't confuse the investing_entity with the fund that is being invested in.
This is likely not at all similar to the investing_enttiy_name.
"""
    )
    line_item_confidence: ConfidenceBracketsEnum = Field(
        description="""The confidence in the line item that was correctly extracted when filling out this value,
reflect back on the input document and make sure you reference the source data when making this decision."""
    )


class DocVaultOutput(BaseModel):
    """
    Information about private equity documents stored in a document table.
    The document table is a database table that stores information about private equity documents.

    Make sure when evaluating a new piece of data to independently check each piece of the user's query.
    Remember that if the document name or other information in the new metadata contains an indication of the category
    use that category information to inform the subsequent field extraction.

    You must fill out every field to the best of your ability.

    When calling this task, make sure to think about your reasoning for the response,
        and record that before calling the tool. Make references to the specific parts
        of the document that you are using to make your decision.

    When judging the confidence, make sure you are not overly sure about yourself,
        think thru step by step what would make you really_high_confidence to no_confidence
    """

    pk: str = Field(description="The primary key of the document")
    effective_date: datetime.date = Field(
        description="""The effective date of the document.
For capital calls its the due date. This should be strictly greater than the posted date, by at least a few days.
For reporting documents like financial statements, capital account statements, or tax documents its the reporting date,
    this should be strictly less than the posted date. Typically this should be very close to the quarter end date.
For all other documents, or if you're unsure its the exact same as posted date.
    """
    )
    effective_date_confidence: ConfidenceBracketsEnum = Field(
        description="""The confidence in the effective date that was extracted from the DocVaultInput object"""
    )
    document_type: DocumentTypeEnum = Field(description="The category of the private equity investment document")
    sub_document_type: SubDocumentTypeEnum | None = Field(
        default=None, description="The sub category of the private equity investment document"
    )
    document_type_confidence: ConfidenceBracketsEnum = Field(
        description="""The confidence in the document_type that was extracted from the DocVaultInput object"""
    )
    line_items: list[LineItemModel] = Field(
        description="""
The investing entity and the investor referenced in this document.
If there are multiple entities referenced, enumerate thru each of on of them,
do not write "Multiple entities" or "multiple investors".
Make sure to reference the possible_line_items in the DocVaultInputKnownLineItems object if available.
Calls to "doc_vault_insert" will fail if "possible_line_items" is not used.
"""
    )


class DocVaultInput(BaseModel):
    pk: str = Field(description="The primary key of the document")
    scraped_posted_date: datetime.date = Field(
        description="""The date the document was posted to the portal and available for the user to download.
        This could also be considered document creation date, however we are scraping this data from random sources,
        so this could just be the date we scraped it from the website as well."""
    )
    scraped_document_type: DocumentTypeEnum = Field(
        description="""The category of the private equity investment document that was estimated by either:
            what the website said when we scraped it or loosely inferred. This is often wrong."""
    )
    scraped_sub_document_type: SubDocumentTypeEnum | None = Field(
        default=None,
        description="""The sub category of the private equity investment document that was estimated by either:
            what the website said when we scraped it or loosely inferred. This is often wrong.""",
    )
    first_page: str = Field(description="The first page of the document")
    metadata: str = Field(description="The metadata of the document from the website we downloaded it from")


class DocVaultInputKnownLineItems(DocVaultInput):
    possible_line_items: list[LineItemModel] = Field(
        description="""
Possible options for line items referenced in this document.
When using this field, make sure you use the EXACT values of one of these line items.
"""
    )


class LineItemOutput(BaseModel):
    line_items: list[LineItemModel] = Field(
        description="""
The investing entity and the investor referenced in this document.
If there are multiple entities referenced, enumerate thru each of on of them,
do not write "Multiple entities" or "multiple investors".
Make sure to reference the possible_line_items in the DocVaultInputKnownLineItems object if available.
Calls to "doc_vault_insert" will fail if "possible_line_items" is not used.
"""
    )


class DocVaultTaskIO(BaseModel):
    pk: str = Field(description="The primary key of the document")
    input: DocVaultInput = Field(description="The input to the model")
    output: DocVaultOutput | None = Field(description="The output to the tool")


class DocVaultTaskWithKnownLineItemsIO(BaseModel):
    pk: str = Field(description="The primary key of the document")
    input: DocVaultInputKnownLineItems = Field(description="The input to the model")
    output: DocVaultOutput | None = Field(description="The output to the tool")


class CountingLineItemModels(BaseModel):
    """
    A model that counts the number of line items in a document.
    """

    line_item: LineItemModel = Field(description="The line item to count")
    count: int = Field(description="The number of times this line item has been predicted across documents")


class LineItemSummary(BaseModel):
    document_type_to_line_items_count: dict[DocumentTypeEnum, list[CountingLineItemModels]] = Field(
        description="""
This represents the count of line items for each document type."""
    )


class LineItemClassification(BaseModel):
    accurate_line_items: list[LineItemModel] = Field(
        description="""
Line items that we believe are accurate and are correct entities
"""
    )
    wrong_line_items: list[LineItemModel] = Field(
        description="""
Line items that we believe are wrong and are incorrect entities
"""
    )
