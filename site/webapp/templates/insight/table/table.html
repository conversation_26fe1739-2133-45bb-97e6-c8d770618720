<div>
    <div class="DetailSection p-8">
        <div class="OverflowScroll self-stretch">
            <table class="Table w-full bg-white">
                <thead>
                    <tr class="sticky top-0 z-20 min-w-full">
                        <!-- Asset-Level Detail -->
                        <th class="TableHeaderCell bg-base-darker-olive-muted text-left text-xs font-medium p-3 text-white"
                            colspan="8">Asset-Level Detail</th>
                        <!-- Investment Summary -->
                        <th class="TableHeaderCell bg-primary-olive text-left text-xs font-medium p-3 border-l"
                            colspan="3">Investment Summary</th>
                        <!-- Performance Metrics -->
                        <th class="TableHeaderCell bg-primary-olive text-left text-xs font-medium p-3 border-l border-r  min-w-[150px]"
                            colspan="2">Performance Metrics</th>
                    </tr>
                    <tr class="top-[40px] sticky z-10 min-w-full">
                        <!-- Asset-Level Detail Columns -->
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3 min-w-[235px]">Holding</th>
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3 min-w-[280px]">Fund</th>
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3 min-w-[145px]">Entity</th>
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-1 min-w-[20px]">Year</th>
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3 min-w-[105px]">Asset Class</th>
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3">Sector</th>
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3">Sub-Sector</th>
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3 border-r">Geography</th>
                        <!-- Investment Summary Columns -->
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3">Current Value</th>
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3">Distributions</th>
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3 border-r">Paid In Capital</th>
                        <!-- Performance Metrics Columns -->
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3">DPI</th>
                        <th class="TableHeaderCell bg-secondary-orange-beige text-left text-xs font-medium p-3 border-r ">TVPI</th>
                    </tr>
                </thead>
                <tbody>
                    {% if financial_data %}
                        {% include "insight/table/row.html" %}
                    {% else %}
                        <tr>
                            <td colspan="13" class="text-center">
                                <div class="MetricItem h-[82px] p-8 bg-white border border-[#eceee8] justify-center items-center w-full">
                                    <div class="Text text-[#343d36] text-base font-bold font-['Inter'] leading-[18px]">No data available</div>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
