import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON>enerator
from pathlib import Path

import structlog
from playwright.async_api import Locator, expect
from webapp.models.documents import DocumentType
from webapp.models.portal import MFAType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class DynamoPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "dynamosoftware.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "Dynamo (Audax)"),
        ]

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError

        email_input_locator = self.page.locator("input[id='Username']")
        next_button_locator = self.page.locator("input[type='button'][id='nextButton']")
        if await email_input_locator.count() == 1 and await email_input_locator.is_visible():
            await email_input_locator.click()
            await email_input_locator.fill(username)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        if await next_button_locator.count() == 1 and await next_button_locator.is_visible():
            await next_button_locator.click()
            await asyncio.sleep(2)
        else:
            raise CouldNotFindHTMLElementError

        password_input_locator = self.page.locator("input[id='Password']")
        login_button_locator = self.page.locator("input[type='submit'][id='loginButton']")
        if await password_input_locator.count() == 1 and await password_input_locator.is_visible():
            await password_input_locator.click()
            await password_input_locator.fill(password)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        if await login_button_locator.count() == 1 and await login_button_locator.is_visible():
            await login_button_locator.click()
            await asyncio.sleep(2)
        else:
            raise CouldNotFindHTMLElementError

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError

        new_code_text_ct = await self.page.get_by_text("enter the code").count()
        authenticator_input_ct = await self.page.locator("input[id='appCodeValue']").count()
        sms_input_ct = await self.page.locator("input[id='tbSafePassCode']").count()
        verify_code_ct = await self.page.locator("button[id='btnVerify']").count()
        return (new_code_text_ct + authenticator_input_ct + sms_input_ct + verify_code_ct) > 0

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError

        otp_input_locator = None
        if await self.get_multi_factor_authentication_type() == MFAType.SMS:
            otp_input_locator = self.page.locator("input[id='tbSafePassCode']")
        elif await self.get_multi_factor_authentication_type() == MFAType.AUTHENTICATOR:
            otp_input_locator = self.page.locator("input[id='appCodeValue']")
        if otp_input_locator and await otp_input_locator.count() == 1 and await otp_input_locator.is_visible():
            await otp_input_locator.click()
            await otp_input_locator.fill(otp)
            await asyncio.sleep(3)
        else:
            logger.error(
                "Could not find OTP input - check if MFA type is supported",
                merged_portal_credential=self.retrieval.merged_portal_credential,
            )
            raise CouldNotFindHTMLElementError
        remember_me_locator = self.page.locator("input[type='checkbox'][id='rememberEndpoint']")
        if await remember_me_locator.count() == 1 and await remember_me_locator.is_visible():
            await remember_me_locator.check()
            await asyncio.sleep(2)
        else:
            raise CouldNotFindHTMLElementError
        verify_code_button_locator = self.page.locator("button[id='btnVerify']")
        if await verify_code_button_locator.count() == 1 and await verify_code_button_locator.is_visible():
            await verify_code_button_locator.click()
            await asyncio.sleep(15)
        else:
            raise CouldNotFindHTMLElementError

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return await self.page.get_by_text("Log out").count() > 0

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: C901, PLR0912, PLR0915
        if self.page is None:
            raise ManagerNotInitializedError

        starting_point = await self.get_starting_point()
        side_section_locator = self.page.locator("div.aside-section")
        side_section_count = await side_section_locator.count()
        for s in range(side_section_count):
            side_section = side_section_locator.nth(s)
            inner_div_with_data_index = side_section.locator("div[data-index]")
            inner_divs_with_data_index_count = await inner_div_with_data_index.count()
            if inner_divs_with_data_index_count == 0:
                continue

            ul_locators = side_section_locator.locator("ul.cllps-c")
            ul_locators_count = await ul_locators.count()

            doc_category_outer_starting_idx = 0
            if starting_point and "doc_category_outer_idx" in starting_point:
                logger.info("skipping to outer doc category starting point", starting_point=starting_point)
                doc_category_outer_starting_idx = starting_point["doc_category_outer_idx"]
            for i in range(doc_category_outer_starting_idx, ul_locators_count):
                docs_page = ul_locators.nth(i)
                li_locators = docs_page.locator("li")
                li_locators_count = await li_locators.count()
                logger.info(
                    "update_check_point",
                    checkpoint="doc_category_outer_idx",
                    doc_category_outer_idx=i,
                )
                await self.update_check_point({"doc_category_outer_idx": i})

                doc_category_inner_starting_idx = 0
                use_starting_point = i == doc_category_outer_starting_idx
                if starting_point and "doc_category_inner_idx" in starting_point and use_starting_point:
                    logger.info("skipping to inner doc category starting point", starting_point=starting_point)
                    doc_category_inner_starting_idx = starting_point["doc_category_inner_idx"]
                for j in range(doc_category_inner_starting_idx, li_locators_count):
                    doc_page = li_locators.nth(j)
                    a_tag = doc_page.locator("a")
                    page_title = await a_tag.inner_text()
                    a_tag_inner_div = a_tag.locator("div")
                    a_tag_inner_div_icon = await a_tag_inner_div.get_attribute("class")
                    if a_tag_inner_div_icon != "icon-document icon":
                        continue
                    await a_tag.click()
                    await asyncio.sleep(3)
                    logger.info(
                        "update_check_point",
                        checkpoint="doc_category_inner_idx",
                        doc_category_inner_idx=j,
                    )
                    await self.update_check_point({"doc_category_inner_idx": j})

                    table_outer_locator = self.page.locator("div[id='pvEntityList']")
                    table_locator = table_outer_locator.locator("table")
                    table_body_locator = table_locator.locator("tbody")
                    table_rows = table_body_locator.locator("tr")
                    table_rows_count = await table_rows.count()

                    if table_rows_count == 0:
                        continue

                    get_more_locator = self.page.locator("div.get-more-wrapper")
                    get_more_a_tag = get_more_locator.locator("a")
                    while True:
                        try:
                            await expect(get_more_a_tag).to_have_count(1, timeout=5000)
                            await get_more_a_tag.click()
                        except:  # noqa: E722
                            logger.info("expanded all see more")
                            break
                    await self.page.evaluate("window.scrollTo(0, 0)")

                    table_rows_count = await table_rows.count()
                    doc_row_starting_idx = 0
                    use_starting_point = j == doc_category_inner_starting_idx
                    if starting_point and "doc_row_idx" in starting_point and use_starting_point:
                        logger.info("skipping to doc row starting point", starting_point=starting_point)
                        doc_row_starting_idx = starting_point["doc_row_idx"]
                    for k in range(doc_row_starting_idx, table_rows_count):
                        logger.info(
                            "update_check_point",
                            checkpoint="doc_row_idx",
                            doc_row_idx=k,
                        )
                        await self.update_check_point({"doc_row_idx": k})
                        row = table_rows.nth(k)
                        row_metadata = await self._get_row_metadata(row)
                        row_metadata["Category"] = page_title
                        metadata = json.dumps(row_metadata)
                        if await self.should_download_document(metadata):
                            download_button = row.locator("a.doc-downloader")
                            doc_name, doc_content = await self._download_document(download_button)
                            if doc_content is None:
                                continue
                            doc_type = self._document_mapper(row_metadata["Category"])
                            datetime_date = datetime.datetime.strptime(row_metadata["Documentdate"], "%m/%d/%Y")  # noqa: DTZ007
                            doc = RawDocumentTuple(
                                name=doc_name,
                                date=datetime_date,
                                doc_type=doc_type,
                                content=doc_content,
                                raw_metadata=metadata,
                                doc_hash=self._doc_hash(metadata),
                            )
                            if doc is not None:
                                yield doc

    async def _get_row_metadata(self, row: Locator) -> dict[str, str]:
        row_metadata = {}
        cells = row.locator("td[data-ps]")
        cells_count = await cells.count()
        for i in range(cells_count):
            cell = cells.nth(i)
            row_data_ps = await cell.get_attribute("data-ps")
            if row_data_ps == "FileIcon":
                continue
            row_metadata[row_data_ps] = await cell.inner_text()
        return row_metadata

    async def _download_document(self, download_button: Locator) -> tuple[str, bytes | None]:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        async with self.page.expect_download(timeout=60000) as download_info:
            await download_button.click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.error("Unsupported file format", file_name_suffix=file_name.suffix)
        file_name.unlink()
        return download.suggested_filename, content

    def _document_mapper(self, document_type: str | None) -> DocumentType | str:
        if document_type is None:
            return DocumentType.UNKNOWN

        mapping = {
            "Capital Calls": DocumentType.CAPITAL_CALL,
            "Distributions": DocumentType.DISTRIBUTION_NOTICE,
            "Summary Reports": DocumentType.ACCOUNT_STATEMENT,
            "Legal": DocumentType.LEGAL,
            "Other Correspondence": DocumentType.OTHER,
            "K-1s": DocumentType.TAX,
            "Tax Estimates": DocumentType.TAX,
            "Tax Distribution": DocumentType.TAX,
            "Financial Statements": DocumentType.FINANCIAL_STATEMENTS,
        }

        return mapping.get(document_type, DocumentType.UNKNOWN)
