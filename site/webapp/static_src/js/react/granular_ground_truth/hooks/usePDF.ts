import { useState, useEffect, useRef } from "react";
import * as pdfjsLib from "pdfjs-dist";

// Configure PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = "/static/js/pdf.worker.min.js";

export const usePDF = (s3_url: string) => {
  const [pdf, setPdf] = useState<any>(null);
  const [allPages, setAllPages] = useState<any[]>([]);
  const [currentPageNumber, setCurrentPageNumber] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [scale, setScale] = useState(1.5);
  const overlaysRef = useRef<{ [key: number]: HTMLDivElement }>({});
  const pdfContainerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    loadAllPages();
  }, [s3_url]);

  const loadAllPages = async () => {
    setLoading(true);

    try {
      const response = await fetch(s3_url);
      const arrayBuffer = await response.arrayBuffer();
      const newPdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      console.log(`Loaded PDF with ${newPdf.numPages} page(s)`);

      setPdf(newPdf);
      setTotalPages(newPdf.numPages);

      const pages = [];

      for (let i = 1; i <= newPdf.numPages; i++) {
        const page = await newPdf.getPage(i);
        pages.push(page);
      }

      setAllPages(pages);
    } catch (error) {
      console.error("Error loading PDF:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateCurrentPageFromScroll = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPageNumber(pageNumber);
    }
  };

  const extractTextFromBoundingBox = async (
    boundingBox: {
      x: number;
      y: number;
      width: number;
      height: number;
    },
    pageNumber?: number,
  ): Promise<{ content: string }> => {
    if (!pdf) {
      return { content: "" };
    }

    const targetPageNumber = pageNumber || currentPageNumber;
    const page = await pdf.getPage(targetPageNumber);
    const textContent = await page.getTextContent();
    const viewport = page.getViewport({ scale: 1 });

    const extractedText: any[] = [];
    textContent.items.forEach((item: any) => {
      if (item.str && item.str.trim()) {
        const transform = item.transform;
        const x = transform[4];
        const y = transform[5];
        const itemWidth = item.width || 0;
        const itemHeight = item.height || 10;

        const pageHeight = viewport.height;
        const adjustedY = pageHeight - y - itemHeight;

        const textLeft = x;
        const textTop = adjustedY;
        const textRight = x + itemWidth;
        const textBottom = adjustedY + itemHeight;

        const boxLeft = boundingBox.x;
        const boxTop = boundingBox.y;
        const boxRight = boundingBox.x + boundingBox.width;
        const boxBottom = boundingBox.y + boundingBox.height;

        if (
          textLeft < boxRight &&
          textRight > boxLeft &&
          textTop < boxBottom &&
          textBottom > boxTop
        ) {
          extractedText.push({
            text: item.str,
            x: x,
            y: adjustedY,
            order: textTop * 1000 + textLeft,
          });
        }
      }
    });

    extractedText.sort((a, b) => a.order - b.order);
    const combinedText = extractedText
      .map((item) => item.text)
      .join(" ")
      .trim();

    return {
      content: combinedText,
    };
  };

  return {
    pdf,
    allPages,
    currentPageNumber,
    totalPages,
    loading,
    scale,
    setScale,
    overlaysRef,
    pdfContainerRef,
    updateCurrentPageFromScroll,
    extractTextFromBoundingBox,
  };
};
