from django import forms
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse

from webapp.models.portal import MergedPortalCredential, MFAType, MultiFactorAuthentication, Portal, PortalCredential
from webapp.views.utils import add_hx_trigger_header, get_portal_from_url_params


class AddPortalFlow(forms.Form):
    portal_url = forms.URLField(
        label="Portal URL",
        widget=forms.URLInput(
            attrs={
                "class": "w-full px-3 py-2 border border-[#eceee8] rounded-lg focus:outline-none",
                "placeholder": "https://dynamo.com",
            }
        ),
    )
    portal_name = forms.CharField(
        label="Portal Name",
        widget=forms.TextInput(
            attrs={
                "class": (
                    "w-full px-3 py-2 border border-[#eceee8] rounded-lg focus:outline-none"
                    " read-only:border-gray-300 read-only:bg-gray-100 read-only:text-gray-700"
                ),
                "placeholder": "Dynamo",
            }
        ),
    )


@login_required
def add_portal(request: HttpRequest) -> HttpResponse:
    template = "modal.html"
    default_initial_template = "link/portal/add_portal.html"
    modal_include_template = request.GET.get("modal_include_template", default_initial_template)
    portal = get_portal_from_url_params(request, raises=False)
    if portal is not None:
        form = AddPortalFlow(
            initial={
                "portal_url": portal.portal_login_url,
                "portal_name": portal.name,
            }
        )
    else:
        form = AddPortalFlow()
        # Handles the first three portals for demo users, only when running locally
        if settings.DEBUG and request.user.is_demo:
            demo_portals = [
                {"portal_url": "https://dynamo.com", "portal_name": "Dynamo"},
                {"portal_url": "https://fisdataexchange.com", "portal_name": "FIS Data Exchange"},
                {"portal_url": "https://efront.com", "portal_name": "eFront"},
            ]
            existing_portals = Portal.objects.for_user(request.user)
            portal_index = existing_portals.count()

            if portal_index < len(demo_portals):
                form = AddPortalFlow(initial=demo_portals[portal_index])

    if request.method == "GET":
        return TemplateResponse(
            request,
            template,
            {
                "modal_include_template": modal_include_template,
                "form": form,
                "is_edit": portal is not None,
            },
        )

    if request.method == "POST":
        form = AddPortalFlow(request.POST)
        if form.is_valid():
            portal_url = form.cleaned_data["portal_url"]
            portal_name = form.cleaned_data["portal_name"]

            portal, created = Portal.objects.update_or_create(
                user=request.user,
                name=portal_name,
                defaults={
                    "portal_login_url": portal_url,
                    "portal_type": Portal.PortalType.WEB_BASED,
                },
            )
            if created:
                mfa = MultiFactorAuthentication.objects.create(
                    user=request.user,
                    multi_factor_authentication_type=MFAType.UNKNOWN,
                )

                portal_credential = PortalCredential.create(
                    user=request.user,
                    portal=portal,
                    username="",
                )

                MergedPortalCredential.create(
                    user=request.user,
                    portal=portal,
                    portal_credential=portal_credential,
                    multi_factor_authentication=mfa,
                )
            else:
                portal.merged_portal_credentials.update(last_user_login_validation_retrieval=None)

            if created:
                messages.success(request, f"Portal: {portal_name} with URL: {portal_url} created successfully")
            else:
                messages.success(request, f"Portal: {portal_name} with URL: {portal_url} edited successfully")
            headers = add_hx_trigger_header(
                {
                    "closeModal": None,
                    "changeMergedPortalCredentialTable": None,
                }
            )
            return HttpResponse(headers=headers, status=200)

        headers = add_hx_trigger_header({"closeModal": "Closes the current modal"})
        messages.error(request, f"Error creating portal {form.errors}")
        return HttpResponse(headers=headers, status=400)

    return HttpResponse("Invalid request method", status=405)
