{% load customtags static %}
<form hx-post="{% url 'emails_setup_integration' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
      hx-swap="outerHTML"
      hx-target="#modal">
    <div class="ModalButtonWrapper w-full flex-col justify-start items-start gap-3 inline-flex">
        {% if receiving_email.email_provider_bridge_managed != 'ns' %}
            <button type="submit"
                    name="submit"
                    value="automatic_setup"
                    class="text-white text-base font-semibold font-inter leading-normal Buttons self-stretch h-11 px-4 py-2 bg-[#924f34] rounded-lg  shadow-inner justify-center items-center gap-1.5 inline-flex">
                Proceed via Email Plug-In
            </button>
        {% endif %}
        <button type="submit"
                name="submit"
                value="manual_setup"
                class="text-[#924f34] text-base font-semibold font-inter leading-normal Buttons self-stretch w-full h-11 px-4 py-2.5 bg-white rounded-lg  shadow-inner border border-[#924f34] justify-center items-center gap-1.5 inline-flex">
            Proceed via Manual Setup
        </button>
        <!-- TODO: <button type="submit" name="submit" value="edit" class="text-[#616863] text-base font-semibold font-inter leading-normal Buttons self-stretch w-full h-11 px-4 py-2.5 bg-white rounded-lg  shadow-inner border border-[#616863] justify-center items-center gap-1.5 inline-flex">
            Edit Email ({{ receiving_email.email }})
        </button> -->
    </div>
</form>
