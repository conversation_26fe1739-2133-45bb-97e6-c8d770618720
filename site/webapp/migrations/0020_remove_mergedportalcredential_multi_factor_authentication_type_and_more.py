# Generated by Django 5.1.6 on 2025-03-06 21:25

import django.db.models.deletion
from django.db import migrations, models
import structlog
import os
import boto3
from django.core.exceptions import ObjectDoesNotExist
logger = structlog.get_logger(__name__)


def get_line_item_mfa(li):
    try:
        return li.multi_factor_authentication
    except ObjectDoesNotExist:
        return None

def get_line_item_pc(li):
    try:
        return li.portal_credential
    except ObjectDoesNotExist:
        return None




class Migration(migrations.Migration):
    atomic = True

    def get_and_link_mfa_and_pc_to_mpc(apps, schema_editor):
        # Create session and client once
        region_name = os.environ.get("AWS_REGION", "us-east-1")
        session = boto3.session.Session()
        secrets_client = session.client(
            service_name="secretsmanager",
            region_name=region_name,
        )

        def get_secret(secret_arn, client):
            try:
                return client.get_secret_value(SecretId=secret_arn)["SecretString"]
            except Exception:
                logger.exception("Failed to get secret")
                return None

        def mfa_equality_check(target_mfa, other_mfa) -> bool:
            if target_mfa is None and other_mfa is None:
                return True
            if target_mfa is None or other_mfa is None:
                logger.warning(f"Target MFA {target_mfa} and Other MFA {other_mfa} are inconsistent as one is None")
                return False
            if target_mfa.multi_factor_authentication_type != other_mfa.multi_factor_authentication_type:
                logger.warning(f"Target MFA {target_mfa.id} has {target_mfa.multi_factor_authentication_type} and Other MFA {other_mfa.id} has {other_mfa.multi_factor_authentication_type}")
                return False
            if target_mfa.phone_number != other_mfa.phone_number:
                logger.warning(f"Target MFA {target_mfa.id} has {target_mfa.phone_number} and Other MFA {other_mfa.id} has {other_mfa.phone_number}")
                return False
            if target_mfa.receiving_email != other_mfa.receiving_email:
                logger.warning(f"Target MFA {target_mfa.id} has {target_mfa.receiving_email} and Other MFA {other_mfa.id} has {other_mfa.receiving_email}")
                return False
            target_has_secret = bool(target_mfa.secret_arn)
            other_has_secret = bool(other_mfa.secret_arn)
            if target_has_secret != other_has_secret:
                logger.warning(f"Target MFA {target_mfa.id} has {target_mfa.secret_arn} and Other MFA {other_mfa.id} has {other_mfa.secret_arn}")
                return False
            if target_has_secret and other_has_secret:
                target_secret = get_secret(target_mfa.secret_arn, secrets_client)
                other_secret = get_secret(other_mfa.secret_arn, secrets_client)
                if target_secret != other_secret:
                    logger.warning(f"Target MFA {target_mfa.id} has {target_secret} and Other MFA {other_mfa.id} has {other_secret}")
                    return False
            return True

        def pc_equality_check(target_pc, other_pc) -> bool:
            if target_pc is None and other_pc is None:
                return True
            if target_pc is None or other_pc is None:
                logger.warning(f"Target PC {target_pc} and Other PC {other_pc} are inconsistent as one is None")
                return False
            if target_pc.username != other_pc.username:
                logger.warning(f"Target PC {target_pc.id} has {target_pc.username} and Other PC {other_pc.id} has {other_pc.username}")
                return False
            if target_pc.otp_method != other_pc.otp_method:
                logger.warning(f"Target PC {target_pc.id} has {target_pc.otp_method} and Other PC {other_pc.id} has {other_pc.otp_method}")
                return False
            target_pc_has_secret = bool(target_pc.secret_arn)
            other_pc_has_secret = bool(other_pc.secret_arn)
            if target_pc_has_secret != other_pc_has_secret:
                logger.warning(f"Target PC {target_pc.id} has {target_pc.secret_arn} and Other PC {other_pc.id} has {other_pc.secret_arn}")
                return False
            if target_pc_has_secret and other_pc_has_secret:
                target_secret = get_secret(target_pc.secret_arn, secrets_client)
                other_secret = get_secret(other_pc.secret_arn, secrets_client)
                if target_secret != other_secret:
                    logger.warning(f"PC {target_pc.id} has inconsistent secret across line items")
                    return False
            return True

        # Associate Line Items without MPCs with a new MPC
        LineItems = apps.get_model('webapp', 'lineitem')
        MergedPortalCredentials = apps.get_model('webapp', 'mergedportalcredential')
        line_items_without_mpc = LineItems.objects.filter(merged_portal_credential__isnull=True)
        logger.info(f"Associating {line_items_without_mpc.count()} line items without MPCs with a new MPC")
        for li in line_items_without_mpc.iterator():
            raise ValueError
            multi_factor_authentication = li.multi_factor_authentication
            portal = li.portal
            portal_credential = li.portal_credential
            merged_portal_credential = MergedPortalCredentials.create(
                user=li.user,
                portal=portal,
                portal_credential=portal_credential,
                multi_factor_authentication=multi_factor_authentication,
            )
            li.merged_portal_credential = merged_portal_credential
            li.save()

        # Backfill and check for MPC inconsistencies
        li_mfa_inconsistent_count = 0
        li_pc_inconsistent_count = 0
        li_mfa_inconsistent_count = 0
        li_pc_inconsistent_count = 0
        total_mpc_inconsistent_count = 0
        total_li_count = 0
        MergedPortalCredentials = apps.get_model('webapp', 'mergedportalcredential')
        for mpc in MergedPortalCredentials.objects.all().iterator():
            first_li = mpc.line_items.first()
            if not first_li:
                logger.warning(f"MPC {mpc.id} has no line items")
                continue # orphaned MPC
            
            first_mfa = get_line_item_mfa(first_li)
            first_pc = get_line_item_pc(first_li)

            has_inconsistent_li = False
            for other_li in mpc.line_items.all():
                has_inconsistent_li = False
                total_li_count += 1
                other_mfa = get_line_item_mfa(other_li)
                other_pc = get_line_item_pc(other_li)

                if not mfa_equality_check(first_mfa, other_mfa):
                    logger.warning(f"MPC {mpc.id} has inconsistent MFA across line item {other_li.id}")
                    li_mfa_inconsistent_count += 1
                    has_inconsistent_li = True
                    raise ValueError(f"MPC {mpc.id} has inconsistent MFA across line items")
                if not pc_equality_check(first_pc, other_pc):
                    logger.warning(f"MPC {mpc.id} has inconsistent PC across line item {other_li.id}")
                    li_pc_inconsistent_count += 1
                    has_inconsistent_li = True
                    raise ValueError(f"MPC {mpc.id} has inconsistent PC across line items")
            
            if has_inconsistent_li:
                total_mpc_inconsistent_count += 1
            
            mpc.multi_factor_authentication = first_mfa
            mpc.portal_credential = first_pc
            mpc.save()
        
        logger.info(f"{total_li_count} line items, {total_mpc_inconsistent_count} inconsistent mpcs, {li_mfa_inconsistent_count} inconsistent MFA, {li_pc_inconsistent_count} inconsistent PC")

    def reverse_func(apps, schema_editor):
        pass

    dependencies = [
        ('webapp', '0019_backfill_separate_incomplete_mpcs'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='multifactorauthentication',
            name='merged_portal_credential',
        ),
        migrations.RemoveField(
            model_name='portalcredential',
            name='merged_portal_credential',
        ),
        migrations.RemoveField(
            model_name='mergedportalcredential',
            name='multi_factor_authentication_type',
        ),
        migrations.RemoveField(
            model_name='mergedportalcredential',
            name='otp_method',
        ),
        migrations.RemoveField(
            model_name='mergedportalcredential',
            name='phone_number',
        ),
        migrations.RemoveField(
            model_name='mergedportalcredential',
            name='receiving_email',
        ),
        migrations.RemoveField(
            model_name='mergedportalcredential',
            name='secret_arn',
        ),
        migrations.RemoveField(
            model_name='mergedportalcredential',
            name='secret_format_version',
        ),
        migrations.RemoveField(
            model_name='mergedportalcredential',
            name='username',
        ),
        migrations.AddField(
            model_name='mergedportalcredential',
            name='multi_factor_authentication',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='merged_portal_credential', to='webapp.multifactorauthentication'),
        ),
        migrations.AddField(
            model_name='mergedportalcredential',
            name='portal_credential',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='merged_portal_credential', to='webapp.portalcredential'),
        ),
        migrations.RunPython(get_and_link_mfa_and_pc_to_mpc, reverse_func),
        migrations.RemoveField(
            model_name='multifactorauthentication',
            name='line_item',
        ),
        migrations.RemoveField(
            model_name='portalcredential',
            name='line_item',
        ),
    ]
