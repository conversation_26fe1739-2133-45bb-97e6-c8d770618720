from datetime import date
from decimal import Decimal
from enum import Enum
from uuid import UUID

from ninja import Field, Schema
from webapp.models import DocumentType, ProcessedDocument


class ClientSchema(Schema):
    """
    Schema for Client data.

    Represents an investment client in the system. Clients typically have one or more
    investing entities through which they manage their investments.
    """

    client_id: UUID = Field(..., description="The client ID", alias="id")
    legal_name: str = Field(..., description="The legal name of the client")


class InvestingEntitySchema(Schema):
    """
    Schema for Investing Entity data.

    Represents a legal entity like an LLC, Trust, or other investment vehicle
    that a client uses to invest in various funds and opportunities.
    """

    client: ClientSchema = Field(..., description="The client associated with this investing entity")
    entity_id: UUID = Field(..., description="The entity ID", alias="id")
    legal_name: str = Field(..., description="The legal name of the entity")


class InvestmentSchema(Schema):
    """
    Schema for Investment data.

    Represents an investment fund or opportunity that entities can invest in.
    These are typically private equity funds, venture capital funds, or other
    investment vehicles.
    """

    investment_id: UUID = Field(..., description="The investment ID", alias="id")
    legal_name: str = Field(..., description="The legal name of the investment")
    managing_firm_name: str = Field(..., description="The name of the managing firm for this investment")


class LineItemSchema(Schema):
    """
    Schema for Line Item data.

    Represents the relationship between an investing entity and an investment.
    This is the core data model that tracks which entity has invested in which fund.
    """

    line_item_id: UUID = Field(..., description="The line item ID", alias="id")
    entity: InvestingEntitySchema = Field(
        ..., description="The investing entity associated with this line item", alias="investing_entity"
    )
    investment: InvestmentSchema = Field(..., description="The investment associated with this line item")


class DocumentTypeEnum(str, Enum):
    CAPITAL_CALL = "Capital Call"
    DISTRIBUTION_NOTICE = "Distribution Notice"
    ACCOUNT_STATEMENT = "Account Statement"
    INVESTMENT_UPDATE = "Investment Update"
    FINANCIAL_STATEMENTS = "Financial Statements"
    TAX = "Tax"
    LEGAL = "Legal"
    OTHER = "Other"
    UNKNOWN = "Unknown"


class DocumentSchema(Schema):
    """
    Schema for Processed Document data.

    Represents a document that has been processed by the system, such as
    capital calls, distribution notices, or account statements.
    """

    line_item: LineItemSchema = Field(..., description="The line item associated with this document")
    document_id: UUID = Field(..., description="The document ID", alias="id")
    name: str = Field(..., description="The name of the document")
    md5: str = Field(..., description="The MD5 hash of the document")
    posted_date: date = Field(..., description="The date the document was posted")
    document_type: DocumentTypeEnum | None = Field(..., description="The type of this document")

    @staticmethod
    def resolve_document_type(obj: ProcessedDocument | dict) -> DocumentTypeEnum:
        if isinstance(obj, dict):
            document_type = obj.get("document_type")
        elif isinstance(obj, ProcessedDocument):
            document_type = obj.document_type
        return str(DocumentType(document_type).label) if document_type else DocumentTypeEnum.UNKNOWN


class DocumentSchemaWithS3(DocumentSchema):
    """
    Schema for Processed Document data with S3 URL.

    Represents a document that has been processed by the system, such as
    capital calls, distribution notices, or account statements.
    """

    s3_url: str = Field(..., description="The pre-signed S3 URL of the document")

    @staticmethod
    def resolve_s3_url(obj: ProcessedDocument) -> str:
        return obj.signed_url


class CashFlowSchema(Schema):
    """Schema for cash flow data in the API response."""

    document: DocumentSchema = Field(..., description="The document associated with this cash flow")
    flow_type: str = Field(..., description="The type of cash flow (e.g., capital call, distribution)")
    amount: Decimal = Field(..., description="The amount of the cash flow as an absolute value")
    flow_date: date = Field(..., description="The date of the cash flow")

    @staticmethod
    def resolve_document(obj: dict) -> DocumentSchema:
        return {
            "line_item": {
                "id": obj["processed_document__line_item__id"],
                "investing_entity": {
                    "id": obj["processed_document__line_item__investing_entity__id"],
                    "legal_name": obj["processed_document__line_item__investing_entity__legal_name"],
                    "client": {
                        "id": obj["processed_document__line_item__investing_entity__client__id"],
                        "legal_name": obj["processed_document__line_item__investing_entity__client__legal_name"],
                    },
                },  # Use the correct field name based on alias
                "investment": {
                    "id": obj["processed_document__line_item__investment__id"],
                    "legal_name": obj["processed_document__line_item__investment__legal_name"],
                    "managing_firm_name": obj["processed_document__line_item__investment__managing_firm_name"],
                },
            },
            "id": obj["processed_document__id"],
            "name": obj["processed_document__name"],
            "md5": obj["processed_document__md5"],
            "posted_date": obj["processed_document__posted_date"],
            "document_type": obj["processed_document__document_type"],
        }


class ValuationSchema(Schema):
    """Schema for valuation data in the API response."""

    document: DocumentSchema = Field(
        ..., description="The document associated with this cash flow", alias="processed_document"
    )
    as_of_date: date = Field(
        ..., description="As of date of teh reported valuation", alias="processed_document.effective_date"
    )

    # Investment values in original currency
    committed_capital: Decimal = Field(..., description="The committed capital amount")
    invested: Decimal = Field(..., description="The invested amount")
    total_value: Decimal = Field(..., description="The total value of the investment")
    realized_value: Decimal = Field(..., description="The realized value of the investment")
    unrealized_value: Decimal = Field(..., description="The unrealized value of the investment")
    currency: str = Field(..., description="The currency of the investment")
