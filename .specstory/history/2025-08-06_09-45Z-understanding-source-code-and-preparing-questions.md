<!-- Generated by SpecStory -->

# Understanding source code and preparing questions (2025-08-06 09:45Z)

_**User**_

help me understand this source code, and list out any questions existed so that I can ask my lead to understand deeply about the codebase, business, etc
list into a md file so I can probably take notes on that too

---

_**Assistant**_

<think><details><summary>Thought Process</summary>The user wants me to help them understand this codebase and create a list of questions they can ask their lead. They want this in a markdown file so they can take notes.

Looking at the project structure, this appears to be a Python Django-based financial investment platform called "Bridge Investment". I should:

1. First explore the codebase to understand its structure and main components
2. Look at key files to understand the business domain and architecture
3. Generate a comprehensive list of questions that would help the user understand the codebase better
4. Create a markdown file with these questions organized by category

Let me start by exploring the codebase to understand:
- The main business domain (appears to be investment/financial)
- Architecture and tech stack
- Key components and their purposes
- Integration points
- Data models

I'll need to look at several key files to understand the system better.</details></think>

---

I'll help you understand this codebase and create a comprehensive list of questions to ask your lead. Let me explore the structure and key components first.

---

Read file: README.md

---

Read file: site/bridge_project/settings/base.py

---

Read file: site/webapp/models/__init__.py

---

Read file: site/retrieval/core/registry.py

---

<think><details><summary>Thought Process</summary>Let me continue exploring the codebase to understand more about its architecture and business domain. I should look at more key files to get a comprehensive understanding.</details></think>

---

Read file: site/webapp/models/core.py

---

Read file: site/retrieval/core/strategy.py

---

Read file: site/ml_app/core/doc_vault_lib.py

---

Read file: infrastructure/lib/cluster-stack.ts

---

<think><details><summary>Thought Process</summary>Now I have a good understanding of the codebase. Let me look at a few more key areas to complete my analysis before creating the markdown file with questions.</details></think>

---

<details>
            <summary>Listed directory site/retrieval/core/managers • **32** results</summary>
        
| Name |
|-------|
| 📁 `__pycache__` |
| 📁 `demo` |
| 📄 `rialto_capital.py` |
| 📄 `sharefile.py` |
| 📄 `ssc.py` |
| 📄 `umbaltpro.py` |
| 📄 `vector_ais.py` |
| 📄 `kleiner_perkins_caz.py` |
| 📄 `kleiner_perkins_investor.py` |
| 📄 `mock.py` |
| 📄 `nav_fund_services.py` |
| 📄 `pei.py` |
| 📄 `revcap.py` |
| 📄 `fundpanel.py` |
| 📄 `goldman.py` |
| 📄 `intralinks.py` |
| 📄 `juniper_square.py` |
| 📄 `dynamo.py` |
| 📄 `efront.py` |
| 📄 `egnyte.py` |
| 📄 `fis_brookfield.py` |
| 📄 `fis_data_ex.py` |
| 📄 `dst.py` |
| 📄 `altvia_solutions.py` |
| 📄 `ark_pes.py` |
| 📄 `aw_property.py` |
| 📄 `carta.py` |
| 📄 `caz_investor_flow.py` |
| 📄 `__init__.py` |
| 📄 `akkadian_investment_gateway.py` |
| 📄 `altareturn_allvue.py` |
| 📄 `alterdomus.py` |

</details>

---

Read file: site/webapp/models/retrieval.py

---

Read file: site/email_webhook/api.py

---

Read file: site/public_api/api.py

---

<think><details><summary>Thought Process</summary>Now I have a comprehensive understanding of the Bridge Investment codebase. Let me create a markdown file with detailed questions organized by category that would help someone understand the codebase, business, and architecture deeply.</details></think>

---



---

<think><details><summary>Thought Process</summary>I should also create a brief summary of what I've learned about the codebase to help the user have context when asking these questions.</details></think>

---



---

<think><details><summary>Thought Process</summary>I should also create a final document that organizes technical concepts and areas for deeper exploration.</details></think>

---



---

<think><details><summary>Thought Process</summary>I've created three comprehensive documents to help the user understand the codebase:

1. **bridge-investment-questions.md** - A detailed list of questions organized by category that covers business domain, technical architecture, features, operations, and more.

2. **bridge-investment-codebase-summary.md** - A high-level overview of what Bridge Investment is, its key features, technical architecture, and the problems it solves.

3. **bridge-investment-technical-areas.md** - A deep-dive into specific technical areas with file references and focused questions for each component.

Let me create one final quick reference guide that the user can use during their conversation with their lead.</details></think>

---



---

<think><details><summary>Thought Process</summary>Now let me check if I've created all the files successfully and provide a summary to the user.</details></think>

---

<details>
            <summary>Listed current directory • **26** results</summary>
        
| Name |
|-------|
| 📄 `bridge-investment-quick-reference.md` |
| 📄 `bridge-investment-technical-areas.md` |
| 📄 `bridge-investment-codebase-summary.md` |
| 📄 `bridge-investment-questions.md` |
| 📁 `.git` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |
| 📁 `site` |
| 📄 `uv.lock` |
| 📄 `pyproject.toml` |
| 📄 `qr_test_code.png` |
| 📁 `infrastructure` |
| 📄 `lint.py` |
| 📁 `ml` |
| 📁 `bin` |
| 📄 `bootstrap_qr_library.py` |
| 📄 `deploy.py` |
| 📄 `docker-compose.likeprod.yml` |
| 📄 `docker-compose.yml` |
| 📄 `.gitignore` |
| 📄 `.python-version` |
| 📄 `Bridge_Investment_Template_250423.xlsx` |
| 📄 `Dockerfile` |
| 📄 `Makefile` |
| 📄 `README.md` |
| 📁 `.github` |

</details>

---

<think><details><summary>Thought Process</summary>Perfect! All the markdown files have been created successfully. Let me provide a summary to the user about what I've created and how they can use these documents.</details></think>

---

I've analyzed the Bridge Investment codebase and created four comprehensive markdown files to help you understand the system and prepare questions for your lead:

## 📄 Created Documents:

### 1. **`bridge-investment-questions.md`** (7.3KB)
A comprehensive list of questions organized by category:
- Business Domain Questions
- Architecture & Technical Stack
- Core Features (Document Retrieval, Email Integration, ML/AI)
- Data Model Questions
- Operations & Maintenance
- Development Process
- Security & Compliance
- Scaling & Future Plans

This document includes space for taking notes during your discussion.

### 2. **`bridge-investment-codebase-summary.md`** (3.6KB)
A high-level overview that explains:
- What Bridge Investment does (financial platform for investment tracking)
- Key features (automated document retrieval, AI processing, portfolio management)
- Technical architecture and stack
- Business model and challenges addressed

### 3. **`bridge-investment-technical-areas.md`** (5.4KB)
A deep-dive into specific technical components:
- Web scraping architecture
- State machine implementation
- Document processing pipeline
- Multi-tenant permissions
- Architecture decisions and patterns

### 4. **`bridge-investment-quick-reference.md`** (4.3KB)
A handy reference guide with:
- Directory structure
- Critical business flows
- Key models and commands
- Common status codes
- Debugging tips

## 🔍 Key Insights About the Codebase:

**Bridge Investment** is a sophisticated financial technology platform that acts like "Plaid for private investments." It:
- Automates document retrieval from 30+ investment portals using web scraping
- Processes documents with AI to extract key financial data
- Provides unified dashboards for investment advisors and their clients
- Handles complex authentication including MFA via email capture

The tech stack is Django-based with Celery for async tasks, Playwright for web scraping, and AWS infrastructure managed by CDK.

## 💡 How to Use These Documents:

1. **Start with the summary** to understand the big picture
2. **Review the questions** before meeting with your lead
3. **Use the quick reference** during your conversation for quick lookups
4. **Explore technical areas** for deeper dives into specific components

The platform solves a real business problem of aggregating investment data from multiple portals into a single interface, which is particularly valuable for advisors managing multiple client portfolios across various alternative investment platforms.

---

