import asyncio
import datetime
import hashlib
import json
import math
import re
from collections.abc import AsyncGenerator
from pathlib import Path
from zipfile import ZipFile

import structlog
from dateutil import parser
from playwright.async_api import Locator, expect
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class UmbAltProPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "altpro.umb.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "UMB AltPro"),
            ("<EMAIL>", "UMB"),
        ]

    async def _enter_credentials(self, username: str, password: str) -> None:
        # Enter Username
        login_input = self.page.locator("input[id='loginNameId']")
        await login_input.click()
        await asyncio.sleep(1)
        await login_input.fill(username)
        await asyncio.sleep(1)

        # Enter Password
        password_input = self.page.locator("input[id='loginPasswordId']")
        await password_input.click()
        await asyncio.sleep(1)
        await password_input.fill(password)
        await asyncio.sleep(1)

        # Click Submit
        submit_button = self.page.get_by_text("Log in")
        await submit_button.click()

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        if self.page is None:
            raise ManagerNotInitializedError
        return False

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return await self.page.get_by_text("Log in").count() == 0

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _handle_retry_setup(self) -> None:
        await self.login()

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        starting_point = await self.get_starting_point()
        skip_all_documents = False
        if starting_point and "skip_all_documents" in starting_point:
            logger.info("Skipping all documents retrieval", starting_point=starting_point)
            skip_all_documents = True
        # Click on "All Documents" tab
        if not skip_all_documents:
            await self.page.locator("#tabDocumentId").click()
            await asyncio.sleep(10)

            async for doc in self._retrieve_all_documents(is_all_document_flow=True):
                yield doc

            logger.info("update_check_point", checkpoint="skip_all_documents")
            # Update check point
            await self.update_check_point({"skip_all_documents": True})

        # Click on "Investors" tab
        await self.page.locator("#tabInvestorId").click()
        await asyncio.sleep(10)

        async for doc in self._retrieve_investor_documents():
            yield doc

    async def _retrieve_all_documents(self, *, is_all_document_flow: bool) -> AsyncGenerator[RawDocumentTuple, None]:
        number_of_pages = await self._get_number_of_pages()

        current_page = 1
        while current_page <= number_of_pages:
            table = self.page.locator("#allDocTableId tbody tr")
            rows = await table.all()

            for row in rows:
                [
                    checkbox,
                    _,
                    _,
                    doc_type,
                    posted_date,
                    _,
                    metadata,
                ] = await self._get_row_metadata(row)

                doc_hash = await self.should_download_document(metadata)
                if doc_hash:
                    doc = await self.__download_doc(
                        doc_type=doc_type,
                        posted_date=posted_date,
                        checkbox=checkbox,
                        row_metadata=metadata,
                        is_all_document_flow=is_all_document_flow,
                    )
                    if doc is not None:
                        yield doc

            if current_page < number_of_pages:
                await asyncio.gather(
                    *[
                        self.page.locator("#allDocTableId_next").click(),
                        expect(self.page.locator("#allDocTableId_processing")).to_be_visible(),
                    ]
                )
                await expect(self.page.locator("#allDocTableId_processing")).to_be_hidden()

            current_page += 1

    async def _retrieve_investor_documents(self) -> AsyncGenerator[RawDocumentTuple, None]:
        # Gets the number of pages of investor pages
        number_of_investor_pages = await self._get_number_of_investor_pages()
        logger.info("Number of investor pages", number_of_investor_pages=number_of_investor_pages)

        # set starting investor page
        starting_point = await self.get_starting_point()
        starting_investor_page = 1
        if starting_point and "skip_to_investor_page" in starting_point:
            logger.info(
                "Skipping to investor page",
                starting_point=starting_point,
            )
            starting_investor_page = int(starting_point["skip_to_investor_page"])
        current_investor_page = starting_investor_page
        # Loops through each investor page
        while current_investor_page <= number_of_investor_pages:
            # Go to initial investor page to get row count
            get_back_to_investor_page = 1
            while get_back_to_investor_page < current_investor_page:
                await asyncio.gather(
                    *[
                        self.page.locator("#listTableId_next").click(),
                        expect(self.page.locator("#listTableId_processing")).to_be_visible(),
                    ]
                )
                await expect(self.page.locator("#listTableId_processing")).to_be_hidden()
                get_back_to_investor_page += 1

            investor_row = self.page.locator("#listTableId tbody tr")
            await asyncio.sleep(2)
            investor_row_count = await investor_row.count()  # Number of investors on the page
            await asyncio.sleep(2)
            logger.info(
                "Number of investors on page",
                current_investor_page=current_investor_page,
                investor_row_count=investor_row_count,
            )

            # set starting investor row index
            starting_investor_row_index = 0
            if starting_point and "skip_to_investor_row" in starting_point:
                logger.info(
                    "Skipping to investor row",
                    starting_point=starting_point,
                )
                starting_investor_row_index = int(starting_point["skip_to_investor_row"])

            # Loops through each investor on the page
            for investor_row_index in range(starting_investor_row_index, investor_row_count):
                logger.info(
                    "Getting docs for investor",
                    current_investor_page=current_investor_page,
                    investor_row_index=investor_row_index,
                )

                # click next to go to investor page unless we just got to this page:
                # - this is the first investor row of the page
                # - this is the starting_point we just skipped to
                if investor_row_index > 0 and not (
                    starting_investor_row_index == investor_row_index
                    and starting_investor_page == current_investor_page
                ):
                    # Back btns do not preserve state, so we need to get back to the investor page every time
                    get_back_to_investor_page = 1
                    while get_back_to_investor_page < current_investor_page:
                        await asyncio.gather(
                            *[
                                self.page.locator("#listTableId_next").click(),
                                expect(self.page.locator("#listTableId_processing")).to_be_visible(),
                            ]
                        )
                        await expect(self.page.locator("#listTableId_processing")).to_be_hidden()
                        get_back_to_investor_page += 1

                investor_row_text = await investor_row.nth(investor_row_index).all_inner_texts()
                logger.info(
                    "update_check_point",
                    checkpoint="skip_to_investor_row",
                    investor_row_index=investor_row_index,
                    investor=investor_row_text,
                )

                # Update check point
                await self.update_check_point({"skip_to_investor_row": investor_row_index})

                async for doc in self._retrieve_investor_page(investor_row, investor_row_index):
                    yield doc

                # Clicks Investor tab since back button does not preserve state
                await self.page.locator("#tabInvestorId").click()
                await asyncio.sleep(2)

            # Goes to next investor page
            current_investor_page += 1
            logger.info("Going to next investor page", current_investor_page=current_investor_page)

            # Update check point
            await self.update_check_point({"skip_to_investor_page": current_investor_page, "skip_to_investor_row": 0})
            logger.info(
                "update_check_point", checkpoint="skip_to_investor_page", current_investor_page=current_investor_page
            )

    async def _retrieve_investor_page(
        self, investor_row: Locator, investor_row_index: int
    ) -> AsyncGenerator[RawDocumentTuple, None]:
        # Goes to funds page within investor
        row = investor_row.nth(investor_row_index)
        await row.click()
        await asyncio.sleep(2)

        # Goes to all documents page within fund
        await self.page.wait_for_selector("#tabInvestorDocumentsId")
        await self.page.click("#tabInvestorDocumentsId")
        await asyncio.sleep(2)

        # Follows same logic as all documents page
        async for doc in self._retrieve_all_documents(is_all_document_flow=False):
            yield doc

    async def _get_number_of_investor_pages(self) -> int:
        await asyncio.sleep(5)
        info_text = await self.page.locator("#listTableId_info").inner_text()
        match = re.search(r"Showing\s+\d+\s+to\s+(\d+)\s+of\s+(\d+)", info_text)
        if match:
            current_end = int(match.group(1))
            total_entries = int(match.group(2))
            return math.ceil(total_entries / current_end)
        return 1

    async def _get_number_of_pages(self) -> int:
        info_element = self.page.locator("#allDocTableId_info")
        # Waiting 5 seconds is not enough sometimes, so we will retry up to 5 times
        for _ in range(5):
            # try up to 5 times to get the inner text
            info_text = await info_element.inner_text()
            parts = info_text.split()
            if info_text and len(parts) >= 6:  # noqa: PLR2004 need to get parts[5]
                break
            await asyncio.sleep(2)
        current_end = int("".join(filter(str.isdigit, parts[3])))
        total_entries = int("".join(filter(str.isdigit, parts[5])))
        if current_end == 0:
            return 0
        return math.ceil(total_entries / current_end)

    async def _get_row_metadata(self, row: Locator) -> list[str, str, str]:
        checkbox = row.locator("td:nth-child(1) input[type='checkbox']")
        doc_name = await row.locator("td:nth-child(3)").inner_text()
        as_of_date = await row.locator("td:nth-child(4)").inner_text()
        doc_type = await row.locator("td:nth-child(5)").inner_text()
        posted_date = await row.locator("td:nth-child(6)").inner_text()
        entity_info = await row.locator("td:nth-child(7)").inner_text()

        metadata = json.dumps(
            {
                "File name": doc_name,
                "As of Date": as_of_date,
                "Document Type": doc_type,
                "Posted Date": posted_date,
                "Entity Info": entity_info,
            }
        )
        logger.info(
            "Row metadata",
            doc_name=doc_name,
            as_of_date=as_of_date,
            doc_type=doc_type,
            posted_date=posted_date,
            entity_info=entity_info,
        )
        return [checkbox, doc_name, as_of_date, doc_type, posted_date, entity_info, metadata]

    async def __download_doc(
        self,
        *,
        doc_type: str,
        posted_date: str,
        checkbox: Locator,
        row_metadata: str,
        is_all_document_flow: bool,
    ) -> RawDocumentTuple | None:
        download_button_id = "#downloadMultipleDocs" if is_all_document_flow else "#downloadMultipleDocSub"
        await checkbox.click()
        await asyncio.sleep(1)
        try:
            async with self.page.expect_download() as download_info:
                await self.page.locator(download_button_id).click()
        except PlaywrightTimeoutError:
            logger.warning(
                "Download failed likely due to 404 missing document",
                row_metadata=row_metadata,
                error="download_timeout",
            )
            await self.page.bring_to_front()
            await checkbox.click()
            await asyncio.sleep(1)
            return None
        await checkbox.click()
        await asyncio.sleep(1)

        download = await download_info.value
        suggested_filename = download.suggested_filename
        file_name = self.local_directory / suggested_filename
        await download.save_as(file_name)

        content = None

        if file_name.suffix.lower() == ".zip":
            with ZipFile(file_name, "r") as zip_file:
                pdf_files = [f for f in zip_file.namelist() if not (f.endswith("/") or f.startswith("__MACOSX/"))]

                if len(pdf_files) != 1:
                    logger.error("Expected 1 PDF in zip file", found_files=pdf_files)
                    error_message = f"Unexpected number of files in ZIP: {len(pdf_files)}"
                    raise ValueError(error_message)

                pdf_filename = pdf_files[0]
                with zip_file.open(pdf_filename, "r") as pdf_file:
                    content = pdf_file.read()

                suggested_filename = pdf_filename

        elif file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.error("Unsupported file format", file_name=file_name)
            error_message = f"Unsupported file format: {file_name.suffix}"
            raise ValueError(error_message)

        file_name.unlink()
        await asyncio.sleep(1)

        doc_type = self.__document_mapper(doc_type)
        cleaned_date = posted_date.replace("\u2011", "-")

        parsed_date = self._parse_date(cleaned_date)

        return RawDocumentTuple(
            name=suggested_filename,
            date=parsed_date,
            doc_type=doc_type,
            content=content,
            raw_metadata=row_metadata,
            doc_hash=self._doc_hash(row_metadata),
        )

    def _parse_date(self, effective_date: str) -> datetime:
        try:
            return parser.parse(effective_date)
        except (ValueError, TypeError, parser.ParserError):
            # some files have an empty effective date column, return today as a place holder
            return datetime.datetime.now()  # noqa: DTZ005

    def __document_mapper(self, doc_type: str) -> str:
        """
        Document Types in Portal:
        - Fund Legal Documents
        - Investor Statement
        - Investor K-1
        - Commentaries
        - Audited Financials
        """
        mapping = {
            "Financial Statements": DocumentType.FINANCIAL_STATEMENTS,
            "Capital Calls": DocumentType.CAPITAL_CALL,
            "Tax Documents": DocumentType.TAX,
            "Fund Documents": DocumentType.INVESTMENT_UPDATE,
            "Distribution Notice": DocumentType.DISTRIBUTION_NOTICE,
            # Include?
            "Account Statement": DocumentType.ACCOUNT_STATEMENT,
            "Investment Update": DocumentType.INVESTMENT_UPDATE,
            "Tax": DocumentType.TAX,
            "Legal": DocumentType.LEGAL,
            "Other": DocumentType.OTHER,
            "Unknown": DocumentType.UNKNOWN,
        }

        if doc_type in mapping:
            return mapping.get(doc_type)

        return DocumentType.UNKNOWN
