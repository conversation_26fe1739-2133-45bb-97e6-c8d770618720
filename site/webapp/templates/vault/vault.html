{% extends "base.html" %}
{% load customtags static %}
{% block title %}
    Vault
{% endblock title %}
{% block webapp_content %}
    <main class="main-content ml-16 gap-4 bg-base-beige h-screen">
        <div id="vault_container"
             hx-post="{% url 'vault' %}"
             hx-trigger="reloadVault from:document"
             hx-target="#vault_container"
             hx-swap="outerHTML"
             hx-select="#vault_container"
             hx-disinherit="*">
            {% include "components/dashboard_header.html" with title="Document Vault" user=user %}
            <div class="py-9">
                <div class="w-full px-9">
                    {% include "vault/cards/new_notifications.html" %}
                    <div class="overflow-x-auto">{% include "vault/table/table.html" %}</div>
                </div>
            </div>
        </div>
    </main>
{% endblock webapp_content %}
