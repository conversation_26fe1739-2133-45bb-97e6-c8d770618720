# Docker file for the website and the celery worker

# For core-env try to only do 0 bite operations here. We try to define all flags/env vars in this step for subsequent steps.
# the only envvar not defined here is ENVIRONMENT which is set in the final steps to differentiate images
FROM python:3.13-slim-bullseye AS bridge-core-env

ENV \
  PYTHONUNBUFFERED=1 \
  PIP_NO_CACHE_DIR=1 \
  DJANGO_SETTINGS_MODULE=bridge_project.settings \
  PYTHONPATH=/bridge-python/site \
  WORKDIR=/bridge-python \
  DISPLAY=:99 \
# ENVIRONMENT=cicd is a special flag to tell the code not to do network operations
  ENVIRONMENT=cicd

# Create a non-root user and group
RUN adduser --disabled-password --gecos "" siteuser

FROM bridge-core-env AS bridge-python
# If you update UV from 0.5.21, please update pyproject.toml required-version
COPY --from=ghcr.io/astral-sh/uv:0.7.3 --chown=siteuser:siteuser /uv /uvx /bin/
WORKDIR $WORKDIR
COPY --chown=siteuser:siteuser uv.lock pyproject.toml bootstrap_qr_library.py $WORKDIR/
RUN apt-get update \
  && apt-get install -y --no-install-recommends \
    apt-transport-https \
    ca-certificates \
    curl \
    libzbar0 \
    gnupg \
    lsb-release \
  && echo "deb http://security.debian.org/debian-security $(lsb_release -cs)-security main contrib non-free" > /etc/apt/sources.list.d/security.list \
  && apt-get clean \
  && apt-get update \
  && apt-get upgrade -y \
  # qreader+pytorch blows up our image size from ~650mb to ~950mb
  # getting the QR code dependencies separated could be an infra goal.
  && uv sync --no-cache --frozen \
  && uv run -m patchright install --with-deps chrome \
  && mkdir -p /home/<USER>/.cache/ms-playwright \
  && cp -r /root/.cache/ms-playwright /home/<USER>/.cache/ \
  && chown siteuser:siteuser -R /home/<USER>/.cache/ \
  && chown siteuser:siteuser -R /bridge-python/.venv \
  # Below downloads the weights for the ML QR code detector.
  && ./bootstrap_qr_library.py \
  && rm -rf /root/.cache/ms-playwright \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean
COPY --chown=siteuser:siteuser ./site $WORKDIR/site

# Stage 2: Build assets
FROM node:lts-bookworm-slim AS assets
ENV WORKDIR=/bridge-python
COPY --from=bridge-python $WORKDIR/site/webapp/static_src/ $WORKDIR/site/webapp/static_src/
COPY --from=bridge-python $WORKDIR/site/webapp/static/ $WORKDIR/site/webapp/static/
COPY --from=bridge-python $WORKDIR/site/webapp/templates/ $WORKDIR/site/webapp/templates/
RUN (cd $WORKDIR/site/webapp/static_src/ && npm install) \
    && (cd $WORKDIR/site/webapp/static_src/ && npm run build)

# We copy the node binaries over so that we can use them to rebuild dev assets dynamically.
# Copying is far faster than installing from scratch.
FROM bridge-python AS bridge-dev
COPY --chown=siteuser:siteuser --from=assets /usr/local/bin/node /usr/bin/
COPY --chown=siteuser:siteuser --from=assets /usr/local/include/node /usr/local/include/node
COPY --chown=siteuser:siteuser --from=assets /usr/local/lib/node_modules /usr/local/lib/node_modules
# Link npm
RUN ln -vs /usr/local/lib/node_modules/npm/bin/npm-cli.js /usr/local/bin/npm \
    && ln -vs /usr/local/lib/node_modules/npm/bin/npx-cli.js /usr/local/bin/npx
USER siteuser
ENTRYPOINT ["./site/entrypoint.py"]

# Keeping these around because docker target and docker repo name are tightly coupled. Maybe get rid of env in repo name?
FROM bridge-python AS bridge-prod
COPY --chown=siteuser:siteuser --from=assets $WORKDIR/site/webapp/static/ $WORKDIR/site/webapp/static/
RUN mkdir -p $WORKDIR/static && ./site/manage.py collectstatic --noinput && rm ./site/unsafe_ops.py
USER siteuser
ENTRYPOINT ["./site/entrypoint.py"]
