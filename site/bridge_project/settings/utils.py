import json
import os
import urllib
from typing import Any, NamedTuple

import boto3
import boto3.session
import structlog
from botocore.exceptions import ClientError
from django.http import HttpRequest


class OutputInit:
    def __init__(self, **kwargs: str | list[str] | None) -> None:
        valid_kwargs = set(dir(self.__class__))
        for k, v in kwargs.items():
            # if V exists and the object expects it, set it
            # this allows us to decouple deployment config from the object definition.
            if v is not None and k in valid_kwargs:
                setattr(self, k, v)
            else:
                logger.warning("Invalid CDK parameter", class_name=self.__class__.__name__, key=k, value=v)

    def __str__(self) -> str:
        parts = [self.__class__.__name__, "("]
        for k, v in self.__dict__.items():
            parts.append(f"{k}={v}")
            parts.append(", ")
        if len(parts) > 2:  # noqa: PLR2004
            parts[-1] = ")"
        else:
            parts.append(")")
        return "".join(parts)


class ConfigStackOutputs(OutputInit):
    AwsAccount: str | None = None
    AwsRegion: str | None = None
    DeploymentTarget: str | None = None
    HostedZoneId: str | None = None
    DomainName: str | None = None
    DjangoSecretArn: str | None = None
    BulkUploadsS3Path: str | None = None
    EmailDocsS3Path: str | None = None
    ProcessedDocsS3Path: str | None = None
    RetrievalLogsS3Path: str | None = None
    EmailS3Path: str | None = None
    VantaAuditorRoleArn: str | None = None
    GitHubActionsRoleArn: str | None = None
    MsftEmailOauthSecretArn: str | None = None
    DevTwilioSecretArn: str | None = None
    SlackSecretArn: str | None = None
    PythonContainerRepositoryName: str | None = None
    UserBucketName: str | None = None
    SftpBucketName: str | None = None
    DatabaseName: str | None = None
    UserDataKmsName: str | None = None
    EmailTopicName: str | None = None
    EmailWebhookPath: str | None = None
    EmailWebhookUsername: str | None = None


class NetworkStackOutputs(OutputInit):
    VpcId: str | None = None
    VpcAvailabilityZones: list[str] | None = None
    VpcPublicSubnetIds: list[str] | None = None
    VpcPublicRouteTableIds: list[str] | None = None
    VpcCidrBlock: str | None = None


class StorageStackOutputs(OutputInit):
    EmailConfigurationSetName: str | None = None
    EmailSendAddress: str | None = None
    DBCredentialsSecretArn: str | None = None
    DBInstanceIds: list[str] | None = None
    DBClusterArn: str | None = None
    S3BucketName: str | None = None
    RedisSecretArn: str | None = None
    RedisClusterName: str | None = None
    UserDataKmsArn: str | None = None
    RedisClusterPrimaryEndPointAddress: str | None = None
    RedisClusterPrimaryEndPointPort: str | None = None
    RedisSecurityGroupId: str | None = None


class ClusterStackOutputs(OutputInit):
    ClusterName: str | None = None
    ExecutionRoleArn: str | None = None


class CeleryServiceStackOutputs(OutputInit):
    CeleryTaskRoleArn: str | None = None


class SiteServiceStackOutputs(OutputInit):
    LoadBalancerName: str | None = None


class EmailStackOutputs(OutputInit):
    pass


class AllStackOutputs(OutputInit):
    ConfigStack: ConfigStackOutputs | None = None
    NetworkStack: NetworkStackOutputs | None = None
    StorageStack: StorageStackOutputs | None = None
    ClusterStack: ClusterStackOutputs | None = None
    CeleryServiceStack: CeleryServiceStackOutputs | None = None
    SiteServiceStack: SiteServiceStackOutputs | None = None
    EmailStack: EmailStackOutputs | None = None


logger = structlog.get_logger(__name__)


def get_secret(secret_name: str, region_name: str | None = None) -> str:
    if region_name is None:
        region_name = os.environ.get("AWS_REGION", "us-east-1")

    session = boto3.session.Session()
    client = session.client(
        service_name="secretsmanager",
        region_name=region_name,
    )

    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name,
        )
    except ClientError as e:
        if "Error" in e.response:
            logger.exception("Error getting secret", secret_name=secret_name, response=e.response)
        else:
            logger.exception("Unknown error getting secret", secret_name=secret_name)
        raise
    else:
        # Secrets Manager decrypts the secret value using the associated KMS CMK
        # Depending on whether the secret was a string or binary, only one of these fields will be populated
        if "SecretString" in get_secret_value_response:
            return get_secret_value_response["SecretString"]
        return get_secret_value_response["SecretBinary"]


def get_field_type(schema: dict[str, Any], field_names: list[str]) -> str:
    _field_names = field_names.copy()
    first = _field_names.pop(0)
    _sub_schema = schema["definitions"][first]
    while _field_names:
        field_name = _field_names.pop(0)
        if "properties" in _sub_schema and field_name in _sub_schema["properties"]:
            _sub_schema = _sub_schema["properties"][field_name]

    return _sub_schema.get("type", None)


class ParameterKeyValue(NamedTuple):
    Name: str
    Value: str


def get_all_params_by_path(env: str, path: str, region_name: str | None = None) -> list[ParameterKeyValue]:
    if region_name is None:
        region_name = os.environ.get("AWS_REGION", "us-east-1")
    if env in boto3.session.Session().available_profiles:
        logger.info("Profile found", profile=env)
        session = boto3.session.Session(profile_name=env)
    else:
        logger.info("No profile found, using default")
        session = boto3.session.Session()
    ssm = session.client(service_name="ssm", region_name=region_name)
    res = ssm.get_parameters_by_path(Path=path, Recursive=True)
    arr = [ParameterKeyValue(Name=param["Name"], Value=param["Value"]) for param in res["Parameters"]]
    while "NextToken" in res:
        res = ssm.get_parameters_by_path(Path=path, Recursive=True, NextToken=res["NextToken"])
        parameters = res["Parameters"]
        arr.extend([ParameterKeyValue(Name=param["Name"], Value=param["Value"]) for param in parameters])
    logger.info("Found parameters", length=len(arr))
    return arr


def get_parameter_store_output(env: str) -> AllStackOutputs:
    param_name_to_class = [(k, v.__args__[0]) for k, v in AllStackOutputs.__annotations__.items()]
    return AllStackOutputs(
        **{
            param_name: param_class(
                **{
                    param.Name.split("/")[-1]: param.Value
                    for param in get_all_params_by_path(env, f"/bridge/infra/{env}/{param_class.__name__}/")
                }
            )
            for param_name, param_class in param_name_to_class
        }
    )


def create_redis_uri(stack_outputs: StorageStackOutputs) -> str:
    if stack_outputs.RedisSecretArn is None:
        logger.error("RedisSecretArn not found in config")
        raise ValueError
    credentials_str = get_secret(stack_outputs.RedisSecretArn)
    credentials = json.loads(credentials_str)
    password = urllib.parse.quote(credentials["password"])  # type: ignore[attr-defined]
    host = stack_outputs.RedisClusterPrimaryEndPointAddress
    port = stack_outputs.RedisClusterPrimaryEndPointPort
    return f"rediss://:{password}@{host}:{port}/0"


class ParsedAWSSettings(NamedTuple):
    DATABASE_CREDENTIALS: dict
    REDIS_CONN_URI: str


def generate_aws_configuration(env: str) -> tuple[ParsedAWSSettings, AllStackOutputs]:
    stack_outputs = get_parameter_store_output(env)
    if stack_outputs.StorageStack is None or stack_outputs.ConfigStack is None:
        logger.error("Need StorageStack and ConfigStack")
        raise ValueError
    redis_uri = create_redis_uri(stack_outputs.StorageStack)
    if stack_outputs.StorageStack.DBCredentialsSecretArn is None:
        logger.error("DBCredentialsSecretArn not found in config")
        raise ValueError
    db_credentials = json.loads(get_secret(stack_outputs.StorageStack.DBCredentialsSecretArn))
    return ParsedAWSSettings(
        DATABASE_CREDENTIALS=db_credentials,
        REDIS_CONN_URI=redis_uri,
    ), stack_outputs


def show_debug_toolbar(request: HttpRequest) -> bool:
    from django.conf import settings

    if "HTTP_ENABLEDEBUGTOOLBAR" in request.META:
        return request.META["HTTP_ENABLEDEBUGTOOLBAR"] == "ENABLEMEPLZ"
    return settings.DEBUG


if __name__ == "__main__":
    logger.info("Getting CDK outputs")
    outputs = get_parameter_store_output("prod")
    logger.info(outputs)
