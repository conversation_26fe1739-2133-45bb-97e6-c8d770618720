from datetime import UTC, datetime, timedelta

RECENT_SECONDS = 10
SECONDS_IN_MINUTE = 60
SECONDS_IN_HOUR = 3600
SECONDS_IN_DAY = 86400
DAYS_IN_YEAR = 365
DAYS_IN_MONTH = 30
DAYS_IN_WEEK = 7


def _ts_difference(timestamp: int | datetime | None = None, now_override: int | None = None) -> timedelta:
    now = datetime.now(UTC) if not now_override else datetime.fromtimestamp(now_override, tz=UTC)
    if type(timestamp) is int:
        diff = now - datetime.fromtimestamp(timestamp, tz=datetime.UTC)
    elif isinstance(timestamp, datetime):
        # Ensure both datetimes have the same timezone awareness
        if timestamp.tzinfo is not None and now.tzinfo is None:
            # If timestamp is timezone-aware but now is naive, make now aware
            now = now.replace(tzinfo=timestamp.tzinfo)
        elif timestamp.tzinfo is None and now.tzinfo is not None:
            # If now is timezone-aware but timestamp is naive, make timestamp aware
            timestamp = timestamp.replace(tzinfo=now.tzinfo)
        diff = now - timestamp
    elif not timestamp:
        diff = now - now
    return diff


def pretty_date(  # NOQA: PLR0911, C901  # too many return statements, too complex
    timestamp: int | datetime | None = None,
    now_override: int | None = None,
) -> str:
    """
    Adapted from
    http://stackoverflow.com/questions/1551382/
    user-friendly-time-format-in-python
    """
    diff = _ts_difference(timestamp, now_override)
    second_diff = diff.seconds
    day_diff = diff.days

    if day_diff < 0:
        return ""
    if day_diff == 0:
        if second_diff < RECENT_SECONDS:
            return "just now"
        if second_diff < SECONDS_IN_MINUTE:
            return str(second_diff) + " seconds ago"
        if second_diff < 2 * SECONDS_IN_MINUTE:
            return "a minute ago"
        if second_diff < SECONDS_IN_HOUR:
            return str(int(second_diff / SECONDS_IN_MINUTE)) + " minutes ago"
        if second_diff < 2 * SECONDS_IN_HOUR:
            return "an hour ago"
        if second_diff < SECONDS_IN_DAY:
            return str(int(second_diff / SECONDS_IN_HOUR)) + " hours ago"
    elif day_diff == 1:
        return "Yesterday"
    elif day_diff < DAYS_IN_WEEK:
        return str(int(day_diff)) + " days ago"
    elif day_diff < 31 * DAYS_IN_MONTH:
        return str(int(day_diff / 7)) + " weeks ago"
    elif day_diff < DAYS_IN_YEAR:
        return str(int(day_diff / DAYS_IN_MONTH)) + " months ago"
    return str(int(day_diff / DAYS_IN_YEAR)) + " years ago"
