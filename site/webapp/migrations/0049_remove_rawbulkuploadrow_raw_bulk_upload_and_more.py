# Generated by Django 5.2.2 on 2025-06-13 20:07

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0048_remove_rawdocument_forwarding_rule_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='rawbulkuploadrow',
            name='raw_bulk_upload',
        ),
        migrations.RemoveField(
            model_name='rawbulkuploadrow',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='rawbulkuploadrow',
            name='line_item',
        ),
        migrations.RemoveField(
            model_name='rawbulkuploadrow',
            name='organization',
        ),
        migrations.RemoveField(
            model_name='rawbulkuploadrow',
            name='updated_by',
        ),
        migrations.DeleteModel(
            name='RawBulkUpload',
        ),
        migrations.DeleteModel(
            name='RawBulkUploadRow',
        ),
    ]
