# Generated by Django 5.2.1 on 2025-05-19 21:12

import structlog
from django.db import migrations

logger = structlog.get_logger(__name__)


def migrate_role_name_and_description(apps, schema_editor):
    Role = apps.get_model('webapp', 'Role')
    BridgeUser = apps.get_model('webapp', 'BridgeUser')
    logger.info("Starting role migration")

    # Create new standardized roles if they don't exist
    manager_role, _ = Role.objects.get_or_create(
        name="manager",
        defaults={"description": "Full access to all objects and settings within an organization."}
    )
    viewer_role, _ = Role.objects.get_or_create(
        name="viewer",
        defaults={"description": "Gated line item access to view data within an organization."}
    )

    # Map old role names to new standardized roles
    role_mapping = {
        "admin": manager_role,
        "Admin": manager_role,
        "Manager": manager_role,
        "manager": manager_role,
        "reader": viewer_role,
        "Reader": viewer_role,
        "Viewer": viewer_role,
        "viewer": viewer_role
    }

    # Get all users with their current roles
    users = BridgeUser.objects.prefetch_related('roles')
    logger.info(f"Processing {users.count()} users")
    
    for user in users:
        old_roles = list(user.roles.all())
        new_roles = set()
        
        # Map each user's roles to the new standardized roles
        for old_role in old_roles:
            if old_role.name in role_mapping:
                new_roles.add(role_mapping[old_role.name])
        
        if new_roles:
            # Clear old roles and add new ones
            user.roles.clear()
            user.roles.add(*new_roles)
            logger.info(f"Updated roles for user {user.username}: {[r.name for r in old_roles]} → {[r.name for r in new_roles]}")

    # Clean up old roles
    old_roles = Role.objects.exclude(name__in=["manager", "viewer"])
    if old_roles.exists():
        logger.info(f"Removing {old_roles.count()} old roles")
        old_roles.delete()

    logger.info("Role migration complete")


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0036_lineitempermission'),
    ]

    operations = [
        migrations.RunPython(migrate_role_name_and_description),
    ]
