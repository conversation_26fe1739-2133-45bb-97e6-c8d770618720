customElements.define(
  "bridge-run-retrieval-button",
  class extends HTMLElement {
    connectedCallback() {
      const state = this.dataset.state || "enabled"; // enabled, disabled, active
      const tooltipText = this.dataset.tooltipText;
      const hxPost = this.dataset.hxPost;
      const hxTarget = this.dataset.hxTarget;
      const hxSwap = this.dataset.hxSwap || "outerHTML";

      const button = this.createButton(state, hxPost, hxTarget, hxSwap);

      if (tooltipText && state === "disabled") {
        // Use bridge-tooltip component
        const tooltip = document.createElement("bridge-tooltip");
        tooltip.dataset.tooltipText = tooltipText;
        tooltip.dataset.content = button.outerHTML;
        this.appendChild(tooltip);
      } else {
        this.appendChild(button);
      }
    }

    createButton(state, hxPost, hxTarget, hxSwap) {
      const button = document.createElement("button");
      let baseClasses =
        "inline-flex items-center justify-center rounded-md bg-base-white px-2 py-1 text-xs font-inter font-medium shadow border border-base-dark-gray w-32";

      // Create the dot indicator
      const dot = document.createElement("div");
      dot.className = "rounded-full mr-1 w-[0.4rem] h-[0.4rem]";

      // Create text node
      const textNode = document.createTextNode("");

      switch (state) {
        case "active":
          baseClasses += " opacity-50";
          dot.className += " bg-red-500";
          textNode.textContent = "Active Retrieval";
          button.disabled = true;
          break;

        case "disabled":
          baseClasses += " opacity-50";
          dot.className += " bg-primary-burnt-orange opacity-60";
          textNode.textContent = "Run Retrieval";
          button.disabled = true;
          break;

        case "enabled":
        default:
          dot.className += " bg-primary-burnt-orange";
          textNode.textContent = "Run Retrieval";
          button.style.cursor = "pointer";

          // Add HTMX attributes
          if (hxPost) button.setAttribute("hx-post", hxPost);
          if (hxTarget) button.setAttribute("hx-target", hxTarget);
          if (hxSwap) button.setAttribute("hx-swap", hxSwap);
          break;
      }

      // Apply the classes to the button
      button.className = baseClasses;

      // Append dot and text to button
      button.appendChild(dot);
      button.appendChild(textNode);

      return button;
    }
  },
);
