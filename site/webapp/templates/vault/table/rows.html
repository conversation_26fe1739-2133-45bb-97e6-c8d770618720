{% load customtags static %}
{% if processed_documents %}
    {% for pd in processed_documents %}
        {% if not pd.has_been_viewed %}
            {% alias "bg-secondary-orange-beige font-semibold" as background %}
        {% else %}
            {% alias "bg-white" as background %}
        {% endif %}
        <tr class="border-b {{ background }}"
            {# djlint:off #}
            _="
            on openedProcessedDocPdf[pk=='{{ pd.pk }}' and viewed] from elsewhere 
                remove .bg-secondary-orange-beige
                remove .font-semibold
                add .bg-white
                set checkbox to first .item-checkbox in me
                set checkbox.checked to false
                send change to checkbox
            on openedProcessedDocPdf[pk=='{{ pd.pk }}' and viewed==false] from elsewhere
                add .bg-secondary-orange-beige
                remove .font-semibold
                remove .bg-white
                set checkbox to first .item-checkbox in me
                set checkbox.checked to false
                send change to checkbox
            on openedProcessedDocPdf[pk=='{{ pd.pk }}' and viewed]
                remove .bg-secondary-orange-beige
                remove .font-semibold
                add .bg-white
            "
            {# djlint:on #}
            >
            <td class="px-4 py-2 text-sm">
                <input type="checkbox"
                       class="item-checkbox"
                       name="processed_doc_pks"
                       value="{{ pd.pk }}"
                       onchange="toggleSelection('{{ pd.pk }}', this)">
            </td>
            {% if pd.has_multiple_investors and raw_doc_to_multiple_clients_name_dict|lookup:pd.raw_retreival_document|length > 1 %}
                <td class="px-4 py-2 text-sm whitespace-nowrap">
                    <span class="text-primary-burnt-orange">Multiple Clients</span>
                    <div class="group relative inline-block">
                        <div class="w-4 h-4 bg-[#924f34] text-white rounded-full flex items-center justify-center text-xs font-bold cursor-help hover:bg-[#924F34]">
                            i
                        </div>
                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap pointer-events-none bg-base-beige px-1 py-1">
                            <div class="bg-white px-3 py-2 rounded-md max-h-40 overflow-y-auto">
                                <span class="font-bold text-primary-dark-olive">Clients:</span>
                                <ul class="list-disc pl-4 text-primary-dark-olive marker:mr-1">
                                    {% for client_name in raw_doc_to_multiple_clients_name_dict|lookup:pd.raw_retreival_document %}
                                        <li>{{ client_name }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </td>
            {% else %}
                <td class="px-4 py-2 text-sm text-gray-700 whitespace-nowrap">
                    {{ pd.line_item.investing_entity.client.legal_name }}
                </td>
            {% endif %}
            {% if pd.has_multiple_investors and raw_doc_to_multiple_entities_name_dict|lookup:pd.raw_retreival_document|length > 1 %}
                <td class="px-4 py-2 text-sm whitespace-nowrap">
                    <span class="text-primary-burnt-orange">Multiple Entities</span>
                    <div class="group relative inline-block">
                        <div class="w-4 h-4 bg-[#924f34] text-white rounded-full flex items-center justify-center text-xs font-bold cursor-help hover:bg-[#924F34]">
                            i
                        </div>
                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap pointer-events-none bg-base-beige px-1 py-1">
                            <div class="bg-white px-3 py-2 rounded-md max-h-40 overflow-y-auto">
                                <span class="font-bold text-primary-dark-olive">Entities:</span>
                                <ul class="list-disc pl-4 text-primary-dark-olive marker:mr-1">
                                    {% for entity_name in raw_doc_to_multiple_entities_name_dict|lookup:pd.raw_retreival_document %}
                                        <li>{{ entity_name }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </td>
            {% else %}
                <td class="px-4 py-2 text-sm text-gray-700 whitespace-nowrap">{{ pd.line_item.investing_entity.legal_name }}</td>
            {% endif %}
            <td class="px-4 py-2 text-sm text-gray-700 whitespace-nowrap">{{ pd.line_item.investment.legal_name }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ pd.document_type_label|title }}</td>
            <td class="px-4 py-2 text-sm text-gray-700 whitespace-nowrap">{{ pd.posted_date }}</td>
            <td class="px-4 py-2 text-sm text-gray-700 whitespace-nowrap">{{ pd.effective_date }}</td>
            <td class="px-4 py-2 whitespace-nowrap">
                <a href="{% url 'pdf_modal' %}{% urlparams processed_doc_pk=pd.pk %}"
                   target="_blank"
                   _="on click or auxclick trigger openedProcessedDocPdf(pk:'{{ pd.pk }}', viewed:true)"
                   class="flex items-center space-x-2">
                    <!-- PDF Icon -->
                    <img height="20"
                         width="20"
                         src="{% static 'assets/images/pdf.svg' %}"
                         alt="pdf icon" />
                    <!-- File Badge -->
                    <div class="px-2 py-1 text-primary-burnt-orange text-sm truncate-text"
                         data-max-length="50"
                         title="{{ pd.name }}">{{ pd.name }}</div>
                </a>
            </td>
        </tr>
    {% endfor %}
{% else %}
    <tr>
        <td colspan="8" class="text-center">
            <!-- Adjusted colspan to account for the checkbox column without sub category column -->
            <div class="MetricItem h-[82px] p-8 bg-white border border-[#eceee8] justify-center items-center w-full">
                <div class="Text text-[#343d36] text-base font-bold font-['Inter'] leading-[18px]">No data available</div>
            </div>
        </td>
    </tr>
{% endif %}
{{ vault_query_data|json_script:'vault_query_data' }}
<script>
    function update() {
        const vault_query_elem = document.getElementById('vault_query_data');
        if (!vault_query_elem) {
            return;
        }
        const vault_query_data = JSON.parse(document.getElementById('vault_query_data').textContent);

        document.getElementById("vault_from").innerHTML = vault_query_data.start_index;
        document.getElementById("vault_to").innerHTML = vault_query_data.end_index;
        document.getElementById("vault_ct").innerHTML = vault_query_data.total_count;
        document.getElementById("vault_current_page").value = vault_query_data.current_page;
        document.getElementById("vault_page_change").value = 0;
        document.getElementById("vault_prev").onclick = () => {
            document.getElementById("vault_page_change").value = 1;
            document.getElementById("vault_current_page").value = parseInt(document.getElementById("vault_current_page").value, 10) - 1;
            document.getElementById("vault_current_page").dispatchEvent(new Event('change', {
                bubbles: true
            }));
        };
        document.getElementById("vault_next").onclick = () => {
            document.getElementById("vault_page_change").value = 1;
            document.getElementById("vault_current_page").value = parseInt(document.getElementById("vault_current_page").value, 10) + 1;
            document.getElementById("vault_current_page").dispatchEvent(new Event('change', {
                bubbles: true
            }));
        };
        Object.entries(vault_query_data.query_filters).forEach(([name, value_list]) => {
            const elems = document.querySelectorAll(`.vaultinputs[name='${name}']`);
            if (elems.length > 1) {
                value_list.forEach((value) => {
                    document.querySelectorAll(`.vaultinputs[name='${name}'][value='${value}']`).forEach((elem) => {
                        elem.checked = true;
                        elem.dispatchEvent(new Event('change-update'));
                    });
                });
            } else if (elems.length == 1) {
                const elem = elems[0];
                if ("datepicker" in elem) {
                    elem.datepicker.setDate(value_list);
                    elem.dispatchEvent(new Event('changeDate-update'));
                }
            }
        });
        document.dispatchEvent(new Event('vaultPageChange'));
        document.getElementById('select-all-checkbox').checked = false;
    };
    document.addEventListener("DOMContentLoaded", update);
    document.addEventListener("htmx:afterSwap", update);
</script>
