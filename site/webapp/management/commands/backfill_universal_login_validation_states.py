from argparse import Argument<PERSON>arser

import structlog
from django.core.management.base import BaseCommand
from django_extensions.management.utils import signalcommand

from webapp.models.portal import MergedPortalCredential
from webapp.models.retrieval import Retrieval

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = (
        "Resets all MergedPortalCredential last_user_login_validation_retrievals"
        " and backfills them w correct user login status."
    )

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("-r", "--reset", action="store_true")

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002
        reset = kwargs.get("reset", False)
        mpcs = MergedPortalCredential.objects.all()
        skipped_incomplete = 0
        skipped_no_docs = 0
        skipped_has_validation_retrieval = 0
        skipped_deleted = 0
        altered = 0
        for mpc in mpcs:
            if reset:
                mpc.last_user_login_validation_retrieval = None
                mpc.save(update_fields=["last_user_login_validation_retrieval"])
                mpc.refresh_from_db()
            if mpc.deleted_at is not None:
                logger.debug("MPC is deleted, skipping", mpc_id=mpc.id)
                skipped_deleted += 1
                continue
            if not mpc.is_complete or mpc.created_by is None:
                logger.info(
                    "MPC not complete, skipping",
                    mpc_id=mpc.id,
                    pc_not_none=mpc.portal_credential is not None,
                    pc_is_complete=mpc.portal_credential.is_complete,
                    mfa_not_none=mpc.multi_factor_authentication is not None,
                    mfa_is_complete=mpc.multi_factor_authentication.is_complete,
                )
                skipped_incomplete += 1
                continue
            has_successful_retrieval = mpc.retrievals.filter(
                retrieval_status=Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
            ).exists()
            if not has_successful_retrieval:
                logger.info("MPC has no successful retrievals, skipping", mpc_id=mpc.id)
                skipped_no_docs += 1
                continue
            if mpc.last_user_login_validation_retrieval is not None:
                mpc.last_user_login_validation_retrieval.update_user_login_status(
                    Retrieval.UserLoginStatus.SUCCESS_LOGGED_IN
                )
                mpc.last_user_login_validation_retrieval.update_login_status(
                    Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL
                )
                mpc.last_user_login_validation_retrieval.save(update_fields=["user_login_status", "retrieval_status"])
                logger.info(
                    "MPC already has user login validation retrieval",
                    mpc_id=mpc.id,
                    retrieval_id=mpc.last_user_login_validation_retrieval.id,
                    mpc_retrieval_status=mpc.last_user_login_validation_retrieval.user_login_status,
                    mpc_portal_user_login_status=mpc.portal_user_login_status,
                    mpc_portal_credential_user_login_status=mpc.portal_credential_user_login_status,
                    mpc_multi_factor_authentication_user_login_status=mpc.multi_factor_authentication_user_login_status,
                )
                skipped_has_validation_retrieval += 1
                continue
            altered += 1
            retrieval = Retrieval.create(
                user=mpc.created_by,
                merged_portal_credential=mpc,
                is_user_login_validation_retrieval=True,
                is_backfill=True,
            )
            retrieval.update_user_login_status(Retrieval.UserLoginStatus.SUCCESS_LOGGED_IN)
            retrieval.update_login_status(Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL)
            logger.info(
                "Created user login validation retrieval",
                mpc_id=mpc.id,
                retrieval_id=retrieval.id,
            )
        logger.info(
            "Backfill complete",
            altered=altered,
            skipped_has_validation_retrieval=skipped_has_validation_retrieval,
            skipped_incomplete=skipped_incomplete,
            skipped_no_docs=skipped_no_docs,
            skipped_deleted=skipped_deleted,
        )
