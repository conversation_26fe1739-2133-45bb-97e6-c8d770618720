from typing import Annotated

from langgraph.graph.message import add_messages
from typing_extensions import TypedDict

from .doc_vault import DocVaultOutput, LineItemClassification


class State(TypedDict):
    # Messages have the type "list". The `add_messages` function
    # in the annotation defines how this state key should be updated
    # (in this case, it appends messages to the list, rather than overwriting them)
    messages: Annotated[list, add_messages]
    line_items: LineItemClassification | None
    id2messages: dict[str, list]
    id2obj: dict[str, DocVaultOutput]
    raw_doc_pk: str
    use_known_line_items: bool
    use_ground_truth: bool
    use_past_predictions: bool
    do_database_insertion_processed_doc: bool


class BootstrapState(TypedDict):
    # Messages have the type "list". The `add_messages` function
    # in the annotation defines how this state key should be updated
    # (in this case, it appends messages to the list, rather than overwriting them)
    line_items: LineItemClassification
    messages: Annotated[list, add_messages]
    id2messages: dict[str, list]
    id2obj: dict[str, DocVaultOutput]
    do_database_insertion_line_item: bool
    line_items_predict_work_todo: list[str]
    line_items_predict_work_current: str
    line_items_predict_work_done: list[str]
    line_items_predict_work_errors: list[str]
    merged_portal_credential_pk: str
    final_predict_work_todo: list[str]
    final_predict_work_current: str
    final_predict_work_done: list[str]
    final_predict_work_errors: list[str]
