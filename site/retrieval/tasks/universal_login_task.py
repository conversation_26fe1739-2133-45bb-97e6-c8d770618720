import asyncio

import structlog
from asgiref.sync import sync_to_async
from celery import Task, shared_task
from webapp.models import Bridge<PERSON>ser, Retrieval

logger = structlog.get_logger(__name__)


@shared_task(bind=True, track_started=True, time_limit=2 * 60 * 60, soft_time_limit=2 * 60 * 60)
def universal_login_task(self: Task, *, retrieval_id: str) -> None:  # noqa: ARG001
    retrieval = Retrieval.objects.get(id=retrieval_id)
    user = retrieval.created_by
    if user is None:
        logger.error("No user found for retrieval", retrieval_pk=retrieval.pk)
        raise ValueError
    asyncio.run(async_universal_login_task(retrieval_id=str(retrieval.pk)))


async def async_universal_login_task(retrieval_id: str) -> None:
    retrieval = await Retrieval.objects.select_related("created_by").aget(id=retrieval_id)
    user_id = retrieval.created_by.pk
    user = await BridgeUser.objects.aget(id=user_id)
    if user is None:
        logger.error("No user found for retrieval", retrieval_pk=retrieval.pk)
        raise ValueError
    if await user.ais_demo():
        await asyncio.sleep(10)
        await update_universal_login_status(retrieval_id, Retrieval.UserLoginStatus.SUCCESS_LOGGED_IN)
        await update_retrieval_status(retrieval_id, Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL)
        return


@sync_to_async
def update_universal_login_status(retrieval_id: str, status: Retrieval.UserLoginStatus) -> None:
    retrieval = Retrieval.objects.get(id=retrieval_id)
    retrieval.update_user_login_status(status)


@sync_to_async
def update_retrieval_status(retrieval_id: str, status: Retrieval.RetrievalStatus) -> None:
    retrieval = Retrieval.objects.get(id=retrieval_id)
    retrieval.update_login_status(status)
