from argparse import ArgumentParser

import structlog
from django.core.management.base import BaseCommand
from django_extensions.management.utils import signalcommand
from retrieval.tasks.login_portal_task import noop_task

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Spawn no op tasks for testing"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("-n", "--number", type=int)

    @signalcommand
    def handle(self, *args: list[str], **kwargs: int) -> None:  # noqa: ARG002
        number = kwargs.get("number") or 10

        logger.info("spawning no op tasks", number=number)

        for i in range(number):
            noop_task.apply_async()
            msg = f"spawned {i + 1} noop tasks"
            logger.info(msg)

        msg = f"spawned all {number} noop tasks"
        logger.info(msg)
