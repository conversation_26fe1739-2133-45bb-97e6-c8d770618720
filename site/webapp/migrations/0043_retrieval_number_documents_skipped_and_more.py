# Generated by Django 5.2.1 on 2025-06-04 01:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0042_emailretrieval_emailintake_email_retrieval_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='retrieval',
            name='number_documents_skipped',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='retrieval',
            name='number_of_retries',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='retrieval',
            name='start_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='retrieval',
            name='retrieval_status',
            field=models.CharField(choices=[('ns', 'Not Started'), ('su', 'Submitted'), ('pc', 'Cancel Pending'), ('lp', 'Login Pending'), ('2p', 'OTP Pending'), ('2b', 'OTP Blocked'), ('ca', 'Canceled'), ('lf', 'Login Failed'), ('2f', 'OTP Required and could not get OTP'), ('pd', 'Login Failed - Portal Down'), ('fr', 'Portal Down - Retryable'), ('ps', 'Pending Scheduling'), ('li', 'Successfully Logged In'), ('dr', 'Document Retrieval Pending'), ('df', 'Document Retrieval Failed'), ('ds', 'Successfully Retrieved Documents')], default='ns', max_length=2),
        ),
    ]
