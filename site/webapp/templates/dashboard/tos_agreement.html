{% extends "base.html" %}
{% load widget_tweaks static allauth account %}
{% block title %}
    Terms Of Service and Privacy Policy
{% endblock title %}
{% block webapp_content %}
    <!-- Header -->
    <header class="min-w-full h-[100px] bg-primary-dark-olive flex flex-col justify-center items-center gap-2.5">
        <div class="justify-start items-center gap-10 flex">
            <div class="h-9 relative">
                <a href="{% url 'account_login' %}">
                    <img class="items-center w-50"
                         alt="Bridge"
                         width="130"
                         height="37"
                         src="{% static 'assets/images/logo.svg' %}">
                </a>
            </div>
        </div>
    </header>
    <!-- Main Content -->
    <main class="flex flex-col items-center justify-center min-h-screen px-4">
        {% if user.tos_accepted_at is None or user.tos_last_updated_at > user.tos_accepted_at %}
            <div class="bg-white shadow-lg rounded-lg p-6 w-full max-w-md text-center border border-gray-300">
                <!-- Title -->
                <h2 class="text-xl font-semibold text-[#924f34]">We've Updated Our Terms & Privacy Policy</h2>
                <p class="text-gray-700 mt-2 text-sm">
                    Please read and accept the updated Terms of Service to continue using our platform.
                </p>
                <!-- Links to Terms of Service & Privacy Policy -->
                <div class="mt-3">
                    <a href="https://www.bridgeinvest.io/terms-of-service"
                       class="text-[#924f34] underline text-sm">View Terms of Service</a> |
                    <a href="https://www.bridgeinvest.io/privacy-policy"
                       class="text-[#924f34] underline text-sm">View Privacy Policy</a>
                </div>
                <form method="post">
                    {% csrf_token %}
                    <!-- Checkbox -->
                    <div class="mt-4 flex items-center justify-center">
                        <input type="checkbox"
                               id="agreeTOS"
                               class="h-5 w-5 text-[#924f34] border-gray-300 focus:ring-[#924f34]">
                        <label for="agreeTOS" class="ml-2 text-gray-700 text-sm">I have read and agree to the Terms of Service.</label>
                    </div>
                    <!-- Submit Button -->
                    <button id="agreeBtn"
                            class="mt-6 w-full bg-[#924f34] text-white py-2 rounded-md text-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled>Agree & Continue</button>
                </div>
            </form>
            <!-- JavaScript to enable button only when checkbox is checked -->
            <script>
                document.getElementById("agreeTOS").addEventListener("change", function() {
                    document.getElementById("agreeBtn").disabled = !this.checked;
                });
            </script>
        {% endif %}
    </main>
{% endblock webapp_content %}
