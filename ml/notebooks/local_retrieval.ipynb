{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from patchright.async_api import async_playwright as async_patchright\n", "from playwright.async_api import async_playwright\n", "\n", "# This forces to use localhost as the database URL instead of the docker container reference in docker compose\n", "os.environ[\"USE_LOCALHOST\"] = \"1\"\n", "# Make sure we're referencing PROD AWS assets\n", "os.environ[\"AWS_PROFILE\"] = \"prod\"\n", "# HACK: jupyter notebooks don't play well with Django ORM\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "# Make sure we use the production oauth secret for setting rules up for MFA scraping\n", "os.environ[\"USE_PROD_OAUTH_SECRET\"] = \"1\"  # noqa: S105\n", "import importlib\n", "\n", "import cracker_tk as tk\n", "\n", "tk.setup_django()\n", "\n", "from retrieval.core import (\n", "    registry,\n", "    strategy,\n", ")\n", "\n", "# Change this to the manager you want to test\n", "from retrieval.core.managers import ssc as cur_development\n", "from webapp.models import BridgeUser, MergedPortalCredential, Portal, Retrieval\n", "\n", "class_dir = next(x for x in dir(cur_development) if x.endswith(\"Manager\") and x != \"RetrievalManager\")\n", "expected_manager_cls = getattr(cur_development, class_dir)\n", "known_portals = expected_manager_cls.known_user_portals()\n", "display(known_portals)\n", "email, portal = known_portals[0]\n", "display(email, portal)\n", "# CHnage this to the user / portal you want to test\n", "user = BridgeUser.objects.get(email=email)\n", "portal = Portal.objects.get(name=portal, organization=user.organization)\n", "# Possible there are multiple merged portal credentials for the same portal and organization.\n", "# Some might not work, so you might need to try a few.\n", "# Please mark those as inactive in the admin panel.\n", "merged_portal_credential = MergedPortalCredential.objects.for_user(user).filter(portal=portal).first()\n", "\n", "retrieval = Retrieval.create(user=user, merged_portal_credential=merged_portal_credential)\n", "\n", "# merged_portal_credential\n", "\n", "playwright_instance = None\n", "browser = None\n", "context = None\n", "page = None\n", "self = None\n", "\n", "\n", "async def arefresh() -> None:\n", "    \"\"\"\n", "    Refreshes the script to interact with the browser without killing the browser state.\n", "    This allows you to iterate on the class without closing the session/tab.\n", "    \"\"\"\n", "    # We're just hacking about here ain't we\n", "    global browser, context, page, self, playwright_instance  # noqa: PLW0603\n", "\n", "    importlib.reload(registry)\n", "    importlib.reload(strategy)\n", "    importlib.reload(cur_development)\n", "    manager_cls = await registry.RetrievalRegistry.aget_retrieval_manager_for_retrieval(user, retrieval)\n", "    needs_playwright = False\n", "    needs_patchright = False\n", "    if manager_cls is not None:\n", "        needs_playwright = manager_cls.needs_playwright()\n", "        needs_patchright = manager_cls.needs_patchright()\n", "    if needs_patchright and needs_playwright:\n", "        raise ValueError\n", "    playwright_instance = None\n", "    if needs_playwright:\n", "        playwright_instance = await async_playwright().start()\n", "    if needs_patchright:\n", "        playwright_instance = await async_patchright().start()\n", "    self = manager_cls(playwright_instance=playwright_instance, retrieval=retrieval, user=user)  # type: ignore[arg-type]\n", "    if playwright_instance is not None:\n", "        await self.async_init(browser, context, page)\n", "        browser = self.browser\n", "        context = self.context\n", "        page = self.page\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await arefresh()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await arefresh()\n", "await self.login()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await arefresh()\n", "await self.retrieve()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await arefresh()\n", "await self.cleanup()\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}