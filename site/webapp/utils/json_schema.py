import re
from typing import Any

import structlog
from jsonschema import Draft7Validator, ValidationError, validate

logger = structlog.get_logger(__name__)


def validate_data(data: dict[str, Any], schema: dict[str, Any]) -> bool:
    """
    Validate data against the schema.

    Args:
        data: The data to validate
        schema: The JSON schema to validate against

    Returns:
        True if valid, False otherwise

    """
    try:
        validate(instance=data, schema=schema)
        logger.info("Data validation successful")
    except ValidationError as e:
        logger.exception("Validation error", message=e.message, path=" -> ".join(str(p) for p in e.absolute_path))
        return False
    return True


def get_validation_errors(data: dict[str, Any], schema: dict[str, Any]) -> list[str]:
    """
    Get detailed validation errors for data.

    Args:
        data: The data to validate
        schema: The JSON schema to validate against

    Returns:
        List of error messages

    """
    validator = Draft7Validator(schema)
    errors = []
    for error in validator.iter_errors(data):
        error_path = " -> ".join(str(p) for p in error.absolute_path)
        error_msg = f"Path: {error_path}, Error: {error.message}"
        errors.append(error_msg)
    return errors


def validate_with_details(data: dict[str, Any], schema: dict[str, Any]) -> tuple[bool, list[str]]:
    """
    Validate data and return detailed error information.

    Args:
        data: The data to validate
        schema: The JSON schema to validate against

    Returns:
        Tuple of (is_valid, list_of_errors)

    """
    errors = get_validation_errors(data, schema)
    is_valid = len(errors) == 0

    return is_valid, errors


def rename_properties_by_id(schema: dict[str, Any], id_key: str = "$id") -> dict[str, Any]:
    def process(obj: Any) -> Any:  # noqa: ANN401
        if isinstance(obj, dict):
            new_obj = {}
            for k, v in obj.items():
                if k == "properties" and isinstance(v, dict):
                    new_props = {}
                    for prop_key, prop_val in v.items():
                        if isinstance(prop_val, dict) and id_key in prop_val and isinstance(prop_val[id_key], str):
                            new_key = prop_val[id_key].split("/")[-1]
                            new_props[new_key] = process(prop_val)
                        else:
                            new_props[prop_key] = process(prop_val)
                    new_obj[k] = new_props
                else:
                    new_obj[k] = process(v)
            return new_obj
        if isinstance(obj, list):
            return [process(item) for item in obj]
        return obj

    return process(schema)


def resolve_refs(schema: dict[str, Any]) -> dict[str, Any]:
    """Recursively resolve $ref references in a JSON schema."""
    definitions = schema.get("definitions", {})

    def resolved_refs_helper(_schema: Any) -> Any:  # noqa: ANN401
        if isinstance(_schema, dict):
            if "$ref" in _schema:
                ref_id = _schema["$ref"]
                # Extract the definition name from the ref ID
                def_name = ref_id
                if def_name in definitions:
                    # Recursively resolve the referenced definition
                    return resolved_refs_helper(definitions[def_name])
                logger.error(
                    "Reference not found in definitions", ref_id=ref_id, available_defs=list(definitions.keys())
                )
                raise ValueError
            # Recursively process all values in the dictionary
            return {key: resolved_refs_helper(value) for key, value in _schema.items()}
        if isinstance(_schema, list):
            # Recursively process all items in the list
            return [resolved_refs_helper(item) for item in _schema]
        # Return primitive values as-is
        return _schema

    result = resolved_refs_helper(schema)
    result.pop("definitions", None)  # Remove definitions from the result
    return result


def find_schemas_with_reference(schemas: list[dict[str, Any]], target_schema_id: str) -> list[dict[str, Any]]:
    def has_reference(obj: Any, target_id: str) -> bool:  # noqa: ANN401
        """Recursively search for $ref references to target_id."""
        if isinstance(obj, dict):
            # Check if this object has a $ref to our target
            if "$ref" in obj and obj["$ref"] == target_id:
                return True
            # Recursively check all values in the dictionary
            return any(has_reference(value, target_id) for value in obj.values())
        if isinstance(obj, list):
            # Recursively check all items in the list
            return any(has_reference(item, target_id) for item in obj)
        # Primitive values can't contain references
        return False

    return [schema for schema in schemas if has_reference(schema, target_schema_id)]


def build_schema_reference_map(schemas: list[dict[str, Any]]) -> dict[str, list[str]]:
    def find_references(obj: Any) -> list[str]:  # noqa: ANN401
        """Recursively find all $ref values in an object."""
        refs = []

        if isinstance(obj, dict):
            # Check if this object has a $ref
            if "$ref" in obj:
                refs.append(obj["$ref"])
            # Recursively check all values in the dictionary
            for value in obj.values():
                refs.extend(find_references(value))
        elif isinstance(obj, list):
            # Recursively check all items in the list
            for item in obj:
                refs.extend(find_references(item))

        return refs

    reference_map = {}

    for schema in schemas:
        schema_id = schema.get("$id")
        if schema_id:
            # Find all references in this schema
            references = find_references(schema)
            # Remove duplicates while preserving order
            unique_refs = list(dict.fromkeys(references))
            reference_map[schema_id] = unique_refs

    return reference_map


def check_dag_and_topological_sort(reference_map: dict[str, list[str]]) -> list[str]:  # noqa: C901
    # Check for cycles using DFS
    def has_cycle() -> None:
        WHITE, GRAY, BLACK = 0, 1, 2  # noqa: N806
        colors = dict.fromkeys(reference_map, WHITE)

        def dfs(node: Any, path: list[Any]) -> bool:  # noqa: ANN401
            if colors[node] == GRAY:
                # Found a back edge - cycle detected
                cycle_start = path.index(node)
                cycle = [*path[cycle_start:], node]
                logger.error("Cycle detected in schema references", cycles=" -> ".join(cycle))
                raise ValueError
            if colors[node] == BLACK:
                return False

            colors[node] = GRAY
            path.append(node)

            for neighbor in reference_map.get(node, []):
                # Only check neighbors that exist in our reference map
                if neighbor in reference_map:
                    dfs(neighbor, path)

            path.pop()
            colors[node] = BLACK
            return False

        for node in reference_map:
            if colors[node] == WHITE:
                dfs(node, [])

    # Check for cycles first
    has_cycle()

    # Perform topological sort using Kahn's algorithm
    def topological_sort() -> list[str]:
        # Calculate in-degrees
        in_degree = dict.fromkeys(reference_map, 0)

        for neighbors in reference_map.values():
            for neighbor in neighbors:
                if neighbor in in_degree:  # Only count references to schemas in our map
                    in_degree[neighbor] += 1

        # Initialize queue with nodes that have no dependencies
        queue = [node for node, degree in in_degree.items() if degree == 0]
        result = []

        while queue:
            # Sort queue to ensure deterministic output
            queue.sort()
            node = queue.pop(0)
            result.append(node)

            # Remove this node and update in-degrees of its neighbors
            for neighbor in reference_map[node]:
                if neighbor in in_degree:
                    in_degree[neighbor] -= 1
                    if in_degree[neighbor] == 0:
                        queue.append(neighbor)

        return result

    return topological_sort()[::-1]


def update_json_schema_refs(schemas: list[Any], old_ref_id: str, new_ref_id: str) -> list[Any] | Any:  # noqa: ANN401
    """
    Recursively update $ref values in JSON schema objects.

    Args:
        schemas: List of JSON schema objects or a single schema object
        old_ref_id: The old reference ID to replace
        new_ref_id: The new reference ID to use as replacement

    Returns:
        Updated schemas with replaced $ref values

    """

    def update_refs_recursive(obj: Any) -> Any:  # noqa: ANN401
        if isinstance(obj, dict):
            # Check if this is a $ref and needs updating
            if "$ref" in obj and obj["$ref"] == old_ref_id:
                obj["$ref"] = new_ref_id

            # Recursively process all dictionary values
            for key, value in obj.items():
                obj[key] = update_refs_recursive(value)

        elif isinstance(obj, list):
            # Recursively process all list items
            for i, item in enumerate(obj):
                obj[i] = update_refs_recursive(item)

        return obj

    # Handle both single schema and list of schemas
    if isinstance(schemas, list):
        return [update_refs_recursive(schema) for schema in schemas]
    return update_refs_recursive(schemas)


def unresolve_refs(schema: dict[str, Any]) -> dict[str, Any]:
    """Convert inline schema objects back to $ref references with definitions."""
    definitions = {}

    def extract_definitions(obj: Any) -> Any:  # noqa: ANN401
        """Recursively extract objects with $id into definitions."""
        if isinstance(obj, dict):
            if "$id" in obj:
                # Extract the definition name from the $id
                schema_id = obj["$id"]
                def_name = schema_id

                # Only process if we haven't seen this definition before (prevents infinite loops)
                if def_name not in definitions:
                    # Create a copy of the object for the definition
                    definition = dict(obj)

                    # Recursively process the definition to extract nested definitions
                    # but exclude the current definition from processing to avoid loops
                    for key, value in definition.items():
                        if key != "$id":
                            definition[key] = extract_definitions(value)

                    # Store in definitions (keeping the $id)
                    definitions[def_name] = definition

                # Return a reference to this definition
                return {"$ref": schema_id}
            # Recursively process all values
            return {key: extract_definitions(value) for key, value in obj.items()}
        if isinstance(obj, list):
            # Recursively process all items in the list
            return [extract_definitions(item) for item in obj]
        # Return primitive values as-is
        return obj

    # Process the schema to extract definitions and create references
    processed_schema = extract_definitions(schema)

    # Add definitions section to the schema
    if definitions:
        processed_schema["definitions"] = definitions

    return processed_schema


def map_schema_ids_to_data(data: dict[str, Any], schema: dict[str, Any]) -> dict[str, Any]:
    """
    Maps schema $id values to their corresponding data from the JSON object.

    Args:
        data: The filled-in JSON data
        schema: The JSON schema with $id fields

    Returns:
        A dictionary where keys are schema $ids and values are the corresponding data

    """
    result = {}

    def extract_mappings(current_data: Any, current_schema: dict[str, Any], path: list | None = None) -> None:  # noqa: ANN401
        if path is None:
            path = []

        # If current schema has an $id, map it to the current data
        if isinstance(current_schema, dict) and "$id" in current_schema:
            result[current_schema["$id"]] = current_data

        # If current schema has properties, recurse into them
        if isinstance(current_schema, dict) and "properties" in current_schema:
            properties = current_schema["properties"]

            for prop_name, prop_schema in properties.items():
                # Get the corresponding data value if it exists
                if isinstance(current_data, dict) and prop_name in current_data:
                    prop_data = current_data[prop_name]
                    extract_mappings(prop_data, prop_schema, [*path, prop_name])
                else:
                    # Even if data doesn't exist, we might want to map schema $ids to None
                    extract_mappings(None, prop_schema, [*path, prop_name])

    # Start the recursive extraction
    extract_mappings(data, schema)

    return result


def create_resolved_schema(definitions: list[dict], schema_id: str) -> dict[str, Any]:
    """
    Create a resolved JSON schema for a specific schema definition ID.

    Args:
        definitions (dict): Dictionary of schema definitions
        schema_id (str): The ID of the schema definition to resolve

    Returns:
        dict: Resolved JSON schema with all $refs inlined

    """
    definitions_dict = {d["$id"]: d for d in definitions}

    def resolve_refs(schema: Any, defs: dict[str, Any]) -> Any:  # noqa: ANN401
        """Recursively resolve $ref references in a JSON schema."""
        if isinstance(schema, dict):
            if "$ref" in schema:
                ref_id = schema["$ref"]
                def_name = ref_id
                if def_name in defs:
                    return resolve_refs(defs[def_name], defs)
                logger.error("reference not found", ref_id=ref_id, available_defs=list(defs.keys()))
                raise ValueError
            return {key: resolve_refs(value, defs) for key, value in schema.items()}
        if isinstance(schema, list):
            return [resolve_refs(item, defs) for item in schema]
        return schema

    # Extract the definition name from the schema ID
    def_name = schema_id

    # Check if the definition exists
    if def_name not in definitions_dict:
        logger.error("Schema definition not found", def_name=def_name, available_defs=list(definitions_dict.keys()))
        raise ValueError

    # Get the base schema definition
    base_schema = definitions_dict[def_name]

    # Resolve all references in the schema
    return resolve_refs(base_schema, definitions_dict)


def json_paths_to_dict(json_paths: list[tuple[str, Any]]) -> dict[str, Any]:  # noqa: C901, PLR0912, PLR0915
    """
    Convert a list of (json_path, value) tuples into a nested dictionary.

    Args:
        json_paths: List of tuples containing JSON path strings and their values

    Returns:
        A nested dictionary with the JSON paths filled in

    """
    result = {}

    for json_path, value in json_paths:
        # Remove the leading $. if present
        path = json_path.lstrip("$.")

        # Split the path into segments, handling array indices
        segments = []
        current_segment = ""
        bracket_count = 0

        i = 0
        while i < len(path):
            char = path[i]
            if char == "[":
                if current_segment:
                    segments.append(current_segment)
                    current_segment = ""
                bracket_count += 1
                bracket_start = i + 1
            elif char == "]":
                if bracket_count > 0:
                    # Extract the index
                    index_str = path[bracket_start:i]
                    segments.append(int(index_str))
                    bracket_count -= 1
            elif char == "." and bracket_count == 0:
                if current_segment:
                    segments.append(current_segment)
                    current_segment = ""
            elif bracket_count == 0:
                current_segment += char
            i += 1

        if current_segment:
            segments.append(current_segment)

        # Navigate/create the nested structure
        current = result
        for i, segment in enumerate(segments[:-1]):
            if isinstance(segment, int):
                # Array index - ensure array is long enough and fill gaps with None
                while len(current) <= segment:
                    current.append(None)
                if current[segment] is None:
                    # Look ahead to see if next segment is an array index
                    next_segment = segments[i + 1] if i + 1 < len(segments) else None
                    if isinstance(next_segment, int):
                        current[segment] = []
                    else:
                        current[segment] = {}
                current = current[segment]
            else:
                # Object key
                if segment not in current:
                    # Look ahead to see if next segment is an array index
                    next_segment = segments[i + 1] if i + 1 < len(segments) else None
                    if isinstance(next_segment, int):
                        current[segment] = []
                    else:
                        current[segment] = {}
                current = current[segment]

        # Set the final value
        final_segment = segments[-1]
        if isinstance(final_segment, int):
            # Ensure array is long enough and fill gaps with None
            while len(current) <= final_segment:
                current.append(None)
            current[final_segment] = value
        else:
            current[final_segment] = value

    return result


def dict_to_json_paths(obj: Any, prefix: str = "$") -> list[tuple[str, Any]]:  # noqa: ANN401
    """
    Convert a nested dictionary/list into a list of (json_path, value) tuples.

    Args:
        obj: The object to convert (dict, list, or primitive value)
        prefix: The current JSON path prefix

    Returns:
        List of tuples containing JSON path strings and their values

    """
    result = []

    if isinstance(obj, dict):
        for key, value in obj.items():
            new_prefix = f"{prefix}.{key}" if prefix != "$" else f"$.{key}"
            if isinstance(value, (dict, list)):
                result.extend(dict_to_json_paths(value, new_prefix))
            else:
                result.append((new_prefix, value))
    elif isinstance(obj, list):
        for i, value in enumerate(obj):
            new_prefix = f"{prefix}[{i}]"
            if isinstance(value, (dict, list)):
                result.extend(dict_to_json_paths(value, new_prefix))
            else:
                result.append((new_prefix, value))
    else:
        # Primitive value
        result.append((prefix, obj))

    return result


def compact_array_indices(json_paths: list[tuple[str, Any]]) -> tuple[list[tuple[str, Any]], dict[str, str]]:
    """
    Compact array indices by removing gaps and shifting indices down.

    For example, indices [0, 1, 2, 5] become [0, 1, 2, 3].

    Args:
        json_paths: List of tuples containing JSON path strings and their values

    Returns:
        Tuple of:
        - List of tuples with compacted array indices
        - Dictionary mapping old paths to new paths (only for changed paths)

    """
    # Group paths by their array prefix (the part before the first [index])
    array_prefix_groups = {}
    non_array_paths = []

    for path, value in json_paths:
        # Find the first array index in the path
        match = re.search(r"^(.*?)\[(\d+)\](.*)$", path)

        if match:
            prefix = match.group(1)  # Everything before first [index]
            index = int(match.group(2))  # The first index
            suffix = match.group(3)  # Everything after first [index]

            # Group by just the prefix (the array path)
            if prefix not in array_prefix_groups:
                array_prefix_groups[prefix] = []
            array_prefix_groups[prefix].append((index, suffix, path, value))
        else:
            # Not an array path, keep as is
            non_array_paths.append((path, value))

    # Compact each array prefix group
    compacted_paths = []
    path_changes = {}  # old_path -> new_path for changed paths only

    for prefix, items in array_prefix_groups.items():
        # Sort by original index
        items.sort(key=lambda x: x[0])

        # Get unique indices and create mapping to consecutive indices
        unique_indices = sorted({item[0] for item in items})
        index_mapping = {old_idx: new_idx for new_idx, old_idx in enumerate(unique_indices)}

        # Rebuild paths with new indices
        for old_index, suffix, old_path, value in items:
            new_index = index_mapping[old_index]
            new_path = f"{prefix}[{new_index}]{suffix}"
            compacted_paths.append((new_path, value))

            # Only add to changes dict if the path actually changed
            if old_path != new_path:
                path_changes[old_path] = new_path

    # Add non-array paths back
    compacted_paths.extend(non_array_paths)

    return compacted_paths, path_changes


if __name__ == "__main__":
    json_schema = {}
    normalized = unresolve_refs(resolve_refs(json_schema))

    def identity(x: Any) -> Any:  # noqa: ANN401
        return unresolve_refs(resolve_refs(x))

    if identity(json_schema) == identity(identity(json_schema)):
        logger.info("works")
    test_paths = [
        ("$.accounting_facts[6].fee", 8),
        ("$.accounting_facts[6].foo", 88),
        ("$.accounting_facts[6].fum", 888),
        ("$.accounting_facts[0].foo", 1),
        ("$.accounting_facts[1][0]", 2),
        ("$.accounting_facts[5]", 2),
        ("$.document_type", "Accounting"),
        ("$.investment_name", "Fund"),
        ("$.effective_date", "2024-04-01"),
    ]

    compacted_paths, changes = compact_array_indices(test_paths)

    result_dict = json_paths_to_dict(test_paths)

    back_to_paths = dict_to_json_paths(result_dict)

    result_dict = json_paths_to_dict(compacted_paths)

    back_to_paths = dict_to_json_paths(result_dict)
