{% extends "account/base_entrance.html" %}
{% comment %}
From: https://docs.allauth.org/en/dev/common/templates.html
Source: https://codeberg.org/allauth/django-allauth/src/branch/main/allauth/templates/account/login.html
{% endcomment %}
{% load widget_tweaks static allauth account %}
{% block head_title %}
    Sign In
{% endblock head_title %}
{% block extra_head %}
    <header class="min-w-full h-[100px] bg-primary-dark-olive flext flex-col justify-center items-center gap-2.5 inline-flex">
        <div class="justify-start items-center gap-10 inline-flex">
            <div class="h-9 relative">
                <a href="{% url 'account_login' %}">
                    <img class="items-center w-50"
                         alt="Bridge Logo"
                         height="37px"
                         width="130px"
                         src="{% static 'assets/images/logo.svg' %}">
                </a>
            </div>
        </div>
    </header>
{% endblock extra_head %}
{% block content %}
    <main class="mx-auto">
        <div class="w-[400px] flex-col justify-start items-center gap-6 flex">
            <div class="self-stretch flex-col justify-start items-start gap-3 flex">
                <div class="text-primary-dark-olive text-4xl font-extrabold font-nanum leading-[54px]">Sign In</div>
            </div>
            <form method="post"
                  hx-boost="false"
                  action="{% url 'account_login' %}"
                  class="self-stretch h-56 flex-col justify-start items-center gap-6 flex">
                {% csrf_token %}
                {% for field in form.visible_fields %}
                    {% if field.name == "login" %}
                        <div class="self-stretch h-11 flex-col justify-start items-start gap-1.5 flex">
                            <div class="self-stretch h-11 flex-col justify-start items-start gap-1.5 flex">
                                <div class="self-stretch bg-white rounded-lg shadow border border-[#eceee8] flex justify-between items-center gap-2">
                                    {{ field|attr:"placeholder:Enter your email"|attr:"class: grow shrink h-11 flex px-3.5 py-2.5 bg-white border-none rounded-xl text-[#343d36] text-sm font-normal font-inter focus:outline-none focus:ring-0" }}
                                    <!-- Icon -->
                                    <div class="flex items-center pr-3">
                                        <svg xmlns="http://www.w3.org/2000/svg"
                                             width="20"
                                             height="21"
                                             viewBox="0 0 20 21"
                                             fill="none">
                                            <path d="M10 5.5C9.38194 5.5 8.77775 5.68328 8.26384 6.02666C7.74994 6.37004 7.3494 6.8581 7.11288 7.42911C6.87635 8.00013 6.81447 8.62847 6.93505 9.23466C7.05563 9.84085 7.35325 10.3977 7.79029 10.8347C8.22733 11.2717 8.78415 11.5694 9.39034 11.69C9.99654 11.8105 10.6249 11.7486 11.1959 11.5121C11.7669 11.2756 12.255 10.8751 12.5983 10.3612C12.9417 9.84725 13.125 9.24307 13.125 8.625C13.125 7.7962 12.7958 7.00134 12.2097 6.41529C11.6237 5.82924 10.8288 5.5 10 5.5ZM10 10.5C9.62916 10.5 9.26665 10.39 8.95831 10.184C8.64997 9.97798 8.40964 9.68514 8.26773 9.34253C8.12581 8.99992 8.08868 8.62292 8.16103 8.25921C8.23338 7.89549 8.41195 7.5614 8.67418 7.29917C8.9364 7.03695 9.27049 6.85837 9.63421 6.78603C9.99792 6.71368 10.3749 6.75081 10.7175 6.89273C11.0601 7.03464 11.353 7.27496 11.559 7.58331C11.765 7.89165 11.875 8.25416 11.875 8.625C11.8745 9.12213 11.6768 9.59875 11.3253 9.95028C10.9738 10.3018 10.4971 10.4995 10 10.5Z" fill="#D0D2BF" />
                                            <path d="M10 1.75C8.26942 1.75 6.57769 2.26318 5.13876 3.22464C3.69983 4.1861 2.57832 5.55267 1.91606 7.15152C1.25379 8.75037 1.08051 10.5097 1.41813 12.207C1.75575 13.9044 2.58911 15.4635 3.81282 16.6872C5.03653 17.9109 6.59563 18.7442 8.29296 19.0819C9.9903 19.4195 11.7496 19.2462 13.3485 18.5839C14.9473 17.9217 16.3139 16.8002 17.2754 15.3612C18.2368 13.9223 18.75 12.2306 18.75 10.5C18.7474 8.18017 17.8246 5.95611 16.1843 4.31574C14.5439 2.67537 12.3198 1.75265 10 1.75ZM6.25 16.9856V16.125C6.2505 15.6279 6.4482 15.1512 6.79973 14.7997C7.15125 14.4482 7.62788 14.2505 8.125 14.25H11.875C12.3721 14.2505 12.8488 14.4482 13.2003 14.7997C13.5518 15.1512 13.7495 15.6279 13.75 16.125V16.9856C12.612 17.6501 11.3178 18.0002 10 18.0002C8.68219 18.0002 7.38805 17.6501 6.25 16.9856ZM14.9956 16.0788C14.9835 15.2587 14.6495 14.4764 14.0657 13.9004C13.4819 13.3245 12.6951 13.0011 11.875 13H8.125C7.30502 13.0012 6.51837 13.3247 5.93471 13.9006C5.35104 14.4766 5.01714 15.2589 5.005 16.0788C3.87161 15.0667 3.07234 13.7343 2.71303 12.2579C2.35372 10.7815 2.45132 9.23084 2.9929 7.81116C3.53449 6.39148 4.49452 5.16979 5.74586 4.30785C6.99721 3.44592 8.48084 2.98439 10.0003 2.98439C11.5198 2.98439 13.0034 3.44592 14.2548 4.30785C15.5061 5.16979 16.4661 6.39148 17.0077 7.81116C17.5493 9.23084 17.6469 10.7815 17.2876 12.2579C16.9283 13.7343 16.129 15.0667 14.9956 16.0788Z" fill="#D0D2BF" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% elif field.name == "password" %}
                        <div class="self-stretch h-11 flex-col justify-start items-start gap-1.5 flex">
                            <div class="self-stretch h-11 flex-col justify-start items-start gap-1.5 flex">
                                <div class="self-stretch bg-white rounded-lg shadow border border-[#eceee8] flex justify-between items-center gap-2">
                                    {{ field|attr:"type:password"|attr:"placeholder:••••••••"|attr:"id:password-field"|attr:"class:grow shrink h-11 flex px-3.5 py-2.5 bg-white border-none rounded-xl text-[#343d36] text-smfont-normal font-inter focus:outline-none focus:ring-0" }}
                                    <!-- Icon -->
                                    <div class="flex items-center pr-3 cursor-pointer"
                                         tabindex="-1"
                                         onclick="togglePasswordVisibility('password-field', 'hide-icon')">
                                        <img id="hide-icon"
                                             alt="hide icon"
                                             src="{% static 'assets/images/hide_icon.svg' %}"
                                             width="20"
                                             height="21">
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% elif field.name == "remember" %}
                        <div class="h-5 w-full justify-between items-center inline-flex gap-4">
                            <div class="self-stretch justify-start items-center gap-2.5 flex">
                                <div class="justify-center items-center flex">
                                    {{ field|attr:"class:w-4 h-4 relative rounded border border-[#343d36]" }}
                                </div>
                                <div class="text-[#4b554d] text-sm font-normal font-['Inter'] leading-tight">
                                    Remember for
                                    30 days
                                </div>
                            </div>
                            <div class="ButtonsButton self-stretch justify-center items-center gap-1.5 flex">
                                <a href="{% url 'account_reset_password' %}"
                                   class="text-[#924f34] text-sm font-semibold font-['Inter'] leading-tight">
                                    Forgot
                                    password?
                                </a>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
                <button type="submit"
                        class="self-stretch px-4 py-2.5 bg-[#924f34] rounded-lg shadow-inner inline-flex items-center justify-center gap-1.5 text-white text-base font-semibold font-inter leading-normal">
                    Sign In
                </button>
            </form>
            <div class="self-stretch h-[46px] flex-col justify-center items-center gap-1 flex">
                <div class="text-[#343d36] text-sm font-normal font-inter leading-[21px]">Not a partner yet?</div>
                {% comment %} TODO add contact us page {% endcomment %}
                <div class="text-[#924f34] text-sm font-semibold font-inter leading-[21px]">
                    <a href="mailto:<EMAIL>">Contact us</a>
                </div>
            </div>
        </div>
        {% if form.errors %}
            <div class="self-stretch fixed bottom-0 left-0 right-0 flex justify-center z-50 pointer-events-none pb-4">
                <div data-toast-container
                     class="w-[400px] flex flex-col items-center space-y-2 pointer-events-auto">
                    {% for field, errors in form.errors.items %}
                        {% for error in errors %}
                            <bridge-toast data-variant="error" data-message="{{ error }}" data-timeout="5000">
                            </bridge-toast>
                        {% endfor %}
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </main>
{% endblock content %}
