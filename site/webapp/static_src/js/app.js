// This is our esbuild app.js entry point to bundle everything into a single file.
// Mostly for adding the htmx and hyperscript libraries. Also for adding event listeners?
import _hyperscript from "hyperscript.org";
import { initFlowbite } from "flowbite";
import htmx from "htmx.org";
import * as LottiePlayer from "@lottiefiles/lottie-player";
import LogRocket from "logrocket";
import * as toast from "./web_components/toast.js";
import * as tooltip from "./web_components/tooltip.js";
import * as runRetrievalButton from "./web_components/run_retrieval_button.js";
import * as pdflabler from "./web_components/pdf-labeler.js";
import * as pdfjsLib from "pdfjs-dist";
pdfjsLib.GlobalWorkerOptions.workerSrc = "/static/js/pdf.worker.min.js";

const debug_content =
  document.getElementsByName("debug")[0]?.content === "True";
const user_is_super_admin =
  document.getElementsByName("user_is_super_admin")[0]?.content == "True";
const user_email = document.getElementsByName("user_email")[0]?.content;
const user_is_hijacked =
  document.getElementsByName("user_is_hijacked")[0]?.content === "True";

window.is_bridge_debugging = debug_content;

if (!window.is_bridge_debugging) {
  console.log = function () {};
}
console.log("Debug content:", debug_content);
console.log("User ID:", user_is_super_admin);
console.log("User email:", user_email);
console.log("User is hijacked:", user_is_hijacked);

if (
  !window.is_bridge_debugging &&
  !user_is_hijacked &&
  !user_is_super_admin &&
  user_email !== undefined
) {
  console.log("Log rocket init");
  LogRocket.init("nq1bcd/bridge-invest");
  LogRocket.identify(user_email, {
    is_hijacked: user_is_hijacked,
    is_super_admin: user_is_super_admin,
  });
}

window.htmx = htmx;
window._hyperscript = _hyperscript;
window._hyperscript.browserInit();
initFlowbite();
if (window.is_bridge_debugging) {
  htmx.logAll();
}

// Timezone settings
const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone; // e.g. "America/New_York"
document.cookie = "django_timezone=" + timezone;

function formatCurrencyInput(element) {
  let currency = element.dataset.currency || "USD";
  const rawValue = parseFloat(element.value.replace(/[^0-9.-]+/g, ""));

  try {
    if (!isNaN(rawValue)) {
      const options = {
        style: "currency",
        currency: currency,
      };

      options.minimumFractionDigits = 2;

      element.value = new Intl.NumberFormat("en-US", options).format(rawValue);
    }
  } catch (error) {
    console.error("Formatting error:", error);
  }
}

function formatCurrencyInputElements(selector) {
  document.querySelectorAll(selector).forEach((element) => {
    formatCurrencyInput(element);
    element.addEventListener("change", function (event) {
      formatCurrencyInput(event.target);
    });
  });
}

function formatTableDates(selector) {
  // Select all elements matching the selector
  document.querySelectorAll(selector).forEach((cell) => {
    const inputDate = cell.textContent.trim(); // Get the current text in the cell
    const date = new Date(inputDate);

    // Validate date parsing
    if (!isNaN(date.getTime())) {
      // Format the date as M/D/YY
      const month = date.getMonth() + 1; // Months are 0-based
      const day = date.getDate();
      const year = date.getFullYear() % 100; // Get the last two digits of the year

      // Update the cell content
      cell.textContent = `${month}/${day}/${year}`;
    } else {
      if (window.is_bridge_debugging) {
        console.error(`Invalid date format: "${inputDate}" in`, cell);
      }
    }
  });
}

function toggleTableRowInDashboard(element) {
  const details = element.nextElementSibling; // Get the next sibling (details div)
  const icon = element.querySelector("#dropdown-icon"); // Get the icon inside the span

  if (details.style.maxHeight === "0px" || !details.style.maxHeight) {
    // Expand
    details.style.maxHeight = details.scrollHeight + "px"; // Set maxHeight to content height
    icon.classList.remove("rotate-90"); // Remove rotate-90
    icon.classList.add("rotate-0"); // Add rotate-0 for downward arrow
    element.classList.add("bg-white", "border-b", "border-light-olive"); // Add styles
  } else {
    // Collapse
    details.style.maxHeight = "0px"; // Collapse the section
    icon.classList.remove("rotate-0"); // Remove rotate-0
    icon.classList.add("rotate-90"); // Add rotate-90 for left arrow
    element.classList.remove("bg-white", "border-b", "border-light-olive"); // Remove styles
  }
}

function truncateText() {
  // Should this be just Django filters, much more consistant for SSR.
  const elements = document.querySelectorAll(".truncate-text");
  elements.forEach((el) => {
    const maxLength = parseInt(el.dataset.maxLength, 10);
    const text = el.textContent.trim();
    if (text.length > maxLength) {
      el.textContent = text.substring(0, maxLength) + "...";
    }
  });
}

function formatPhoneNumber(input) {
  // Remove all non-numeric characters
  let value = input.value.replace(/\D/g, "");

  // Apply U.S. phone number formatting
  if (value.length > 3 && value.length <= 6) {
    input.value = `(${value.slice(0, 3)})-${value.slice(3)}`;
  } else if (value.length > 6) {
    input.value = `(${value.slice(0, 3)})-${value.slice(3, 6)}-${value.slice(6, 10)}`;
  } else {
    input.value = value; // Unformatted if less than 3 digits
  }
}
function togglePasswordVisibility(inputId, iconId) {
  const passwordField = document.getElementById(inputId);
  const icon = document.getElementById(iconId);
  const isPasswordVisible = passwordField.type === "text";

  if (isPasswordVisible) {
    passwordField.type = "password";
    icon.src = hideIconPath; // Hide icon
  } else {
    passwordField.type = "text";
    icon.src = showIconPath; // Show icon
  }
}

window.toggleTableRowInDashboard = toggleTableRowInDashboard;
window.togglePasswordVisibility = togglePasswordVisibility;
window.formatPhoneNumber = formatPhoneNumber;
window.window.formatCurrencyInputElements = formatCurrencyInputElements;
document.addEventListener("DOMContentLoaded", () => {
  formatTableDates(".date-cell");
  formatCurrencyInputElements(".currency_input");
  truncateText();
});

document.addEventListener("htmx:afterSwap", function (event) {
  // Check if the event target matches your specific element
  formatTableDates(".date-cell");
  formatCurrencyInputElements(".currency_input");
  truncateText();
  initFlowbite();
});

if (window.is_bridge_debugging) {
  console.log("app.js loaded!");
}

document.addEventListener("changeLineItems", function (event) {
  event.detail.line_item_pks.forEach((line_item_pk) => {
    document.dispatchEvent(
      new CustomEvent("changeLineItem", {
        bubbles: true,
        detail: {
          line_item_pk: line_item_pk,
        },
      }),
    );
  });
});

document.addEventListener("openedProcessedDocPdfs", function (event) {
  event.detail.processed_doc_pks.forEach((processed_doc_pk) => {
    document.dispatchEvent(
      new CustomEvent("openedProcessedDocPdf", {
        bubbles: true,
        detail: {
          pk: processed_doc_pk,
          viewed: event.detail.viewed,
        },
      }),
    );
  });
});
function createToast(message) {
  const newElement = document.createElement("bridge-toast");
  newElement.dataset.variant = message.tags || "info";
  newElement.dataset.message = message.message || "";
  newElement.dataset.timeout = 4000;
  document.querySelector("[data-toast-container]").appendChild(newElement);
}

document.addEventListener("django_messages", (event) => {
  event.detail.value.forEach(createToast);
});
