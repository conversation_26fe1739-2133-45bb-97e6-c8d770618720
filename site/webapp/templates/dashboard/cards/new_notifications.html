{% comment %}
HACK: We add a delay here to give the server a chance to mark the document as read, since we trigger this event client-side.
TODO: move to an event triggered from the server (like before) when opening a PDF in a new tab
{% endcomment %}
<div hx-get="{% url 'dashboard_new_notifications_card' %}"
     hx-trigger="openedProcessedDocPdf from:document delay:200ms"
     hx-swap='outerHTML'
     class="flex flex-col lg:flex-row items-start lg:items-center justify-between bg-gray-100 p-4 rounded-lg shadow gap-4 lg:gap-0">
    <a href="{% url 'vault' %}?has_been_viewed=False"
       class="text-lg lg:text-2xl font-semibold text-gray-800">
        New Notifications: <span class="font-bold">{{ new_notifications }}</span>
    </a>
    <div class="text-sm text-gray-600 w-full lg:w-auto lg:mt-2">
        <p class="mb-1 lg:mb-0">
            Total line items: <span class="font-semibold text-[#689757]">{{ number_of_investments }}</span>
        </p>
        <p>
            Last notice received: <span class="font-semibold text-[#689757]">
            {% if time_of_last_notification is None %}
                -
            {% else %}
                {{ time_of_last_notification }}
            {% endif %}
        </span>
    </p>
</div>
</div>
