from argparse import ArgumentParser

import structlog
from django.core.management.base import BaseCommand
from django.db import transaction
from django_extensions.management.utils import signalcommand

from webapp.models.emails import BridgeEmail, CustomerEmailCredential, EmailRetrieval, UserForwardingRule
from webapp.models.line_item import LineItem
from webapp.models.portal import MergedPortalCredential, Portal

# ./site/manage.py merge_email_mpc --email "<EMAIL>"
logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "merge exisiting email based MergedPortalCredentials with identical emails"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("-e", "--email", type=str)

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002
        email = kwargs.get("email")
        if email is None:
            logger.info("email is None, must be set, exiting")
            return

        cec = CustomerEmailCredential.objects.get(email=email)
        user = cec.created_by
        organization = cec.organization
        logger.info("found CustomerEmailCredential", cec=cec)
        bridge_email, created_bridge_email = BridgeEmail.objects.get_or_create(
            user=user,
            email=f"{organization.name.lower().replace(' ', '-')}@app.bridgeinvest.io",
        )
        user_forwarding_rule, created_user_forwarding_rule = UserForwardingRule.objects.get_or_create(
            user=user,
            organization=organization,
            receiving_email=cec,
            bridge_email=bridge_email,
        )
        logger.info(
            "found or created user_forwarding_rule",
            user_forwarding_rule=user_forwarding_rule,
            created_user_forwarding_rule=created_user_forwarding_rule,
            created_bridge_email=created_bridge_email,
        )
        portal, created_email_portal = Portal.objects.update_or_create(
            user=user,
            organization=organization,
            portal_type=Portal.PortalType.EMAIL_BASED,
            defaults={"name": cec.email},
        )
        main_mpc, created_email_mpc = MergedPortalCredential.objects.update_or_create(
            user=user, user_forwarding_rule=user_forwarding_rule, defaults={"portal": portal}
        )
        logger.info(
            "found or created MPC",
            main_mpc=main_mpc.id,
            created_email_mpc=created_email_mpc,
            created_email_portal=created_email_portal,
        )

        # handle line items without EmailRetrieval
        line_items = LineItem.objects.filter(
            organization=organization,
            merged_portal_credential__portal__portal_type=Portal.PortalType.EMAIL_BASED,
            deleted_at__isnull=True,
            email_retrieval=None,
        )
        logger.info("found email based line_items", count=len(line_items))

        email_retrieval_create_count = 0
        email_retrieval_update_count = 0
        for line_item in line_items:
            with transaction.atomic():
                line_item.merged_portal_credential.soft_delete()
                line_item.merged_portal_credential = main_mpc
                line_item.save()
                _, created_email_retrieval = EmailRetrieval.objects.update_or_create(
                    user=user,
                    line_item=line_item,
                    user_forwarding_rule=user_forwarding_rule,
                )
                if created_email_retrieval:
                    email_retrieval_create_count += 1
                else:
                    email_retrieval_update_count += 1

        res = f"EmailRetrievals created: {email_retrieval_create_count}; EmailRetrievals updated: {email_retrieval_update_count}"  # noqa: E501
        logger.info(res)

        # handle line items with EmailRetrieval
        email_mpcs = MergedPortalCredential.objects.filter(
            organization=cec.organization,
            portal__portal_type=Portal.PortalType.EMAIL_BASED,
            deleted_at__isnull=True,
            user_forwarding_rule__isnull=True,
        )
        logger.info(
            "found email based mpc with no forwarding rule",
            count=len(email_mpcs),
        )
        mpc_with_line_items_delete_count = 0
        mpc_without_line_items_delete_count = 0
        for email_mpc in email_mpcs:
            email_mpc_line_items = LineItem.objects.filter(merged_portal_credential=email_mpc)
            has_line_items = len(email_mpc_line_items) > 0
            for line_item in email_mpc_line_items:
                with transaction.atomic():
                    line_item.merged_portal_credential = main_mpc
                    line_item.save()
            email_mpc.soft_delete()
            if has_line_items:
                mpc_with_line_items_delete_count += 1
            else:
                mpc_without_line_items_delete_count += 1
        res = f"MergedPortalCredential with line items deleted: {mpc_with_line_items_delete_count};MergedPortalCredential without line items deleted: {mpc_without_line_items_delete_count}"  # noqa: E501
        logger.info(res)
