# Generated by Django 5.1.4 on 2025-01-16 15:59

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0008_bridgeuser_invitation_token_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='capitalcalldocumentfact',
            name='processed_document',
        ),
        migrations.RemoveField(
            model_name='distributionnoticedocumentfact',
            name='processed_document',
        ),
        migrations.RemoveField(
            model_name='investmentupdatedocumentfact',
            name='processed_document',
        ),
        migrations.RemoveField(
            model_name='retrieval',
            name='line_item',
        ),
        migrations.RemoveField(
            model_name='retrieval',
            name='retrieval_location',
        ),
        migrations.AddField(
            model_name='processeddocument',
            name='capital_call_document',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='processed_document', to='webapp.capitalcalldocumentfact'),
        ),
        migrations.AddField(
            model_name='processeddocument',
            name='distribution_notice_document',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='processed_document', to='webapp.distributionnoticedocumentfact'),
        ),
        migrations.AddField(
            model_name='processeddocument',
            name='investment_update_document',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='processed_document', to='webapp.investmentupdatedocumentfact'),
        ),
        migrations.AddField(
            model_name='processeddocument',
            name='is_ground_truth',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='processeddocument',
            name='is_visible',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='processeddocument',
            name='is_wrong',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='processeddocument',
            name='labeled_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tagged_documents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='processeddocument',
            name='notes',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='processeddocument',
            name='process_document_source',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='rawdocument',
            name='doc_hash',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='rawdocument',
            name='doc_hash_source',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='rawdocument',
            name='doc_hash_version',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='rawdocument',
            name='has_ground_truth',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='rawdocument',
            name='metadata',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='retrieval',
            name='exists_in_s3',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='retrieval',
            name='manager',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='retrieval',
            name='s3_bucket',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='retrieval',
            name='s3_format_version',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='retrieval',
            name='s3_key',
            field=models.CharField(blank=True, max_length=1024),
        ),
        migrations.AlterField(
            model_name='bridgeemail',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='bridgeemail',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='capitalcalldocumentfact',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='capitalcalldocumentfact',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='client',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='client',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='customeremailcredential',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='customeremailcredential',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='distributionnoticedocumentfact',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='distributionnoticedocumentfact',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='forwardingrule',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='forwardingrule',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='investingentity',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='investingentity',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='investment',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='investment',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='investmentupdatedocumentfact',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='investmentupdatedocumentfact',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='lineitem',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='lineitem',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='multifactorauthentication',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='multifactorauthentication',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='portal',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='portal',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='portalcredential',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='portalcredential',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='processeddocument',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='processeddocument',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='rawbulkupload',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='rawbulkupload',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='rawdocument',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='rawdocument',
            name='s3_key',
            field=models.CharField(blank=True, max_length=1024),
        ),
        migrations.AlterField(
            model_name='rawdocument',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='retrieval',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='retrieval',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='MergedPortalCredential',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('secret_arn', models.CharField(blank=True, max_length=255)),
                ('secret_format_version', models.IntegerField(default=0)),
                ('multi_factor_authentication_type', models.CharField(choices=[('sm', 'SMS'), ('em', 'Email'), ('ap', 'Authenticator'), ('ll', 'Live Login'), ('no', 'No MFA'), ('un', 'Unknown')], default='un', max_length=2)),
                ('phone_number', models.CharField(blank=True, max_length=255)),
                ('is_valid_mfa', models.BooleanField(null=True)),
                ('is_valid_credentials', models.BooleanField(null=True)),
                ('username', models.CharField(max_length=255)),
                ('otp_method', models.CharField(choices=[('cl', 'Cold Login'), ('ll', 'Live Login'), ('sm', 'SMS'), ('em', 'Email'), ('ap', 'Authenticator'), ('ns', 'Not Supported')], default='cl', max_length=2)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s', to='webapp.organization')),
                ('portal', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='merged_portal_credentials', to='webapp.portal')),
                ('receiving_email', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='merged_portal_credentials', to='webapp.customeremailcredential')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
                'default_manager_name': 'objects',
            },
        ),
        migrations.AddField(
            model_name='lineitem',
            name='merged_portal_credential',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='line_items', to='webapp.mergedportalcredential'),
        ),
        migrations.AddField(
            model_name='multifactorauthentication',
            name='merged_portal_credential',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='multi_factor_authentications', to='webapp.mergedportalcredential'),
        ),
        migrations.AddField(
            model_name='portalcredential',
            name='merged_portal_credential',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='portal_credentials', to='webapp.mergedportalcredential'),
        ),
        migrations.AddField(
            model_name='retrieval',
            name='merged_portal_credential',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='retrievals', to='webapp.mergedportalcredential'),
        ),
    ]
