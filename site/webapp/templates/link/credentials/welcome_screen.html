{% load customtags static %}
<div id="modal-container-{{ merged_portal_credential.pk }}">
    <div class="flex items-center justify-between p-3 rounded-t dark:border-gray-600">
        <div class="p-2 bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
            <div class="relative flex-col justify-start items-start flex">
                <div class="relative">
                    <img src="{% static 'assets/images/fin_tag.svg' %}"
                         width="38"
                         height="20"
                         alt="closeButton">
                </div>
            </div>
        </div>
        <button _="on click trigger closeModal"
                type="button"
                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <img src="{% static 'assets/images/close.svg' %}"
                 width="16"
                 height="19"
                 alt="closeButton">
            <span class="sr-only">Close modal</span>
        </button>
    </div>
    {% if merged_portal_credential.portal_credential.is_complete %}
        <div class="text-[#343d36] px-3 pb-3 text-lg font-semibold font-inter text-[18px] leading-7">
            Would you like to edit or delete these credentials? Please choose an option carefully, as deletion cannot be undone.
        </div>
        <div class="px-3">
            <button hx-get="{% url 'credentials_manage' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
                    hx-swap='outerHTML'
                    hx-target="#modal-container-{{ merged_portal_credential.pk }}"
                    class="text-[#924f34] text-base font-semibold font-inter leading-normal Buttons self-stretch w-full h-11 px-4 py-2.5 bg-white rounded-lg  shadow-inner border border-[#924f34] justify-center items-center gap-1.5 inline-flex"
                    hx-target='closest div'>Edit Credentials</button>
            <br>
            <br>
            <button hx-delete="{% url 'credentials_manage' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
                    hx-swap='outerHTML'
                    hx-target="#modal-container-{{ merged_portal_credential.pk }}"
                    class="text-[#924f34] text-base font-semibold font-inter leading-normal Buttons self-stretch w-full h-11 px-4 py-2.5 bg-white rounded-lg  shadow-inner border border-[#924f34] justify-center items-center gap-1.5 inline-flex"
                    hx-target='closest div'>Delete Credentials</button>
        </div>
    {% elif merged_portal_credential.portal.portal_type == 'eb' %}
        <div class="text-[#343d36] px-3 pb-3 text-lg font-semibold font-inter text-[18px] leading-7">
            This line item is not connected to a portal. Please reach out to support if you would like to connect this line item to a portal.
        </div>
    {% else %}
        <div class="text-[#343d36] px-3 pb-3 text-lg font-semibold font-inter text-[18px] leading-7">
            Bridge uses Amazon Key Management Service (AWS KMS) to encrypt and securely store your credentials.
        </div>
        <div class="h-[104px] px-3 w-full flex-col justify-start items-start gap-5 inline-flex">
            <div class="self-stretch h-[104px] flex-col justify-start items-start gap-6 flex">
                <div class="self-stretch h-[104px] py-6 rounded-lg border border-[#eceee8] flex-col justify-start items-start gap-4 flex">
                    <div class="self-stretch px-3.5 bg-white rounded-lg justify-start items-center gap-2 inline-flex">
                        <div class="w-5 h-5 relative">
                            <img alt="secure_image"
                                 width="20"
                                 height="20"
                                 src="{% static 'assets/images/secure.svg' %}">
                        </div>
                        <div class="grow shrink basis-0 h-5 justify-start items-center gap-2 flex">
                            <div class="text-[#343d36] text-sm font-semibold font-inter text-[14px] leading-tight">We never see your password</div>
                        </div>
                    </div>
                    <div class="self-stretch px-3.5 bg-white rounded-lg justify-start items-center gap-2 inline-flex">
                        <div class="w-5 h-5 relative">
                            <img alt="aws_image"
                                 width="20"
                                 height="20"
                                 src="{% static 'assets/images/aws.svg' %}">
                        </div>
                        <div class="grow shrink basis-0 h-5 justify-start items-center gap-2 flex">
                            <div class="text-[#343d36] text-sm font-semibold font-inter text-[14px] leading-tight">
                                AWS KMS is trusted by the Fortune 100
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="opacity-75 px-3 mt-4">
            <span class="text-[#343d36] text-sm font-normal font-inter text-[14px] leading-tight">By continuing, you agree to Bridge's </span><a class="text-[#343d36] text-sm font-normal font-inter underline leading-tight"
   href="https://www.bridgeinvest.io/privacy-policy">Privacy Policy</a><span class="text-[#343d36] text-sm font-normal font-inter leading-tight"> and to receiving updates on bridgeinvest.io</span>
        </div>
        <div class="px-3">
            <button hx-get="{% url 'credentials_manage' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
                    hx-swap="outerHTML"
                    hx-target="#modal-container-{{ merged_portal_credential.pk }}"
                    class="h-11 px-4 mt-4 w-full py-2.5 bg-[#924f34] rounded-lg border border-[#924f34] justify-center items-center gap-1.5 inline-flex">
                <div class="px-0.5 justify-center items-center flex">
                    <div class="text-white text-base font-semibold font-inter text-[16px] leading-normal">Continue</div>
                </div>
            </button>
        </div>
    {% endif %}
</div>
