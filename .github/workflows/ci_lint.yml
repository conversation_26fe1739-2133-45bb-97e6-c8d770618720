name: 🚀✨ Linting and Testing ✨🚀

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches:
      - main

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - name: 💥🥊 Checkout the Code 💥🥊
      uses: actions/checkout@v4


    - name: 🐍🔧 Node Install 🔧🐍
      uses: actions/setup-node@v4
      with:
        node-version: '22.x'

    - name: 🛠️📦 Install prettier 📦🛠️
      working-directory: ./infrastructure
      run: |
        npm install prettier

    - name: ☀️️🕶 Install uv 🕶☀
      uses: astral-sh/setup-uv@v5
      with:
        enable-cache: true
        cache-dependency-glob: "uv.lock"
        pyproject-file: "pyproject.toml"

    - name: 🧹✨ Code Polishing Time ✨🧹
      run: |
        ./lint.py

    - name: 🛠️📦 Install the Python Dependency Toolbox 📦🛠️
      working-directory: ./site
      run: |
        uv sync --dev --frozen
  
    # - name: 🧪✨ Run Tests ✨🧪
    #   run: |
    #     PYTHONPATH=site ENVIRONMENT=test uv run pytest
  