{% load django_htmx static tailwind_tags customtags %}
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="description"
              content="Bridge | Private markets are no longer out of reach." />
        <meta name="keywords" content="fintech private equity PE the best" />
        <meta name="debug" content="{% settings_value 'DEBUG' %}" />
        <meta name="user_email" content="{{ request.user.email }}" />
        <meta name="user_is_hijacked" content="{{ request.user.is_hijacked }}" />
        <meta name="user_is_super_admin" content="{{ request.user.is_superuser }}" />
        <title>
            {% block title %}
                Bridge
            {% endblock title %}
        </title>
        <link rel="shortcut icon"
              type="image/png"
              href="{% static 'assets/images/favicon.ico' %}" />
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Nanum+Myeongjo&display=swap"
              rel="stylesheet">
        <script>
            const hideIconPath = "{% static 'assets/images/hide_icon.svg' %}";
            const showIconPath = "{% static 'assets/images/show_icon.svg' %}";
        </script>
        {% include "web_components/toast.html" %}
        {% include "web_components/tooltip.html" %}
        <script src="{% static 'js/app.js' %}" defer></script>
        {% tailwind_css %}
    </head>
    <body class="min-h-screen grid"
          hx-boost="true"
          hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'>
        {% url 'account_logout' as logout_url %}
        {% if user.is_authenticated and request.path != logout_url and hide_sidebar is None %}
            {% include "components/side_nav.html" %}
        {% endif %}
        {% block webapp_content %}
        {% endblock webapp_content %}
        {% comment %} This is a hack for when we have to trigger an event on page load {% endcomment %}
        {% if hx_triggers %}
            {% for hx_trigger in hx_triggers %}
                <div class="hidden"
                     _="init immediately send {{ hx_trigger }} to body remove me"></div>
            {% endfor %}
        {% endif %}
        {% if remove_url_params %}
            <script>
                window.history.pushState('no_params_page', '', window.location.pathname);
            </script>
        {% endif %}
        {% include "components/toast_messages_container.html" %}
    </body>
</html>
