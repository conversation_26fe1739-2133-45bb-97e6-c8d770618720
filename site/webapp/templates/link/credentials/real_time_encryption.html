<div class="px-2 w-full">
    <div class="flex items-center mb-4">
        <label class="text-gray-700 font-medium pr-3 w-1/5" for="encryption">Encrypted</label>
        <input type="text"
               id="encrypted_password"
               name="encrypted_password"
               class="w-4/5 border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
               {% if encrypted_password %}value="{{ encrypted_password }}"{% endif %}
               readonly />
    </div>
    {% if error %}<div class="text-red-500 text-sm">{{ error }}</div>{% endif %}
    <div class="w-full flex items-center p-1 border-gray-200 rounded-b dark:border-gray-600">
        <button type="submit"
                name="submit"
                value="save"
                class="Buttons self-stretch h-11 w-full py-2 rounded-lg shadow-inner justify-center items-center gap-1.5 inline-flex {% if disable_button %}bg-gray-400 cursor-not-allowed opacity-50{% else %}bg-[#924f34] text-white{% endif %}"
                {% if disable_button %}disabled{% endif %}>
            <div class="BodySSemibod text-base font-semibold font-inter leading-normal">Continue</div>
        </button>
    </div>
</div>
