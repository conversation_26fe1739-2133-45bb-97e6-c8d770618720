from typing import Any

import colorama
import structlog
from structlog.processors import ExceptionRender<PERSON>
from structlog.tracebacks import ExceptionDictTransformer


def configure_logging(formatter: str, log_level: str, file_name: str | None) -> Any:  # noqa: ANN401
    handlers = ["console"]
    if file_name:
        handlers.append("file")

    handlers_obj = {}
    for handle in handlers:
        if handle == "console":
            handlers_obj[handle] = {
                "level": log_level,
                "class": "logging.StreamHandler",
                "formatter": formatter,
            }
        elif handle == "file":
            handlers_obj[handle] = {
                "level": log_level,
                "class": "logging.handlers.WatchedFileHandler",
                "filename": file_name,
                "formatter": "plain_console",
            }

    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json_formatter": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.processors.JSONRenderer(),
            },
            "color_console": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.dev.ConsoleRenderer(
                    colors=True,
                    # Toggle this if you want the colorful tracebacks or not.
                    exception_formatter=structlog.dev.plain_traceback if True else structlog.dev.rich_traceback,
                    columns=[
                        structlog.dev.Column(
                            "level",
                            structlog.dev.KeyValueColumnFormatter(
                                key_style=None,
                                value_style=colorama.Style.BRIGHT + colorama.Fore.RED,
                                reset_style=colorama.Style.RESET_ALL,
                                value_repr=str,
                            ),
                        ),
                        structlog.dev.Column(
                            "timestamp",
                            structlog.dev.KeyValueColumnFormatter(
                                key_style=None,
                                value_style=colorama.Fore.YELLOW,
                                reset_style=colorama.Style.RESET_ALL,
                                value_repr=str,
                            ),
                        ),
                        structlog.dev.Column(
                            "event",
                            structlog.dev.KeyValueColumnFormatter(
                                key_style=None,
                                value_style=colorama.Style.BRIGHT + colorama.Fore.MAGENTA,
                                reset_style=colorama.Style.RESET_ALL,
                                value_repr=str,
                            ),
                        ),
                        structlog.dev.Column(
                            "",
                            structlog.dev.KeyValueColumnFormatter(
                                key_style=colorama.Fore.CYAN,
                                value_style=colorama.Fore.GREEN,
                                reset_style=colorama.Style.RESET_ALL,
                                value_repr=str,
                            ),
                        ),
                    ],
                ),
            },
            "plain_console": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.dev.ConsoleRenderer(
                    colors=False,
                    exception_formatter=structlog.dev.plain_traceback,
                ),
            },
        },
        "handlers": handlers_obj,
        "loggers": {
            "django_structlog": {
                "handlers": handlers,
                "level": log_level,
            },
            "root": {
                "handlers": handlers,
                "level": log_level,
            },
        },
    }

    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.stdlib.filter_by_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.dev.set_exc_info,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.UnicodeDecoder(),
        structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
    ]
    if formatter == "json_formatter":
        processors.insert(0, ExceptionRenderer(ExceptionDictTransformer(show_locals=False)))

    structlog.configure(
        processors=processors,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    return logging_config
