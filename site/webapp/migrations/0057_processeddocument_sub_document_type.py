# Generated by Django 5.2.4 on 2025-07-19 18:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0056_retrieval_check_point_retrieval_starting_point'),
    ]

    operations = [
        migrations.AddField(
            model_name='processeddocument',
            name='sub_document_type',
            field=models.CharField(blank=True, choices=[('audited_fs', 'Audited Financial Statements'), ('unaudited_fs', 'Unaudited Financial Statements'), ('quarterly_letter', 'Quarterly Letter'), ('annual_letter', 'Annual Letter'), ('investment_memo', 'Investment Memo'), ('tax_estimates', 'Tax Estimates'), ('tax_final', 'Tax Final'), ('other', 'Other')], max_length=32),
        ),
    ]
