import json
from abc import ABC, abstractmethod

import boto3


class Embedder(ABC):
    @abstractmethod
    def name(self) -> str:
        pass

    @abstractmethod
    def embed(self, chunk: str) -> list[float]:
        pass


def aws_embed(
    chunk: str,
    dimensions: int = 1024,
    model_name: str = "amazon.titan-embed-text-v2",
    *,
    normalize: bool = True,
) -> list[float]:
    bedrock_runtime = boto3.client(service_name="bedrock-runtime", region_name="us-east-1")
    response = bedrock_runtime.invoke_model(
        body=json.dumps(
            {
                "inputText": chunk,
                "dimensions": dimensions,
                "normalize": normalize,
            }
        ),
        modelId=model_name,
        accept="application/json",
        contentType="application/json",
    )

    response_body = json.loads(response.get("body").read())
    embedding = response_body["embedding"]
    if not isinstance(embedding, list) or len(embedding) != dimensions or not isinstance(embedding[0], (float, int)):
        raise ValueError
    return embedding


class AwsV2TitanText1024Norm(Embedder):
    def name(self) -> str:
        return "embedding_titan_text_1024"

    def embed(self, chunk: str) -> list[float]:
        return aws_embed(chunk, dimensions=1024, model_name="amazon.titan-embed-text-v2:0", normalize=True)
