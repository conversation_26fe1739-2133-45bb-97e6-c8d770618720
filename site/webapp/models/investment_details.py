import structlog
from django.db import models
from django.utils.translation import gettext_lazy as _
from phonenumber_field.modelfields import PhoneNumber<PERSON>ield

logger = structlog.get_logger(__name__)

from webapp.models.core import AbstractBaseModel

ADDITIONAL_ENTITY_TYPE_CHOICES = [
    ("general_partner", "General partner or LLC member-manager"),
    ("limited_partner", "Limited partner or other LLC member"),
    ("domestic_partner", "Domestic partner"),
    ("foreign_partner", "Foreign partner"),
]


class Client(AbstractBaseModel):
    legal_name = models.CharField(max_length=255)
    note = models.TextField(blank=True, default="")

    class Meta(AbstractBaseModel.Meta):
        unique_together = ("organization", "legal_name")

    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["entities__line_items"]

    def __str__(self) -> str:
        return self.legal_name


class InvestingEntity(AbstractBaseModel):
    class EntityType(models.TextChoices):
        INDIVIDUAL = "in", _("Individual")
        TRUST = "tr", _("Trust")
        CORPORATION = "co", _("Corporation")
        PARTNERSHIP = "pa", _("Partnership")
        LLC = "ll", _("LLC")
        LLP = "lp", _("LLP")
        OTHER = "ot", _("Other")

    legal_name = models.CharField(max_length=255)
    client = models.ForeignKey("Client", on_delete=models.CASCADE, related_name="entities")
    note = models.TextField(blank=True, default="")
    entity_type = models.CharField(max_length=2, choices=EntityType.choices, default=EntityType.OTHER)

    additional_entity_types = models.JSONField(null=True, blank=True)

    ssn_or_tin = models.CharField(max_length=50, blank=True)
    address = models.TextField(blank=True)
    phone = PhoneNumberField(blank=True)
    email = models.EmailField(blank=True)
    dob = models.DateField(null=True, blank=True)

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_items"]

    class Meta(AbstractBaseModel.Meta):
        unique_together = ("organization", "legal_name")

    def __str__(self) -> str:
        return self.legal_name


class Investment(AbstractBaseModel):
    legal_name = models.CharField(max_length=255)
    managing_firm_name = models.CharField(max_length=255)
    note = models.TextField(blank=True, default="")

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_items"]

    class Meta(AbstractBaseModel.Meta):
        unique_together = ("organization", "legal_name", "managing_firm_name")

    def __str__(self) -> str:
        return self.legal_name
