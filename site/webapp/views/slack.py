import hashlib
import hmac
import json
import time

import structlog
from django.conf import settings
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from retrieval.tasks import rerun_retrieval_task

logger = structlog.get_logger(__name__)


@csrf_exempt
@require_http_methods(["POST"])
def handle_slack_event(request: HttpRequest) -> HttpResponse:
    if is_verified_slack_request(request):
        try:
            # Slack sends the payload as form-encoded data
            payload = json.loads(request.POST.get("payload"))

            payload_type = payload["type"]

            # TODO: implement other actions
            if payload_type == "block_actions":
                actions = payload["actions"]
                for action in actions:
                    action_id = action["action_id"]
                    action_value = action.get("value", "")
                    if action_id == "rerun_scrape" and action_value:
                        rerun_retrieval_task.apply_async(kwargs={"failed_retrieval_id": action_value})

            return JsonResponse({"status": 200})
        except Exception as e:  # noqa: BLE001
            logger.info("handle_slack_event exception", error=str(e))
            return JsonResponse({"error": str(e)}, status=400)
    return HttpResponse("Unauthorized", status=401)


def is_verified_slack_request(request: HttpRequest) -> bool:
    timestamp = request.headers.get("X-Slack-Request-Timestamp")
    slack_signature = request.headers.get("X-Slack-Signature")

    try:
        timestamp_int = int(timestamp)
    except ValueError:
        return False

    if abs(time.time() - timestamp_int) > 60 * 5:
        # The request timestamp is more than five minutes from local time.
        # It could be a replay attack, so let's ignore it.
        return False

    # Create the basestring
    sig_basestring = f"v0:{timestamp}:{request.body.decode('utf-8')}"

    # Generate your signature
    my_signature = (
        "v0="
        + hmac.new(
            settings.SLACK_SIGNING_SECRET.encode("utf-8"), sig_basestring.encode("utf-8"), hashlib.sha256
        ).hexdigest()
    )

    # Compare signatures using constant-time comparison
    return hmac.compare_digest(my_signature, slack_signature)
