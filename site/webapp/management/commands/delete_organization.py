import argparse
import typing
from argparse import ArgumentParser

import structlog
from django.core.management.base import BaseCommand
from django.db import transaction
from django_extensions.management.utils import signalcommand

from webapp.models import (
    BridgeEmail,
    BridgeUser,
    CapitalCallDocumentFact,
    Client,
    CustomerEmailCredential,
    DistributionNoticeDocumentFact,
    InvestingEntity,
    Investment,
    InvestmentUpdateDocumentFact,
    LineItem,
    MergedPortalCredential,
    MultiFactorAuthentication,
    Organization,
    Portal,
    PortalCredential,
    ProcessedDocument,
    RawDocument,
    Retrieval,
    SMSMessage,
    ZipJob,
)

if typing.TYPE_CHECKING:
    from webapp.models.core import SimpleBaseModel

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Recovers and manages forwarding rules"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("-o", "--organization", type=str)
        parser.add_argument("--users", action=argparse.BooleanOptionalAction)

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002, C901
        organization = kwargs.get("organization")
        users = kwargs.get("users")

        if organization is None:
            logger.error("Organization name is required to proceed")
            return

        org_obj = Organization.objects.get(name=organization)
        if org_obj is None:
            logger.error("Could not find organization", organization=organization)
            return

        organization_confirm = input("Confirm the organization you are trying to delete... ")
        if organization_confirm != organization:
            logger.error(
                "Organization confirmed is not the same organization name",
                organization=organization,
                organization_confirm=organization_confirm,
            )

        models_to_clean: list[type[SimpleBaseModel]] = [
            BridgeEmail,
            CapitalCallDocumentFact,
            Client,
            CustomerEmailCredential,
            DistributionNoticeDocumentFact,
            InvestingEntity,
            Investment,
            InvestmentUpdateDocumentFact,
            LineItem,
            MergedPortalCredential,
            MultiFactorAuthentication,
            Portal,
            PortalCredential,
            ProcessedDocument,
            RawDocument,
            Retrieval,
            SMSMessage,
            ZipJob,
        ]
        if users:
            models_to_clean.append(BridgeUser)
        all_deletes: dict[str, int] = {}
        delete_counts = 0
        with transaction.atomic():
            for model in models_to_clean:
                logger.info("Finding", model=model.__name__)
                to_delete = model.objects.filter(organization=org_obj)
                ct, delete_dict = to_delete.delete()
                for key, value in delete_dict.items():
                    if key not in all_deletes:
                        all_deletes[key] = 0
                    all_deletes[key] += value
                delete_counts += ct
            for key, ct in all_deletes.items():
                logger.info("Deleting", model=key, ct=ct)
            if users:
                ct, delete_dict = org_obj.delete()
                for key, value in delete_dict.items():
                    if key not in all_deletes:
                        all_deletes[key] = 0
                    all_deletes[key] += value
                delete_counts += ct
            logger.info("Total deletes", delete_counts=delete_counts)
            if input("Input DELETE to delete all the above models... ") != "DELETE":
                raise ValueError
        logger.info("delete successful")
