import json
import uuid
from pathlib import Path

import boto3
import structlog
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.core.files.uploadedfile import UploadedFile
from django.core.paginator import Paginator
from django.db import models, transaction
from django.db.models import <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Count, Exists, F, Max, Min, OuterRef, Q, Value, When, Window
from django.db.models.functions import Rank
from django.http import HttpRequest, HttpResponse, JsonResponse, QueryDict
from django.http.response import ResponseHeaders
from django.template.response import TemplateResponse
from django.utils import timezone
from django.views.decorators.http import require_POST
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, save_document

from webapp.models.documents import (
    DOCUMENT_TYPE_SUB_TYPE_MAPPING,
    REQUIRED_SUB_TYPE_DOCUMENTS,
    DocumentType,
    ProcessedDocument,
    RawDocument,
    SubDocumentType,
)
from webapp.models.investment_details import Client, InvestingEntity, Investment
from webapp.models.line_item import LineItem
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser
from webapp.utils.slack import send_message_to_slack
from webapp.views.utils import add_hx_trigger_header

logger = structlog.get_logger(__name__)

BUCKET = settings.AWS_STORAGE_BUCKET_NAME


def parse_category_filters(request_prefix: QueryDict) -> dict[str, list[str]]:
    category_filter_data = request_prefix.get("category_data")
    category_filters = {}
    if category_filter_data:
        try:
            category_filters = json.loads(category_filter_data)
        except (json.JSONDecodeError, AttributeError):
            category_filters = {}
    else:
        category_filters = {}
    return category_filters


def get_line_item_from_investment_details(
    user: BridgeUser, client_id: str, investing_entity_id: str, investment_id: str
) -> LineItem | None:
    return (
        LineItem.objects.for_user(user)
        .filter(
            investment_id=investment_id, investing_entity_id=investing_entity_id, investing_entity__client_id=client_id
        )
        .first()
    )


def get_processed_documents(user: BridgeUser) -> models.QuerySet[ProcessedDocument]:
    # Fetch documents with optimized query
    return (
        ProcessedDocument.objects.for_user(user)
        .filter(is_visible=True)
        .annotate(
            document_type_label=Case(
                When(document_type="cc", then=Value("capital call")),
                When(document_type="dn", then=Value("distribution notice")),
                When(document_type="as", then=Value("account statement")),
                When(document_type="iu", then=Value("investment update")),
                When(document_type="fs", then=Value("financial statements")),
                When(document_type="tx", then=Value("tax")),
                When(document_type="lg", then=Value("legal")),
                When(document_type="ot", then=Value("other")),
                When(document_type="un", then=Value("unknown")),
                default=Value("unknown"),
                output_field=CharField(),
            ),
            sub_document_type_label=Case(
                When(sub_document_type="audited_fs", then=Value("Audited Financial Statements")),
                When(sub_document_type="unaudited_fs", then=Value("Unaudited Financial Statements")),
                When(sub_document_type="quarterly_letter", then=Value("Quarterly Letter")),
                When(sub_document_type="annual_letter", then=Value("Annual Letter")),
                When(sub_document_type="investment_memo", then=Value("Investment Memo")),
                When(sub_document_type="tax_estimates", then=Value("Tax Estimates")),
                When(sub_document_type="tax_final", then=Value("Tax Final")),
                When(sub_document_type="other", then=Value("Other")),
                default=Value(""),
                output_field=CharField(),
            ),
        )
        .select_related(
            "raw_retreival_document",
            "line_item",
            "line_item__investing_entity",
            "line_item__investment",
            "line_item__investing_entity__client",
        )
        .only(
            "name",
            "document_type",
            "sub_document_type",
            "posted_date",
            "effective_date",
            "s3_bucket",
            "s3_key",
            "has_been_viewed",
            "raw_retreival_document",
            "line_item__investing_entity__client__id",  # Added ID field
            "line_item__investing_entity__client__legal_name",
            "line_item__investing_entity__id",  # Added ID field
            "line_item__investing_entity__legal_name",
            "line_item__investment__id",  # Added ID field
            "line_item__investment__legal_name",
        )
        .filter(line_item__is_visible=True)
    )


def annote_processed_docs_with_multiple_investors(
    processed_docs: models.QuerySet[ProcessedDocument],
) -> models.QuerySet[ProcessedDocument]:
    return processed_docs.annotate(
        has_multiple_investors=Exists(
            processed_docs.filter(raw_retreival_document=OuterRef("raw_retreival_document"))
            .values("raw_retreival_document")
            .annotate(doc_count=Count("raw_retreival_document"))
            .filter(doc_count__gt=1)
        )
    )


def filter_processed_docs_with_multiple_investors(
    processed_docs: models.QuerySet[ProcessedDocument],
) -> models.QuerySet[ProcessedDocument]:
    return processed_docs.annotate(
        rank=Window(
            expression=Rank(),
            partition_by=[F("raw_retreival_document")],
            order_by=[F("pk")],
        )
    ).filter(rank=1)


@login_required
def vault(request: HttpRequest) -> HttpResponse:  # noqa: C901, PLR0912, PLR0915
    request_prefix = request.POST if request.method == "POST" else request.GET

    template = "vault/vault.html"

    line_items = LineItem.objects.for_user(request.user).order_by(
        "investing_entity__client__legal_name",
        "investing_entity__legal_name",
        "investment__legal_name",
    )

    processed_documents = get_processed_documents(request.user)

    # Calculate notifications count and get last notice date
    notifications = sum(1 for doc in processed_documents if not doc.has_been_viewed)

    # Get total line items
    total_line_items = len({doc.line_item_id for doc in processed_documents})

    # Get last notice received date
    last_notice_date = processed_documents.order_by("-created_at").values("created_at").first()
    last_notice_received = last_notice_date["created_at"] if last_notice_date else None

    date_ranges = processed_documents.aggregate(
        min_posted_date=Min("posted_date"),
        max_posted_date=Max("posted_date"),
        min_effective_date=Min("effective_date"),
        max_effective_date=Max("effective_date"),
    )
    for key, value in date_ranges.items():
        if value:
            date_ranges[key] = value.strftime("%Y-%m-%d")

    # Handle date filters dynamically
    filters = {
        "posted_date__gte": request_prefix.get("posted_date_date_from"),
        "posted_date__lte": request_prefix.get("posted_date_date_to"),
        "effective_date__gte": request_prefix.get("effective_date_date_from"),
        "effective_date__lte": request_prefix.get("effective_date_date_to"),
        "line_item__investing_entity__client__legal_name__in": request_prefix.getlist("client"),
        "line_item__investing_entity__legal_name__in": request_prefix.getlist("entity"),
        "line_item__investment__legal_name__in": request_prefix.getlist("investment"),
        "has_been_viewed": request_prefix.get("has_been_viewed"),
    }
    # If select all or clear column filter is triggered, update filters
    if request_prefix.get("clear_column_filter"):
        column_name = request_prefix.get("clear_column_filter")
        if column_name == "client":
            filters["line_item__investing_entity__client__legal_name__in"] = []
        elif column_name == "entity":
            filters["line_item__investing_entity__legal_name__in"] = []
        elif column_name == "investment":
            filters["line_item__investment__legal_name__in"] = []
        elif column_name == "category":
            filters["document_type__in"] = []
            filters["sub_document_type__in"] = []

    # apply simple filters
    processed_documents = processed_documents.filter(**{k: v for k, v in filters.items() if v})

    # apply category filters
    category_filters = parse_category_filters(request_prefix)
    if category_filters:
        category_q = Q()
        for doc_type, subtypes in category_filters.items():
            if not subtypes or sorted(subtypes) == sorted(
                [s.value for s in ProcessedDocument.get_sub_document_types_for_document_type(doc_type)]
            ):
                category_q |= Q(document_type=doc_type)
            else:
                category_q |= Q(document_type=doc_type, sub_document_type__in=subtypes)
        processed_documents = processed_documents.filter(category_q)

    annotated_processed_documents = annote_processed_docs_with_multiple_investors(processed_documents)
    processed_documents = filter_processed_docs_with_multiple_investors(annotated_processed_documents)
    all_processed_documents_with_multiple_investors = annotated_processed_documents.filter(has_multiple_investors=True)
    raw_doc_to_multiple_clients_name_dict = {}
    raw_doc_to_multiple_entities_name_dict = {}
    for processed_doc in all_processed_documents_with_multiple_investors:
        raw_doc = processed_doc.raw_retreival_document
        if raw_doc not in raw_doc_to_multiple_clients_name_dict:
            raw_doc_to_multiple_clients_name_dict[raw_doc] = set()
        if raw_doc not in raw_doc_to_multiple_entities_name_dict:
            raw_doc_to_multiple_entities_name_dict[raw_doc] = set()
        raw_doc_to_multiple_clients_name_dict[raw_doc].add(processed_doc.line_item.investing_entity.client.legal_name)
        raw_doc_to_multiple_entities_name_dict[raw_doc].add(processed_doc.line_item.investing_entity.legal_name)
    search_query = request_prefix.get("q", "").strip().lower()
    logger.info("search_query", extra={"search_query": search_query})

    if search_query:
        # First try exact match for document type code
        if search_query in [choice[0] for choice in DocumentType.choices]:
            processed_documents = processed_documents.filter(document_type=search_query)
        else:
            # Then try other searches
            processed_documents = processed_documents.filter(
                Q(line_item__investing_entity__client__legal_name__icontains=search_query)
                | Q(line_item__investing_entity__legal_name__icontains=search_query)
                | Q(line_item__investment__legal_name__icontains=search_query)
                | Q(document_type_label__iexact=search_query)  # Exact match for full document type name
                | Q(document_type_label__icontains=search_query)  # Partial match as fallback
            )

    # Order and paginate
    posted_date_sorted_by_most_recent = request_prefix.get("posted_date_sort_hidden", None)
    effective_date_sorted_by_most_recent = request_prefix.get("effective_date_sort_hidden", None)
    if posted_date_sorted_by_most_recent not in (None, "None"):
        if posted_date_sorted_by_most_recent in (True, "True"):
            processed_documents = processed_documents.order_by("-posted_date", "name")
        else:
            processed_documents = processed_documents.order_by("posted_date", "name")
    elif effective_date_sorted_by_most_recent not in (None, "None"):
        if effective_date_sorted_by_most_recent in (True, "True"):
            processed_documents = processed_documents.order_by("-effective_date", "name")
        else:
            processed_documents = processed_documents.order_by("effective_date", "name")
    else:
        # default
        processed_documents = processed_documents.order_by("-posted_date", "-effective_date", "name")

    per_page = int(request_prefix.get("per_page", 50))
    paginator = Paginator(processed_documents, per_page)

    page = int(request_prefix.get("current_page", 1))
    has_page_change = bool(int(request_prefix.get("page_change", 0)))
    page = 1 if not has_page_change else page

    # Implement circular pagination
    page = ((page - 1) % paginator.num_pages) + 1
    page_obj = paginator.get_page(page)
    ct = paginator.count
    start_index = 0 if ct == 0 else (page * per_page - per_page + 1)
    end_index = 0 if ct == 0 else min(page * per_page, ct)

    def doc_type_lookup(x: str) -> str:
        try:
            return DocumentType(x).label
        except ValueError:
            return x

    # Prepare context
    context = {
        "processed_documents": page_obj,
        "per_page": per_page,
        "notifications": notifications,
        "total_line_items": total_line_items,
        "last_notice_received": last_notice_received,
        "vault_query_data": {
            "current_page": page,
            "start_index": start_index,
            "end_index": end_index,
            "total_count": ct,
            "query_filters": {
                "posted_date_date_from": request_prefix.get("posted_date_date_from"),
                "posted_date_date_to": request_prefix.get("posted_date_date_to"),
                "effective_date_date_from": request_prefix.get("effective_date_date_from"),
                "effective_date_date_to": request_prefix.get("effective_date_date_to"),
                "q": search_query,
            },
        },
        "line_items": LineItem.objects.for_user(request.user),
        "date_ranges": date_ranges,
        "DocumentType": DocumentType,
        "disable_upload_button": line_items.count() == 0,
        "posted_date_sorted_by_most_recent": posted_date_sorted_by_most_recent,
        "effective_date_sorted_by_most_recent": effective_date_sorted_by_most_recent,
        "raw_doc_to_multiple_clients_name_dict": raw_doc_to_multiple_clients_name_dict,
        "raw_doc_to_multiple_entities_name_dict": raw_doc_to_multiple_entities_name_dict,
    }
    return TemplateResponse(request, template, context)


@login_required
def vault_header(request: HttpRequest) -> HttpResponse:  # noqa: C901, PLR0912, PLR0915
    template = "vault/table/header.html"
    headers = ResponseHeaders(data={})
    enum = None
    processed_documents = get_processed_documents(request.user)
    request_prefix = request.POST if request.method == "POST" else request.GET

    column_name = request_prefix.get("column_name")
    select_all = request_prefix.get("select_all", None)
    clear = request_prefix.get("clear", None)
    dropdown_filter = request_prefix.get("dropdown_filter", None)
    client_filters = request_prefix.getlist("client", None)
    entity_filters = request_prefix.getlist("entity", None)
    investment_filters = request_prefix.getlist("investment", None)

    # Handle hierarchical category filter
    category_filters = parse_category_filters(request_prefix)

    client_available_vals = request_prefix.getlist("client-available", None)
    entity_available_vals = request_prefix.getlist("entity-available", None)
    investment_available_vals = request_prefix.getlist("investment-available", None)
    category_available_vals = request_prefix.getlist("category-available", None)
    clear_column_filter = request_prefix.get("clear_column_filter")

    filters = {
        "posted_date__gte": request_prefix.get("posted_date_date_from"),
        "posted_date__lte": request_prefix.get("posted_date_date_to"),
        "effective_date__gte": request_prefix.get("effective_date_date_from"),
        "effective_date__lte": request_prefix.get("effective_date_date_to"),
        "line_item__investing_entity__client__legal_name__in": client_filters,
        "line_item__investing_entity__legal_name__in": entity_filters,
        "line_item__investment__legal_name__in": investment_filters,
        "document_type__in": category_filters,
        "has_been_viewed": request_prefix.get("has_been_viewed"),
    }
    processed_documents = processed_documents.filter(**{k: v for k, v in filters.items() if v})
    client_vals = processed_documents.values_list(
        "line_item__investing_entity__client__legal_name",
        flat=True,
    ).distinct()

    entity_vals = processed_documents.values_list(
        "line_item__investing_entity__legal_name",
        flat=True,
    ).distinct()

    investment_vals = processed_documents.values_list(
        "line_item__investment__legal_name",
        flat=True,
    ).distinct()

    category_vals = processed_documents.values_list(
        "document_type",
        flat=True,
    ).distinct()

    if column_name == "client":
        title = "Client"
        selected_vals = client_filters
        column_vals = client_available_vals if len(client_available_vals) >= len(client_vals) else client_vals
        if entity_filters or investment_filters:
            if client_filters:
                column_vals = client_available_vals if len(client_available_vals) >= len(client_vals) else client_vals
            else:
                column_vals = client_vals
        if clear_column_filter:
            column_vals = client_available_vals if len(client_available_vals) >= len(client_vals) else client_vals

        if select_all:
            selected_vals = column_vals
            add_hx_trigger_header({"clearFilter": {"clear_column_filter": column_name}}, header=headers)
        elif clear:
            selected_vals = []
            add_hx_trigger_header({"clearFilter": {"clear_column_filter": column_name}}, header=headers)

        if dropdown_filter:
            add_hx_trigger_header({"conditionalFilter": {"column_name": "client"}}, header=headers)

    elif column_name == "entity":
        title = "Entity"
        selected_vals = entity_filters
        column_vals = entity_available_vals if len(entity_available_vals) >= len(entity_vals) else entity_vals
        if client_filters or investment_filters:
            if entity_filters:
                column_vals = entity_available_vals if len(entity_available_vals) >= len(entity_vals) else entity_vals
            else:
                column_vals = entity_vals
        if clear_column_filter:
            column_vals = entity_available_vals if len(entity_available_vals) >= len(entity_vals) else entity_vals

        if select_all:
            selected_vals = column_vals
            add_hx_trigger_header({"clearFilter": {"clear_column_filter": column_name}}, header=headers)
        elif clear:
            selected_vals = []
            add_hx_trigger_header({"clearFilter": {"clear_column_filter": column_name}}, header=headers)

        if dropdown_filter:
            add_hx_trigger_header(
                {
                    "conditionalFilter": {
                        "column_name": "entity",
                    }
                },
                header=headers,
            )

    elif column_name == "investment":
        title = "Investment"
        selected_vals = investment_filters
        column_vals = (
            investment_available_vals if len(investment_available_vals) >= len(investment_vals) else investment_vals
        )
        if client_filters or entity_filters:
            if investment_filters:
                column_vals = (
                    investment_available_vals
                    if len(investment_available_vals) >= len(investment_vals)
                    else investment_vals
                )
            else:
                column_vals = investment_vals
        if clear_column_filter:
            column_vals = (
                investment_available_vals if len(investment_available_vals) >= len(investment_vals) else investment_vals
            )

        if select_all:
            selected_vals = column_vals
            add_hx_trigger_header({"clearFilter": {"clear_column_filter": column_name}}, header=headers)
        elif clear:
            selected_vals = []
            add_hx_trigger_header({"clearFilter": {"clear_column_filter": column_name}}, header=headers)

        if dropdown_filter:
            add_hx_trigger_header(
                {
                    "conditionalFilter": {
                        "column_name": "investment",
                    }
                },
                header=headers,
            )

    if column_name == "category":
        title = "Category"
        selected_vals = category_filters
        column_vals = category_available_vals if category_available_vals else category_vals
        enum = DocumentType

        if select_all:
            selected_vals = column_vals
            add_hx_trigger_header({"clearFilter": {"clear_column_filter": column_name}}, header=headers)
        elif clear:
            selected_vals = []
            add_hx_trigger_header({"clearFilter": {"clear_column_filter": column_name}}, header=headers)

        # Use custom multiselect template for category
        template = "vault/partials/multiselect_dropdown_header.html"

        # Prepare category filter items for multiselect dropdown
        category_filter_items = []
        for doc_type_value, doc_type_label in DocumentType.choices:
            if doc_type_value == DocumentType.UNKNOWN.value:  # Skip unknown documents
                continue

            item = {"value": doc_type_value, "label": doc_type_label, "submenu": []}

            # Look up document type enum and add sub-document types if they exist
            doc_type_enum = DocumentType(doc_type_value)
            if doc_type_enum in DOCUMENT_TYPE_SUB_TYPE_MAPPING:
                for sub_type_enum in DOCUMENT_TYPE_SUB_TYPE_MAPPING[doc_type_enum]:
                    item["submenu"].append({"value": sub_type_enum.value, "label": sub_type_enum.label})

            category_filter_items.append(item)

        context = {
            "title": title,
            "column_name": column_name,
            "filter_items": category_filter_items,
        }
        return TemplateResponse(request, template, context, headers=headers)

    # Returned for non-category columns (client, entity, investment)
    context = {
        "title": title,
        "column_name": column_name,
        "column_vals": sorted(column_vals),
        "selected_vals": selected_vals,
        "enum": enum,
    }
    return TemplateResponse(request, template, context, headers=headers)


@login_required
@require_POST
def mark_documents_viewed(request: HttpRequest) -> HttpResponse:
    """Mark multiple documents as viewed."""
    template = "vault/cards/new_notifications.html"
    logger.info("mark_documents_viewed", request=request.POST, get=request.GET)
    doc_pks = request.POST.getlist("processed_doc_pks")
    viewed = request.GET.get("viewed", None)
    viewed_bool = None
    if viewed is not None and doc_pks:
        viewed_bool = viewed.lower() == "true"
        logger.info("mark", viewed_bool=viewed_bool, doc_pks=doc_pks)
        ProcessedDocument.objects.for_user(request.user).filter(pk__in=doc_pks).update(has_been_viewed=viewed_bool)

    # Calculate updated notifications count
    processed_documents = get_processed_documents(request.user)
    # Calculate notifications count and get last notice date
    notifications = processed_documents.filter(has_been_viewed=False).count()

    # Get total line items
    total_line_items = processed_documents.values("line_item_id").distinct().count()

    # Get last notice received date
    last_notice_date = processed_documents.order_by("-created_at").values("created_at").first()
    last_notice_received = last_notice_date["created_at"] if last_notice_date else None
    headers = add_hx_trigger_header({"openedProcessedDocPdfs": {"processed_doc_pks": doc_pks, "viewed": viewed_bool}})

    context = {
        "notifications": notifications,
        "total_line_items": total_line_items,
        "last_notice_received": last_notice_received,
    }
    return TemplateResponse(request, template, context, headers=headers)


from typing import Any

from django import forms


class EditProcessedDocumentsForm(forms.Form):
    line_item = forms.UUIDField()
    effective_date = forms.DateField(widget=forms.DateInput(attrs={"type": "date"}))
    posted_date = forms.DateField(widget=forms.DateInput(attrs={"type": "date"}))
    document_type = forms.ChoiceField(choices=DocumentType.choices)
    sub_document_type = forms.ChoiceField(choices=SubDocumentType.choices, required=False)
    processed_doc_pk = forms.UUIDField()

    def clean(self) -> dict[str, Any]:
        cleaned_data = super().clean()
        document_type = cleaned_data.get("document_type")
        sub_document_type = cleaned_data.get("sub_document_type")
        if document_type in REQUIRED_SUB_TYPE_DOCUMENTS:
            if not sub_document_type:
                msg = "Sub document type is required for this document type"
                raise forms.ValidationError(msg)
            if sub_document_type not in ProcessedDocument.get_sub_document_types_for_document_type(document_type):
                msg = "Invalid sub document type for this document type"
                raise forms.ValidationError(msg)
        else:
            cleaned_data["sub_document_type"] = ""
        return cleaned_data


@login_required
def edit_processed_documents(request: HttpRequest) -> HttpResponse:
    if request.method == "GET":
        template = "modal.html"
        line_items = LineItem.objects.for_user(request.user).order_by(
            "investing_entity__client__legal_name",
            "investing_entity__legal_name",
            "investment__legal_name",
        )
        processed_docs = get_processed_documents(request.user)
        annotated_processed_docs = annote_processed_docs_with_multiple_investors(processed_docs)
        request_processed_docs = request.GET.getlist("processed_doc_pks")
        processed_doc = annotated_processed_docs.filter(pk__in=request_processed_docs).first()
        valid_sub_document_types = ProcessedDocument.get_sub_document_types_for_document_type(
            processed_doc.document_type
        )
        context = {
            "modal_include_template": "vault/modal/edit.html",
            "line_items": line_items,
            "processed_doc": processed_doc,
            "DocumentType": DocumentType,
            "SubDocumentType": SubDocumentType,
            "REQUIRED_SUB_TYPE_DOCUMENTS": REQUIRED_SUB_TYPE_DOCUMENTS,
            "valid_sub_document_types": valid_sub_document_types,
        }
        return TemplateResponse(request, template, context)

    if request.method == "POST":
        form = EditProcessedDocumentsForm(request.POST)
        if not form.is_valid():
            return JsonResponse({"status": "error", "message": form.errors}, status=400)

        line_item_id = form.cleaned_data.get("line_item")
        line_item = LineItem.objects.for_user(request.user).get(id=line_item_id)

        if not line_item:
            return JsonResponse({"status": "error", "message": "Invalid line item"}, status=400)

        effective_date = form.cleaned_data.get("effective_date")
        posted_date = form.cleaned_data.get("posted_date")
        document_type = form.cleaned_data.get("document_type")
        sub_document_type = form.cleaned_data.get("sub_document_type")
        processed_doc_pk = form.cleaned_data.get("processed_doc_pk")

        try:
            old_doc = ProcessedDocument.objects.get(id=processed_doc_pk)
            if old_doc is None:
                raise ValueError  # noqa: TRY301
            ProcessedDocument.create(
                raw_document=old_doc.raw_retreival_document,
                line_items=[line_item],
                hide_line_items=[old_doc.line_item] if old_doc.line_item != line_item else [],
                document_type=document_type,
                sub_document_type=sub_document_type,
                posted_date=posted_date,
                effective_date=effective_date,
                process_document_version=1,
                process_document_source="user_ground_truth",
                labeled_by=request.user,
                has_been_viewed=False,  # or should we use old_doc.has_been_viewed?
                is_visible=True,
                is_ground_truth=True,
                document_summary=old_doc.document_summary,
                is_document_summary_approved=old_doc.is_document_summary_approved,
            )

            headers = add_hx_trigger_header({"reloadVault": {}, "closeModal": {}})
            return JsonResponse({"success": True}, headers=headers)
        except Exception as e:
            logger.exception("Unexpected error in vault_upload", extra={"error": str(e)})
            return JsonResponse({"status": "error", "message": "Unexpected error occurred"}, status=500)

    return HttpResponse("Invalid request method", status=405)


@login_required
def get_valid_sub_document_types(request: HttpRequest) -> HttpResponse:
    template = "vault/partials/sub_document_type_dropdown.html"
    document_type = request.GET.get("document_type")
    valid_sub_document_types = ProcessedDocument.get_sub_document_types_for_document_type(document_type)
    context = {
        "valid_sub_document_types": valid_sub_document_types,
    }

    return TemplateResponse(request, template, context)


def save_raw_document(
    user: BridgeUser,
    file: UploadedFile,
    document_metadata: dict | None = None,
    document_type: DocumentType = DocumentType.UNKNOWN,
    *,
    folder: Path,
) -> tuple[str, bool]:
    if document_metadata is None:
        document_metadata = {}

    doc_hash = DocumentHash(str(uuid.uuid4()), 1, "ManualUpload")
    doc = RawDocumentTuple(
        name=file.name,
        date=timezone.now(),
        doc_type=document_type,
        content=file.read(),
        raw_metadata=json.dumps(document_metadata),
        doc_hash=doc_hash,
    )
    return save_document(user, doc, folder=folder)


def promote_raw_document(
    user: BridgeUser,
    line_item: LineItem,
    raw_doc: RawDocument,
) -> ProcessedDocument:
    raw_doc_id = str(raw_doc.pk)
    # TODO: should we automatically process this document using ML / the demo task?
    raw_doc = RawDocument.objects.get(pk=raw_doc_id)
    document_metadata = json.loads(raw_doc.metadata)
    document_type = raw_doc.document_type
    posted_date = raw_doc.posted_date
    effective_date = document_metadata.get("effective_date")
    sub_document_type = document_metadata.get("sub_document_type", "")
    if document_metadata.get("renamed_to"):
        raw_doc.name = document_metadata.get("renamed_to")

    logger.info("Raw document created", extra={"raw_doc_id": raw_doc_id})
    processed_doc = ProcessedDocument.create(
        raw_document=raw_doc,
        line_items=[line_item],
        document_type=document_type,
        sub_document_type=sub_document_type,
        posted_date=posted_date,
        effective_date=effective_date,
        process_document_version=1,
        process_document_source="manual_upload",
        labeled_by=user,
        is_visible=True,
        is_ground_truth=True,
    )
    return processed_doc[0]


def _validate_input(request: HttpRequest) -> tuple[bool, str | None]:
    effective_date = request.POST.get("effective_date")
    posted_date = request.POST.get("posted_date")
    document_type = request.POST.get("document_type")
    sub_document_type = request.POST.get("sub_document_type")
    raw_document_ids = request.POST.getlist("raw_document_ids")

    client_id = request.POST.get("client")
    investing_entity_id = request.POST.get("entity")
    investment_id = request.POST.get("investment")

    logger.info(
        "Validating input",
        user=request.user,
        effective_date=effective_date,
        posted_date=posted_date,
        document_type=document_type,
        raw_document_ids=raw_document_ids,
        client_id=client_id,
        investing_entity_id=investing_entity_id,
        investment_id=investment_id,
    )

    required_fields = [
        document_type,
        posted_date,
        effective_date,
        raw_document_ids,
        client_id,
        investing_entity_id,
        investment_id,
    ]
    if document_type in REQUIRED_SUB_TYPE_DOCUMENTS:
        required_fields.append(sub_document_type)

    if not all(required_fields):
        return False, "All fields are required"

    line_item = get_line_item_from_investment_details(request.user, client_id, investing_entity_id, investment_id)
    if not line_item:
        return False, "Invalid line item"

    raw_docs = RawDocument.objects.for_user(request.user).filter(id__in=raw_document_ids)

    if not raw_docs:
        return False, "No uploaded documents found"

    return True, None


def get_selected_line_item(request: HttpRequest, request_type: str) -> dict[str, str]:
    selected_client_id = request.POST.get("client") if request_type == "POST" else request.GET.get("client")
    selected_entity_id = request.POST.get("entity") if request_type == "POST" else request.GET.get("entity")
    selected_investment_id = request.POST.get("investment") if request_type == "POST" else request.GET.get("investment")
    selected_client = Client.objects.for_user(request.user).get(pk=selected_client_id)
    selected_entity = InvestingEntity.objects.for_user(request.user).get(pk=selected_entity_id)
    selected_investment = Investment.objects.for_user(request.user).get(pk=selected_investment_id)
    logger.info(
        "Selected line item",
        selected_client=selected_client.legal_name,
        selected_entity=selected_entity.legal_name,
        selected_investment=selected_investment.legal_name,
    )
    return {
        "selected_client": selected_client,
        "selected_entity": selected_entity,
        "selected_investment": selected_investment,
    }


def determine_selected_values(raw_doc: RawDocument) -> dict[str, str | None]:
    document_metadata = json.loads(raw_doc.metadata)
    default: dict[str, str | None] = {
        "selected_posted_date": str(raw_doc.posted_date)
        if raw_doc.posted_date
        else (document_metadata.get("posted_date") or None),
        "selected_effective_date": document_metadata.get("effective_date") or None,
        "selected_document_type": (raw_doc.document_type or document_metadata.get("document_type") or None),
        "pdf_link": raw_doc.signed_url,
        "doc_file_type": Path(raw_doc.name).suffix.lower(),
        "document_name": document_metadata.get("renamed_to") or raw_doc.name,
    }
    if raw_doc.created_by is not None and raw_doc.created_by.is_demo:
        from retrieval.core.managers.demo.demo_generic import get_demo_data

        lookup_dict, _ = get_demo_data()
        lookup_key = raw_doc.name.replace(".pdf", "")
        if lookup_key not in lookup_dict:
            logger.info("Demo data not found", lookup_key=lookup_key)
            return default
        data = lookup_dict[lookup_key]

        logger.info("Demo data found", data=data)
        return {
            "selected_posted_date": None,
            "selected_effective_date": data.get("Effective Date"),
            "selected_document_type": data.get("Document Type"),
            "pdf_link": raw_doc.signed_url,
            "doc_file_type": Path(raw_doc.name).suffix.lower(),
            "document_name": raw_doc.name,
        }
    # TODO: add ML here
    return default


@login_required
def get_upload_signed_url(request: HttpRequest) -> HttpResponse:
    if request.method == "POST":
        json_body = json.loads(request.body)
        parent_path = json_body.get(
            "s3_parent_path", str(Path("user_upload_retrieval") / str(timezone.now().strftime("%Y-%m-%d_%H-%M-%S")))
        )
        logger.info("s3_parent_path", path=parent_path)

        temp_raw_doc_id = uuid.uuid4()
        s3 = boto3.client("s3")
        s3_path = f"{parent_path}/raw_documents/{temp_raw_doc_id}"

        response = s3.generate_presigned_post(
            Bucket=BUCKET,
            Key=s3_path,
            ExpiresIn=3600,  # URL valid for 1 hour
        )
        logger.info("response", response=response, url=response.get("url"), fields=response.get("fields"))
        return JsonResponse(
            {
                "url": response["url"],
                "raw_doc_id": str(temp_raw_doc_id),
                "s3_path": s3_path,
                "fields": response["fields"],
            }
        )

    return HttpResponse("Invalid request method", status=405)


@login_required
def vault_upload(request: HttpRequest) -> HttpResponse:  # noqa: C901
    context: dict[str, str | None | models.QuerySet | type | list[str]] = {}
    if request.method == "GET":
        template = "labeling_modal.html"

        clients = Client.objects.for_user(request.user).order_by("legal_name")
        investing_entities = InvestingEntity.objects.for_user(request.user).order_by("legal_name")
        investments = Investment.objects.for_user(request.user).order_by("legal_name")
        s3_parent_path = Path("user_upload_retrieval") / str(timezone.now().strftime("%Y-%m-%d_%H-%M-%S"))

        context = {
            "modal_include_template": "vault/modal/upload.html",
            "clients": clients,
            "investing_entities": investing_entities,
            "investments": investments,
            "s3_parent_path": s3_parent_path,
        }
        return TemplateResponse(request, template, context)

    if request.method == "POST":
        raw_doc_ids = []
        first_raw_doc = None
        error_files_and_trace: list[tuple[str, str] | None] = []
        for key, s3_path in request.POST.items():
            if key.startswith("hidden-raw-doc-pk-") and s3_path:
                try:
                    file_name = key.split("hidden-raw-doc-pk-")[1]
                    raw_doc_id = s3_path.split("/")[-1]
                    logger.info("Attempting to create raw document", raw_doc_id=raw_doc_id, s3_path=s3_path)
                    obj = boto3.client("s3").head_object(Bucket=BUCKET, Key=s3_path)
                    md5 = obj["ETag"].strip('"')
                    doc_hash = DocumentHash(str(uuid.uuid4()), 1, "ManualUpload")
                    with transaction.atomic():
                        raw_doc, created = RawDocument.objects.update_or_create(
                            user=request.user,
                            doc_hash=doc_hash.hash,
                            doc_hash_version=doc_hash.version,
                            doc_hash_source=doc_hash.name,
                            md5=md5,
                            name=file_name,
                            create_defaults={
                                "id": raw_doc_id,
                                "posted_date": timezone.now(),
                                "original_posted_date": timezone.now(),
                                "document_type": DocumentType.UNKNOWN,
                                "original_document_type": DocumentType.UNKNOWN,
                                "metadata": json.dumps({}),
                                "s3_key": s3_path,
                                "s3_bucket": BUCKET,
                                "exists_in_s3": True,
                            },
                        )
                    if not first_raw_doc:
                        first_raw_doc = raw_doc
                    if not created:
                        logger.info(
                            "user uploaded duplicate document",
                            raw_doc_id=raw_doc.id,
                            unused_raw_doc_id=raw_doc_id,
                            s3_path=s3_path,
                        )
                    raw_doc_ids.append(raw_doc.id)
                except Exception as e:
                    logger.exception("Error saving files", extra={"error": str(e)})
                    error_files_and_trace.append((file_name, str(e)))

        if error_files_and_trace:
            logger.error("Error uploading documents", extra={"error_files_and_trace": error_files_and_trace})
            message = f"{request.user!s} encountered an error uploading {len(error_files_and_trace)} files:\n"
            for error_file, error_trace in error_files_and_trace:
                if error_file and error_trace:
                    message += f"file name: {error_file}\n error: {error_trace}\n"
            send_message_to_slack(
                channel="#user-actions",
                message=message,
            )
            return JsonResponse(
                {
                    "status": "error",
                    "message": "Error uploading documents",
                    "error_files_and_trace": error_files_and_trace,
                },
                status=400,
            )

        template = "labeling_modal.html"
        context = {
            "modal_include_template": "vault/modal/label_document.html",
            "DocumentType": DocumentType,
            "SubDocumentType": SubDocumentType,
            "current_doc_index": 0,
            "total_docs": len(raw_doc_ids),
            "raw_document_ids": raw_doc_ids,
            **determine_selected_values(raw_doc=first_raw_doc),
            **get_selected_line_item(request, "POST"),
        }

        logger.info("Successfully uploaded documents", num_files=len(raw_doc_ids))
        message = f"{request.user!s} uploaded {len(raw_doc_ids)} raw docs:\n"
        for uploaded_id in raw_doc_ids:
            label_url = f"https://{settings.DOMAIN_NAME}/admin/webapp/rawdocument/{uploaded_id}/label"
            message += f"- `{uploaded_id}`: <{label_url}|label>\n"
        send_message_to_slack(
            channel="#user-actions",
            message=message,
        )

        return TemplateResponse(request, template, context)
    return HttpResponse("Invalid request method", status=405)


@login_required
def delete_label_document(request: HttpRequest) -> HttpResponse:
    if request.method == "POST":
        logger.info("Deleting document label")
        raw_document_ids = request.POST.getlist("raw_document_ids")
        current_doc_index = int(request.POST.get("current_doc_index", 0))

        new_raw_document_ids = raw_document_ids[:current_doc_index] + raw_document_ids[current_doc_index + 1 :]
        if len(new_raw_document_ids) == 0:
            logger.info("No more document to label")
            response = JsonResponse({"status": "success", "message": "Document label deleted successfully"}, status=200)
            response["HX-Trigger"] = "closeModal"
            response["HX-Refresh"] = "true"
            return response

        total_docs = len(new_raw_document_ids)
        next_index = min(current_doc_index, total_docs - 1)
        raw_doc = RawDocument.objects.get(pk=new_raw_document_ids[next_index])

        template = "labeling_modal.html"
        context = {
            "modal_include_template": "vault/modal/label_document.html",
            "DocumentType": DocumentType,
            "SubDocumentType": SubDocumentType,
            "current_doc_index": next_index,
            "total_docs": total_docs,
            "raw_document_ids": new_raw_document_ids,
            **determine_selected_values(raw_doc=raw_doc),
            **get_selected_line_item(request, "POST"),
        }
        return TemplateResponse(request, template, context)
    return HttpResponse("Invalid request method", status=405)


@login_required
def label_document(request: HttpRequest) -> HttpResponse:  # noqa: PLR0911
    if request.method == "GET":
        logger.info("Getting previous document label")
        raw_document_ids = request.GET.getlist("raw_document_ids")
        current_doc_index = int(request.GET.get("current_doc_index", 0))
        total_docs = int(request.GET.get("total_docs", 1))
        prev_index = max(current_doc_index - 1, 0)
        next_doc_id = raw_document_ids[prev_index]
        raw_doc = RawDocument.objects.get(pk=next_doc_id)

        template = "labeling_modal.html"
        context = {
            "modal_include_template": "vault/modal/label_document.html",
            "DocumentType": DocumentType,
            "SubDocumentType": SubDocumentType,
            "current_doc_index": prev_index,
            "total_docs": total_docs,
            "raw_document_ids": raw_document_ids,
            "valid_sub_document_types": ProcessedDocument.get_sub_document_types_for_document_type(
                raw_doc.document_type
            ),
            **determine_selected_values(raw_doc=raw_doc),
            **get_selected_line_item(request, "GET"),
        }
        return TemplateResponse(request, template, context)

    if request.method == "POST":
        logger.info("Attemping to label document")
        is_valid, error_message = _validate_input(request)
        if not is_valid:
            logger.info("Invalid input", error_message=error_message)
            return JsonResponse({"status": "error", "message": error_message}, status=400)

        effective_date = request.POST.get("effective_date")
        posted_date = request.POST.get("posted_date")
        document_type = request.POST.get("document_type")
        sub_document_type = request.POST.get("sub_document_type")
        document_rename = request.POST.get("document_rename")

        raw_document_ids = request.POST.getlist("raw_document_ids")
        current_doc_index = int(request.POST.get("current_doc_index", 0))
        total_docs = int(request.POST.get("total_docs", 1))

        logger.info(
            "Input validated",
            effective_date=effective_date,
            posted_date=posted_date,
            document_type=document_type,
            raw_document_ids=raw_document_ids,
            current_doc_index=current_doc_index,
            total_docs=total_docs,
        )

        try:
            with transaction.atomic():
                try:
                    raw_doc = RawDocument.objects.get(pk=raw_document_ids[current_doc_index])

                    document_metadata = json.loads(raw_doc.metadata)
                    raw_doc.posted_date = posted_date
                    raw_doc.document_type = document_type
                    document_metadata["sub_document_type"] = sub_document_type if sub_document_type else ""
                    document_metadata["effective_date"] = effective_date
                    if document_rename:
                        document_metadata["renamed_to"] = document_rename
                    raw_doc.metadata = json.dumps(document_metadata)
                    logger.info("Updating raw document", raw_doc=raw_doc)
                    raw_doc.save()
                except Exception:
                    logger.exception("Error updating raw document", extra={"raw_doc_id": raw_doc.id})
                    return JsonResponse(
                        {"status": "error", "message": "Error updating raw document", "file_pk": raw_doc.id},
                        status=400,
                    )

                if current_doc_index + 1 >= total_docs:
                    return vault_confirm(request)

                next_index = min(current_doc_index + 1, total_docs - 1)
                raw_doc = RawDocument.objects.get(pk=raw_document_ids[next_index])

                template = "labeling_modal.html"
                context = {
                    "modal_include_template": "vault/modal/label_document.html",
                    "DocumentType": DocumentType,
                    "SubDocumentType": SubDocumentType,
                    "REQUIRED_SUB_TYPE_DOCUMENTS": REQUIRED_SUB_TYPE_DOCUMENTS,
                    "current_doc_index": next_index,
                    "total_docs": total_docs,
                    "raw_document_ids": raw_document_ids,
                    **determine_selected_values(raw_doc=raw_doc),
                    **get_selected_line_item(request, "POST"),
                }

                return TemplateResponse(request, template, context)

        except Exception as e:
            logger.exception("Unexpected error in vault_confirm", extra={"error": str(e)})
            return JsonResponse({"status": "error", "message": "Unexpected error occurred"}, status=500)

    return HttpResponse("Invalid request method", status=405)


@login_required
def vault_confirm(request: HttpRequest) -> HttpResponse:  # noqa: PLR0911
    if request.method == "POST":
        logger.info("Attempting to confirm input")
        is_valid, error_message = _validate_input(request)
        if not is_valid:
            logger.info("Invalid input", error_message=error_message)
            return JsonResponse({"status": "error", "message": error_message}, status=400)

        effective_date = request.POST.get("effective_date")
        posted_date = request.POST.get("posted_date")
        document_type = request.POST.get("document_type")
        sub_document_type = request.POST.get("sub_document_type")
        client_id = request.POST.get("client")
        investing_entity_id = request.POST.get("entity")
        investment_id = request.POST.get("investment")
        raw_document_ids = request.POST.getlist("raw_document_ids")
        logger.info(
            "Input validated",
            effective_date=effective_date,
            posted_date=posted_date,
            document_type=document_type,
            sub_document_type=sub_document_type,
            client_id=client_id,
            investing_entity_id=investing_entity_id,
            investment_id=investment_id,
            raw_document_ids=raw_document_ids,
        )

        raw_docs = RawDocument.objects.for_user(request.user).filter(id__in=raw_document_ids)
        if len(raw_docs) == 0:
            logger.info("No uploaded documents found", raw_document_ids=raw_document_ids)
            return JsonResponse({"status": "error", "message": "No uploaded documents found"}, status=400)

        line_item = get_line_item_from_investment_details(request.user, client_id, investing_entity_id, investment_id)

        if not line_item:
            logger.info(
                "Invalid line item",
                client_id=client_id,
                investing_entity_id=investing_entity_id,
                investment_id=investment_id,
            )
            return JsonResponse({"status": "error", "message": "Invalid line item"}, status=400)

        logger.info("Creating retrieval")

        retrieval = Retrieval.create(
            user=request.user,
            merged_portal_credential=line_item.merged_portal_credential,
            s3_format_version=-1,
            manager="UserUploadV1",
        )
        retrieval.update_login_status(Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL)
        retrieval.line_items.add(line_item)

        logger.info(
            "Retrieval created and associated with line item", retrieval_id=retrieval.id, line_item_id=line_item.id
        )

        logger.info("Promoting raw documents", num_raw_docs=len(raw_docs))
        files = []
        error_files = []
        try:
            with transaction.atomic():
                for raw_doc in raw_docs:
                    try:
                        logger.info("Promoting raw document", raw_doc_id=raw_doc.id)
                        processed_doc_id = promote_raw_document(request.user, line_item, raw_doc)
                        files.append(processed_doc_id)
                    except Exception:
                        logger.exception("Error promoting raw document", extra={"raw_doc_id": raw_doc.id})
                        error_files.append(raw_doc.id)

                if error_files:
                    logger.error("Error promoting raw documents", extra={"error_files": error_files})
                    return JsonResponse(
                        {"status": "error", "message": "Error uploading documents", "error_files": error_files},
                        status=400,
                    )
                logger.info("Successfully promoted raw documents", num_files=len(files))

                template = "modal.html"
                context = {
                    "modal_include_template": "label_end_modal.html",
                    "total_docs": len(files),
                    "line_item_str": str(line_item.investing_entity.client)
                    + " / "
                    + str(line_item.investing_entity)
                    + " / "
                    + str(line_item.investment),
                }

                logger.info("confirm context", context=context)

                headers = add_hx_trigger_header({"reloadVault": {}})
                return TemplateResponse(request, template, context, headers=headers)

        except Exception as e:
            logger.exception("Unexpected error in vault_confirm", extra={"error": str(e)})
            return JsonResponse({"status": "error", "message": "Unexpected error occurred"}, status=500)

    return HttpResponse("Invalid request method", status=405)


@login_required
def vault_filtered_options(request: HttpRequest) -> HttpResponse:  # noqa: C901
    """Get filtered entities and investments based on selected client/entity."""
    client_id = request.GET.get("client")
    entity_id = request.GET.get("entity")
    investment_id = request.GET.get("investment")

    logger.info("Getting filtered options", client_id=client_id, entity_id=entity_id, investment_id=investment_id)

    clients = Client.objects.for_user(request.user).order_by("legal_name")

    investing_entities = InvestingEntity.objects.for_user(request.user).order_by("legal_name")

    investments = Investment.objects.for_user(request.user).order_by("legal_name")

    if client_id:
        clients = clients.filter(id=client_id)
        clients_first = clients.first()
        if clients_first is not None:
            client_id = clients_first.id

            if not entity_id:
                investing_entities = investing_entities.filter(client_id=client_id).distinct()

            if not investment_id:
                investments = investments.filter(line_items__investing_entity__client_id=client_id).distinct()

    if entity_id:
        investing_entities = investing_entities.filter(id=entity_id)
        investing_entities_first = investing_entities.first()
        if investing_entities_first is not None:
            entity_id = investing_entities_first.id

            if not client_id:
                clients = clients.filter(entities__id=entity_id).distinct()

            if not investment_id:
                investments = investments.filter(line_items__investing_entity__id=entity_id).distinct()

    if investment_id:
        investments = investments.filter(id=investment_id)
        investments_first = investments.first()
        if investments_first is not None:
            investment_id = investments_first.id

            if not client_id:
                clients = clients.filter(entities__line_items__investment_id=investment_id).distinct()

            if not entity_id:
                investing_entities = investing_entities.filter(line_items__investment_id=investment_id).distinct()

    logger.info(
        "Selected and filtered option counts",
        num_clients=clients.count(),
        num_entities=investing_entities.count(),
        num_investments=investments.count(),
        client_id=client_id,
        entity_id=entity_id,
        investment_id=investment_id,
    )

    context = {
        "clients": clients,
        "investing_entities": investing_entities,
        "investments": investments,
        "selected_client_id": client_id,
        "selected_entity_id": entity_id,
        "selected_investment_id": investment_id,
    }

    return TemplateResponse(request, "vault/modal/dropdown_options.html", context)
