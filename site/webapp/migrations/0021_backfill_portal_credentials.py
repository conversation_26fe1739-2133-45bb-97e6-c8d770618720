from django.db import migrations
import structlog
logger = structlog.get_logger(__name__)

class Migration(migrations.Migration):
    atomic = True

    dependencies = [
        ('webapp', '0020_remove_mergedportalcredential_multi_factor_authentication_type_and_more'),
    ]

    def backfill_mpc_portal_credentials(apps, schema_editor):
        MergedPortalCredential = apps.get_model('webapp', 'mergedportalcredential')
        PortalCredential = apps.get_model('webapp', 'portalcredential')


        untouched_mpcs = 0
        touched_mpcs = 0
        for mpc in MergedPortalCredential.objects.order_by("organization__name", "portal__name").all():
            if mpc.portal_credential is None:
                touched_mpcs += 1
                portal_credential = PortalCredential.objects.create(
                    organization=mpc.organization,
                    updated_by=mpc.created_by,
                    created_by=mpc.created_by,
                    portal=mpc.portal,
                    username="",
                )
                portal_credential.save()
                mpc.portal_credential = portal_credential
                mpc.save()

            else:
                untouched_mpcs += 1
        logger.info("Made portal credentials", touched_mpcs=touched_mpcs, untouched_mpcs=untouched_mpcs)

    def reverse_func(apps, schema_editor):
        pass

    operations = [
        migrations.RunPython(backfill_mpc_portal_credentials, reverse_func),
    ]
