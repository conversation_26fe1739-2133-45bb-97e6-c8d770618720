{"name": "theme", "version": "3.8.0", "description": "", "private": true, "scripts": {"build": "npm run build:clean && npm run build:tailwind && npm run build:esbuild", "build:clean": "rimraf ../static/css/dist", "build:tailwind": "cross-env NODE_ENV=production tailwindcss --postcss -i ./css/styles.css -o ../static/css/dist/styles.css --minify", "build:esbuild": "NODE_ENV=production node esbuild.config.mjs", "dev": "npm run dev:tailwind & npm run dev:esbuild", "dev:tailwind": "cross-env NODE_ENV=development tailwindcss --postcss -i ./css/styles.css -o ../static/css/dist/styles.css", "dev:esbuild": "NODE_ENV=development node esbuild.config.mjs", "tailwindcss": "node ./node_modules/tailwindcss/lib/cli.js"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {"@lottiefiles/lottie-player": "^2.0.12", "@r2wc/react-to-web-component": "^2.0.4", "flowbite": "^2.5.2", "htmx.org": "^2.0.3", "hyperscript.org": "^0.9.13", "logrocket": "^10.0.0", "moveable": "^0.53.0", "pdfjs-dist": "^5.3.93", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@rjsf/core": "^5.24.12", "@rjsf/utils": "^5.24.12", "@rjsf/validator-ajv8": "^5.24.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "monaco-editor": "^0.52.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-moveable": "^0.56.0", "zod": "^3.25.75"}, "devDependencies": {"autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "esbuild-wasm": "^0.24.0", "postcss": "^8.4.32", "postcss-import": "^15.1.0", "postcss-nested": "^6.0.1", "postcss-simple-vars": "^7.0.1", "rimraf": "^5.0.5", "tailwindcss": "^3.4.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "tailwind-merge": "^3.3.1", "typescript": "^5.8.3"}}