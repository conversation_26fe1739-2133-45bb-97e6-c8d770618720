import structlog
from allauth.account.adapter import DefaultAccountAdapter

logger = structlog.get_logger(__name__)


class CustomAccountAdapter(DefaultAccountAdapter):
    def __init__(self, request=None) -> None:  # noqa: ANN001
        super().__init__(request)  # Pass the request to the parent constructor

    def send_mail(self, template_prefix: any, email: str, context: any) -> None:
        # Log the existing context for debugging
        logger.info("send_mail called with template: {template_prefix}, email: {email}, context: {context}")

        # Add custom fields to the context
        user = context.get("user")
        if user:
            context["first_name"] = getattr(user, "first_name", "")
            context["last_name"] = getattr(user, "last_name", "")

        super().send_mail(template_prefix, email, context)
