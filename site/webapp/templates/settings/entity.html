{% extends "settings/base_settings.html" %}
{% load static %}
{% load widget_tweaks %}
{% block settings_content %}
    <div class="py-9 px-12">
        <div class="bg-[#f8f6f1] p-6 rounded-lg shadow-sm border border-gray-200 w-full max-w-4xl mx-auto">
            <h2 class="text-2xl font-semibold mb-6">Entity {{ entity.legal_name }}</h2>
            <!-- Message area -->
            <form method="post"
                  hx-post="{% url 'settings_entity' entity.id %}"
                  hx-target="body">
                {% csrf_token %}
                <input type="hidden" name="active_tab" value="entity">
                {% if entity %}<input type="hidden" name="entity_id" value="{{ entity.id }}">{% endif %}
                <!-- FIELD: Entity Legal Name -->
                <div class="flex items-start gap-4 mb-5">
                    <label for="{{ form.legal_name.id_for_label }}"
                           class="w-1/3 text-sm font-medium text-gray-800 pt-2">
                        {{ form.legal_name.label }}
                        {% if form.legal_name.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    <div class="w-2/3">
                        {% with base_classes="w-full px-4 py-2 border rounded-md shadow-sm bg-white" %}
                            {% if form.legal_name.errors %}
                                {% render_field form.legal_name class+=base_classes class+="border-red-500" placeholder="Full Entity Name" %}
                            {% else %}
                                {% render_field form.legal_name class+=base_classes class+="border-gray-300" placeholder="Full Entity Name" %}
                            {% endif %}
                        {% endwith %}
                        {% if form.legal_name.errors %}
                            <p class="text-red-500 text-sm mt-1">
                                {% for error in form.legal_name.errors %}
                                    {{ error }}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </p>
                        {% endif %}
                    </div>
                </div>
                <!-- FIELD: Entity Type (Dropdown) -->
                <div class="flex items-start gap-4 mb-5">
                    <label for="{{ form.entity_type.id_for_label }}"
                           class="w-1/3 text-sm font-medium text-gray-800 pt-2">
                        {{ form.entity_type.label|default:"Entity Type" }}
                        {% if form.entity_type.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    <div class="w-2/3">
                        {% with base_select_classes="w-full px-4 py-3 border rounded-md shadow-sm bg-white text-sm text-black focus:outline-none focus:ring-2 focus:ring-offset-2" %}
                            {% if form.entity_type.errors %}
                                {% render_field form.entity_type class=base_select_classes class+=" border-red-500 focus:ring-red-500" %}
                            {% else %}
                                {% render_field form.entity_type class=base_select_classes class+=" border-gray-300 focus:ring-indigo-500" %}
                            {% endif %}
                        {% endwith %}
                        {% if form.entity_type.errors %}
                            <p class="text-red-500 text-sm mt-1">
                                {% for error in form.entity_type.errors %}
                                    {{ error }}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </p>
                        {% endif %}
                    </div>
                </div>
                <!-- FIELD: Additional Entity Types (Multi-select Checkbox Dropdown) -->
                <div class="flex items-start gap-4 mb-5">
                    <label for="dropdownAdditionalEntityTypesButton"
                           class="w-1/3 text-sm font-medium text-gray-800 pt-2">
                        {{ form.additional_entity_types.label|default:"Entity Subtype" }}
                        {% if form.additional_entity_types.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    <div class="w-2/3">
                        <button id="dropdownAdditionalEntityTypesButton"
                                data-dropdown-toggle="dropdownAdditionalEntityTypes"
                                data-dropdown-placement="bottom-start"
                                class="border rounded-md bg-white px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 inline-flex justify-start items-center w-full min-h-[38px] {% if form.additional_entity_types.errors %}border-red-500 focus:ring-red-500{% else %}border-gray-300 focus:ring-indigo-500{% endif %}"
                                type="button">
                            <span class="dropdown-selected-text font-normal overflow-hidden text-left whitespace-nowrap flex-grow pr-2">Select Entity Subtypes</span>
                            <img src="{% static 'assets/images/down_arrow.svg' %}"
                                 class="h-3 w-3 text-gray-400 flex-shrink-0"
                                 alt="Toggle dropdown"
                                 height="12"
                                 width="12">
                        </button>
                        <!-- Dropdown menu -->
                        <div id="dropdownAdditionalEntityTypes"
                             class="z-50 hidden bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700 dark:divide-gray-600 border border-gray-300 max-h-60 overflow-y-auto mt-1">
                            <ul class="p-3 space-y-1 text-sm text-gray-700 dark:text-gray-200"
                                aria-labelledby="dropdownAdditionalEntityTypesButton">
                                {% for checkbox in form.additional_entity_types %}
                                    <li>
                                        <div class="flex items-center px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                                            {{ checkbox.tag }}
                                            <label for="{{ checkbox.id_for_label }}"
                                                   class="w-full ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">
                                                {{ checkbox.choice_label }}
                                            </label>
                                        </div>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% if form.additional_entity_types.errors %}
                            <p class="text-red-500 text-sm mt-1">
                                {% for error in form.additional_entity_types.errors %}
                                    {{ error }}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </p>
                        {% endif %}
                    </div>
                </div>
                <!-- FIELD: SSN or TIN -->
                <div class="flex items-start gap-4 mb-5">
                    <label class="w-1/3 text-sm font-medium text-gray-800 pt-2">SSN or TIN</label>
                    <div class="flex flex-col w-2/3 gap-3">
                        {{ form.ssn_or_itin.label }}
                        {% if form.ssn_or_tin.field.required %}<span class="text-red-500">*</span>{% endif %}
                        {% with base_classes="w-full px-4 py-2 border rounded-md shadow-sm bg-white" %}
                            {% if form.ssn_or_tin.errors %}
                                {% render_field form.ssn_or_tin class+=base_classes class+="border-red-500" placeholder="###########" %}
                            {% else %}
                                {% render_field form.ssn_or_tin class+=base_classes class+="border-gray-300" placeholder="###########" %}
                            {% endif %}
                        {% endwith %}
                        {% if form.ssn_or_tin.errors %}
                            <p class="text-red-500 text-sm mt-1">
                                {% for error in form.ssn_or_tin.errors %}
                                    {{ error }}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </p>
                        {% endif %}
                    </div>
                </div>
                <!-- FIELD: Address -->
                <div class="flex items-start gap-4 mb-5">
                    <label for="{{ form.address.id_for_label }}"
                           class="w-1/3 text-sm font-medium text-gray-800 pt-2">
                        {{ form.address.label|default:"Address" }}
                        {% if form.address.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    <div class="w-2/3">
                        {% with base_classes="w-full px-4 py-2 border rounded-md shadow-sm bg-white" %}
                            {% if form.address.errors %}
                                {% render_field form.address class+=base_classes class+="border-red-500" placeholder="Street address, City, State, ZIP Code" rows="1" style="overflow-y: hidden; resize: none;" %}
                            {% else %}
                                {% render_field form.address class+=base_classes class+="border-gray-300" placeholder="Street address, City, State, ZIP Code" rows="1" style="overflow-y: hidden; resize: none;" %}
                            {% endif %}
                        {% endwith %}
                        {% if form.address.errors %}
                            <p class="text-red-500 text-sm mt-1">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </p>
                        {% endif %}
                    </div>
                </div>
                <!-- FIELD: Phone Number -->
                <div class="flex items-start gap-4 mb-5">
                    <label for="{{ form.phone.id_for_label }}"
                           class="w-1/3 text-sm font-medium text-gray-800 pt-2">
                        {{ form.phone.label|default:"Phone Number" }}
                        {% if form.phone.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    <div class="w-2/3">
                        {% with base_classes="w-full px-4 py-2 border rounded-md shadow-sm bg-white" %}
                            {% if form.phone.errors %}
                                {% render_field form.phone class+=base_classes class+="border-red-500" placeholder="(XXX) XXX-XXXX" %}
                            {% else %}
                                {% render_field form.phone class+=base_classes class+="border-gray-300" placeholder="(XXX) XXX-XXXX" %}
                            {% endif %}
                        {% endwith %}
                        {% if form.phone.errors %}
                            <p class="text-red-500 text-sm mt-1">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </p>
                        {% endif %}
                    </div>
                </div>
                <!-- FIELD: Email -->
                <div class="flex items-start gap-4 mb-5">
                    <label for="{{ form.email.id_for_label }}"
                           class="w-1/3 text-sm font-medium text-gray-800 pt-2">
                        {{ form.email.label|default:"Primary Email Address" }}
                        {% if form.email.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    <div class="w-2/3">
                        {% with base_classes="w-full px-4 py-2 border rounded-md shadow-sm bg-white" %}
                            {% if form.email.errors %}
                                {% render_field form.email class+=base_classes class+="border-red-500" placeholder="<EMAIL>" %}
                            {% else %}
                                {% render_field form.email class+=base_classes class+="border-gray-300" placeholder="<EMAIL>" %}
                            {% endif %}
                        {% endwith %}
                        {% if form.email.errors %}
                            <p class="text-red-500 text-sm mt-1">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </p>
                        {% endif %}
                    </div>
                </div>
                <!-- FIELD: DOB -->
                <div class="flex items-start gap-4 mb-5">
                    <label for="{{ form.dob.id_for_label }}"
                           class="w-1/3 text-sm font-medium text-gray-800 pt-2">Date of Birth</label>
                    <div class="w-2/3">
                        {% with base_classes="w-full px-4 py-2 border rounded-md shadow-sm bg-white" %}
                            {% if form.dob.errors %}
                                {% render_field form.dob class+=base_classes class+="border-red-500" %}
                            {% else %}
                                {% render_field form.dob class+=base_classes class+="border-gray-300" %}
                            {% endif %}
                        {% endwith %}
                        {% if form.dob.errors %}
                            <p class="text-red-500 text-sm mt-1">
                                {% for error in form.dob.errors %}
                                    {{ error }}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </p>
                        {% endif %}
                    </div>
                </div>
                <!-- Actions -->
                <div class="flex justify-between items-center mt-6">
                    <a href="{% url 'settings_entities' %}"
                       hx-boost="false"
                       class="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md text-sm font-medium inline-block">Back</a>
                    <button type="button"
                            {% if has_linked_line_items %} disabled title="This entity cannot be deleted because it has linked line items." class="px-5 py-2 bg-red-300 text-white rounded-md shadow-inner text-sm font-medium cursor-not-allowed" {% else %} title="Delete this entity" class="px-5 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md shadow-inner text-sm font-medium" hx-delete="{% url 'settings_entity' entity.id %}" hx-confirm="Are you sure you want to delete this entity? It will also delete associated line items." {% endif %}>
                        Delete
                    </button>
                    <button type="submit"
                            class="px-5 py-2 bg-[#924f34] text-white rounded-md shadow-inner text-sm font-medium">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>
{% endblock settings_content %}
