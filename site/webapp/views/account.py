from typing import NamedTuple

import structlog
from django.contrib.auth.password_validation import ValidationError, get_default_password_validators
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse

from webapp.models import BridgeUser

logger = structlog.get_logger(__name__)


class ValidationResult(NamedTuple):
    name: str
    is_valid: bool
    text: str


def validate_password(request: HttpRequest) -> HttpResponse:
    template = "account/password_reset_validators.html"
    button_text = request.POST.get("button_text")
    password1 = request.POST.get("password1")
    password2 = request.POST.get("password2")
    show_tos_checkbox = request.POST.get("show_tos_checkbox") == "true"
    is_tos_checked = show_tos_checkbox and request.POST.get("tos_agreement") == "on"
    logger.info("Validate password", show_tos_checkbox=show_tos_checkbox, post=request.POST)

    validation_results = []

    # Track overall validation status
    all_valid = True

    for v in get_default_password_validators():
        valid = None
        try:
            v.validate(password1, user=BridgeUser)
            valid = True
        except ValidationError:
            valid = False
            all_valid = False  # If any validation fails, overall status is False
        validation_results.append(ValidationResult(v.__class__.__name__, valid, v.get_help_text()))

    # Check if passwords match
    passwords_match = password1 == password2
    validation_results.append(ValidationResult("Passwords match", passwords_match, "Passwords must match"))
    if not passwords_match:
        all_valid = False

    if show_tos_checkbox and not is_tos_checked:
        validation_results.append(
            ValidationResult("Terms of Service", is_tos_checked, "You must accept the Terms of Service")
        )
        logger.info("Terms of Service not accepted")
        all_valid = False

    # Pass validation results and overall status to the template
    return TemplateResponse(
        request,
        template,
        {
            "validation_results": validation_results,
            "all_valid": all_valid,
            "button_text": button_text,
            "show_tos_checkbox": show_tos_checkbox,
            "is_tos_checked": is_tos_checked,
        },
    )
