import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON><PERSON>ator
from datetime import timezone
from typing import override
from zipfile import ZipFile

import structlog
from dateutil import parser
from playwright.async_api import Locator
from webapp.models.documents import DocumentType
from webapp.models.portal import MFAType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.get_logger(__name__)


@register_strategy
class AlterDomusPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "alterdomus.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "Alter Domus CPF IV"),
            ("<EMAIL>", "Delaware Trust (AlterDomus)"),
            ("<EMAIL>", "AlterDomus"),
        ]

    @classmethod
    def email_otp_rules(cls) -> dict:
        return {
            # Apparently, the they use this email address to send OTPs.
            # Here's an example: lnfigj4s0hdklasqvhnm7fonntffe7ohlu6hu881
            "senderContains": ["<EMAIL>"],
        }

    async def _enter_credentials(self, username: str, password: str) -> None:
        username_locator = self.page.get_by_placeholder("Email Address")
        if await username_locator.count() != 1:
            logger.error("Cannot find username locator")
            raise ValueError
        await username_locator.click()
        await username_locator.fill(username)
        await asyncio.sleep(1)
        password_locator = self.page.get_by_placeholder("Password")
        if await password_locator.count() != 1:
            logger.error("Cannot find password locator")
            raise ValueError
        await password_locator.click()
        await password_locator.fill(password)
        await asyncio.sleep(1)
        button_locator = self.page.locator("button[type='submit']")
        if await button_locator.count() != 1:
            logger.error("Cannot find submit button")
            raise ValueError
        await button_locator.click()
        await asyncio.sleep(5)
        email_locator = self.page.get_by_text("Use E-Mail verification")
        if await email_locator.count() == 1:
            await email_locator.click()
        await asyncio.sleep(5)

    async def _check_has_otp(self) -> bool:
        await asyncio.sleep(10)
        return (
            sum(
                [
                    await self.page.locator("#UsePhone1MFA").count(),
                    await self.page.get_by_placeholder("Verification code").count(),
                ]
            )
            > 0
        )

    async def _send_otp(self) -> None:
        # Not used here as it is automatically sent, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(5)
        if (
            await self.page.locator("#UsePhone1MFA").count() > 0
            and await self.get_multi_factor_authentication_type() == MFAType.SMS
        ):
            await asyncio.sleep(1)
            await self.page.locator("#UsePhone1MFA").click(force=True)
            await asyncio.sleep(10)
            await self.page.locator("#verifyCode").click(force=True)

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        verification_input = self.page.get_by_placeholder("Verification code")
        if await verification_input.count() != 1:
            verification_input = self.page.locator("#verificationCode")
            if await verification_input.count() != 1:
                logger.error("Could not find verification input")
                raise ValueError
        await verification_input.click()
        await verification_input.press_sequentially(otp, delay=100)
        await self.page.wait_for_load_state(state="load")
        await asyncio.sleep(1)
        verify_button = self.page.get_by_text("Verify code")
        try:
            if await verify_button.count() != 1:
                logger.error("could not find verify button")
                await asyncio.sleep(5)
            else:
                await verify_button.click()
        except Exception:
            logger.exception("Expected Error: could not click verify button")

    @override
    async def _resend_otp(self) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(5)
        resend_button = self.page.locator("#retryCode")
        if await resend_button.count() != 1:
            logger.error("Could not find resend button")
            raise ValueError
        await resend_button.click(force=True)
        await asyncio.sleep(5)

    async def _check_login_successful(self) -> bool:
        await asyncio.sleep(10)
        return await self.page.get_by_text("Document Center").count() > 0

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        # Wait for this POS portal to load.
        await asyncio.sleep(30)
        popup = self.page.locator("button[aria-label='close']")
        if await popup.count() > 0:
            logger.info("Popup found, closing")
            await popup.click(force=True)
            await asyncio.sleep(5)
        await self.page.get_by_text("Document Center").first.click()
        await asyncio.sleep(7)
        await self._configure_columns()
        await asyncio.sleep(1)
        async for raw_doc_tuple in self._all_doc_iterator():
            if raw_doc_tuple is not None:
                yield raw_doc_tuple
        await self.page.get_by_text("Tax Center").first.click()
        await asyncio.sleep(7)
        await self._configure_columns()
        await asyncio.sleep(1)
        async for raw_doc_tuple in self._all_doc_iterator():
            if raw_doc_tuple is not None:
                yield raw_doc_tuple

    async def _configure_columns(self) -> None:
        await self.page.get_by_text("Configure Columns").click()
        await asyncio.sleep(1)
        unchecked_fn = self.page.locator("div.fileName").locator("span.ag-icon-checkbox-unchecked")
        if await unchecked_fn.count() == 1 and await unchecked_fn.is_visible():
            await unchecked_fn.click()
            await asyncio.sleep(1)
        unchecked_folder = self.page.locator("div.folder").locator("span.ag-icon-checkbox-unchecked")
        if await unchecked_folder.count() == 1 and await unchecked_folder.is_visible():
            await unchecked_folder.click()
            await asyncio.sleep(1)
        await self.page.get_by_role("button", name="Update").click()

    async def _all_doc_iterator(self) -> AsyncGenerator[RawDocumentTuple, None]:
        names = await self.page.locator("div.ag-header").locator("span.ag-header-cell-text").all()
        headers = []
        for name in names:
            header = await name.inner_text()
            if header:
                headers.append(header)
        table = self.page.locator("div[role='rowgroup']").filter(
            has=self.page.locator("div[role='row']"), has_not=self.page.locator("div.ag-header-row")
        )
        seen_row_ids = set()
        unseen_rows = await self._create_unseen_rows(table, seen_row_ids)
        while unseen_rows:
            row_id = unseen_rows.pop(0)
            seen_row_ids.add(row_id)
            doc = await self._get_document(table, headers, row_id)
            if doc:
                yield doc

            unseen_rows = await self._create_unseen_rows(table, seen_row_ids)

    async def _get_document(self, table: Locator, headers: list[str], row_id: str) -> RawDocumentTuple | None:
        row = table.locator(f"div[role='row'][row-id='{row_id}']")
        if await row.count() != 1 or not await row.is_visible():
            logger.info("Row not visible or distinct", row_id=row_id)
        await row.hover()
        await asyncio.sleep(1)
        row_data = []
        for e in await row.locator("span.ag-cell-value").all():
            text = await e.inner_text()
            text = text.encode("ascii", "ignore").decode("ascii")
            if text:
                row_data.append(text)
        try:
            row_metadata = dict(zip(headers, row_data, strict=True))
        except ValueError:
            logger.exception("Could not map row data to headers", row_data=row_data, headers=headers)
        json_metadata = json.dumps(row_metadata)
        if not await self.should_download_document(json_metadata):
            return None
        await row.locator("input[type='checkbox']").click()
        await asyncio.sleep(1)
        return await self._download_document(row_metadata, json_metadata)

    async def _download_document(self, metadata: dict[str, str], json_metadata: str) -> RawDocumentTuple | None:
        async with self.page.expect_download() as download_info:
            await self.page.get_by_text("Download Selected", exact=False).click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        zip_file_name = None
        with ZipFile(file_name, "r") as zf:
            zip_file_names = [zfn for zfn in zf.namelist() if not (zfn.endswith("/") or zfn.startswith("__MACOSX/"))]
            if len(zip_file_names) != 1:
                logger.error("Multiple files in zip", zip_file_name=zip_file_names)
                raise ValueError
            zip_file_name = zip_file_names[0]
            with zf.open(zip_file_name) as pdf_file:
                content = pdf_file.read()
        file_name.unlink()
        date = None
        try:
            date = parser.parse(metadata.get("Posted Date"))
        except TypeError:
            date, _ = self._get_date_from_list(metadata.values())
        if date is None:
            date = timezone.now()
        return RawDocumentTuple(
            name=zip_file_name,
            date=date,
            doc_type=self._document_mapper(metadata),
            content=content,
            raw_metadata=json_metadata,
            doc_hash=self._doc_hash(json_metadata),
        )

    def _get_date_from_list(
        self, maybe_dates: list[str], max_date: datetime.datetime | None = None, raw_max_date: str | None = None
    ) -> datetime.datetime:
        for maybe_date_text in maybe_dates:
            try:
                dt = parser.parse(maybe_date_text)
                if max_date is None or dt > max_date:
                    max_date = dt
                    raw_max_date = maybe_date_text
            except parser.ParserError:
                continue
        return max_date, raw_max_date

    def _document_mapper(self, metadata: dict) -> str:
        keywords = {
            "amendments": (DocumentType.OTHER, 3),
            "capital account and financial statements": (DocumentType.ACCOUNT_STATEMENT, 1),
            "capital account statement": (DocumentType.ACCOUNT_STATEMENT, 1),
            "capital account statements": (DocumentType.ACCOUNT_STATEMENT, 1),
            "capital call": (DocumentType.CAPITAL_CALL, 1),
            "capital calls": (DocumentType.CAPITAL_CALL, 1),
            "compliance/legal": (DocumentType.LEGAL, 3),
            "distribution notice": (DocumentType.DISTRIBUTION_NOTICE, 1),
            "distribution notices": (DocumentType.DISTRIBUTION_NOTICE, 1),
            "distributions": (DocumentType.DISTRIBUTION_NOTICE, 1),
            "fee letter": (DocumentType.INVESTMENT_UPDATE, 3),
            "financial statements": (DocumentType.FINANCIAL_STATEMENTS, 3),
            "form adv": (DocumentType.LEGAL, 3),
            "fund documents": (DocumentType.INVESTMENT_UPDATE, 3),
            "investor letter": (DocumentType.INVESTMENT_UPDATE, 3),
            "investor letters": (DocumentType.INVESTMENT_UPDATE, 3),
            "partner capital statement": (DocumentType.ACCOUNT_STATEMENT, 1),
            "privacy policy": (DocumentType.OTHER, 3),
            "quarterly financials": (DocumentType.FINANCIAL_STATEMENTS, 3),
            "schedule k-1": (DocumentType.TAX, 3),
            "side letter": (DocumentType.INVESTMENT_UPDATE, 3),
            "subscription agreement": (DocumentType.LEGAL, 2),
            "subscription document": (DocumentType.LEGAL, 2),
            "tax communication": (DocumentType.TAX, 1),
            "tax documents": (DocumentType.TAX, 1),
        }
        doc_types = []
        for keyword, (doc_type, priority) in keywords.items():
            for value in metadata.values():
                if keyword in value.lower():
                    doc_types.append((doc_type, priority))  # noqa: PERF401
        if len(doc_types) == 0:
            return DocumentType.UNKNOWN
        return sorted(doc_types, key=lambda x: x[1])[0][0]

    async def _create_unseen_rows(self, table: Locator, seen: set[str]) -> list[str]:
        row_ids = []
        for e in await table.locator("div[role='row']").all():
            if not await e.is_visible():
                continue
            row_id = await e.get_attribute("row-id")
            if row_id not in seen:
                row_ids.append(row_id)
        return sorted(row_ids)
