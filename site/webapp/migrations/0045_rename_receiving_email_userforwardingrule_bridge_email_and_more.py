# Generated by Django 5.2.1 on 2025-06-05 21:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0044_retrieval_is_backfill'),
    ]

    operations = [
        migrations.RenameField(
            model_name='userforwardingrule',
            old_name='receiving_email',
            new_name='bridge_email',
        ),
        migrations.RenameField(
            model_name='userforwardingrule',
            old_name='from_email',
            new_name='receiving_email',
        ),
        migrations.AddField(
            model_name='mergedportalcredential',
            name='user_forwarding_rule',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='merged_portal_credential', to='webapp.userforwardingrule'),
        ),
        migrations.AlterField(
            model_name='emailretrieval',
            name='line_item',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='email_retrieval', to='webapp.lineitem'),
        ),
    ]
