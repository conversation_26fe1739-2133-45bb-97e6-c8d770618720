import * as cdk from "aws-cdk-lib";
import {
  NetworkStackOutputs,
  ConfigStackOutputs,
  SFTPOutputs,
  SharedProps,
  SharedStack,
  CeleryServiceStackOutputs,
} from "./schema";
import { Construct } from "constructs";
import * as iam from "aws-cdk-lib/aws-iam";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as transfer from "aws-cdk-lib/aws-transfer";
import * as s3 from "aws-cdk-lib/aws-s3";

export class SftpServerStack extends SharedStack {
  constructor(scope: Construct, id: string, props: SharedProps) {
    super(scope, id, props);

    // Retrieve outputs from the network and config stacks.
    const networkOutputs = NetworkStackOutputs.fromCdkOutput(
      this,
      NetworkStackOutputs,
    );
    const configOutputs = ConfigStackOutputs.fromCdkOutput(
      this,
      ConfigStackOutputs,
    );
    const celeryOutputs = CeleryServiceStackOutputs.fromCdkOutput(
      this,
      CeleryServiceStackOutputs,
    );
    if (
      networkOutputs === undefined ||
      configOutputs === undefined ||
      celeryOutputs === undefined
    ) {
      return;
    }
    if (props.deploymentTarget === "demo") {
      return;
    }

    // Import from cluster stack
    const celeryRole = iam.Role.fromRoleArn(
      this,
      "ImportedCeleryRole",
      celeryOutputs.CeleryTaskRoleArn,
    );

    // Create a new S3 bucket to serve as the backend for SFTP storage.
    const sftpBucket = new s3.Bucket(this, "SftpBucket", {
      bucketName: configOutputs.SftpBucketName,
      encryption: s3.BucketEncryption.S3_MANAGED,
      removalPolicy: cdk.RemovalPolicy.RETAIN,
      versioned: true,
      autoDeleteObjects: false,
    });

    sftpBucket.grantReadWrite(celeryRole);

    // Import the VPC from network stack outputs.
    const vpc = ec2.Vpc.fromVpcAttributes(this, "Vpc", {
      vpcId: networkOutputs.VpcId,
      availabilityZones: networkOutputs.VpcAvailabilityZones,
      publicSubnetIds: networkOutputs.VpcPublicSubnetIds,
      publicSubnetRouteTableIds: networkOutputs.VpcPublicRouteTableIds,
    });

    // Create a security group for the SFTP server.
    const sftpSg = new ec2.SecurityGroup(this, "SftpSecurityGroup", {
      vpc,
      description:
        "Security group allowing SFTP access from allowed external IPs and internal VPC",
      allowAllOutbound: true,
    });

    // Allowed external IP addresses (using /32 CIDR notation).
    const allowedExternalIps = [
      // Orion IPs
      "**************/32",
      "***********/32",
      "************/32",
      "*************/32",
      "***********/32",

      // Nick's IP don't hack me pls
      "*************/32",
    ];
    allowedExternalIps.forEach((ip) => {
      sftpSg.addIngressRule(
        ec2.Peer.ipv4(ip),
        ec2.Port.tcp(22),
        "Allow SFTP access from external IP",
      );
    });

    // Allow internal VPC traffic.
    sftpSg.addIngressRule(
      ec2.Peer.ipv4(networkOutputs.VpcCidrBlock),
      ec2.Port.tcp(22),
      "Allow SFTP access from within VPC",
    );

    // Allocate an Elastic IP per public subnet for a static IP on the Transfer Family endpoint.
    const sftpEips = vpc.publicSubnets.map(
      (subnet) =>
        new ec2.CfnEIP(this, `SftpElasticIP-public-${subnet.subnetId}`),
    );

    // Create the AWS Transfer Family SFTP server with a VPC endpoint.
    const sftpServer = new transfer.CfnServer(this, "SftpServer", {
      protocols: ["SFTP"],
      endpointType: "VPC",
      endpointDetails: {
        vpcId: vpc.vpcId,
        subnetIds: vpc.publicSubnets.map((subnet) => subnet.subnetId),
        securityGroupIds: [sftpSg.securityGroupId],
        addressAllocationIds: sftpEips.map((eip) => eip.attrAllocationId),
      },
      identityProviderType: "SERVICE_MANAGED",
    });

    const sftpUsers = {
      "celery-user": {
        sshPublicKeys: [
          "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIIUJ8ZIWWroJyI7FFB6HwemVxUF20ss/8cn7E1vnQUgL <EMAIL>",
        ],
        homeDirectory: "sftp/celery-user",
      },
      "orion-service-account": {
        sshPublicKeys: [
          "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIMwhATL+oaYUDR4zTGZwhBv1QjkhDeXWIhMnR5+cjD2N Wayne.Schwarz@LT1162",
          "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIIUJ8ZIWWroJyI7FFB6HwemVxUF20ss/8cn7E1vnQUgL <EMAIL>",
        ],
        homeDirectory: "sftp/orion-service-account",
      },
    };

    for (const [userName, userDetails] of Object.entries(sftpUsers)) {
      // Create a new IAM role for SFTP access that grants permissions to the new S3 bucket.
      const sftpAccessRole = new iam.Role(this, `SftpAccessRole-${userName}`, {
        assumedBy: new iam.ServicePrincipal("transfer.amazonaws.com"),
        description:
          "Role that grants SFTP users access to the S3 bucket for file storage",
      });

      sftpAccessRole.addToPolicy(
        new iam.PolicyStatement({
          actions: ["s3:ListBucket"],
          resources: [sftpBucket.bucketArn],
          conditions: {
            StringLike: {
              "s3:prefix": [
                `${userDetails.homeDirectory}/*`,
                userDetails.homeDirectory,
              ],
            },
          },
        }),
      );
      // Allow basic object operations under the designated prefix.
      sftpAccessRole.addToPolicy(
        new iam.PolicyStatement({
          actions: [
            "s3:GetObject",
            "s3:PutObject",
            "s3:DeleteObject",
            "s3:GetObjectVersion",
          ],
          resources: [
            // Add both patterns to make sure all file access patterns are covered
            `${sftpBucket.bucketArn}/${userDetails.homeDirectory}/*`,
            `${sftpBucket.bucketArn}/${userDetails.homeDirectory}`, // Add this line to cover the directory itself
          ],
        }),
      );

      new transfer.CfnUser(this, `SftpUser-${userName}`, {
        serverId: sftpServer.attrServerId,
        userName,
        role: sftpAccessRole.roleArn,
        homeDirectoryType: "LOGICAL",
        homeDirectoryMappings: [
          {
            entry: "/",
            target: `/${sftpBucket.bucketName}/${userDetails.homeDirectory}`,
          },
        ],
        sshPublicKeys: userDetails.sshPublicKeys,
      });
    }

    // Output the SFTP server and static IP addresses.
    new SFTPOutputs({
      SftpStaticIpAddresses: sftpEips.map((eip) => eip.ref),
      SftpServerId: sftpServer.ref,
    }).toCdkOutput(this);
  }
}
