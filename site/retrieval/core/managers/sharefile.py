import ftplib  # nosec
import hashlib
import io
import json
import ssl
import urllib.parse
from collections.abc import AsyncGenerator
from pathlib import Path

import structlog
from dateutil import parser
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.get_logger(__name__)


# https://gist.github.com/hoogenm/de42e2ef85b38179297a0bba8d60778b
class ImplicitFTP_TLS(ftplib.FTP_TLS):  # noqa: N801
    """FTP_TLS subclass to support implicit FTPS."""

    """Constructor takes a boolean parameter ignore_PASV_host whether o ignore the hostname"""
    """in the PASV response, and use the hostname from the session instead"""

    def __init__(self, *args, **kwargs) -> None:  # noqa: ANN002, ANN003
        self.ignore_PASV_host = kwargs.get("ignore_PASV_host") == True  # noqa: E712
        super().__init__(*args, **{k: v for k, v in kwargs.items() if k != "ignore_PASV_host"})
        self._sock = None

    @property
    def sock(self):  # noqa: ANN201
        """Return the socket."""
        return self._sock

    @sock.setter
    def sock(self, value):  # type: ignore[reportIncompatibleVariableOverride] # noqa: ANN001, ANN202
        """When modifying the socket, ensure that it is ssl wrapped."""
        if value is not None and not isinstance(value, ssl.SSLSocket):
            value = self.context.wrap_socket(value)
        self._sock = value

    def ntransfercmd(self, cmd, rest=None) -> tuple[ssl.SSLSocket, int | None]:  # noqa: ANN001
        """Override the ntransfercmd method"""
        conn, size = ftplib.FTP.ntransfercmd(self, cmd, rest)  # nosec  # noqa: S321
        if self.sock is None:
            raise ValueError
        ssl_conn = self.sock.context.wrap_socket(
            conn,
            server_hostname=self.host,
            session=self.sock.session,
        )
        return ssl_conn, size

    def makepasv(self) -> tuple[str, int]:
        host, port = super().makepasv()
        return (self.host if self.ignore_PASV_host else host), port


@register_strategy
class ShareFilePortalManager(RetrievalManager):
    @classmethod
    def needs_playwright(cls) -> bool:
        return False

    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "sharefile.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "ShareFile"),
            ("<EMAIL>", "ShareFile"),
        ]

    @classmethod
    def email_otp_rules(cls) -> dict:
        return {}

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        pass

    async def _check_has_otp(self) -> bool:
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:
        pass

    async def _check_login_successful(self) -> bool:
        return True

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        ftps = await self.__get_ftp_conn()
        q = ["/"]
        i = 5
        while q:
            cur = q.pop()
            if cur.endswith(".pdf"):
                doc = await self.__download_doc(cur, ftps)
                if doc is not None:
                    yield doc
                continue
            try:
                ftps.cwd(cur)
                q.extend([str(Path(cur) / x) for x in ftps.nlst(cur)])
            except ftplib.all_errors:
                doc = await self.__download_doc(cur, ftps)
                if doc is not None:
                    yield doc
            if i <= 0:
                break

    async def __get_ftp_conn(self) -> ImplicitFTP_TLS:
        username, password, url = await self.get_username(), await self.get_password(), await self.get_url()
        if username is None or password is None:
            raise ManagerNotInitializedError
        instance_name = urllib.parse.urlparse(url).netloc.split(".")[0]
        host = f"{instance_name}.sharefileftp.com"
        port = 990
        ftp_username = f"{instance_name}/{username}"
        logger.info("Logging into FTP", host=host, port=port, ftp_username=ftp_username)
        ftps = ImplicitFTP_TLS()
        ftps.connect(host=host, port=port)
        ftps.login(user=ftp_username, passwd=password)
        ftps.prot_p()
        return ftps

    async def __download_doc(self, cur: str, ftps: ImplicitFTP_TLS) -> RawDocumentTuple | None:
        cur_path = Path(cur)
        name = cur_path.parts[-1]
        # HACK: very specific for FLP, let's see for Sherie
        metadata = json.dumps({f"Portal Level {i} Folder": part for i, part in enumerate(cur_path.parts)})
        if await self.should_download_document(metadata):
            logger.info("Downloading", current_path=cur)
            timestamp = ftps.voidcmd(f"MDTM {cur}")[4:].strip()
            file_in_memory = io.BytesIO()
            ftps.retrbinary(f"RETR {cur}", file_in_memory.write)
            file_in_memory.seek(0)
            return RawDocumentTuple(
                name=name,
                date=parser.parse(timestamp),
                doc_type=self.__document_mapper(list(cur_path.parts)),
                content=file_in_memory.getvalue(),
                raw_metadata=metadata,
                doc_hash=self._doc_hash(metadata),
            )
        logger.info("Skipping", current_path=cur)
        return None

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    def __document_mapper(self, folders: list[str]) -> str:
        keywords = {
            "amendments": (DocumentType.OTHER, 3),
            "audited financials": (DocumentType.FINANCIAL_STATEMENTS, 3),
            "capital account and financial statements": (DocumentType.ACCOUNT_STATEMENT, 1),
            "capital account statement": (DocumentType.ACCOUNT_STATEMENT, 1),
            "capital account statements": (DocumentType.ACCOUNT_STATEMENT, 1),
            "capital statements": (DocumentType.ACCOUNT_STATEMENT, 1),
            "capital call": (DocumentType.CAPITAL_CALL, 1),
            "capital calls": (DocumentType.CAPITAL_CALL, 1),
            "compliance/legal": (DocumentType.LEGAL, 3),
            "distribution notice": (DocumentType.DISTRIBUTION_NOTICE, 1),
            "distribution notices": (DocumentType.DISTRIBUTION_NOTICE, 1),
            "distributions": (DocumentType.DISTRIBUTION_NOTICE, 1),
            "fee letter": (DocumentType.INVESTMENT_UPDATE, 3),
            "financial statements": (DocumentType.FINANCIAL_STATEMENTS, 3),
            "form adv": (DocumentType.LEGAL, 3),
            "fund documents": (DocumentType.INVESTMENT_UPDATE, 3),
            "investor letter": (DocumentType.INVESTMENT_UPDATE, 3),
            "investor letters": (DocumentType.INVESTMENT_UPDATE, 3),
            "partner capital statement": (DocumentType.ACCOUNT_STATEMENT, 1),
            "privacy policy": (DocumentType.OTHER, 3),
            "quarterly financials": (DocumentType.FINANCIAL_STATEMENTS, 3),
            "quarterly reports": (DocumentType.FINANCIAL_STATEMENTS, 3),
            "schedule k-1": (DocumentType.TAX, 3),
            "side letter": (DocumentType.INVESTMENT_UPDATE, 3),
            "subscription agreement": (DocumentType.LEGAL, 2),
            "subscription document": (DocumentType.LEGAL, 2),
            "tax": (DocumentType.TAX, 1),
        }
        doc_types = []
        for keyword, (doc_type, priority) in keywords.items():
            for value in folders:
                if keyword in value.lower():
                    doc_types.append((doc_type, priority))  # noqa: PERF401
        if len(doc_types) == 0:
            return DocumentType.UNKNOWN
        return sorted(doc_types, key=lambda x: x[1])[0][0]
