import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import {
  NetworkStackOutputs,
  ClusterStackOutputs,
  StorageStackOutputs,
  SiteServiceStackOutputs,
  SharedProps,
  SharedStack,
  ConfigStackOutputs,
} from "./schema";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as logs from "aws-cdk-lib/aws-logs";
import * as iam from "aws-cdk-lib/aws-iam";
import * as ecr from "aws-cdk-lib/aws-ecr";
import * as kms from "aws-cdk-lib/aws-kms";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import * as s3 from "aws-cdk-lib/aws-s3";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as elbv2 from "aws-cdk-lib/aws-elasticloadbalancingv2";
import * as route53 from "aws-cdk-lib/aws-route53";
import * as targets from "aws-cdk-lib/aws-route53-targets";
import * as acm from "aws-cdk-lib/aws-certificatemanager";

export class SiteServiceStack extends SharedStack {
  constructor(scope: Construct, id: string, props: SharedProps) {
    super(scope, id, props);
    // From network-stack
    const networkOutputs = NetworkStackOutputs.fromCdkOutput(
      this,
      NetworkStackOutputs,
    );
    const clusterOutputs = ClusterStackOutputs.fromCdkOutput(
      this,
      ClusterStackOutputs,
    );
    const storageOutputs = StorageStackOutputs.fromCdkOutput(
      this,
      StorageStackOutputs,
    );
    const configOutputs = ConfigStackOutputs.fromCdkOutput(
      this,
      ConfigStackOutputs,
    );
    if (
      networkOutputs === undefined ||
      configOutputs === undefined ||
      clusterOutputs === undefined ||
      storageOutputs === undefined
    ) {
      return;
    }
    const vpc = ec2.Vpc.fromVpcAttributes(this, "Vpc", {
      vpcId: networkOutputs.VpcId,
      availabilityZones: networkOutputs.VpcAvailabilityZones,
      publicSubnetIds: networkOutputs.VpcPublicSubnetIds,
      publicSubnetRouteTableIds: networkOutputs.VpcPublicRouteTableIds,
    });

    // Recreate the ECS Cluster
    const cluster = ecs.Cluster.fromClusterAttributes(this, "Cluster", {
      clusterName: clusterOutputs.ClusterName,
      vpc: vpc,
    });

    const executionRole = iam.Role.fromRoleArn(
      this,
      "ImportedExecutionRole",
      clusterOutputs.ExecutionRoleArn,
    );

    const siteTaskRole = new iam.Role(this, "SiteTaskRole", {
      assumedBy: new iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
      description: "IAM role for the website with necessary permissions",
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName("AmazonSSMReadOnlyAccess"),
        iam.ManagedPolicy.fromAwsManagedPolicyName("AmazonSESFullAccess"),
      ],
    });

    siteTaskRole.addToPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        resources: [
          `arn:aws:secretsmanager:${this.region}:${this.account}:secret:User*`,
        ],
        actions: [
          "secretsmanager:CreateSecret",
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret",
          "secretsmanager:UpdateSecret",
        ],
      }),
    );

    const bedrockPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      resources: ["*"],
      actions: ["bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream"],
    });
    siteTaskRole.addToPolicy(bedrockPolicy);

    kms.Key.fromKeyArn(
      this,
      "userDataKms",
      storageOutputs.UserDataKmsArn,
    ).grantEncrypt(siteTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedDbCredentialsSecret",
      storageOutputs.DBCredentialsSecretArn,
    ).grantRead(siteTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedRedisSecret",
      storageOutputs.RedisSecretArn,
    ).grantRead(siteTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedMsftOauthSecret",
      configOutputs.MsftEmailOauthSecretArn,
    ).grantRead(siteTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedRedisClusterSecret",
      configOutputs.DevTwilioSecretArn,
    ).grantRead(siteTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedSlackSecret",
      configOutputs.SlackSecretArn,
    ).grantRead(siteTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedDjangoSecret",
      configOutputs.DjangoSecretArn,
    ).grantRead(siteTaskRole);

    s3.Bucket.fromBucketName(
      this,
      "S3Bucket",
      configOutputs.UserBucketName,
    ).grantReadWrite(siteTaskRole);

    const siteLogGroup = new logs.LogGroup(this, "SiteLogGroup", {
      retention: logs.RetentionDays.ONE_YEAR,
    });
    let memoryLimitMiB = 4096;
    let cpu = 2048;
    if (props.deploymentTarget === "demo") {
      memoryLimitMiB = 2048;
      cpu = 1024;
    }
    const siteTaskDefinition = new ecs.FargateTaskDefinition(
      this,
      "SiteTaskDefinition",
      {
        memoryLimitMiB,
        cpu,
        executionRole,
        taskRole: siteTaskRole,
      },
    );

    const siteRepository = ecr.Repository.fromRepositoryName(
      this,
      "SiteRepository",
      configOutputs.PythonContainerRepositoryName,
    );
    // add the container
    siteTaskDefinition.addContainer("SiteContainer", {
      image: ecs.ContainerImage.fromRegistry(
        siteRepository.repositoryUriForTag("latest"),
      ),
      portMappings: [{ containerPort: 8000, protocol: ecs.Protocol.TCP }],
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: "site-container",
        logGroup: siteLogGroup,
      }),
      environment: {
        ENVIRONMENT: configOutputs.DeploymentTarget,
        APPLICATION: "site",
      },
    });

    // Create the load balancer
    const siteSecurityGroup = new ec2.SecurityGroup(this, "SiteSecurityGroup", {
      vpc: vpc,
      description:
        "Security group for service allowing inbound HTTP/HTTPS traffic",
      allowAllOutbound: true,
    });

    siteSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(80),
      "Allow inbound HTTP traffic",
    );
    siteSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(443),
      "Allow inbound HTTPS traffic",
    );

    // Create a Fargate service
    const siteService = new ecs.FargateService(this, "SiteService", {
      cluster: cluster,
      taskDefinition: siteTaskDefinition,
      desiredCount: 1,
      vpcSubnets: {
        subnets: vpc.publicSubnets, // these are the subnets with access through the vpc endpoints
      },
      securityGroups: [siteSecurityGroup],
      assignPublicIp: true, // required to initiate outbound internet access
      // TODO: we should disable this for real production
      enableExecuteCommand: true,
      platformVersion: ecs.FargatePlatformVersion.VERSION1_4,
    });

    const targetGroup = new elbv2.ApplicationTargetGroup(this, "TargetGroup", {
      vpc: vpc,
      port: 8000,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targets: [
        siteService.loadBalancerTarget({
          containerName: "SiteContainer",
          containerPort: 8000,
        }),
      ],
      healthCheck: {
        path: "/up",
        interval: cdk.Duration.seconds(120),
        timeout: cdk.Duration.seconds(60),
        protocol: elbv2.Protocol.HTTP,
        healthyThresholdCount: 2,
        unhealthyThresholdCount: 2,
        healthyHttpCodes: "200-499",
      },
    });

    const alb = new elbv2.ApplicationLoadBalancer(this, "ALB", {
      vpc: vpc,
      internetFacing: true,
      vpcSubnets: {
        subnets: vpc.publicSubnets,
      },
      securityGroup: siteSecurityGroup,
    });

    // // Get the hosted zone
    const hostedZone = route53.HostedZone.fromHostedZoneAttributes(
      this,
      "HostedZone",
      {
        hostedZoneId: props.hostedZoneId,
        zoneName: props.domainName,
      },
    );

    // Create ACM certificate
    const certificate = new acm.Certificate(this, "Certificate", {
      domainName: props.domainName,
      validation: acm.CertificateValidation.fromDns(hostedZone),
      subjectAlternativeNames: [`www.${props.domainName}`],
    });

    // HTTPS listener will handle the main traffic
    const httpsListener = alb.addListener("HttpsListener", {
      port: 443,
      certificates: [certificate],
      defaultTargetGroups: [targetGroup],
    });

    // HTTP listener will handle redirects
    const httpListener = alb.addListener("HttpListener", {
      port: 80,
      open: true,
      defaultAction: elbv2.ListenerAction.redirect({
        protocol: "HTTPS",
        port: "443",
        host: "#{host}",
        permanent: true,
      }),
    });

    // this acction redirects http://www. to https://
    httpListener.addAction("HttpWwwToHttpsNonWwwRedirect", {
      priority: 10,
      conditions: [
        elbv2.ListenerCondition.hostHeaders([`www.${props.domainName}`]),
      ],
      action: elbv2.ListenerAction.redirect({
        protocol: "HTTPS",
        port: "443",
        host: props.domainName,
        permanent: true,
      }),
    });

    // redirect https://www. -> https://
    httpsListener.addAction("WwwToNonWwwRedirect", {
      priority: 10,
      conditions: [
        elbv2.ListenerCondition.hostHeaders([`www.${props.domainName}`]),
      ],
      action: elbv2.ListenerAction.redirect({
        host: props.domainName,
        permanent: true,
      }),
    });

    // redirect http:// -> https://
    httpListener.addAction("HttpToHttpsRedirect", {
      action: elbv2.ListenerAction.redirect({
        protocol: "HTTPS",
        port: "443",
        permanent: true,
      }),
    });

    // Create Route53 alias record for the ALB
    new route53.ARecord(this, "AliasRecord", {
      zone: hostedZone,
      target: route53.RecordTarget.fromAlias(
        new targets.LoadBalancerTarget(alb),
      ),
      recordName: props.domainName,
    });

    // Create Route53 alias record for www subdomain
    new route53.ARecord(this, "WwwAliasRecord", {
      zone: hostedZone,
      target: route53.RecordTarget.fromAlias(
        new targets.LoadBalancerTarget(alb),
      ),
      recordName: `www.${props.domainName}`,
    });
    new SiteServiceStackOutputs({
      LoadBalancerName: alb.loadBalancerName,
    }).toCdkOutput(this);
  }
}
