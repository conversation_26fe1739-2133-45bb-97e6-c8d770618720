import os

from django.core.exceptions import ImproperlyConfigured

match os.environ["ENVIRONMENT"]:
    case "prod" | "demo":
        from .cloud import *  # noqa: F403
    case "test":
        from .test import *  # noqa: F403
    case "cicd":
        from .cicd import *  # noqa: F403
    case "dev" | "likeprod":
        match os.environ.get("AWS_PROFILE", "default"):
            case "default":
                from .dev import *  # noqa: F403
            case "prod" | "demo":
                from .aws_profile import *  # noqa: F403
            case _:
                raise ImproperlyConfigured
    case _:
        raise ImproperlyConfigured
