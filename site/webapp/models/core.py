import os
from abc import abstractmethod
from typing import Any, ClassVar

import boto3
import structlog
from asgiref.sync import sync_to_async
from botocore.exceptions import ClientError
from django.conf import settings
from django.db import models
from django.utils import timezone

from webapp.models.user import BridgeUser, ModelABCMeta, Organization, Role, SimpleBaseModel

logger = structlog.get_logger(__name__)


class PermissionsQuerySet(models.QuerySet):
    def active(self) -> models.QuerySet:
        return self.filter(deleted_at__isnull=True)

    def deleted(self) -> models.QuerySet:
        return self.filter(deleted_at__isnull=False)


def get_permissions_read(user: BridgeUser) -> models.Q:
    # TODO: Apply permissions on a per object basis
    user_roles = user.roles.values_list("name", flat=True)
    read_roles = {Role.RoleName.MANAGER, Role.RoleName.VIEWER}
    if any(role in read_roles for role in user_roles):
        return models.Q(organization=user.organization)
    # If the user has no roles, they should not have access to any documents
    return models.Q(pk__in=[])


def has_write_permissions(user: BridgeUser) -> bool:
    # TODO: apply permissions on a per object basis
    user_roles = user.roles.values_list("name", flat=True)
    write_roles = {Role.RoleName.MANAGER, Role.RoleName.VIEWER}
    return any(role in write_roles for role in user_roles)


class PermissionManager(models.Manager):
    def get_queryset(self) -> models.QuerySet:
        return PermissionsQuerySet(self.model, using=self._db).all()

    @sync_to_async
    def afor_user(self, user: BridgeUser) -> models.QuerySet:
        return self.for_user(user)

    def for_user(self, user: BridgeUser) -> models.QuerySet:
        q = models.Q(organization=user.organization)

        if hasattr(self.model, "deleted_at"):
            q &= models.Q(deleted_at__isnull=True)

        if hasattr(self.model, "is_visible"):
            q &= models.Q(is_visible=True)

        if user.is_manager:
            return self.get_queryset().filter(q)

        from webapp.models.line_item import LineItemPermission

        permitted_line_item_ids = LineItemPermission.objects.filter(permittee=user).values_list(
            "line_item_id", flat=True
        )

        paths = self.model.get_line_item_paths()
        if not paths or not permitted_line_item_ids.exists():
            return self.get_queryset().none()

        for path in paths:
            q &= models.Q(**{f"{path}__in": permitted_line_item_ids})

        return self.get_queryset().filter(q).distinct()

    def active(self) -> models.QuerySet:
        q = models.Q()
        if hasattr(self.model, "deleted_at"):
            q &= models.Q(deleted_at__isnull=True)

        return self.get_queryset().filter(q)

    def create(self, **kwargs) -> Any:  # noqa: ANN003, ANN401
        user = kwargs.pop("user", None)
        if user is None:
            msg = "User must be provided"
            raise PermissionError(msg)
        if not has_write_permissions(user):
            msg = "User does not have write permissions"
            raise PermissionError(msg)
        kwargs["organization"] = user.organization
        kwargs["updated_by"] = user
        kwargs["created_by"] = user
        return super().create(**kwargs)

    def update_or_create(self, defaults=None, create_defaults=None, **kwargs) -> tuple[Any, bool]:  # noqa: ANN003, ANN001
        user = kwargs.pop("user", None)
        if user is None:
            msg = "User must be provided"
            raise PermissionError(msg)
        if not has_write_permissions(user):
            msg = "User does not have write permissions"
            raise PermissionError(msg)
        if create_defaults is not None:
            create_defaults = dict(create_defaults)
            create_defaults["organization"] = user.organization
            create_defaults["updated_by"] = user
            create_defaults["created_by"] = user
        elif create_defaults is None and defaults is not None:
            create_defaults = dict(defaults)
            create_defaults["organization"] = user.organization
            create_defaults["updated_by"] = user
            create_defaults["created_by"] = user
        elif create_defaults is None and defaults is None:
            create_defaults = {
                "organization": user.organization,
                "created_by": user,
                "updated_by": user,
            }
        if defaults is not None:
            defaults = dict(defaults)
            defaults["organization"] = user.organization
            defaults["updated_by"] = user
        elif defaults is None:
            defaults = {
                "organization": user.organization,
                "updated_by": user,
            }
        kwargs["organization"] = user.organization
        return super().update_or_create(defaults=defaults, create_defaults=create_defaults, **kwargs)

    def get_or_create(self, defaults=None, **kwargs) -> tuple[Any, bool]:  # noqa: ANN003, ANN001
        user = kwargs.pop("user", None)
        if user is None:
            msg = "User must be provided"
            raise PermissionError(msg)
        if not has_write_permissions(user):
            msg = "User does not have write permissions"
            raise PermissionError(msg)
        kwargs["organization"] = user.organization
        if defaults is not None:
            defaults = dict(defaults)
            defaults["organization"] = user.organization
            defaults["created_by"] = user
            defaults["updated_by"] = user
        return super().get_or_create(defaults=defaults, **kwargs)


class AbstractBaseModel(SimpleBaseModel, metaclass=ModelABCMeta):
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name="%(class)s_created_by", on_delete=models.SET_NULL, null=True
    )
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name="%(class)s_updated_by", on_delete=models.SET_NULL, null=True
    )
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name="%(class)s")
    objects_unsafe = models.Manager()
    objects: ClassVar[PermissionManager] = PermissionManager()

    class Meta(SimpleBaseModel.Meta):
        abstract = True
        default_manager_name = "objects"

    def save(self, *args, **kwargs) -> None:  # noqa: ANN003, ANN002
        # TODO: Override save with user to prevent wrong permissions
        """
        User = kwargs.pop("user", None)
        if user is None:
            msg = "User must be provided"
            raise PermissionError(msg)
        if not has_write_permissions(user):
            msg = "User does not have write permissions"
            raise PermissionError(msg)
        """
        super().save(*args, **kwargs)

    def soft_delete(self) -> None:
        self.deleted_at = timezone.now()
        self.save()

    @classmethod
    @abstractmethod
    def get_line_item_paths(cls) -> list[str]:
        """
        Child classes must implement this method to specify which fields contain
        line item references to support permissions.
        """


class SecretAbstractBaseModel(AbstractBaseModel, metaclass=ModelABCMeta):
    SECRET_VERSION = 1
    secret_arn = models.CharField(max_length=255, blank=True)
    secret_format_version = models.IntegerField(default=0)

    class Meta(AbstractBaseModel.Meta):
        abstract = True
        default_manager_name = "objects"

    def __str__(self) -> str:
        return self.secret_arn

    def get_secret_name(self) -> str:
        return f"User-{type(self).__name__}-{self.pk}"

    def get_secret(self) -> str:
        region_name = os.environ.get("AWS_REGION", "us-east-1")
        session = boto3.session.Session()
        client = session.client(
            service_name="secretsmanager",
            region_name=region_name,
        )
        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=self.secret_arn,
            )
        except ClientError:
            logger.exception("Secret Client error", secret=self.secret_arn, action="getting")
            raise
        else:
            return get_secret_value_response["SecretString"]

    def set_secret(self, secret: str) -> None:
        region_name = os.environ.get("AWS_REGION", "us-east-1")
        session = boto3.session.Session()
        client = session.client(
            service_name="secretsmanager",
            region_name=region_name,
        )

        secret_name = self.get_secret_name()
        does_secret_exist = self.has_secret()
        if does_secret_exist:
            secret_name = self.secret_arn
            logger.info("Secret already exists", secret_name=secret_name, from_db=True, from_boto=False)
        try:
            client.describe_secret(SecretId=secret_name)
            does_secret_exist = True
            logger.info("Secret already exists", secret_name=secret_name, from_db=False, from_boto=True)
        except ClientError:
            does_secret_exist = False
        try:
            if does_secret_exist:
                logger.info("Updating secret", secret_name=secret_name)
                response = client.update_secret(
                    SecretId=secret_name,
                    SecretString=secret,
                )

            else:
                logger.info("Creating secret", secret_name=secret_name)
                response = client.create_secret(
                    Name=secret_name,
                    SecretString=secret,
                )
            self.secret_arn = response["ARN"]
            self.secret_format_version = self.SECRET_VERSION
        except ClientError:
            logger.exception("Secret Client error", secret_name=secret_name, action="creating")
            raise
        self.save()

    def has_secret(self) -> bool:
        return bool(self.secret_arn)
