import asyncio
import datetime
import functools
import hashlib
import json
import shutil
from abc import ABC, abstractmethod
from collections.abc import AsyncGenerator, Callable
from pathlib import Path
from typing import Any, NamedTuple

import boto3
import structlog
from asgiref.sync import sync_to_async
from botocore.exceptions import ClientError
from bs4 import BeautifulSoup as bs  # noqa: N813
from django.conf import settings
from patchright.async_api import <PERSON>rowser as pBrowser
from patchright.async_api import Browser<PERSON>ontext as pBrowserContext
from patchright.async_api import Page as pPage
from patchright.async_api import <PERSON><PERSON> as pPlaywright
from playwright.async_api import Browser, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playwright
from webapp.models.documents import RawDocument
from webapp.models.emails import CustomerEmailCredential
from webapp.models.portal import MFAType, OTPMethod
from webapp.models.retrieval import Retrieval, RetrievalError, TimeoutForOTPTokenError
from webapp.models.user import Bridge<PERSON>ser
from webapp.services.emails import forwarding_rules_context

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.tasks.send_mail import send_mfa_notification_email

logger = structlog.get_logger(__name__)


async def retry(func: Callable, num_retries: int = 3, delay: float = 0.75, *, exponential: bool = True) -> Any:  # noqa: ANN401
    last_exc = None
    for _ in range(num_retries):
        try:
            return await func()
        except Exception as e:
            logger.exception("Retrying", num_retries=num_retries, delay=delay, exponential=exponential)
            await asyncio.sleep(delay)
            if exponential:
                delay *= 1 + delay
            last_exc = e
    if last_exc:
        raise last_exc
    logger.error("Retries exhaused, no exception")
    raise ValueError


async def retry_until_success(
    func: Callable, num_retries: int = 3, delay: float = 0.75, *, exponential: bool = True
) -> Any:  # noqa: ANN401
    last_exc = None
    res = None
    for _ in range(num_retries):
        try:
            res = await func()
            if not res:
                logger.error(
                    "Retrying until successful", res=res, num_retries=num_retries, delay=delay, exponential=exponential
                )
                continue
            if res:
                return res
        except Exception as e:
            logger.exception("Retrying until successful", num_retries=num_retries, delay=delay, exponential=exponential)
            last_exc = e
        await asyncio.sleep(delay)
        if exponential:
            delay *= 1 + delay
    if res:
        return res
    if last_exc:
        raise last_exc
    logger.error("Retries exhaused, no exception")
    raise ValueError


async def log_checkpoint(self: "RetrievalManager", checkpoint_name: str) -> None:
    tag = f"{self.__class__.__name__}.{checkpoint_name}"
    try:
        if self.page is not None:
            await self.page.wait_for_load_state(state="load")
        # adds latency per function call, but ensures page is fully loaded (roughly speaking)
        await asyncio.sleep(1)

        if self.page is not None and self.context is not None:
            logger.debug("Log checkpoint", tag=tag, action="starting")

            async def get_content() -> str:
                if self.page is None:
                    return ""
                return await self.page.content()

            soup = bs(markup=await retry(get_content), features="html.parser")
            soup_bytes = soup.prettify().encode("utf-8")

            screenshot_bytes = await self.page.screenshot(full_page=True)

            storage_state_json_bytes = json.dumps(await self.context.storage_state(), indent=4).encode("utf-8")
            bucket, path = await self.get_retrieval_s3_path()
            step_directory = path / "loggable" / checkpoint_name
            logger.debug("Log checkpoint", tag=tag, step_directory=step_directory, action="uploading")
            s3 = boto3.client("s3")
            s3.put_object(Bucket=bucket, Key=str(step_directory / "screenshot.png"), Body=screenshot_bytes)
            s3.put_object(Bucket=bucket, Key=str(step_directory / "page.html"), Body=soup_bytes)
            s3.put_object(Bucket=bucket, Key=str(step_directory / "storage_state.json"), Body=storage_state_json_bytes)
        else:
            logger.debug("Log checkpoint", action="skipped", tag=tag)
    except Exception:
        logger.exception("Log checkpoint", tag=tag, action="exception")


def loggable_step(*, log_to_s3: bool = False) -> Callable:
    def _loggable_step(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(self: "RetrievalManager", *args: list[Any], **kwargs: dict[str, Any]) -> Any:  # noqa: ANN401
            tag = f"{self.__class__.__name__}.{func.__name__}"
            logger.debug("Loggable step", tag=tag, action="starting")
            if self.page is not None:
                await self.page.wait_for_load_state(state="load")
            logger.debug("Loggable step", tag=tag, action="executing")
            result = None
            try:
                result = await func(self, *args, **kwargs)
                logger.info("Loggable step", tag=tag, result=result, action="returned")
                # Right now this is always false, but we always log checkpoints when we error out.
                if log_to_s3:
                    await log_checkpoint(self, func.__name__)
                else:
                    # Sleep here because we used to log checkpoint all the time,
                    # which once we made optional might randomly break shit.
                    # TODO: have the balls to remove this sleep.
                    await asyncio.sleep(1)
            except Exception:
                logger.exception("Loggable step", tag=tag, action="error")
                await log_checkpoint(self, func.__name__)
                raise
            return result

        return wrapper

    return _loggable_step


class DocumentHash(NamedTuple):
    hash: str
    version: int
    name: str


class RawDocumentTuple(NamedTuple):
    name: str
    date: datetime.datetime
    doc_type: str
    doc_hash: DocumentHash
    content: bytes | None = None
    # s3_bucket and key only used for demo
    s3_bucket: str | None = None
    s3_key: str | None = None
    raw_metadata: str | Any | None = None


@sync_to_async
def asave_document(
    user: BridgeUser,
    document: RawDocumentTuple,
    retrieval: Retrieval,
) -> tuple[str, bool]:
    return save_document(user, document, retrieval=retrieval)


def save_document(  # noqa: C901
    user: BridgeUser,
    document: RawDocumentTuple,
    *,
    retrieval: Retrieval | None = None,
    folder: Path | None = None,
) -> tuple[str, bool]:
    if retrieval is None and folder is None:
        raise ValueError
    if retrieval is not None and folder is not None:
        raise ValueError

    # TODO: similar to raw email document saving, should we merge?
    # S3's default, let's use it.
    md5 = None
    if document.content is not None:
        logger.debug("Calculating MD5")
        hash_object = hashlib.md5(document.content)  # nosec # noqa: S324
        md5 = hash_object.hexdigest()
    if document.s3_bucket is not None and document.s3_key is not None:
        logger.info("Found S3 bucket! Should only happen in demo mode!")
        if not user.is_demo:
            raise ValueError
        obj = boto3.client("s3").head_object(Bucket=document.s3_bucket, Key=document.s3_key)
        md5 = obj["ETag"].strip('"')

    raw_doc, created = RawDocument.objects.update_or_create(
        user=user,
        doc_hash=document.doc_hash.hash,
        doc_hash_version=document.doc_hash.version,
        doc_hash_source=document.doc_hash.name,
        defaults={
            "md5": md5,
            "retrieval": retrieval,
            "name": document.name,
            "posted_date": document.date,
            "original_posted_date": document.date,
            "document_type": document.doc_type,
            "original_document_type": document.doc_type,
            "metadata": document.raw_metadata,
        },
    )
    logger.info("Created RawDocument", raw_doc_pk=str(raw_doc.pk), created=created)

    if document.content is None and document.s3_bucket is not None and document.s3_key is not None:
        logger.info("Soft copying to database!! Should only happen in demo mode!")
        if not user.is_demo:
            raise ValueError
        raw_doc.s3_bucket = document.s3_bucket
        raw_doc.s3_key = document.s3_key
        raw_doc.exists_in_s3 = True
        raw_doc.save()
        return str(raw_doc.pk), created
    if document.content is None:
        raise ValueError

    if retrieval is not None:
        bucket, path = retrieval.get_retrieval_s3_path()
    else:
        bucket, path = settings.AWS_STORAGE_BUCKET_NAME, folder
    s3_path = str(path / "raw_documents" / f"{raw_doc.pk}")
    logger.debug("Putting new document to S3", bucket=bucket, s3_path=s3_path)
    boto3.client("s3").put_object(Bucket=bucket, Key=s3_path, Body=document.content)
    obj = boto3.client("s3").head_object(Bucket=bucket, Key=s3_path)
    s3_md5 = obj["ETag"].strip('"')
    logger.debug("Put document in s3", s3_md5=s3_md5, raw_doc_md5=raw_doc.md5, calced_md5=md5)
    if s3_md5 != raw_doc.md5:
        logger.exception("MD5s do not match")
        raise ValueError
    raw_doc.s3_bucket = bucket
    raw_doc.s3_key = s3_path
    raw_doc.exists_in_s3 = True
    raw_doc.save()
    logger.debug("Finished putting new document to S3")
    return str(raw_doc.pk), created


class RetrievalManager(ABC):
    def __init__(
        self, playwright_instance: Playwright | pPlaywright | None, retrieval: Retrieval, user: BridgeUser
    ) -> None:
        self.playwright_instance = playwright_instance
        self.retrieval = retrieval
        self.user = user
        self.local_directory: Path | None = None
        self.local_storage_state: Path | None = None
        self.seen_documents = 0
        self.created_documents = 0
        self.processed_documents = 0
        self.past_documents: set[DocumentHash] | None = None
        self.browser: Browser | pBrowser | None = None
        self.context: BrowserContext | pBrowserContext | None = None
        self.page: Page | pPage | None = None

    @classmethod
    def needs_playwright(cls) -> bool:
        return True

    @classmethod
    def needs_patchright(cls) -> bool:
        return False

    @classmethod
    def needs_persistent_playwright(cls) -> bool:
        # TODO: Set this to True by default.
        # Let's feature flag this for now.
        return False

    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:  # noqa: ARG003
        return False

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return []

    @classmethod
    def email_otp_rules(cls) -> dict:
        return {}

    @classmethod
    def get_otp_methods(cls) -> list[OTPMethod]:
        return [OTPMethod.NOT_SUPPORTED]

    @sync_to_async
    def get_url(self) -> str:
        return self.retrieval.merged_portal_credential.portal.portal_login_url

    @sync_to_async
    def get_portal_name(self) -> str:
        return self.retrieval.merged_portal_credential.portal.name

    @sync_to_async
    def get_username(self) -> str | None:
        if self.retrieval.merged_portal_credential is None:
            raise RetrievalError
        return self.retrieval.merged_portal_credential.username

    @sync_to_async
    def get_password(self) -> str | None:
        if self.retrieval.merged_portal_credential is None:
            raise RetrievalError
        return self.retrieval.merged_portal_credential.get_portal_secret()

    @sync_to_async
    def update_login_status(self, status: Retrieval.RetrievalStatus) -> None:
        structlog.contextvars.bind_contextvars(retrieval_state=status.label)
        return self.retrieval.update_login_status(status)

    @sync_to_async
    def increment_documents_retrieved(self) -> None:
        structlog.contextvars.bind_contextvars(number_documents_retrieved=self.retrieval.number_documents_retrieved + 1)
        return self.retrieval.increment_documents_retrieved()

    @sync_to_async
    def is_backfilled(self) -> bool:
        if self.retrieval is not None:
            merged_portal_credential = self.retrieval.merged_portal_credential
            if merged_portal_credential is not None and merged_portal_credential.is_backfilled:
                return True
        return False

    @sync_to_async
    def increment_documents_skipped(self) -> None:
        structlog.contextvars.bind_contextvars(skipped_documents=self.retrieval.number_documents_skipped + 1)
        return self.retrieval.increment_documents_skipped()

    @sync_to_async
    def set_retrieval_manager(self) -> None:
        manager_name = self.__class__.__name__
        structlog.contextvars.bind_contextvars(manager=manager_name)
        self.retrieval.manager = manager_name
        self.retrieval.save()

    @sync_to_async
    def set_starting_point(self) -> None:
        if not self.retrieval.starting_point:
            last_retrieval = (
                Retrieval.objects.filter(
                    merged_portal_credential=self.retrieval.merged_portal_credential,
                )
                .exclude(id=self.retrieval.id)
                .order_by("-created_at")
                .first()
            )
            logger.info("checking for check point...", last_retrieval=last_retrieval)
            if last_retrieval and last_retrieval.check_point:
                self.retrieval.starting_point = last_retrieval.check_point
        self.retrieval.check_point = self.retrieval.starting_point
        self.retrieval.save()

    @sync_to_async
    def get_starting_point(self) -> dict[str, Any] | None:
        if self.retrieval.starting_point:
            return self.retrieval.starting_point
        return None

    @sync_to_async
    def update_check_point(self, check_point: dict[str, Any]) -> None:
        current_check_point = self.retrieval.check_point
        self.retrieval.check_point = {**current_check_point, **check_point}
        self.retrieval.save()

    @loggable_step()
    async def async_init(  # noqa: C901
        self,
        browser: Browser | pBrowser | None = None,
        context: BrowserContext | pBrowser | None = None,
        page: Page | pPage | None = None,
    ) -> None:
        structlog.contextvars.bind_contextvars(
            retrieval_pk=str(await self.get_retrieval_pk()),
            number_documents_retrieved=0,
            seen_documents=0,
            created_documents=0,
            skipped_documents=0,
            processed_documents=0,
        )
        logger.debug("Initializing", action="start")
        await self.set_retrieval_manager()
        if not settings.DEBUG:
            # Only do this in prod because we call async_init all the time when developing portal managers
            # Makes it very annoying when you're testing the state of the retrieval status.
            await self.update_login_status(Retrieval.RetrievalStatus.PENDING_LOGIN)
        self.local_directory = Path(f"/tmp/{await self.get_retrieval_pk()}")  # nosec  # noqa: S108
        self.local_directory.mkdir(parents=True, exist_ok=True)
        self.local_storage_state = self.local_directory / "storage_state.json"

        self.past_documents = await self.get_past_documents()
        if browser is not None:
            self.browser = browser
        elif self.playwright_instance is not None and not self.needs_persistent_playwright():
            self.browser = await self.playwright_instance.chromium.launch(headless=not settings.DEBUG, channel="chrome")
        record_video_dir = self.local_directory / "log_video"
        context_kwargs: dict[str, Any] = {
            "record_video_dir": str(record_video_dir),
        }

        if context is not None:
            self.context = context
        elif self.browser is not None and not self.needs_persistent_playwright():
            previous_cookies = await self.get_previous_cookies()
            if previous_cookies is not None:
                logger.info("Found previous cookies, using them")
                with self.local_storage_state.open("w") as f:
                    json.dump(previous_cookies, f)
                context_kwargs["storage_state"] = str(self.local_storage_state)
            else:
                logger.info("No previous cookies found")

            context_kwargs["user_agent"] = (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"  # noqa: E501
            )
            self.context = await self.browser.new_context(**context_kwargs)
        elif self.browser is None and self.needs_persistent_playwright():
            await self.get_previous_persistent_state()
            self.context = await self.playwright_instance.chromium.launch_persistent_context(
                user_data_dir=str(self.local_directory / "persistent_chrome_context/."),
                channel="chrome",
                headless=False,
                no_viewport=True,
                **context_kwargs,
            )
        if page is not None:
            self.page = page
        elif self.context is not None:
            self.page = await self.context.new_page()
            try:
                await self.page.goto(url=await self.get_url())
                self.page.on("pageerror", lambda error: logger.debug("Error on page", error=error))
                self.page.on("frameattached", lambda frame: logger.debug("Frame attached", frame=frame))
                self.page.on("framenavigated", lambda frame: logger.debug("Frame navigated", frame=frame))
                self.page.on("framedetached", lambda frame: logger.debug("Frame detached", frame=frame))
                await self.page.wait_for_load_state(state="load")
            except Exception as e:
                # TODO: How do we differentiate between a bad URL and a portal that is down?
                # This could be an us error if we are underprovisioned
                # This could be a portal error if the portal is down.
                # This could be a user error if the URL is malformed.
                # Possible we should only set this for universal login?
                logger.exception("Error navigating to page", url=await self.get_url(), error=e)
                # TODO: UserLoginStatus
                raise
            # TODO: UserLoginStatus
        logger.info("Initializing", action="finished", log_to_llm=True)

    async def cleanup(self) -> None:
        logger.info("Cleaning up", action="start")
        await log_checkpoint(self, "cleanup")
        if self.context is not None:
            await self.context.close()
        if self.browser is not None:
            await self.browser.close()
        # TODO: log the state of the persistent context.
        await self.log_persistent_state()
        await self.log_video()
        if self.playwright_instance is not None:
            await self.playwright_instance.stop()
        if self.local_directory is not None:
            shutil.rmtree(self.local_directory)
        logger.info("Cleaning up", action="finished")

    @sync_to_async
    def get_retrieval_s3_path(self) -> tuple[str, Path]:
        return self.retrieval.get_retrieval_s3_path()

    @sync_to_async
    def get_retrieval_pk(self) -> str:
        return str(self.retrieval.pk)

    @sync_to_async
    def get_merged_portal_credential_pk(self) -> str:
        return str(self.retrieval.merged_portal_credential.pk)

    @sync_to_async
    def get_mfa_notification_email_kwargs(self) -> dict[str, str]:
        if self.retrieval.merged_portal_credential is None:
            raise RetrievalError
        if self.retrieval.merged_portal_credential.created_by is None:
            raise RetrievalError
        if self.retrieval.merged_portal_credential.multi_factor_authentication.receiving_email is None:
            raise RetrievalError
        return {
            "portal_id": str(self.retrieval.merged_portal_credential.portal.pk),
            "user_id": str(self.retrieval.merged_portal_credential.created_by.pk),
            "to_email": self.retrieval.merged_portal_credential.multi_factor_authentication.receiving_email.email,
        }

    @sync_to_async
    def get_multi_factor_authentication_type(self) -> MFAType | str | None:
        if self.retrieval.merged_portal_credential is None:
            raise RetrievalError
        return self.retrieval.merged_portal_credential.multi_factor_authentication_type

    @sync_to_async
    def get_email_provider_bridge_managed(self) -> CustomerEmailCredential.EmailProvider | str:
        if self.retrieval.merged_portal_credential is None:
            raise RetrievalError
        cec = self.retrieval.merged_portal_credential.multi_factor_authentication.receiving_email
        if cec is not None:
            return cec.email_provider_bridge_managed
        return CustomerEmailCredential.EmailProvider.NOT_SUPPORTED

    @sync_to_async
    def is_email_mfa(self) -> bool:
        if self.retrieval.merged_portal_credential is None:
            raise RetrievalError
        return self.retrieval.merged_portal_credential.multi_factor_authentication_type == MFAType.EMAIL

    async def log_video(self) -> None:
        if self.page is not None and self.page.video is not None:
            local_path = await self.page.video.path()
            bucket, path = await self.get_retrieval_s3_path()
            s3_path = str(path / "video" / f"{self.__class__.__name__}.webm")
            logger.info("RetrievalVideo", s3_path=s3_path, action="starting")
            boto3.client("s3").upload_file(str(local_path), bucket, s3_path)
            logger.info("RetrievalVideo", action="finishing")
            Path(local_path).unlink()
            logger.info("RetrievalVideo", action="deleted")

    async def log_persistent_state(self) -> None:
        if self.page is not None and self.needs_persistent_playwright():
            local_path = str(self.local_directory / "persistent_chrome_context/.")
            output_filename = str(self.local_directory / "persistent_chrome_context")
            shutil.make_archive(output_filename, "zip", local_path)
            output_filename_w_ext = output_filename + ".zip"
            bucket, path = await self.get_retrieval_s3_path()
            s3_path = str(path / "persistent_chrome_context/persistent_chrome_context.zip")
            logger.info("PersistentState", s3_path=s3_path, action="starting")
            boto3.client("s3").upload_file(output_filename_w_ext, bucket, s3_path)
            logger.info("PersistentState", action="finishing")
            Path(output_filename_w_ext).unlink()
            logger.info("PersistentState", action="deleted")

    @loggable_step()
    async def login(self) -> None:
        async with forwarding_rules_context(self):
            login_successful = await self._login_successful_wrapped()
            if login_successful:
                logger.info("Login successful without logging in, exiting early", log_to_llm=True)
                # TODO: UserLoginStatus
                await self.update_login_status(Retrieval.RetrievalStatus.SUCCESS_LOGGED_IN)
                return
            await self.update_login_status(Retrieval.RetrievalStatus.PENDING_LOGIN)
            otp_required = await self._check_has_otp_wrapped()
            if otp_required:
                logger.info("Found MFA page first", log_to_llm=True)
                await self.handle_otp()
            else:
                logger.info("Entering credentials", log_to_llm=True)
                await self._enter_credentials(await self.get_username(), await self.get_password())

            async def login_or_otp() -> bool:
                login_successful = await self._login_successful_wrapped()
                otp_required = await self._check_has_otp_wrapped()
                return login_successful or otp_required

            await retry_until_success(login_or_otp, num_retries=5)
            login_successful = await self._login_successful_wrapped()
            otp_required = await self._check_has_otp_wrapped()
            if not login_successful and otp_required:
                await self.handle_otp()
            elif (not login_successful and not otp_required) or (login_successful and otp_required):
                logger.exception("Erronious state", login_successful=login_successful, otp_required=otp_required)
                # TODO: UserLoginStatus
                await self.update_login_status(Retrieval.RetrievalStatus.FAILED_LOGIN_CREDENTIALS)
                raise RetrievalError
            # this is the implict else that the login was successful
            logger.info("Login successful", log_to_llm=True)
            # TODO: UserLoginStatus
            await self.update_login_status(Retrieval.RetrievalStatus.SUCCESS_LOGGED_IN)

    async def handle_otp(self) -> None:
        await self._send_otp()
        # TODO: Should we message the user more aggressively? In this case we *know* they got an email
        # but for carta, every new device gets an email sent to them for a new device.
        # Should we delegate this check to the subclasses on a per-portal basis?
        if await self.is_email_mfa():
            email_kwargs = await self.get_mfa_notification_email_kwargs()
            logger.info("Sending MFA notification email", email_kwargs=email_kwargs, log_to_llm=True)
            send_mfa_notification_email.apply_async(kwargs=email_kwargs)
        await self._enter_otp_wrapped()
        login_successful = await retry_until_success(self._login_successful_wrapped, num_retries=5)
        if not login_successful:
            # TODO: UserLoginStatus
            logger.exception("Login failed after OTP")
            await self.update_login_status(Retrieval.RetrievalStatus.FAILED_LOGIN_CREDENTIALS)
            raise RetrievalError

    @loggable_step()
    async def retrieve(self) -> None:
        await self.update_login_status(Retrieval.RetrievalStatus.PENDING_DOCUMENT_RETRIEVAL)
        await self.set_starting_point()
        logger.info("Starting document retrieval", log_to_llm=True)
        last_exc = None
        await log_checkpoint(self, "retrieve")
        for retries in range(3):
            logger.info("Scrape retry", retries=retries)
            try:
                async for document in self._retrieve():
                    if document is None:
                        continue
                    await self.increment_documents_retrieved()
                    self.seen_documents += 1
                    raw_document_id, created = await asave_document(
                        user=self.user, document=document, retrieval=self.retrieval
                    )
                    if created:
                        self.created_documents += 1
                    if self.user.is_demo:
                        from retrieval.tasks import demo_process_document

                        self.processed_documents += 1
                        demo_process_document.apply_async(
                            task_id=raw_document_id,
                            kwargs={"user_id": self.user.pk, "raw_document_id": raw_document_id},
                        )
                    elif await self.is_backfilled():
                        from ml_app.tasks.doc_vault_tasks import multi_process_doc_vault

                        self.processed_documents += 1
                        multi_process_doc_vault.apply_async(
                            kwargs={"raw_document_ids": [raw_document_id]},
                        )

                    """
                    # TODO: reenable backfill ML
                    else:
                        from ml_app.tasks.doc_vault_tasks import stateful_process_doc_vault

                        self.processed_documents += 1
                        stateful_process_doc_vault.apply_async(
                            task_id=raw_document_id,
                            kwargs={
                                "raw_document_id": raw_document_id,
                                "merged_portal_credential_pk": await self.get_merged_portal_credential_pk(),
                            },
                        )
                    """
                    structlog.contextvars.bind_contextvars(
                        seen_documents=self.seen_documents,
                        created_documents=self.created_documents,
                        processed_documents=self.processed_documents,
                    )
                last_exc = None
                break
            except Exception as e:
                logger.exception("Error in retrieval", retries=retries, error=e)
                if self.page is not None:
                    await self.page.reload()
                    await self.page.wait_for_load_state(state="load")
                await asyncio.sleep(10)
                structlog.contextvars.bind_contextvars(
                    retrieval_retries=retries,
                )
                await self._handle_retry_setup()
                last_exc = e
        logger.info("Last exception after all retries", last_exc=last_exc)
        if last_exc is not None:
            await self.update_login_status(Retrieval.RetrievalStatus.FAILED_DOCUMENT_RETRIEVAL)
            raise last_exc
        await self.update_login_status(Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL)

    @sync_to_async
    def get_previous_cookies(self) -> dict[str, Any] | None:
        last_successful_retrival = (
            Retrieval.objects.filter(
                merged_portal_credential=self.retrieval.merged_portal_credential,
                retrieval_status=Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
            )
            .order_by("-created_at")
            .first()
        )
        if last_successful_retrival is None or not last_successful_retrival.exists_in_s3:
            return None
        logger.info(
            "Using cookies from previous successful retrieval", last_successful_retrival_pk=last_successful_retrival.pk
        )
        bucket = last_successful_retrival.s3_bucket
        path = Path(last_successful_retrival.s3_key)
        s3 = boto3.client("s3")
        for step in ["cleanup", "retrieve"]:
            try:
                obj = s3.get_object(Bucket=bucket, Key=str(path / f"loggable/{step}/storage_state.json"))
                return json.loads(obj["Body"].read().decode("utf-8"))
            except ClientError:
                return None
        return None

    @sync_to_async
    def get_previous_persistent_state(self) -> bool:
        if self.local_directory is None:
            raise ManagerNotInitializedError
        last_successful_retrival = (
            Retrieval.objects.filter(
                merged_portal_credential=self.retrieval.merged_portal_credential,
                retrieval_status=Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
            )
            .order_by("-created_at")
            .first()
        )
        if last_successful_retrival is None:
            return False
        logger.info(
            "Using persistent state from previous successful retrieval",
            last_successful_retrival_pk=last_successful_retrival.pk,
        )
        bucket = last_successful_retrival.s3_bucket
        path = Path(last_successful_retrival.s3_key)
        s3 = boto3.client("s3")
        context_path = str(path / "persistent_chrome_context/persistent_chrome_context.zip")
        output_filename = str(self.local_directory / "persistent_chrome_context.zip")
        local_context_path = str(self.local_directory / "persistent_chrome_context/.")
        try:
            with Path(output_filename).open("wb") as f:
                s3.download_fileobj(bucket, context_path, f)
            shutil.unpack_archive(output_filename, local_context_path, "zip")
            logger.info("Unzipped persistent state", output_filename=output_filename)
        except ClientError:
            return False
        return True

    @sync_to_async
    def get_past_documents(self) -> set[DocumentHash]:
        # if self.past_docs is None:
        previous_docs = (
            Retrieval.objects.filter(merged_portal_credential=self.retrieval.merged_portal_credential)
            .prefetch_related("raw_documents")
            .only(
                "raw_documents__doc_hash",
                "raw_documents__doc_hash_version",
                "raw_documents__doc_hash_source",
                "raw_documents__exists_in_s3",
            )
            .all()
        )
        # TODO: add recomputation logic for old hash versions stored.
        all_docs = [doc for retrieval in previous_docs for doc in retrieval.raw_documents.all()]
        res = {
            DocumentHash(
                doc.doc_hash,
                doc.doc_hash_version,
                doc.doc_hash_source,
            )
            for doc in all_docs
            if doc.exists_in_s3
            # Only consider documents that have been saved to S3, so we can redownload them.
        }
        logger.info("Found past documents", length_exists_in_s3=len(res), length_all_docs=len(all_docs))
        return res

    async def should_download_document(self, document_metadata: str) -> DocumentHash | None:
        if self.past_documents is None:
            raise ValueError
        doc_hash = self._doc_hash(document_metadata)
        if doc_hash in self.past_documents:
            logger.info("Skipping document", doc_hash=doc_hash, document_metadata=document_metadata)
            await self.increment_documents_skipped()
            return None
        return doc_hash

    @loggable_step()
    async def _login_successful_wrapped(self) -> bool:
        async def _login_successful() -> bool:
            return await self._check_login_successful()

        # Wait for a total of 18 seconds
        res = await retry(_login_successful, num_retries=4)
        if res:
            await self.update_login_status(Retrieval.RetrievalStatus.SUCCESS_LOGGED_IN)
        return res

    @loggable_step()
    @sync_to_async
    def _wait_for_otp(self) -> str | None:
        otp = self.retrieval.try_get_otp()
        if otp is None:
            otp = self.retrieval.wait_for_token_otp()
        self.retrieval.update_login_status(Retrieval.RetrievalStatus.PENDING_LOGIN_OTP)
        # TODO: do this more securely? at least we only save this temporarily
        # Maybe we should keep it for debugging if not a vulnerability.
        self.retrieval.token_otp = ""  # nosec
        self.retrieval.save()
        return otp

    @loggable_step()
    async def _enter_otp_wrapped(self) -> None:
        try:
            otp = await self._wait_for_otp()
        except TimeoutForOTPTokenError:
            logger.info("Timeout waiting for OTP token, resending OTP")
            await self._resend_otp()
            otp = await self._wait_for_otp()
        if otp is not None:
            await self._enter_otp(otp)

    @loggable_step()
    async def _check_has_otp_wrapped(self) -> bool:
        async def _check_has_otp() -> bool:
            return await self._check_has_otp()

        # Wait for a total of 18 seconds
        res = await retry(_check_has_otp, num_retries=4)
        if res:
            await self.update_login_status(Retrieval.RetrievalStatus.BLOCKED_LOGIN_OTP)
        return res

    async def _resend_otp(self) -> None:
        raise NotImplementedError

    async def _handle_retry_setup(self) -> None:
        logger.info("no retry set up implemented")

    # Abstract methods to implement per portal below

    @abstractmethod
    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        pass

    @abstractmethod
    async def _check_login_successful(self) -> bool:
        pass

    @abstractmethod
    async def _check_has_otp(self) -> bool:
        pass

    @abstractmethod
    async def _send_otp(self) -> None:
        pass

    @abstractmethod
    async def _enter_otp(self, otp: str) -> None:
        pass

    @abstractmethod
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        pass

    @abstractmethod
    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple | None, None]:
        yield None

    @classmethod
    def is_demo(cls) -> bool:
        return False
