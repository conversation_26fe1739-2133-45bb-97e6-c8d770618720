{% extends "account/base_entrance.html" %}
{% comment %}
    From: https://docs.allauth.org/en/dev/common/templates.html
    Source: https://codeberg.org/allauth/django-allauth/src/branch/main/allauth/templates/account/password_reset_from_key.html
{% endcomment %}
{% load widget_tweaks static allauth account %}
{% block head_title %}
    Forgot Password
{% endblock head_title %}
{% block extra_head %}
    {% include "components/bridge_header.html" %}
{% endblock extra_head %}
{% block content %}
    <main>
        {% if token_fail %}
            <div class="flex justify-center items-center h-screen bg-gray-100">
                <div class="px-4 py-3 rounded-lg shadow-lg w-1/2 text-center bg-red-100 border border-red-400 text-red-700">
                    <p class="text-lg font-medium mb-4">
                        The password reset link was invalid, possibly because it has already been used or time has run out to reset the password.
                    </p>
                    <a href="{% url 'account_reset_password' %}"
                       class="text-[#924f34] text-sm font-semibold hover:underline">
                        Click here to request a new link
                    </a>
                </div>
            </div>
        {% else %}
            {% if form.errors %}
                <div class="flex justify-center items-center h-screen bg-gray-100">
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative w-1/2 max-w-md"
                         role="alert">
                        <ul class="list-disc list-inside">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}<li>{{ error }}</li>{% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            {% endif %}
            <div class="h-[636px] w-full px-8 flex flex-col justify-center items-center">
                <div class="w-4/5 flex flex-col justify-center items-center gap-20">
                    <div class="h-[700px] flex-col justify-center items-center inline-flex">
                        <div class="self-stretch grow shrink basis-0 px-8 flex-col justify-center items-center flex">
                            <div class="self-stretch flex-col justify-start items-start gap-20 flex">
                                <div class="self-stretch flex-col justify-start items-start flex">
                                    <div class="self-stretch flex-col justify-start items-center flex">
                                        <div class="self-stretch h-24 flex-col justify-start items-start gap-3 flex">
                                            <div class="self-stretch text-[#343d36] text-4xl font-bold font-['Nanum Myeongjo'] leading-[44px]">
                                                Set new password
                                            </div>
                                            <div class="self-stretch text-[#343d36] text-sm font-normal font-['Inter'] leading-tight">
                                                Your new password must be different to previously used passwords.
                                            </div>
                                        </div>
                                    </div>
                                    <form method="post"
                                          class="self-stretch rounded-xl flex-col justify-start items-start gap-6 flex"
                                          hx-trigger="keyup, load"
                                          hx-post="{% url 'password_validators' %}"
                                          hx-target="#password_reset_validators"
                                          hx-swap="innerHTML">
                                        {% csrf_token %}
                                        <div class=" self-stretch h-[70px] flex-col justify-start items-start gap-1.5 flex">
                                            <div class=" justify-start items-start gap-0.5 inline-flex">
                                                <label for="password1"
                                                       class="Label text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                                                    Password
                                                </label>
                                            </div>
                                            <div class="relative self-stretch h-11 px-3 py-2 bg-white rounded-lg shadow border border-[#d0d2bf] flex justify-between items-center">
                                                <input type="password"
                                                       id="password1"
                                                       name="password1"
                                                       placeholder="Create new password"
                                                       class="w-full outline-none focus:outline-none border-none text-[#4b554d] text-sm font-normal font-['Inter']" />
                                                <button type="button"
                                                        class="absolute right-3 top-1/2 transform -translate-y-1/2"
                                                        tabindex="-1"
                                                        onclick="togglePasswordVisibility('password1', 'toggleIcon1')">
                                                    <img id="toggleIcon1"
                                                         src="{% static 'assets/images/hide_icon.svg' %}"
                                                         alt="Toggle visibility"
                                                         width="20"
                                                         height="20">
                                                </button>
                                            </div>
                                        </div>
                                        <div class=" self-stretch h-[70px] flex-col justify-start items-start gap-1.5 flex">
                                            <div class=" justify-start items-start gap-0.5 inline-flex">
                                                <label for="password2"
                                                       class="text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                                                    Confirm Password
                                                </label>
                                            </div>
                                            <div class="relative self-stretch h-11 px-3 py-2 bg-white rounded-lg shadow border border-[#d0d2bf] flex justify-between items-center">
                                                <input type="password"
                                                       id="password2"
                                                       name="password2"
                                                       placeholder="Confirm your password"
                                                       class="w-full outline-none focus:outline-none  border-none text-[#4b554d] text-sm font-normal font-['Inter']" />
                                                <button type="button"
                                                        class="absolute right-3 top-1/2 transform -translate-y-1/2"
                                                        tabindex="-1"
                                                        onclick="togglePasswordVisibility('password2', 'toggleIcon2')">
                                                    <img id="toggleIcon2"
                                                         src="{% static 'assets/images/hide_icon.svg' %}"
                                                         alt="Toggle visibility"
                                                         width="20"
                                                         height="20">
                                                </button>
                                            </div>
                                        </div>
                                        <input hidden type="text" name="button_text" value="Reset Password" />
                                        <div id="password_reset_validators"></div>
                                    </form>
                                    <a href="{% url 'account_login' %}">
                                        <div class="self-stretch justify-center pt-5 items-start inline-flex">
                                            <div class="justify-center items-center flex">
                                                <div class="w-3.5 h-3.5 justify-center items-center flex"></div>
                                                <div class="text-center text-[#924f34] text-sm font-semibold font-['Inter'] leading-tight">Back to Sign in</div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    {% endif %}
{% endblock content %}
