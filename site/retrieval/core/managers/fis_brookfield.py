import asyncio
import datetime
import hashlib
import json
import mimetypes
import urllib.parse
from collections.abc import Async<PERSON>enerator

import structlog
from playwright.async_api import Locator
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import Document<PERSON>ash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class FISBrookfieldPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "fiscloudservices.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [("<EMAIL>", "Brookfield (IV and VI)")]

    @classmethod
    def email_otp_rules(cls) -> dict:
        return {
            "senderContains": ["fisclientservices.com"],
        }

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        login_email_locator = self.page.locator("input[id='email']")
        await login_email_locator.click()
        await asyncio.sleep(1)
        await login_email_locator.fill(username)
        await asyncio.sleep(1)
        login_pass_locator = self.page.locator("input[id='password']")
        await login_pass_locator.click()
        await asyncio.sleep(1)
        await login_pass_locator.fill(password)
        await asyncio.sleep(1)
        await self.page.get_by_text("Submit").click()
        await asyncio.sleep(3)

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return await self.page.get_by_label(text="One-time PIN").is_visible()

    async def _send_otp(self) -> None:
        # Not used here as it is automatically sent, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        otp_locator = self.page.locator("input[id='pinCode']")
        await otp_locator.click()
        await asyncio.sleep(1)
        await otp_locator.fill(otp)
        await asyncio.sleep(1)
        remember_me_locator = self.page.locator("span[id='remember-me']")
        await remember_me_locator.click()
        await asyncio.sleep(1)
        await self.page.get_by_text("Submit").click()
        await asyncio.sleep(3)
        await self.page.reload()
        await asyncio.sleep(3)

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return (
            sum(
                [
                    await self.page.locator("input[id='email']").count(),
                    await self.page.locator("input[id='password']").count(),
                    await self.page.locator("span[id='remember-me']").count(),
                    await self.page.locator("input[id='pinCode']").count(),
                    await self.page.get_by_label(text="One-time PIN").count(),
                ]
            )
            == 0
        )

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: C901, PLR0912, PLR0915
        if self.page is None:
            raise ManagerNotInitializedError

        starting_point = await self.get_starting_point()
        nav_bar_locator = self.page.locator("table.dx-navigationbar")
        documents_tab = nav_bar_locator.locator("td.dx-document-tab-2713916")
        if await documents_tab.count() == 1 and await documents_tab.is_visible():
            await documents_tab.click()
            await asyncio.sleep(10)
        else:
            raise CouldNotFindHTMLElementError

        grid_container = self.page.locator("td.dx-grid-container-treeview")
        grid_container_table = grid_container.locator("> table")
        tree_items_list = grid_container_table.locator("ul.k-treeview-lines")
        tree_items = tree_items_list.locator("> li.k-item")
        tree_items_count = await tree_items.count()
        document_type_outer_category_starting_idx = 0
        if starting_point and "document_type_outer_category_idx" in starting_point:
            logger.info("skipping to document type outer category starting point", starting_point=starting_point)
            document_type_outer_category_starting_idx = starting_point["document_type_outer_category_idx"]
        for i in range(document_type_outer_category_starting_idx, tree_items_count):
            if i == 0:
                continue
            tree_item = tree_items.nth(i)
            await tree_item.hover()
            inner_div = tree_item.locator("> div")
            span = inner_div.locator("span.dx-treeview-item")
            await span.click(force=True)
            await asyncio.sleep(10)

            logger.info(
                "update_check_point",
                checkpoint="document_type_outer_category_idx",
                document_type_outer_category_idx=i,
            )
            await self.update_check_point({"document_type_outer_category_idx": i})

            inner_categories = tree_item.locator("> ul.k-group")
            inner_items = inner_categories.locator("> li.k-item")
            inner_items_count = await inner_items.count()
            document_type_inner_category_starting_idx = 0
            use_starting_point = i == document_type_outer_category_starting_idx
            if starting_point and "document_type_inner_category_idx" in starting_point and use_starting_point:
                logger.info("skipping to document type inner category starting point", starting_point=starting_point)
                document_type_inner_category_starting_idx = starting_point["document_type_inner_category_idx"]
            for j in range(document_type_inner_category_starting_idx, inner_items_count):
                inner_item = inner_items.nth(j)
                await inner_item.hover()
                inner_item_div = inner_item.locator("> div")
                inner_span = inner_item_div.locator("span.dx-treeview-item")
                await inner_span.click(force=True)
                await asyncio.sleep(10)

                logger.info(
                    "update_check_point",
                    checkpoint="document_type_inner_category_idx",
                    document_type_inner_category_idx=j,
                )
                await self.update_check_point({"document_type_inner_category_idx": j})

                table_grid_header = self.page.locator("tr.dx-grid-header")
                filter_options_dropdown_span = table_grid_header.locator("span.k-state-default")
                if await filter_options_dropdown_span.count() == 1 and await filter_options_dropdown_span.is_visible():
                    await filter_options_dropdown_span.click()
                    await asyncio.sleep(3)
                else:
                    raise CouldNotFindHTMLElementError

                filter_options_list = self.page.locator(
                    "ul[data-role='staticlist'][aria-label='Filter Options'][aria-hidden='false']"
                )
                filter_all = filter_options_list.locator("> li").first
                if await filter_all.count() == 1 and await filter_all.is_visible():
                    await filter_all.click()
                    await asyncio.sleep(10)
                else:
                    raise CouldNotFindHTMLElementError

                use_starting_point = j == document_type_inner_category_starting_idx
                async for doc in self._download_rows(starting_point, use_starting_point=use_starting_point):
                    yield doc

    async def _download_rows(
        self, starting_point: dict, *, use_starting_point: bool
    ) -> AsyncGenerator[RawDocumentTuple, None]:
        outer_table_content = self.page.locator("tr.dx-grid-content")
        grid_table = outer_table_content.locator("table[role='grid'][tabindex='0']")
        table_body = grid_table.locator("> tbody[role='rowgroup']")
        rows = table_body.locator("> tr[role='row']")
        rows_count = await rows.count()
        if rows_count == 0:
            return
        document_row_starting_idx = 0
        if starting_point and "document_row_idx" in starting_point and use_starting_point:
            logger.info("skipping to document row index", starting_point=starting_point)
            document_row_starting_idx = starting_point["document_row_idx"]
        for i in range(document_row_starting_idx, rows_count):
            logger.info("update_check_point", checkpoint="document_row_idx", document_row_idx=i)
            await self.update_check_point({"document_row_idx": i})
            row = rows.nth(i)
            row_metadata = await self._get_row_metadata(row)
            metadata = json.dumps(row_metadata)
            if await self.should_download_document(metadata):
                file_cell = row.locator("td[role='gridcell'][data-field='Name']")
                file_name = await file_cell.inner_text()
                a_tag = file_cell.locator("a")
                href = await a_tag.get_attribute("href")
                base_url = "https://services.dataexchange.fiscloudservices.com"
                full_url = urllib.parse.urljoin(base_url, href)
                response = await self.page.request.get(full_url)
                if response.ok:
                    doc_content = await response.body()
                    content_type = response.headers.get("content-type")
                    doc_category = row_metadata["DocumentCategories"]
                    doc_type = self._document_mapper(doc_category)
                    doc_effective_date = row_metadata["DocumentEffectiveDate"]
                    datetime_date = datetime.datetime.strptime(doc_effective_date, "%B %d, %Y")  # noqa: DTZ007
                    file_extensions = [".pdf", ".xlsx", ".xls", ".doc", ".docx", ".zip", ".csv"]
                    if all(ext not in file_name.lower() for ext in file_extensions):
                        extension = mimetypes.guess_extension(content_type)
                        if extension:
                            file_name += extension
                        else:
                            logger.info("Could not determine document content type", row=row, row_metadata=row_metadata)
                            continue
                    doc = RawDocumentTuple(
                        name=file_name,
                        date=datetime_date,
                        doc_type=doc_type,
                        content=doc_content,
                        raw_metadata=metadata,
                        doc_hash=self._doc_hash(metadata),
                    )
                    if doc is not None:
                        yield doc
                else:
                    logger.error("Could not download document contents", row=row, row_metadata=row_metadata)
            else:
                logger.info("Skipping row", row=row, row_metadata=row_metadata)

    async def _get_row_metadata(self, row: Locator) -> dict[str, str]:
        row_metadata = {}
        for cell in await row.locator("td[role='gridcell']").all():
            # Want to skip data fields without any actual data
            if await cell.get_attribute("data-field") in ("Starred", "Viewed", "Checked"):
                continue
            data_field = await cell.get_attribute("data-field")
            if data_field is None:
                continue
            inner_text = await cell.inner_text()
            row_metadata[data_field] = inner_text

        return row_metadata

    def _document_mapper(self, document_type: str) -> DocumentType | str:  # noqa: C901, PLR0911, PLR0912
        doc_type_lower = document_type.lower()

        if "capital activity" in doc_type_lower:
            if "capital call & distribution schedule" in doc_type_lower:
                return DocumentType.UNKNOWN
            if "capital call notices" in doc_type_lower:
                return DocumentType.CAPITAL_CALL
            if "distribution notices" in doc_type_lower:
                return DocumentType.DISTRIBUTION_NOTICE

        elif "investor communications" in doc_type_lower:
            if any(
                keyword in doc_type_lower
                for keyword in [
                    "annual investor conference",
                    "quarterly webcast & conference calls",
                    "significant news",
                ]
            ):
                return DocumentType.OTHER
            if "fund closing details" in doc_type_lower:
                return DocumentType.LEGAL
            if "transaction announcements" in doc_type_lower:
                return DocumentType.INVESTMENT_UPDATE
            if "videos & articles archive" in doc_type_lower:
                return DocumentType.UNKNOWN

        elif "legal, regulatory & compliance" in doc_type_lower:
            return DocumentType.LEGAL

        elif "reporting" in doc_type_lower:
            if "capital account statements" in doc_type_lower:
                return DocumentType.ACCOUNT_STATEMENT
            if "flash reports" in doc_type_lower:
                return DocumentType.OTHER
            if "interim & annual reports" in doc_type_lower:
                return DocumentType.INVESTMENT_UPDATE
            if "other" in doc_type_lower:
                return DocumentType.OTHER

        elif "tax" in doc_type_lower:
            return DocumentType.TAX

        return DocumentType.UNKNOWN
