<div hx-get="{% url 'line_item_aggregate' %}"
     hx-trigger="changeMergedPortalCredentialRow from:document"
     hx-swap='innerHTML'>
    <div class="h-auto lg:h-60 w-full justify-center items-start gap-8 flex">
        <div class="w-full pt-6 lg:pt-12 px-4 lg:px-9 border pb-6 lg:pb-8 border-b-[#f4ebe3] justify-start items-start gap-6 flex flex-col lg:flex-row">
            <div class="w-full flex-col justify-start items-start gap-3 flex">
                <div class="w-full h-10 px-3.5 py-2.5 bg-[#4b554d] rounded-lg justify-start items-center gap-1 flex">
                    <div class="px-0.5 justify-center items-center flex">
                        <div class="Text text-white text-sm lg:text-base font-extrabold font-['Inter'] leading-[18px]">Connection Health</div>
                    </div>
                </div>
                <div class="w-full justify-start items-start gap-2 flex flex-col sm:flex-row">
                    <div class="w-full sm:flex-1 p-4 lg:p-6 bg-white rounded-xl border border-[#eceee8] flex-col justify-start items-start gap-4 lg:gap-6 flex">
                        <div class="w-full justify-start items-center gap-4 lg:gap-6 flex">
                            <div class="flex-1 text-[#343d36] text-sm lg:text-base font-semibold font-['Inter'] leading-normal">Line Items</div>
                        </div>
                        <div class="w-full justify-start items-end gap-4 flex">
                            <div class="flex-1 flex-col justify-start items-start gap-4 flex">
                                <div class="w-full text-[#343d36] text-xl lg:text-[28px] font-bold font-['Nanum Myeongjo'] leading-7">
                                    {{ line_item_count }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full sm:flex-1 p-4 lg:p-6 bg-gray-100 rounded-xl border border-[#eceee8] flex-col justify-start items-start gap-4 lg:gap-6 flex">
                        <div class="w-full justify-start items-center gap-4 lg:gap-6 flex">
                            <div class="flex-1 text-[#343d36] text-sm lg:text-base font-semibold font-['Inter'] leading-normal">Linked</div>
                        </div>
                        <div class="w-full justify-start items-end gap-4 flex">
                            <div class="flex-1 flex-col justify-start items-start gap-4 flex">
                                <div class="w-full text-[#343d36] text-xl lg:text-[28px] font-bold font-['Nanum Myeongjo'] leading-7">
                                    {{ linked_count }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full sm:flex-1 p-4 lg:p-6 bg-gray-100 rounded-xl border border-[#eceee8] flex-col justify-start items-start gap-4 lg:gap-6 flex">
                        <div class="w-full justify-start items-center gap-4 lg:gap-6 flex">
                            <div class="flex-1 text-[#343d36] text-sm lg:text-base font-semibold font-['Inter'] leading-normal">Pending</div>
                        </div>
                        <div class="w-full justify-start items-end gap-4 flex">
                            <div class="flex-1 flex-col justify-start items-start gap-4 flex">
                                <div class="w-full text-[#343d36] text-xl lg:text-[28px] font-bold font-['Nanum Myeongjo'] leading-7">
                                    {{ pending_count }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full sm:flex-1 p-4 lg:p-6 bg-gray-100 rounded-xl border border-[#eceee8] flex-col justify-start items-start gap-4 lg:gap-6 flex">
                        <div class="w-full justify-start items-center gap-4 lg:gap-6 flex">
                            <div class="flex-1 text-[#343d36] text-sm lg:text-base font-semibold font-['Inter'] leading-normal">
                                Action Required
                            </div>
                        </div>
                        <div class="w-full justify-start items-end gap-4 flex">
                            <div class="flex-1 flex-col justify-start items-start gap-4 flex">
                                <div class="w-full text-[#343d36] text-xl lg:text-[28px] font-bold font-['Nanum Myeongjo'] leading-7">
                                    {{ action_required_count }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
