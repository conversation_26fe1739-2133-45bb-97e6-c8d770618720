import boto3
import pymupdf  # type: ignore[import-untyped]
import structlog
from langchain.text_splitter import RecursiveCharacterTextSplitter

from ml_app.core.doc_vault_lib import get_llm

logger = structlog.get_logger(__name__)


class PdfAISummarizer:
    def __init__(self, chunk_size: int = 4000, chunk_overlap: int = 200) -> None:
        self.llm = get_llm()
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=["\n\n", "\n", ". ", " ", ""],
        )

    def extract_full_text_from_s3(self, bucket: str, key: str) -> str:
        s3 = boto3.client("s3")
        obj = s3.get_object(Bucket=bucket, Key=key)
        file_bytes: bytes = obj["Body"].read()

        try:
            doc = pymupdf.open(stream=file_bytes, filetype="pdf")
            full_text = ""
            for page_num, page in enumerate(doc.pages()):
                page_text = page.get_text("text")
                if page_text.strip():
                    full_text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
            doc.close()
        except Exception as e:
            logger.exception("Error reading PDF file", bucket=bucket, key=key, error=str(e))
            raise
        else:
            return full_text

    def extract_full_text_from_file(self, file_path: str) -> str:
        try:
            doc = pymupdf.open(file_path)
            full_text = ""
            for page_num, page in enumerate(doc.pages()):
                page_text = page.get_text("text")
                if page_text.strip():
                    full_text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
            doc.close()
        except Exception as e:
            logger.exception("Error reading PDF file", file_path=file_path, error=str(e))
            raise
        else:
            return full_text

    def summarize_with_stuff(self, text: str, custom_stuff_prompt: str | None = None) -> str:
        """
        Summarize text using the "stuff" strategy.
        This is the simplest strategy and just stuffs all the text into the LLM call.
        This is recommended for small documents.
        """
        logger.info("Summarizing text using the stuff strategy")

        if len(text.strip()) == 0:
            return ""

        stuff_prompt_text = (
            custom_stuff_prompt
            or """You are a financial analyst assistant. Summarize the following investor quarterly
            report into a concise, high-level briefing (100-150 words), suitable for inclusion
            in a client-facing email.

                Focus on qualitative insights that would help an advisor understand and communicate:
                - What changed this quarter
                - How the fund is performing directionally
                - Notable portfolio or strategic developments
                - Any forward-looking commentary

                Prioritize the following four sections **(include only those that appear in the source)**:
                1. **Market Overview** - macro trends or themes impacting the investment landscape
                2. **Fund Performance** - overall performance and drivers (avoid metrics-heavy detail)
                3. **Portfolio Highlights** - key investments, exits, or changes of note
                4. **Strategic Updates** - changes in team, strategy, or positioning

                Omit raw financial metrics like NAV, IRR, TVL, multiples unless directly
                explained in qualitative terms.

                Avoid unnecessary jargon. Write clearly and professionally for a senior
                advisor audience. Use concise paragraphs under clear section headers.

                ---

                REPORT STARTS HERE:
                {text}"""
        )

        formatted_prompt = stuff_prompt_text.format(text=text)

        try:
            response = self.llm.invoke(formatted_prompt)
        except Exception as e:
            logger.exception("Error during stuff summarization", error=str(e))
            raise
        else:
            return response.content

    def summarize_with_map_reduce(
        self, text: str, custom_map_prompt: str | None = None, custom_reduce_prompt: str | None = None
    ) -> str:
        """
        Summarize text using the "map-reduce" strategy.
        This is a more complex strategy that splits the text into chunks, summarizes each chunk, and
        then combines the summaries.
        It's recommended for large documents.
        """
        logger.info("Summarizing text using the map-reduce strategy")

        if len(text.strip()) == 0:
            return ""

        # Create documents from text
        docs = self.text_splitter.create_documents([text])
        logger.info("Split document into chunks for map-reduce", chunk_count=len(docs))

        map_prompt_text = (
            custom_map_prompt
            or """You are a financial analyst assistant. Extract key qualitative insights
            from the following section of an investor quarterly report.

            Focus on identifying:
            - What changed this quarter
            - Performance direction and drivers
            - Notable portfolio or strategic developments
            - Forward-looking commentary

            Preserve important context but avoid raw financial metrics unless they're
            explained qualitatively.
            Use clear, professional language.

            Text:
            {text}

            Key Insights:"""
        )

        reduce_prompt_text = (
            custom_reduce_prompt
            or """You are a financial analyst assistant. Combine the following section insights
                into a concise, high-level briefing (100-150 words), suitable
                for inclusion in a client-facing email.

                Focus on qualitative insights that would help an advisor understand and communicate:
                - What changed this quarter
                - How the fund is performing directionally
                - Notable portfolio or strategic developments
                - Any forward-looking commentary

                Organize under these headers (include only those that appear in the source):
                1. **Market Overview** - macro trends or themes impacting the investment landscape
                2. **Fund Performance** - overall performance and drivers (avoid metrics-heavy detail)
                3. **Portfolio Highlights** - key investments, exits, or changes of note
                4. **Strategic Updates** - changes in team, strategy, or positioning

                Omit raw financial metrics unless directly explained in qualitative terms.
                Write clearly and professionally for a senior advisor audience. Use
                concise paragraphs under clear section headers.

                Insights:
                {multiple_summaries}

                Final Briefing:"""
        )

        try:
            # Map phase: summarize each chunk
            chunk_summaries = []
            for i, doc in enumerate(docs):
                logger.info("Processing chunk", chunk_number=i + 1, total_chunks=len(docs))
                formatted_prompt = map_prompt_text.format(text=doc.page_content)
                response = self.llm.invoke(formatted_prompt)
                chunk_summaries.append(response.content)

            # Reduce phase: combine all summaries
            combined_text = "\n\n".join(chunk_summaries)
            final_prompt = reduce_prompt_text.format(multiple_summaries=combined_text)
            final_response = self.llm.invoke(final_prompt)

        except Exception as e:
            logger.exception("Error during map-reduce summarization", error=str(e))
            raise
        else:
            return final_response.content

    def summarize_with_refine(
        self, text: str, initial_prompt: str | None = None, custom_refine_prompt: str | None = None
    ) -> str:
        """
        Summarize text using the "refine" strategy.
        This is a more complex strategy that iteratively refines the summary with the remaining text.
        It's recommended for large documents.
        """
        logger.info("Summarizing text using the refine strategy")

        docs = self.text_splitter.create_documents([text])
        logger.info("Split document into chunks for refine", chunk_count=len(docs))

        try:
            if not docs:
                return ""

            # Start with the first chunk
            doc = docs[0]

            # Initial summary from first chunk
            initial_prompt = (
                initial_prompt
                or f"""You are a financial analyst assistant. Create a concise, high-level
                    briefing (100-150 words) from the following portion
                    of an investor quarterly report, suitable for inclusion in a client-facing email.

                    Focus on qualitative insights that would help an advisor understand and communicate:
                    - What changed this quarter
                    - How the fund is performing directionally
                    - Notable portfolio or strategic developments
                    - Any forward-looking commentary

                    Organize using these categories (only include relevant ones):
                    1. **Market Overview** - macro trends or themes impacting the investment landscape
                    2. **Fund Performance** - overall performance and drivers (avoid metrics-heavy detail)
                    3. **Portfolio Highlights** - key investments, exits, or changes of note
                    4. **Strategic Updates** - changes in team, strategy, or positioning

                    Omit raw financial metrics unless directly explained in qualitative terms.
                    Write clearly and professionally for a senior advisor audience. Use
                    concise paragraphs under clear section headers.

                    TEXT:
                    {doc.page_content}
            """
            )
            response = self.llm.invoke(initial_prompt)
            current_summary = response.content

            # Iteratively refine with remaining chunks
            for i, doc in enumerate(docs[1:], 1):
                logger.info("Refining with chunk", chunk_number=i + 1, total_chunks=len(docs))
                refine_prompt = (
                    custom_refine_prompt
                    or f"""
                    You have been provided with an existing high-level briefing of an investor report:

                    EXISTING BRIEFING:
                    {current_summary}

                    You now have additional context to work with:

                    ------------
                    {doc.page_content}
                    ------------

                    Update the briefing **only if the new context provides relevant new insights**
                    for a client-facing email.
                    If the new text is unrelated or contains no significant new information, return
                    the briefing unchanged.

                    Maintain the concise format (100-150 words), focusing on qualitative insights:
                    - What changed this quarter
                    - How the fund is performing directionally
                    - Notable portfolio or strategic developments
                    - Any forward-looking commentary

                    Keep the existing structure with clear section headers. Omit raw financial metrics
                    unless explained qualitatively.
                    Write for a senior advisor audience. Do **not** add any meta commentary
                    or explanations.

                    REFINED BRIEFING:
                """
                )

                response = self.llm.invoke(refine_prompt)
                current_summary = response.content

        except Exception as e:
            logger.exception("Error during refine summarization", error=str(e))
            raise
        else:
            return current_summary

    def summarize_pdf_from_s3(self, bucket: str, key: str, strategy: str = "stuff") -> dict:
        logger.info("Starting PDF summarization", bucket=bucket, key=key, strategy=strategy)

        full_text = self.extract_full_text_from_s3(bucket, key)

        if len(full_text.strip()) == 0:
            return {}

        if strategy == "map_reduce":
            summary = self.summarize_with_map_reduce(full_text)
        elif strategy == "refine":
            summary = self.summarize_with_refine(full_text)
        elif strategy == "stuff":
            summary = self.summarize_with_stuff(full_text)
        else:
            msg = f"Unknown strategy: {strategy}"
            logger.error(msg)
            raise ValueError(msg)

        return {
            "summary": summary,
            "original_text_length": len(full_text),
            "strategy_used": strategy,
            "bucket": bucket,
            "key": key,
        }

    def summarize_pdf_from_file(self, file_path: str, strategy: str = "stuff") -> dict:
        logger.info("Starting PDF summarization", file_path=file_path, strategy=strategy)

        full_text = self.extract_full_text_from_file(file_path)

        if len(full_text.strip()) == 0:
            return {}

        if strategy == "map_reduce":
            summary = self.summarize_with_map_reduce(full_text)
        elif strategy == "refine":
            summary = self.summarize_with_refine(full_text)
        elif strategy == "stuff":
            summary = self.summarize_with_stuff(full_text)
        else:
            msg = f"Unknown strategy: {strategy}"
            logger.error(msg)
            raise ValueError(msg)

        return {
            "summary": summary,
            "original_text_length": len(full_text),
            "strategy_used": strategy,
            "file_path": file_path,
        }


def summarize_pdf_s3(bucket: str, key: str) -> dict:
    summarizer = PdfAISummarizer()
    return summarizer.summarize_pdf_from_s3(bucket, key)


def summarize_pdf_file(file_path: str) -> dict:
    summarizer = PdfAISummarizer()
    return summarizer.summarize_pdf_from_file(file_path)


def summarize_raw_document(raw_document_id: str) -> dict:
    from webapp.models import RawDocument

    raw_doc = RawDocument.objects.get(pk=raw_document_id)
    summarizer = PdfAISummarizer()

    return summarizer.summarize_pdf_from_s3(bucket=raw_doc.s3_bucket, key=raw_doc.s3_key)
