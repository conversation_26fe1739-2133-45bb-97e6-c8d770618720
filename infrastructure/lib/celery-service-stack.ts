import * as cdk from "aws-cdk-lib";
import * as appscaling from "aws-cdk-lib/aws-applicationautoscaling";
import * as cloudwatch from "aws-cdk-lib/aws-cloudwatch";
import { Construct } from "constructs";
import {
  NetworkStackOutputs,
  ClusterStackOutputs,
  StorageStackOutputs,
  SharedProps,
  SharedStack,
  ConfigStackOutputs,
  CeleryServiceStackOutputs,
} from "./schema";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as logs from "aws-cdk-lib/aws-logs";
import * as iam from "aws-cdk-lib/aws-iam";
import * as ecr from "aws-cdk-lib/aws-ecr";
import * as kms from "aws-cdk-lib/aws-kms";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import * as s3 from "aws-cdk-lib/aws-s3";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as lambda from "aws-cdk-lib/aws-lambda";
import * as events from "aws-cdk-lib/aws-events";
import * as targets from "aws-cdk-lib/aws-events-targets";

export class CeleryServiceStack extends SharedStack {
  constructor(scope: Construct, id: string, props: SharedProps) {
    super(scope, id, props);
    const networkOutputs = NetworkStackOutputs.fromCdkOutput(
      this,
      NetworkStackOutputs,
    );
    const clusterOutputs = ClusterStackOutputs.fromCdkOutput(
      this,
      ClusterStackOutputs,
    );
    const storageOutputs = StorageStackOutputs.fromCdkOutput(
      this,
      StorageStackOutputs,
    );
    const configOutputs = ConfigStackOutputs.fromCdkOutput(
      this,
      ConfigStackOutputs,
    );
    if (
      networkOutputs === undefined ||
      configOutputs === undefined ||
      clusterOutputs === undefined ||
      storageOutputs === undefined
    ) {
      return;
    }
    const vpc = ec2.Vpc.fromVpcAttributes(this, "Vpc", {
      vpcId: networkOutputs.VpcId,
      availabilityZones: networkOutputs.VpcAvailabilityZones,
      publicSubnetIds: networkOutputs.VpcPublicSubnetIds,
      publicSubnetRouteTableIds: networkOutputs.VpcPublicRouteTableIds,
    });

    // Import the Cluster ARN from cluster stack

    // Recreate the ECS Cluster
    const cluster = ecs.Cluster.fromClusterAttributes(this, "Cluster", {
      clusterName: clusterOutputs.ClusterName,
      vpc: vpc,
    });

    const celeryTaskRole = new iam.Role(this, "CeleryTaskRole", {
      assumedBy: new iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
      description: "IAM role for Celery Tasks with necessary permissions",
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName("AmazonSSMReadOnlyAccess"),
        iam.ManagedPolicy.fromAwsManagedPolicyName("AmazonSESFullAccess"),
      ],
    });

    const readSecretsPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      resources: [
        `arn:aws:secretsmanager:${this.region}:${this.account}:secret:User*`,
      ],
      actions: [
        "secretsmanager:CreateSecret",
        "secretsmanager:GetSecretValue",
        "secretsmanager:DescribeSecret",
        "secretsmanager:UpdateSecret",
      ],
    });
    celeryTaskRole.addToPolicy(readSecretsPolicy);

    const cloudWatchLogsPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      resources: [
        // Allow access to the specific log group
        "*",
      ],
      actions: [
        "logs:StartQuery",
        "logs:GetQueryResults",
        "logs:StopQuery",
        "logs:FilterLogEvents",
        "logs:DescribeLogGroups",
        "logs:DescribeLogStreams",
      ],
    });
    celeryTaskRole.addToPolicy(cloudWatchLogsPolicy);

    const bedrockPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      resources: ["*"],
      actions: ["bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream"],
    });
    celeryTaskRole.addToPolicy(bedrockPolicy);

    kms.Key.fromKeyArn(
      this,
      "userDataKms",
      storageOutputs.UserDataKmsArn,
    ).grantDecrypt(celeryTaskRole);

    const redisSecret = secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedRedisSecret",
      storageOutputs.RedisSecretArn,
    );
    redisSecret.grantRead(celeryTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedMsftOauthSecret",
      configOutputs.MsftEmailOauthSecretArn,
    ).grantRead(celeryTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedRedisClusterSecret",
      configOutputs.DevTwilioSecretArn,
    ).grantRead(celeryTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedSlackSecret",
      configOutputs.SlackSecretArn,
    ).grantRead(celeryTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "ImportedDjangoSecret",
      configOutputs.DjangoSecretArn,
    ).grantRead(celeryTaskRole);

    secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "DbCredentialsSecret",
      storageOutputs.DBCredentialsSecretArn,
    ).grantRead(celeryTaskRole);

    s3.Bucket.fromBucketName(
      this,
      "S3Bucket",
      configOutputs.UserBucketName,
    ).grantReadWrite(celeryTaskRole);

    // Import from cluster stack
    const executionRole = iam.Role.fromRoleArn(
      this,
      "ImportedExecutionRole",
      clusterOutputs.ExecutionRoleArn,
    );
    let memoryLimitMiB = 8192;
    let cpu = 4096;
    if (props.deploymentTarget === "demo") {
      memoryLimitMiB = 2048;
      cpu = 1024;
    }

    const celeryTaskDefinition = new ecs.FargateTaskDefinition(
      this,
      "CeleryTaskDefinition",
      {
        memoryLimitMiB,
        cpu,
        executionRole,
        taskRole: celeryTaskRole,
      },
    );

    const celeryLogGroup = new logs.LogGroup(this, "CeleryLogGroup", {
      retention: logs.RetentionDays.ONE_YEAR,
    });

    const celeryRepository = ecr.Repository.fromRepositoryName(
      this,
      "CeleryRepository",
      configOutputs.PythonContainerRepositoryName,
    );
    // add the container
    celeryTaskDefinition.addContainer("CeleryContainer", {
      image: ecs.ContainerImage.fromRegistry(
        celeryRepository.repositoryUriForTag("latest"),
      ),
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: "celery-container",
        logGroup: celeryLogGroup,
      }),
      environment: {
        ENVIRONMENT: configOutputs.DeploymentTarget,
        APPLICATION: "celery",
      },
    });

    const celeryService = new ecs.FargateService(this, "CeleryService", {
      cluster: cluster,
      taskDefinition: celeryTaskDefinition,
      desiredCount: 1,
      vpcSubnets: {
        subnets: vpc.publicSubnets, // these are the subnets with access through the vpc endpoints
      },
      assignPublicIp: true, // required to initiate outbound internet access
      // we should disable this for real production
      enableExecuteCommand: true,
      platformVersion: ecs.FargatePlatformVersion.VERSION1_4,
    });

    const queueMonitorLambda = new lambda.Function(this, "CeleryQueueMonitor", {
      runtime: lambda.Runtime.PYTHON_3_11,
      handler: "index.lambda_handler",
      code: lambda.Code.fromAsset("lambda/celery-queue-monitor", {
        bundling: {
          image: lambda.Runtime.PYTHON_3_11.bundlingImage,
          user: "root",
          command: [
            "bash",
            "-c",
            "pip install --no-cache-dir -r requirements.txt -t /asset-output && cp -r . /asset-output",
          ],
        },
      }),
      timeout: cdk.Duration.minutes(10),
      memorySize: 256,
      environment: {
        REDIS_SECRET_ARN: storageOutputs.RedisSecretArn,
        REDIS_HOST: storageOutputs.RedisClusterPrimaryEndPointAddress,
        REDIS_PORT: storageOutputs.RedisClusterPrimaryEndPointPort,
        ENVIRONMENT: configOutputs.DeploymentTarget,
      },
      vpc: vpc,
      vpcSubnets: {
        subnets: vpc.publicSubnets,
      },
      allowPublicSubnet: true,
    });

    // Grant permissions to the Lambda
    redisSecret.grantRead(queueMonitorLambda);

    // Grant CloudWatch permissions for metrics publishing
    queueMonitorLambda.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          "cloudwatch:PutMetricData",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
        ],
        resources: ["*"],
      }),
    );

    // EventBridge rule to trigger Lambda periodically (every 1 minute)
    const queueMonitorRule = new events.Rule(this, "CeleryQueueMonitorRule", {
      schedule: events.Schedule.rate(cdk.Duration.minutes(1)),
      description: "Trigger Celery queue monitoring Lambda every 1 minute",
    });

    queueMonitorRule.addTarget(new targets.LambdaFunction(queueMonitorLambda));

    // Define the scalable target (the ECS service)
    const scalableTarget = new appscaling.ScalableTarget(
      this,
      "CeleryServiceScaling",
      {
        serviceNamespace: appscaling.ServiceNamespace.ECS,
        resourceId: `service/${cluster.clusterName}/${celeryService.serviceName}`,
        scalableDimension: "ecs:service:DesiredCount",
        minCapacity: 1,
        maxCapacity: 4,
      },
    );

    // Auto Scaling Configuration
    // Create custom metric for Celery queue length
    // Using the total queue depth from your Lambda
    const celeryQueueLengthMetric = new cloudwatch.Metric({
      namespace: "celery_queue_depths", // Matches your Lambda's namespace
      metricName: "total", // Your Lambda publishes a "total" metric with sum of all queues
      period: cdk.Duration.minutes(1),
      statistic: "Average",
    });

    // Custom metric-based scaling using the recommended scaleOnMetric approach
    scalableTarget.scaleOnMetric("CeleryQueueScaling", {
      metric: celeryQueueLengthMetric,
      scalingSteps: [
        { lower: 0, upper: 1, change: 0 }, // 0-1 queue length: No change (keep workers active)
        { lower: 1, upper: 10, change: -1 }, // 1-10 queue length: Remove 1 worker (scale down)
        { lower: 10, change: 1 }, // 10+ queue length: Add 1 worker (scale up)
      ],
      evaluationPeriods: 2,
      datapointsToAlarm: 2,
      cooldown: cdk.Duration.minutes(10),
    });

    if (props.deploymentTarget === "prod") {
      // Keep your existing scheduled scaling for predictable load patterns
      // Schedule scale up - 9:45am UTC daily
      scalableTarget.scaleOnSchedule("EveningScaleUp", {
        schedule: appscaling.Schedule.cron({
          minute: "45",
          hour: "23",
        }),
        minCapacity: 4,
        maxCapacity: 4,
      });

      // Schedule scale down - 11:00am UTC daily
      scalableTarget.scaleOnSchedule("EveningScaleDown", {
        schedule: appscaling.Schedule.cron({
          minute: "15",
          hour: "2",
        }),
        minCapacity: 1,
        maxCapacity: 1,
      });
    }

    new CeleryServiceStackOutputs({
      CeleryTaskRoleArn: celeryTaskRole.roleArn,
    }).toCdkOutput(this);
  }
}
