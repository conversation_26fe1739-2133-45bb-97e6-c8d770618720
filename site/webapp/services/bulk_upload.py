import json
from io import By<PERSON><PERSON>
from typing import NamedTuple

import openpyxl
import structlog
from openpyxl.worksheet.worksheet import Worksheet

logger = structlog.get_logger(__name__)

XSLX_KWARGS = {
    "Client Name": "client_legal_name",
    "Investment Entity Legal Name": "entity_legal_name",
    "Managing Firm Name": "investment_managing_firm_name",
    "Investment / Fund Legal Name": "investment_fund_legal_name",
    "Receiving Email": "receiving_email",
    "Receiving Email Provider": "receiving_email_provider",
    "Portal Provider Name": "portal_name",
    "Portal Provider URL": "portal_login_url",
    "Multi-Factor Authentication Type": "multi_factor_authentication_type",
    "Multi-Factor Authentication Details": "multi_factor_authentication_details",
}


class XLSXRow(NamedTuple):
    row_number: int
    data: dict[str, str]
    raw_data: str


def enumerate_xslx_holdings(xlsx_bytes: bytes) -> list[XLSXRow]:
    # TODO: WARNING, this can blow up memory...
    dataframe = openpyxl.load_workbook(BytesIO(xlsx_bytes), data_only=True)

    if "Holdings" not in dataframe.sheetnames:
        raise ValueError
    holdings = dataframe["Holdings"]

    start_row, headers = find_header(holdings)

    return read_holdings(holdings, start_row, headers)


def find_header(holdings: Worksheet) -> tuple[int, list[str]]:
    # Iterate the loop to read the cell values
    for row_idx in range(holdings.max_row):
        header = []
        for col_idx in range(holdings.max_column):
            # IDK why this is one based indexing
            cell = holdings.cell(row=row_idx + 1, column=col_idx + 1).value
            header.append(cell)
        ensured = ensure_headers(header)
        if ensured:
            logger.info("Row ensured", row_idx=row_idx + 1, header=header, ensured=ensured)
            return row_idx + 1, header
    logger.error("Could not find header in the xlsx file")
    raise ValueError


def ensure_headers(headers: list[str]) -> bool:
    needed_headers = set(XSLX_KWARGS.keys())
    found_headers = set(headers)
    missing_from_xsxl = needed_headers - found_headers
    missing_from_code = found_headers - needed_headers
    have_all_headers = len(missing_from_xsxl) == 0
    logger.info(
        "Ensuring headers",
        have_all_headers=have_all_headers,
        missing_from_xsxl=missing_from_xsxl,
        missing_from_code=missing_from_code,
    )
    return have_all_headers


def read_holdings(holdings: Worksheet, start_row: int, headers: list[str]) -> list[XLSXRow]:
    # Iterate the loop to read the cell values
    logger.info("Reading holdings", start_row=start_row, headers=headers)
    res = []
    # Skip the header row
    for i, row in enumerate(holdings.iter_rows(start_row + 1, holdings.max_row)):
        raw_data: dict[str, str] = {}
        all_is_none = True
        for idx, h in enumerate(headers):
            if row[idx].value is not None:
                all_is_none = False
            raw_data[h] = str(row[idx].value)
        if all_is_none:
            break
        key_set = set(XSLX_KWARGS.keys())
        validated_data: dict[str, str] = {}
        for k, v in raw_data.items():
            if k in key_set:
                key_set.remove(k)
                validated_data[XSLX_KWARGS[k]] = v
            else:
                logger.debug("Unknown key to program", key=k)
        if key_set:
            logger.error("Missing keys", key_set=key_set)
            # TODO: Gracefully handle errors here
            raise KeyError(key_set)
        raw_data["row_number"] = str(start_row + i)
        res.append(XLSXRow(row_number=start_row + i, data=validated_data, raw_data=json.dumps(raw_data)))
    return res


if __name__ == "__main__":
    from pathlib import Path

    site_folder = Path(__file__).resolve().parent.parent.parent.parent
    logger.info("Site folder", site_folder=site_folder)
    file_name = sorted(site_folder.glob("*.xlsx"), reverse=True)[0]
    logger.info("File name", file_name=file_name)
    with file_name.open("rb") as f:
        xl_file = f.read()
        i = 0
        for row in enumerate_xslx_holdings(xl_file):
            i += 1
            logger.debug("Row", row=row)
        logger.info("Total rows", length=i)
    logger.info("Done parsing the file", file_name=file_name)
