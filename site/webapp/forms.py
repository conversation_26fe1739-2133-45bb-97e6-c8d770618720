import typing
import uuid
from typing import Any, ClassVar

from allauth.account.forms import ResetPasswordForm  # type: ignore[import-untyped]
from django import forms
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.forms import CheckboxSelectMultiple
from phonenumbers import PhoneNumberFormat, format_number, is_valid_number

from webapp.models.emails import CustomerEmailCredential
from webapp.models.investment_details import Investment
from webapp.models.portal import MergedPortalCredential

from .models import ADDITIONAL_ENTITY_TYPE_CHOICES, BridgeUser, InvestingEntity, Role

User = get_user_model()

US_LENGTH_PHONE_NUMBER = 10
LOWER_BOUND_PHONE_NUMBER = 9
UPPER_BOUND_PHONE_NUMBER = 15


class XSLXUploadForm(forms.Form):
    xslx_file = forms.FileField()


class CredentialsFlowForm(forms.Form):
    user_name = forms.CharField(
        label="User Name",
        widget=forms.TextInput(attrs={"placeholder": "Enter your user name"}),
        required=True,
    )
    portal_password = forms.CharField(
        label="Password",
        widget=forms.PasswordInput(attrs={"placeholder": "Enter your password"}),
        required=True,
    )
    confirm_password = forms.CharField(
        label="Confirm Password",
        widget=forms.PasswordInput(attrs={"placeholder": "Confirm your password"}),
        required=True,
    )

    def clean(self) -> dict[str, Any]:
        cleaned_data = super().clean()
        portal_password = cleaned_data.get("portal_password")
        confirm_password = cleaned_data.get("confirm_password")

        if portal_password != confirm_password:
            msg = "Passwords do not match."
            raise ValidationError(msg)

        return cleaned_data


class EmailMPCForm(forms.Form):
    email_provider = forms.ChoiceField(
        choices=[("", "Select Email Provider")]
        + [
            (value, "Not listed" if label == "Not Supported" else label)
            for value, label in CustomerEmailCredential.EmailProvider.choices
        ],
        label="Email provider",
        required=True,
    )
    email = forms.EmailField(required=True)

    def clean_email(self) -> str:
        email = self.cleaned_data.get("email")
        initial_email = self.initial.get("email")
        if email and email != initial_email:
            query = MergedPortalCredential.objects.filter(user_forwarding_rule__receiving_email__email=email)
            if query.exists():
                msg = f"{email} inbox is already set up."
                raise forms.ValidationError(msg)
        return email


class CustomResetPasswordForm(ResetPasswordForm):
    def clean_email(self) -> str:
        email = self.cleaned_data.get("email")
        if email is None or type(email) is not str:
            msg = "No account found with the provided email address."
            raise ValidationError(msg)

        self.users = User.objects.filter(email__iexact=email, is_active=True)
        # Check if the user exists
        if not User.objects.filter(email=email).exists():
            msg = "No account found with the provided email address."
            raise ValidationError(msg)

        return email


class OTPFlowForm(forms.Form):
    otp = forms.CharField(label="One Time Password")


class RegistrationForm(forms.ModelForm):
    password1 = forms.CharField(
        label="New Password",
        widget=forms.PasswordInput(attrs={"placeholder": "New Password", "class": "form-control"}),
        validators=[validate_password],
    )
    password2 = forms.CharField(
        label="Confirm Password",
        widget=forms.PasswordInput(attrs={"placeholder": "Confirm Password", "class": "form-control"}),
    )
    contact_number = forms.CharField(
        label="Contact Number",
        required=True,
        widget=forms.TextInput(attrs={"placeholder": "Enter your contact number", "class": "form-control"}),
    )
    tos_agreement = forms.BooleanField(
        required=True,
        label="I agree to the Terms of Service",
        error_messages={"required": "You must agree to the Terms of Service."},
    )

    class Meta:
        model = BridgeUser
        fields: ClassVar[list[str]] = ["email", "first_name", "last_name", "contact_number", "tos_agreement"]

    def clean(self) -> dict[str, typing.Any] | None:
        cleaned_data = super().clean()
        if cleaned_data is None:
            self.add_error("first_name", "First name is required.")
            self.add_error("last_name", "Last name is required.")
            self.add_error("tos_agreement", "You must agree to the Terms of Service.")
            self.add_error("password2", "New password and confirm password do not match.")
            return None
        first_name = cleaned_data.get("first_name")
        last_name = cleaned_data.get("last_name")
        password1 = cleaned_data.get("password1")
        password2 = cleaned_data.get("password2")
        tos_agreement = cleaned_data.get("tos_agreement")

        # Validate non-empty fields
        if not first_name:
            self.add_error("first_name", "First name is required.")
        if not last_name:
            self.add_error("last_name", "Last name is required.")
        # Validate that new password and confirm password match
        if password1 and password2 and password1 != password2:
            self.add_error("password2", "New password and confirm password do not match.")

        # Validate TOS agreement
        if tos_agreement is not True:
            self.add_error("tos_agreement", "You must agree to the Terms of Service.")

        return cleaned_data

    def save(self, commit: bool = True) -> BridgeUser | None:  # noqa: FBT001, FBT002
        user = super().save(commit=False)
        password1 = self.cleaned_data.get("password1")
        if password1:
            user.set_password(password1)
        user.invitation_token = None  # Remove the token
        user.contact_number = self.cleaned_data.get("contact_number", "")
        if commit:
            user.save()
        return user


class ProfileForm(forms.ModelForm):
    class Meta:
        model = BridgeUser
        fields: ClassVar[list[str]] = ["first_name", "last_name", "email", "contact_number", "organization", "roles"]

    def __init__(self, *args: list[Any], **kwargs: dict[str, Any]) -> None:
        super().__init__(*args, **kwargs)
        self.fields["organization"].disabled = True
        self.fields["email"].disabled = True
        self.fields["roles"].disabled = True
        self.fields["roles"].queryset = Role.objects.all().order_by("name")


class PasswordUpdateForm(forms.Form):
    current_password = forms.CharField(
        label="Current Password",
        widget=forms.PasswordInput(attrs={"placeholder": "Current Password", "class": "form-control"}),
    )
    password1 = forms.CharField(
        label="New Password",
        widget=forms.PasswordInput(attrs={"placeholder": "New Password", "class": "form-control"}),
        validators=[validate_password],
    )
    password2 = forms.CharField(
        label="Confirm Password",
        widget=forms.PasswordInput(attrs={"placeholder": "Confirm Password", "class": "form-control"}),
    )

    def __init__(self, user: BridgeUser, *args, **kwargs) -> None:  # noqa: ANN002, ANN003
        super().__init__(*args, **kwargs)
        self.user = user

    def clean_current_password(self) -> str:
        current_password = self.cleaned_data.get("current_password")
        if (
            current_password is None
            or type(current_password) is not str
            or not self.user.check_password(current_password)
        ):
            msg = "Current password is incorrect."
            raise forms.ValidationError(msg)
        return current_password

    def clean(self) -> dict[str, typing.Any] | None:
        cleaned_data = super().clean()
        if cleaned_data is None:
            msg = "New password and confirm password do not match."
            raise forms.ValidationError(msg)

        password1 = cleaned_data.get("password1")
        password2 = cleaned_data.get("password2")

        # Validate that new password and confirm password match
        if password1 and password2 and password1 != password2:
            msg = "New password and confirm password do not match."
            raise forms.ValidationError(msg)

        return cleaned_data


class InvestingEntityForm(forms.ModelForm):
    additional_entity_types = forms.MultipleChoiceField(
        choices=ADDITIONAL_ENTITY_TYPE_CHOICES,
        required=False,
        label="Entity Subtype",
        widget=forms.CheckboxSelectMultiple(
            attrs={
                "class": """flowbite-individual-checkbox form-checkbox text-black
                border-black focus:ring-0 focus:outline-0 w-5 h-5 mr-2"""
            }
        ),
    )

    def clean_phone(self) -> str:
        raw_phone_number = self.cleaned_data.get("phone", "")
        if not raw_phone_number:
            return ""

        if not is_valid_number(raw_phone_number):
            msg = "Please enter a valid US phone number"
            raise ValidationError(msg)

        return raw_phone_number

    def __init__(self, *args: list[Any], **kwargs: dict[str, Any]) -> None:
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.phone:
            phone_number = self.instance.phone
            self.initial["phone"] = format_number(phone_number, PhoneNumberFormat.NATIONAL)

    def clean(self) -> dict[str, Any]:
        cleaned_data = super().clean()
        legal_name = cleaned_data.get("legal_name")

        organization = self.instance.organization if self.instance else None

        """
        Handles the case where the user edits an existing entity with the
            same name of another entity in the organization
        """
        if legal_name and organization:
            query = InvestingEntity.objects.filter(organization=organization, legal_name=legal_name).exclude(
                pk=self.instance.pk if self.instance else None
            )

            if query.exists():
                self.add_error(
                    "legal_name",
                    f"An investing entity with the name '{legal_name}' already exists for this organization.",
                )

        return cleaned_data

    class Meta:
        model = InvestingEntity
        fields = [  # noqa: RUF012
            "legal_name",
            "entity_type",
            "additional_entity_types",
            "ssn_or_tin",
            "address",
            "phone",
            "email",
            "dob",
            "note",
        ]
        widgets = {  # noqa: RUF012
            "entity_type": forms.Select(attrs={"class": "form-select w-full"}),
            "dob": forms.DateInput(attrs={"type": "date", "class": "form-input w-full"}),
            "note": forms.Textarea(attrs={"rows": 3, "class": "form-textarea w-full"}),
            "legal_name": forms.TextInput(attrs={"class": "form-input w-full"}),
            "ssn_or_tin": forms.TextInput(attrs={"class": "form-input w-full"}),
            "address": forms.Textarea(attrs={"rows": 2, "class": "form-textarea w-full"}),
            "phone": forms.TextInput(attrs={"class": "form-input w-full"}),
            "email": forms.EmailInput(attrs={"class": "form-input w-full"}),
        }


class UserInviteForm(forms.Form):
    first_name = forms.CharField(
        label="First Name:",
        max_length=150,
        required=True,
        widget=forms.TextInput(attrs={"class": "form-control w-full"}),
    )
    last_name = forms.CharField(
        label="Last Name:",
        max_length=150,
        required=True,
        widget=forms.TextInput(attrs={"class": "form-control w-full"}),
    )
    email = forms.EmailField(
        label="Email:", required=True, widget=forms.EmailInput(attrs={"class": "form-control w-full"})
    )
    roles = forms.ModelChoiceField(
        label="Role:",
        queryset=None,
        required=True,
        empty_label="Select",
        widget=forms.Select(
            attrs={"class": "form-select w-full rounded-md px-3 py-2 border border-gray-300 focus:outline-none"}
        ),
    )

    def __init__(self, *args: list[Any], **kwargs: dict[str, Any]) -> None:
        self.user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)
        self.fields["roles"].queryset = Role.objects.all().order_by("name")

    def clean_email(self) -> str:
        email = self.cleaned_data.get("email")
        if email and BridgeUser.objects.filter(email=email).exists():
            msg = "A user with this email already exists."
            raise ValidationError(msg)
        return email

    def clean(self) -> dict[str, Any]:
        cleaned_data = super().clean()
        if self.user and not self.user.is_manager:
            msg = "Non-managers cannot invite other users."
            raise ValidationError(msg)
        return cleaned_data


class UserPermissionsForm(forms.Form):
    user_id = forms.UUIDField(widget=forms.HiddenInput())
    roles = forms.ChoiceField(
        required=True,
        choices=Role.RoleName.choices,
        widget=forms.Select(
            attrs={
                "class": """form-select border border-gray-300 rounded-md bg-white px-3
                py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none
                focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 w-full min-h-[38px]"""
            }
        ),
    )
    entities = forms.MultipleChoiceField(
        required=False,
        choices=[],
        widget=CheckboxSelectMultiple(
            attrs={
                "class": """flowbite-individual-checkbox form-checkbox rounded-sm text-black
                border-black focus:ring-0 focus:outline-0 w-5 h-5 mr-2"""
            }
        ),
    )
    investments = forms.MultipleChoiceField(
        required=False,
        choices=[],
        widget=CheckboxSelectMultiple(
            attrs={
                "class": """flowbite-individual-checkbox form-checkbox rounded-sm text-black
                border-black focus:ring-0 focus:outline-0 w-5 h-5 mr-2"""
            }
        ),
    )

    def __init__(self, *args: list[Any], **kwargs: dict[str, Any]) -> None:
        self.user_form = kwargs.pop("user_form", None)
        entities = kwargs.pop("entities", [])
        investments = kwargs.pop("investments", [])
        self.logged_in_user = kwargs.pop("logged_in_user", None)
        super().__init__(*args, **kwargs)

        entity_choices = [(str(entity.id), entity.legal_name) for entity in entities]
        self.fields["entities"].choices = entity_choices

        # If data is passed and entities are selected, filter investments
        if entities:
            # Filter investments based on selected entities
            investment_choices = Investment.objects.filter(line_items__investing_entity__id__in=entities).distinct()
        else:
            # If no entities are selected, show all investments available to the user
            investment_choices = investments

        self.fields["investments"].choices = [
            (str(investment.id), investment.legal_name) for investment in investment_choices
        ]

        if self.user_form:
            first_role = self.user_form.roles.first()
            if first_role:
                self.fields["roles"].initial = first_role.name

            self.fields["user_id"].initial = self.user_form.id

        # Disable fields if the logged in user is the same as the user we are assigning permissions to
        if self.logged_in_user == self.user_form:
            self.fields["entities"].disabled = True
            self.fields["investments"].disabled = True
            self.fields["roles"].disabled = True
        # Disable fields if the logged in user is not a manager
        if self.logged_in_user and not self.logged_in_user.is_manager:
            self.fields["entities"].disabled = True
            self.fields["investments"].disabled = True
            self.fields["roles"].disabled = True

        # Disable fields if logged in user is a manager and user we are assigning permissions to is a manager
        if self.logged_in_user and self.logged_in_user.is_manager and self.user_form and self.user_form.is_manager:
            self.fields["entities"].disabled = True
            self.fields["investments"].disabled = True
            self.fields["roles"].disabled = True

    def clean_user_id(self) -> uuid.UUID:
        user_id = self.cleaned_data.get("user_id")
        try:
            user = BridgeUser.objects.get(organization=self.logged_in_user.organization, id=user_id)
        except BridgeUser.DoesNotExist:
            msg = "Invalid user ID."
            raise ValidationError(msg) from None
        self.user_form = user
        return user.id

    def clean(self) -> dict[str, Any]:
        cleaned_data = super().clean()

        if not self.user_form:
            msg = "User not found."
            raise ValidationError(msg)

        if self.logged_in_user == self.user_form:
            msg = "You cannot assign permissions to yourself."
            raise ValidationError(msg)

        if self.logged_in_user and not self.logged_in_user.is_manager:
            msg = "Non-managers are not authorized to assign permissions."
            raise ValidationError(msg)

        if self.logged_in_user.is_manager and self.user_form.is_manager:
            msg = "Managers cannot assign permissions to other managers."
            raise ValidationError(msg)

        entities = self.cleaned_data.get("entities", [])
        investments = self.cleaned_data.get("investments", [])
        if (entities and not investments) or (investments and not entities):
            msg = "You must select at least one entity and one investment."
            raise ValidationError(msg)

        return cleaned_data


class DeletePermissionBatchForm(forms.Form):
    batch_id = forms.UUIDField()
    user_id = forms.UUIDField()

    def __init__(self, *args: list[Any], **kwargs: dict[str, Any]) -> None:
        self.request_user = kwargs.pop("request_user", None)
        super().__init__(*args, **kwargs)

    def clean_user_id(self) -> uuid.UUID:
        user_id = self.cleaned_data.get("user_id")
        if not self.request_user:
            msg = "You do not have permission to revoke permissions."
            raise ValidationError(msg)
        if not user_id:
            msg = "You must select a user to revoke permissions from."
            raise ValidationError(msg)
        try:
            self.permittee_user = BridgeUser.objects.get(id=user_id, organization=self.request_user.organization)
        except BridgeUser.DoesNotExist:
            msg = "User not found."
            raise ValidationError(msg) from None
        return user_id

    def clean(self) -> dict[str, Any]:
        cleaned_data = super().clean()

        permittee_user = getattr(self, "permittee_user", None)
        request_user = self.request_user

        if not permittee_user or not request_user:
            return cleaned_data

        if not request_user.is_manager:
            msg = "You are not authorized to revoke permissions (not a manager)."
            raise ValidationError(msg)

        if permittee_user.is_manager:
            msg = "Permissions for other managers cannot be revoked this way."
            raise ValidationError(msg)

        if permittee_user == request_user:
            msg = "You cannot revoke permissions from yourself."
            raise ValidationError(msg)

        if permittee_user.organization != request_user.organization:
            msg = "You are not authorized to revoke permissions (cross-organization attempt)."
            raise ValidationError(msg)

        return cleaned_data
