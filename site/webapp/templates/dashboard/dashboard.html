{% extends "base.html" %}
{% block title %}
    Dashboard
{% endblock title %}
{% block webapp_content %}
    <main id="main_dashboard_container"
          class="main-content ml-16 lg:ml-16 md:ml-16 sm:ml-4 bg-base-beige gap-4 h-screen">
        <div id="dashboard_container"
             hx-get="{% url 'dashboard' %}"
             hx-trigger="reloadClientDashboard"
             hx-target="#dashboard_container"
             hx-swap="outerHTML"
             hx-select="#dashboard_container"
             hx-disinherit="*">
            {% include "components/dashboard_header.html" with title="Dashboard" user=user %}
            <div class="p-4 lg:p-9">
                {% include "dashboard/cards/new_notifications.html" %}
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-3 pt-5">
                    {% include "dashboard/cards/period_total_value.html" with period_date=current_period_date period_investment_ct=current_period_investment_ct period_total_value=current_period_total_value %}
                    {% include "dashboard/cards/total_invested_capital.html" %}
                    {% include "dashboard/cards/period_total_value.html" with period_date=previous_period_date period_investment_ct=previous_period_investment_ct period_total_value=previous_period_total_value %}
                    {% include "dashboard/cards/total_committed_capital.html" %}
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-0 border-t border-gray-200 border-b w-full"
                 id="dashboard-tables-container">
                <div class="w-full lg:border-r border-gray-200">{% include "dashboard/latest_updates/table.html" %}</div>
                <div class="w-full">{% include "dashboard/upcoming_capital_calls/table.html" %}</div>
            </div>
            {% include "dashboard/investment_details/table.html" %}
        </div>
    </main>
{% endblock webapp_content %}
