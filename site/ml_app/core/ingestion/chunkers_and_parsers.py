from ml_app.core.ingestion.chunkers import <PERSON><PERSON>, PassthroughChunker, SemanticSegmentationChunker
from ml_app.core.ingestion.core import ParsedDocumentWithChunk
from ml_app.core.ingestion.parsers import CamelotHybridHTMLParser, MuPyPDFHTMLParser, Parser


class ParserAndChunker:
    def __init__(self, parser: Parser, chunker: Chunker) -> None:
        self.parser = parser
        self.chunker = chunker

    def parse_and_chunk(self, raw_document_pk: str) -> list[ParsedDocumentWithChunk]:
        return self.chunker.chunk(self.parser.parse(raw_document_pk))

    def get_parser_name(self) -> str:
        return self.parser.name()

    def get_chunker_name(self) -> str:
        return self.chunker.name()


class CamelotPnC(ParserAndChunker):
    def __init__(self) -> None:
        super().__init__(CamelotHybridHTMLParser(), PassthroughChunker())


class PyMuPDFHtmlSemanticPnC(ParserAndChunker):
    def __init__(self) -> None:
        super().__init__(MuPyPDFHTMLParser(), SemanticSegmentationChunker())
