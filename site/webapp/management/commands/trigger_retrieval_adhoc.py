import json
from argparse import ArgumentParser

import structlog
from django.core.management.base import BaseCommand
from django_extensions.management.utils import signalcommand
from retrieval.tasks import login_portal_task

from webapp.models.portal import MergedPortalCredential, Portal
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser
from webapp.utils.slack import send_message_to_slack

logger = structlog.get_logger(__name__)
SHOULD_EMAIL_USER = False


class Command(BaseCommand):
    help = "Recovers and manages forwarding rules"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("-u", "--user", type=str)
        parser.add_argument("-p", "--portal", type=str)
        parser.add_argument("-s", "--starting", type=str)

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002
        user = kwargs.get("user")
        portal_name = kwargs.get("portal")
        starting_point = kwargs.get("starting")
        if user is None or portal_name is None:
            logger.error("User and Portal name are required to proceed", user=user, portal_name=portal_name)
            return

        user = BridgeUser.objects.get(email=user)
        portal = Portal.objects.get(name=portal_name, organization=user.organization)
        merged_portal_credential = MergedPortalCredential.objects.for_user(user).filter(portal=portal).all()
        if len(merged_portal_credential) == 0:
            logger.error("No merged portal credential found", email=user.email, portal_name=portal_name)
            return
        if merged_portal_credential is not None and len(merged_portal_credential) > 1:
            logger.error("Multiple merged portal credentials found", email=user.email, portal_name=portal_name)
            return
        if merged_portal_credential is not None:
            merged_portal_credential = merged_portal_credential[0]
            retrieval = Retrieval.create(user=user, merged_portal_credential=merged_portal_credential)
        retrieval_id = str(retrieval.id)
        task = login_portal_task.apply_async(kwargs={"retrieval_id": retrieval_id})  # type: ignore[attr-defined]
        retrieval.task_id = task.id
        if starting_point:
            logger.info("setting starting_point", starting_point=starting_point)
            retrieval.starting_point = json.loads(starting_point)
        retrieval.save()
        blocks = [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": ":large_yellow_circle: Adhoc Retrieval Triggered",
                    "emoji": True,
                },
            },
            {
                "type": "section",
                "text": {"type": "mrkdwn", "text": f"*Object ID:*\n<{retrieval.admin_panel_link}|{retrieval.pk}>"},
            },
            retrieval.retrieval_meta_data_slack_block,
        ]
        message = f"Adhoc Retrieval <{retrieval.admin_panel_link}|{retrieval.pk}> for {retrieval.merged_portal_credential.portal} for {retrieval.organization} kicked off"  # noqa: E501
        send_message_to_slack(channel="#portals", message=message, blocks=blocks)
        logger.info(
            "Retrieval Started",
            task_id=task.id,
            retrieval_id=retrieval_id,
            user_email=user.email,
            portal_name=portal_name,
        )
