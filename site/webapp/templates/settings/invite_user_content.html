{% load static customtags widget_tweaks %}
<div id="invite-user-form">
    <div class="relative w-[600px]"></div>
    <div class="flex items-center justify-between p-3 rounded-t dark:border-gray-600">
        <div class="p-2 bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
            <div class="relative flex-col justify-start items-start flex">
                <div class="Group relative">
                    <img src="{% static 'assets/images/invite_users.svg' %}"
                         width="38"
                         height="20"
                         alt="Invite Users">
                </div>
            </div>
        </div>
        <button _="on click trigger closeModal"
                type="button"
                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <img src="{% static 'assets/images/close.svg' %}"
                 width="16"
                 height="19"
                 alt="Close">
            <span class="sr-only">Close modal</span>
        </button>
    </div>
    <div class="px-6 py-5">
        <h3 class="text-xl font-semibold text-[#343d36] mb-6">Invite Users</h3>
        <form hx-post="{% url 'user_invite_modal' %}"
              hx-target="#invite-user-form"
              hx-swap="outerHTML"
              class="space-y-5">
            {% csrf_token %}
            {% if form.non_field_errors %}
                <div class="w-full text-red-500 text-sm space-y-1 mb-4">
                    {% for error in form.non_field_errors %}<div>{{ error }}</div>{% endfor %}
                </div>
            {% endif %}
            {% for field in form.visible_fields %}
                <div class="px-2 w-full">
                    <div class="flex items-center mb-4">
                        <label class="text-gray-700 font-medium pr-2 w-1/3 text-left"
                               for="{{ field.id_for_label }}">{{ field.label }}</label>
                        <div class="w-2/3 relative">
                            {% with error_class=field.errors|yesno:"border-red-500,border-gray-300" %}
                                {{ field|attr:"class:w-full border rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"|add_class:error_class }}
                            {% endwith %}
                        </div>
                    </div>
                    {% if field.errors %}
                        <div class="text-red-500 text-xs space-y-1 mt-1 ml-1/3">
                            {% for error in field.errors %}<div>{{ error }}</div>{% endfor %}
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
            <!-- Button Row -->
            <div class="flex space-x-2 items-center pt-2 px-2">
                <button type="button"
                        _="on click trigger closeModal"
                        class="flex-1 px-6 py-2.5 bg-[#FFFFF] rounded-md border border-gray-300">Cancel</button>
                <button type="submit"
                        class="flex-1 px-6 py-2.5 bg-[#924f34] text-white rounded-md">Send Invite</button>
            </div>
        </form>
    </div>
</div>
