import json
from unittest.mock import MagicMock, patch

import pytest
from django.test import Client
from django.urls import reverse

from webapp.models.portal import SMSMessage
from webapp.repository.sms_repository import InvalidMessageError


@pytest.mark.django_db
class TestForwardSMS:
    def setup_method(self) -> None:
        self.client = Client()
        self.url = reverse("forward_sms")
        self.valid_data = {
            "from": "+15551234567",
            "to": "+14045551234",
            "message": "Your verification code is 123456",
            "email": "<EMAIL>",
            "portal": "Test Portal",
        }

    @patch("webapp.repository.sms_repository.SMSRepository.receive_message_as_webhook")
    def test_post_form_successful(self, mock_receive) -> None:
        mock_message = MagicMock(spec=SMSMessage)
        mock_receive.return_value = mock_message
        response = self.client.post(self.url, self.valid_data)
        assert response.status_code == 200
        response_data = json.loads(response.content)
        assert response_data["success"] is True
        assert "message" in response_data
        assert response_data["user_phone_number"] == self.valid_data["to"]
        assert response_data["portal_phone_number"] == self.valid_data["from"]
        mock_receive.assert_called_once_with(
            user_phone_number=self.valid_data["to"],
            portal_phone_number=self.valid_data["from"],
            body=self.valid_data["message"],
            email=self.valid_data["email"],
            portal_name=self.valid_data["portal"],
        )

    @patch("webapp.repository.sms_repository.SMSRepository.receive_message_as_webhook")
    def test_get_successful(self, mock_receive) -> None:
        mock_message = MagicMock(spec=SMSMessage)
        mock_receive.return_value = mock_message
        response = self.client.get(
            f"{self.url}?from={self.valid_data['from']}&to={self.valid_data['to']}&message={self.valid_data['message']}&email={self.valid_data['email']}&portal={self.valid_data['portal']}"
        )
        assert response.status_code == 200
        response_data = json.loads(response.content)
        assert response_data["success"] is True
        assert response_data["user_phone_number"] == self.valid_data["to"]
        assert response_data["portal_phone_number"] == self.valid_data["from"]
        mock_receive.assert_called_once_with(
            user_phone_number=self.valid_data["to"],
            portal_phone_number=self.valid_data["from"],
            body=self.valid_data["message"],
            email=self.valid_data["email"],
            portal_name=self.valid_data["portal"],
        )

    @patch("webapp.repository.sms_repository.SMSRepository.receive_message_as_webhook")
    def test_json_successful(self, mock_receive) -> None:
        mock_message = MagicMock(spec=SMSMessage)
        mock_receive.return_value = mock_message
        response = self.client.post(self.url, data=json.dumps(self.valid_data), content_type="application/json")
        assert response.status_code == 200
        response_data = json.loads(response.content)
        assert response_data["success"] is True
        mock_receive.assert_called_once_with(
            portal_phone_number=self.valid_data["from"],
            user_phone_number=self.valid_data["to"],
            body=self.valid_data["message"],
            email=self.valid_data["email"],
            portal_name=self.valid_data["portal"],
        )

    @patch("webapp.repository.sms_repository.SMSRepository.receive_message_as_webhook")
    def test_alternative_parameter_names(self, mock_receive) -> None:
        mock_message = MagicMock(spec=SMSMessage)
        mock_receive.return_value = mock_message
        alternative_data = {
            "From": "+15551234567",
            "To": "+14045551234",
            "Message": "Your verification code is 123456",
            "Email": "<EMAIL>",
            "Portal": "Test Portal",
        }
        response = self.client.post(self.url, alternative_data)
        assert response.status_code == 200
        response_data = json.loads(response.content)
        assert response_data["success"] is True
        mock_receive.assert_called_once_with(
            portal_phone_number=alternative_data["From"],
            user_phone_number=alternative_data["To"],
            body=alternative_data["Message"],
            email=alternative_data["Email"],
            portal_name=alternative_data["Portal"],
        )

    @patch("webapp.repository.sms_repository.SMSRepository.receive_message_as_webhook")
    def test_invalid_message_error(self, mock_receive) -> None:
        error_message = "Message does not contain a code"
        mock_receive.side_effect = InvalidMessageError(error_message)
        response = self.client.post(self.url, self.valid_data)
        assert response.status_code == 400  # Bad request
        response_data = json.loads(response.content)
        assert response_data["success"] is False
        assert response_data["error"] == "Message does not contain a code"

    def test_invalid_json(self) -> None:
        response = self.client.post(self.url, data="this is not valid json", content_type="application/json")
        assert response.status_code == 200  # Bad request
        response_data = json.loads(response.content)
        assert response_data["success"] is True
        assert "Successfully contacted Bridge servers" in response_data["message"]
