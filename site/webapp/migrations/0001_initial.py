# Generated by Django 5.1.1 on 2024-11-12 16:11

import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=255, unique=True)),
                ("description", models.TextField(blank=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Role",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=255, unique=True)),
                ("description", models.TextField(blank=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="BridgeUser",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="email address"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_admin", models.BooleanField(default=False)),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "roles",
                    models.ManyToManyField(related_name="%(class)s", to="webapp.role"),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="BridgeEmail",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("email", models.EmailField(max_length=254, unique=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="Client",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("legal_name", models.CharField(max_length=255)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
                "unique_together": {("organization", "legal_name")},
            },
        ),
        migrations.CreateModel(
            name="CustomerEmailCredential",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("secret_arn", models.CharField(blank=True, max_length=255)),
                ("secret_format_version", models.IntegerField(default=0)),
                ("email", models.EmailField(max_length=254)),
                ("email_provider_raw", models.CharField(max_length=255)),
                (
                    "email_provider_customer_managed",
                    models.CharField(
                        choices=[
                            ("ou", "Outlook"),
                            ("gm", "Gmail"),
                            ("yh", "Yahoo"),
                            ("zh", "Zoho"),
                            ("ns", "Not Supported"),
                        ],
                        default="ns",
                        max_length=2,
                    ),
                ),
                (
                    "email_provider_bridge_managed",
                    models.CharField(
                        choices=[
                            ("ou", "Outlook"),
                            ("gm", "Gmail"),
                            ("yh", "Yahoo"),
                            ("zh", "Zoho"),
                            ("ns", "Not Supported"),
                        ],
                        default="ns",
                        max_length=2,
                    ),
                ),
                (
                    "email_integration_status",
                    models.CharField(
                        choices=[
                            ("ns", "Not Started"),
                            ("cm", "Customer Managed"),
                            ("bf", "Bridge Managed Filters"),
                        ],
                        default="ns",
                        max_length=2,
                    ),
                ),
                (
                    "secret_format",
                    models.CharField(
                        choices=[("mc", "MSAL Cache"), ("ns", "Not Supported")],
                        default="ns",
                        max_length=2,
                    ),
                ),
                ("email_secret_format_version", models.IntegerField(default=0)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
                "unique_together": {("organization", "email")},
            },
        ),
        migrations.CreateModel(
            name="InvestingEntity",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("legal_name", models.CharField(max_length=255)),
                (
                    "entity_type",
                    models.CharField(
                        choices=[
                            ("in", "Individual"),
                            ("tr", "Trust"),
                            ("co", "Corporation"),
                            ("pa", "Partnership"),
                            ("ll", "LLC"),
                            ("lp", "LLP"),
                            ("ot", "Other"),
                        ],
                        default="ot",
                        max_length=2,
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="entities",
                        to="webapp.client",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
                "unique_together": {("organization", "legal_name")},
            },
        ),
        migrations.CreateModel(
            name="Investment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("legal_name", models.CharField(max_length=255)),
                ("managing_firm_name", models.CharField(max_length=255)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
                "unique_together": {
                    ("organization", "legal_name", "managing_firm_name")
                },
            },
        ),
        migrations.CreateModel(
            name="LineItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "investing_entity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="line_items",
                        to="webapp.investingentity",
                    ),
                ),
                (
                    "investment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="line_items",
                        to="webapp.investment",
                    ),
                ),
                (
                    "receiving_email",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="line_items",
                        to="webapp.customeremailcredential",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="MultiFactorAuthentication",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("secret_arn", models.CharField(blank=True, max_length=255)),
                ("secret_format_version", models.IntegerField(default=0)),
                (
                    "multi_factor_authentication_type",
                    models.CharField(
                        choices=[
                            ("sm", "SMS"),
                            ("em", "Email"),
                            ("ap", "Authenticator"),
                            ("ll", "Live Login"),
                            ("no", "No MFA"),
                            ("un", "Unknown"),
                        ],
                        default="un",
                        max_length=2,
                    ),
                ),
                ("phone_number", models.CharField(blank=True, max_length=255)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "line_item",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="multi_factor_authentication",
                        to="webapp.lineitem",
                    ),
                ),
                (
                    "receiving_email",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="multi_factor_authentications",
                        to="webapp.customeremailcredential",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="ForwardingRule",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("forwarding_rule", models.TextField(blank=True)),
                ("last_email_received_at", models.DateTimeField(blank=True, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "from_email",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="forwarding_rules",
                        to="webapp.customeremailcredential",
                    ),
                ),
                (
                    "to_email",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="forwarding_rules",
                        to="webapp.bridgeemail",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "line_item",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="forwarding_rule",
                        to="webapp.lineitem",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="Portal",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=255)),
                ("portal_login_url", models.URLField()),
                (
                    "portal_type",
                    models.CharField(
                        choices=[("wb", "Web Based"), ("eb", "Email Based")],
                        default="wb",
                        max_length=2,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.AddField(
            model_name="lineitem",
            name="portal",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="line_items",
                to="webapp.portal",
            ),
        ),
        migrations.CreateModel(
            name="PortalCredential",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("secret_arn", models.CharField(blank=True, max_length=255)),
                ("secret_format_version", models.IntegerField(default=0)),
                ("username", models.CharField(max_length=255)),
                (
                    "otp_method",
                    models.CharField(
                        choices=[
                            ("cl", "Cold Login"),
                            ("ll", "Live Login"),
                            ("sm", "SMS"),
                            ("em", "Email"),
                            ("ap", "Authenticator"),
                            ("ns", "Not Supported"),
                        ],
                        default="cl",
                        max_length=2,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "line_item",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="portal_credential",
                        to="webapp.lineitem",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "portal",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="portal_credentials",
                        to="webapp.portal",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="ProcessedDocument",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "document_type",
                    models.CharField(
                        choices=[
                            ("cc", "Capital Call"),
                            ("iu", "Investment Update"),
                            ("dn", "Distribution Notice"),
                            ("tl", "Tax or Legal"),
                            ("ot", "Other"),
                            ("un", "Unknown"),
                        ],
                        default="un",
                        max_length=2,
                    ),
                ),
                ("md5", models.CharField(max_length=255)),
                ("name", models.CharField(max_length=255)),
                ("process_document_version", models.IntegerField()),
                ("posted_date", models.DateField()),
                ("content_type", models.CharField(max_length=255)),
                ("s3_key", models.CharField(blank=True, max_length=1024)),
                ("s3_bucket", models.CharField(blank=True, max_length=255)),
                ("exists_in_s3", models.BooleanField(default=False)),
                ("has_been_viewed", models.BooleanField(default=False)),
                ("first_viewed_date", models.DateTimeField(blank=True, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "line_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="processed_documents",
                        to="webapp.lineitem",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="InvestmentUpdateDocumentFact",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("md5", models.CharField(max_length=255)),
                ("invested", models.DecimalField(decimal_places=2, max_digits=15)),
                ("total_value", models.DecimalField(decimal_places=2, max_digits=15)),
                ("unfunded", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "line_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="investment_update_documents",
                        to="webapp.lineitem",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "processed_document",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="investment_update_document",
                        to="webapp.processeddocument",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="DistributionNoticeDocumentFact",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("md5", models.CharField(max_length=255)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "line_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="distribution_notice_documents",
                        to="webapp.lineitem",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "processed_document",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="distribution_notice_document",
                        to="webapp.processeddocument",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="CapitalCallDocumentFact",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("md5", models.CharField(max_length=255)),
                ("capital_call_due_date", models.DateField()),
                ("amount", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "line_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="capital_call_documents",
                        to="webapp.lineitem",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "processed_document",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="capital_call_document",
                        to="webapp.processeddocument",
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="RawBulkUpload",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("file_location", models.CharField(blank=True, max_length=255)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="RawBulkUploadRow",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("row_number", models.IntegerField(blank=True)),
                ("errors", models.TextField(blank=True)),
                ("raw_json", models.JSONField(blank=True)),
                ("client_legal_name", models.CharField(blank=True, max_length=255)),
                ("entity_legal_name", models.CharField(blank=True, max_length=255)),
                (
                    "investment_fund_legal_name",
                    models.CharField(blank=True, max_length=255),
                ),
                (
                    "investment_managing_firm_name",
                    models.CharField(blank=True, max_length=255),
                ),
                ("portal_name", models.CharField(blank=True, max_length=255)),
                ("portal_login_url", models.CharField(blank=True, max_length=255)),
                ("receiving_email", models.CharField(blank=True, max_length=255)),
                (
                    "receiving_email_provider",
                    models.CharField(blank=True, max_length=255),
                ),
                (
                    "multi_factor_authentication_type",
                    models.CharField(blank=True, max_length=255),
                ),
                (
                    "multi_factor_authentication_details",
                    models.CharField(blank=True, max_length=255),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "line_item",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="builk_upload_rows",
                        to="webapp.lineitem",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "raw_bulk_upload",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rows",
                        to="webapp.rawbulkupload",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.CreateModel(
            name="RawDocument",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "document_type",
                    models.CharField(
                        choices=[
                            ("cc", "Capital Call"),
                            ("iu", "Investment Update"),
                            ("dn", "Distribution Notice"),
                            ("tl", "Tax or Legal"),
                            ("ot", "Other"),
                            ("un", "Unknown"),
                        ],
                        default="un",
                        max_length=2,
                    ),
                ),
                ("md5", models.CharField(max_length=255)),
                ("name", models.CharField(max_length=255)),
                ("s3_key", models.CharField(blank=True, max_length=255)),
                ("s3_bucket", models.CharField(blank=True, max_length=255)),
                ("exists_in_s3", models.BooleanField(default=False)),
                ("posted_date", models.DateField()),
                ("process_document_version", models.IntegerField(default=0)),
                ("has_been_processed", models.BooleanField(default=False)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "forwarding_rule",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="raw_documents",
                        to="webapp.forwardingrule",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.AddField(
            model_name="processeddocument",
            name="raw_retreival_document",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="processed_documents",
                to="webapp.rawdocument",
            ),
        ),
        migrations.CreateModel(
            name="Retrieval",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("task_id", models.CharField(blank=True, max_length=255)),
                ("retrieval_location", models.CharField(blank=True)),
                ("token_otp", models.CharField(blank=True, max_length=255)),
                ("number_documents_retrieved", models.IntegerField(default=0)),
                (
                    "retrieval_status",
                    models.CharField(
                        choices=[
                            ("ns", "Not Started"),
                            ("su", "Submitted"),
                            ("pc", "Cancel Pending"),
                            ("lp", "Login Pending"),
                            ("2p", "OTP Pending"),
                            ("2b", "OTP Blocked"),
                            ("ca", "Canceled"),
                            ("lf", "Login Failed"),
                            ("2f", "OTP Required and could not get OTP"),
                            ("pd", "Login Failed - Portal Down"),
                            ("li", "Successfully Logged In"),
                            ("dr", "Document Retrieval Pending"),
                            ("df", "Document Retrieval Failed"),
                            ("ds", "Successfully Retrieved Documents"),
                        ],
                        default="ns",
                        max_length=2,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "line_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="retrievals",
                        to="webapp.lineitem",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s",
                        to="webapp.organization",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_manager_name": "objects",
            },
        ),
        migrations.AddField(
            model_name="rawdocument",
            name="retrieval",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="raw_documents",
                to="webapp.retrieval",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="lineitem",
            unique_together={("organization", "investing_entity", "investment")},
        ),
    ]
