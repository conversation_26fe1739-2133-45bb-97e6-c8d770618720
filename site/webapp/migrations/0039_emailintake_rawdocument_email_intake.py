# Generated by Django 5.2.1 on 2025-05-22 13:46

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0038_alter_role_name_constraints'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailIntake',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('email_subject', models.CharField()),
                ('to_email', models.EmailField(max_length=254)),
                ('from_email', models.EmailField(max_length=254)),
                ('all_emails', models.J<PERSON>NField(default=list)),
                ('received_date', models.DateTimeField()),
                ('email_text_content', models.TextField(blank=True)),
                ('s3_key', models.CharField(blank=True, max_length=1024)),
                ('s3_bucket', models.CharField(blank=True, max_length=255)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s', to='webapp.organization')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
                'default_manager_name': 'objects',
            },
        ),
        migrations.AddField(
            model_name='rawdocument',
            name='email_intake',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='raw_documents', to='webapp.emailintake'),
        ),
    ]
