from django.http import HttpRequest, JsonResponse
from email_webhook.api import router as email_webhook_router
from ninja import NinjaAPI
from ninja.errors import AuthenticationError
from public_api.api import router as public_api_router

internal_api = NinjaAPI(docs_url=None, openapi_url=None, urls_namespace="internal")
internal_api.add_router("/email-webhook/", email_webhook_router)


@internal_api.exception_handler(AuthenticationError)
def custom_authentication_error_handler(request: HttpRequest, exc: AuthenticationError) -> JsonResponse:  # noqa: ARG001
    # How much google foo can a foo' google
    # https://repost.aws/knowledge-center/sns-topic-https-endpoints-notification
    headers = {}
    if request.path == "/api/email-webhook/sns-webhook":
        headers["WWW-Authenticate"] = "Basic"
    return JsonResponse({"detail": "Unauthorized", "path": request.path}, status=401, headers=headers)


external_api_v1 = NinjaAPI(
    title="Bridge API",
    version="1.0.0",
    description="Bridge API",
)
external_api_v1.add_router("/", public_api_router)
