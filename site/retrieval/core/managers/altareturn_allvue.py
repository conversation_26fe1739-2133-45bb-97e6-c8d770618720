import asyncio
import datetime
import hashlib
import json
import re
from collections.abc import Async<PERSON>enerator
from zipfile import ZipFile

import structlog
from bs4 import BeautifulSoup as bs  # noqa: N813
from dateutil import parser
from playwright.async_api import Locator
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.get_logger(__name__)


@register_strategy
class AllvueAltaReturnPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "altareturn.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "Altareturn"),
            ("<EMAIL>", "ALLVUE"),
            ("<EMAIL>", "Allvue - Luminate Capital"),
            ("<EMAIL>", "Allvue - Level Equity"),
            ("<EMAIL>", "Alta Return"),
            ("<EMAIL>", "ARC Aspenleaf (Alta)"),
        ]

    async def _enter_credentials(self, username: str, password: str) -> None:
        await self.page.get_by_placeholder("<EMAIL>").click()
        await self.page.get_by_placeholder("<EMAIL>").fill(username)
        await asyncio.sleep(1)
        await self.page.get_by_placeholder("Password").click()
        await self.page.get_by_placeholder("Password").fill(password)
        await asyncio.sleep(1)
        await self.page.locator("span#submitButton").click()

    async def _check_has_otp(self) -> bool:
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _check_login_successful(self) -> bool:
        return await self.page.get_by_text("Logout").count() > 0

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _dismiss_it_scheduled_maintenance(self) -> None:
        frames = [f for f in self.page.frames if "maintenance" in f.url.lower()]
        if len(frames) == 0:
            logger.info("No maintenance frame found")
        elif len(frames) == 1:
            logger.info("Dismissing maintenance frame")
            frame = frames[0]
            checked, clicked = 0, 0
            checkbox_locator = frame.locator("input[type='checkbox'][name='Notify']")
            if await checkbox_locator.count() > 0 and await checkbox_locator.is_visible():
                checked += await checkbox_locator.count()
                await checkbox_locator.first.check()
                await asyncio.sleep(1)
            button_locator = frame.locator("input[name='Ok']")
            if await button_locator.count() > 0 and await button_locator.is_visible():
                clicked += await button_locator.count()
                await button_locator.first.click()
                await asyncio.sleep(1)
            logger.info("Dismissed maintenance frame", checked=checked, clicked=clicked)
        else:
            logger.error("Multiple maintenance frames found", frame_urls=[f.url for f in frames])
            raise ValueError

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        await self._dismiss_it_scheduled_maintenance()
        soup = bs(markup=await self.page.content(), features="html.parser")
        balance_max_date, balance_metadata, balance_doc_hash = self.__account_balance_metadata(soup)
        if balance_metadata is not None and await self.should_download_document(balance_metadata):
            yield await self.__download_account_balance(balance_max_date, balance_metadata, balance_doc_hash)
        else:
            logger.info("Skipping account balance document", balance_max_date=balance_max_date)
        seen = set()
        rows = await self._create_rows_enumerator(seen)
        while rows:
            while rows:
                row_id, metadata, ref_date, raw_doc_type, doc_name = rows.pop(0)
                if await self.should_download_document(metadata):
                    try:
                        doc = await self.__download_doc(
                            row_id, metadata, ref_date, self.__document_mapper(raw_doc_type)
                        )
                    except ValueError:
                        logger.exception("Exception skipping document", doc_name=doc_name)
                        await self.page.reload()
                        await asyncio.sleep(5)
                        continue
                    if doc is not None:
                        yield doc
                else:
                    logger.info("Skipping document", doc_name=doc_name)
                    await self._row_locator(row_id).hover()
                    await asyncio.sleep(0.1)
            await asyncio.sleep(2)
            rows = await self._create_rows_enumerator(seen)

    def _row_locator(self, row_idx: str | None = None) -> Locator:
        selector = "div[role='row']"
        if row_idx is not None:
            selector = f"{selector}[aria-rowindex='{row_idx}']"
        return self.page.locator(".ms-DetailsList-contentWrapper").locator(selector)

    async def _create_rows_enumerator(self, seen: set[str]) -> list[tuple[str, str, datetime.datetime, str, str]]:
        rows = await self._row_locator().all()
        res = []
        for row in rows:
            row_id = await row.get_attribute("aria-rowindex")
            cells = row.locator("div[role='gridcell']")
            raw_date = (await cells.nth(5).inner_text()).strip()
            ref_date = parser.parse(raw_date)
            doc_name = (await cells.nth(1).inner_text()).strip()
            raw_doc_type = (await cells.nth(2).inner_text()).strip()
            metadata = json.dumps(
                {
                    "Document Name": doc_name,
                    "Document Type": raw_doc_type,
                    "Fund": (await cells.nth(3).inner_text()).strip(),
                    "Investor": (await cells.nth(4).inner_text()).strip(),
                    "Ref Date": raw_date,
                }
            )
            if metadata in seen:
                continue
            seen.add(metadata)
            res.append((row_id, metadata, ref_date, raw_doc_type, doc_name))
        logger.info(
            "Recreating rows enumerator",
            length_new_rows=len(res),
            length_seen_rows=len(seen),
            length_available_rows=len(rows),
        )
        return res

    async def __download_doc(
        self, row_id: str, metadata: str, ref_date: datetime.datetime, doc_type: str
    ) -> RawDocumentTuple:
        await self._row_locator(row_id).locator("div[role='gridcell']").nth(0).click()
        await asyncio.sleep(1)
        await self.page.locator("button").filter(has_text="Download").filter(has_text="document").click()
        await asyncio.sleep(1)
        async with self.page.expect_download() as download_info:
            await self.page.locator("button").filter(has_text="Download").filter(has_not_text="document").click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        zip_file_name = None
        with ZipFile(file_name, "r") as zf:
            zip_file_names = [zfn for zfn in zf.namelist() if not (zfn.endswith("/") or zfn.startswith("__MACOSX/"))]
            if len(zip_file_names) != 1:
                logger.warning("Multiple files in zip", zip_file_names=zip_file_names)
                raise ValueError
            zip_file_name = zip_file_names[0]
            with zf.open(zip_file_name) as pdf_file:
                content = pdf_file.read()
        file_name.unlink()
        return RawDocumentTuple(
            name=zip_file_name,
            date=ref_date,
            doc_type=doc_type,
            content=content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    def __account_balance_metadata(self, soup: bs) -> tuple[datetime.datetime | None, str | None, DocumentHash | None]:
        table_list = soup.select(".dashboardSummaryAccountBalance")
        if table_list is None or len(table_list) == 0:
            return None, None, None
        table = table_list[0]
        table_str = []
        table_str.append(",".join([h.text for h in table.select("th")]))
        max_date = None
        raw_max_date = None
        for row in table.select("tr"):
            tds = row.select("td")
            if len(tds) == 0:
                continue
            table_str.append(",".join([td.text for td in tds]))
            date_text = row.select("td")[1].text
            if date_text:
                try:
                    dt = parser.parse(date_text)
                    if max_date is None or dt > max_date:
                        max_date = dt
                        raw_max_date = date_text
                except parser.ParserError:
                    continue
        if raw_max_date is None:
            match = re.search(r"as of\s+([\w\s,]+?)\s*</span>", str(table.parent))
            raw_max_date = match.group(1) if match else None
            if raw_max_date:
                max_date = parser.parse(raw_max_date)
        csv = "\n".join(table_str)
        metadata = json.dumps(
            {
                "max_date": raw_max_date,
                "csv": csv,
            }
        )
        return max_date, metadata, self._doc_hash(metadata)

    def __document_mapper(self, document_type: str) -> str:
        return {
            "Annual Meeting": DocumentType.INVESTMENT_UPDATE,
            "Annual Meetings": DocumentType.INVESTMENT_UPDATE,
            "Audited Financials": DocumentType.INVESTMENT_UPDATE,
            "Audited Statements": DocumentType.INVESTMENT_UPDATE,
            "Quarterly and Annual Reports": DocumentType.INVESTMENT_UPDATE,
            "Quarterly Reports": DocumentType.INVESTMENT_UPDATE,
            "Annual Reports": DocumentType.INVESTMENT_UPDATE,
            "Capital Account Statements": DocumentType.ACCOUNT_STATEMENT,
            "Capital Call Notices": DocumentType.CAPITAL_CALL,
            "Capital Calls": DocumentType.CAPITAL_CALL,
            "Distribution Notices": DocumentType.DISTRIBUTION_NOTICE,
            "Distributions": DocumentType.DISTRIBUTION_NOTICE,
            "Fund Legal Documents": DocumentType.LEGAL,
            "Legal Documents - Fund": DocumentType.LEGAL,
            "Legal Documents - Investor": DocumentType.LEGAL,
            "Investor Legal Documents": DocumentType.LEGAL,
            "Tax Documents": DocumentType.TAX,
            "Tax Information": DocumentType.TAX,
            "K1 Consent": DocumentType.TAX,
            "General Correspondence": DocumentType.OTHER,
            "Other Notices": DocumentType.OTHER,
            "Miscellaneous": DocumentType.OTHER,
        }.get(document_type, DocumentType.UNKNOWN)

    async def __download_account_balance(
        self, max_date: datetime.datetime, metadata: str, doc_hash: DocumentHash
    ) -> RawDocumentTuple:
        async with self.page.expect_download() as download_info:
            await self.page.get_by_text("Export to PDF").click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = file_name.read_bytes()
        file_name.unlink()

        return RawDocumentTuple(
            name=download.suggested_filename,
            date=max_date,
            doc_type=DocumentType.ACCOUNT_STATEMENT,
            content=content,
            raw_metadata=metadata,
            doc_hash=doc_hash,
        )
