import structlog
from django import forms
from django.contrib import admin, messages
from django.contrib.admin.filters import AllValuesFieldListFilter
from django.db import transaction
from django.db.models import QuerySet
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from django.urls import URLPattern, path, reverse

from webapp.admin.utils import AllValuesAndNoneFieldListFilter, dropdown_filter
from webapp.models import LineItem, Organization, Portal
from webapp.views.utils import redirect_with_url_params

logger = structlog.get_logger(__name__)


class LineItemInline(admin.TabularInline):
    model = LineItem
    list_select_related = ("investing_entity", "investment", "investing_entity__client")


class SelectPortalForm(forms.Form):
    def __init__(self, *args, organization: Organization | None = None, **kwargs) -> None:  # noqa: ANN002, ANN003
        super().__init__(*args, **kwargs)
        portal_field = self.fields["portal"]
        if not isinstance(portal_field, forms.ModelChoiceField):
            raise TypeError
        if organization is None:
            portal_field.queryset = Portal.objects.none()
        else:
            portal_field.queryset = Portal.objects.filter(organization=organization)

    portal: "forms.ModelChoiceField[Portal]" = forms.ModelChoiceField(queryset=None)


@admin.action(description="Reassign portal")
def reassign_portal_action(
    modeladmin: "LineItemAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: QuerySet,
) -> HttpResponseRedirect | None:
    raw_pks = queryset.values_list("pk", flat=True)
    selected = ",".join([str(pk) for pk in raw_pks])
    line_items = LineItem.objects.active().filter(pk__in=raw_pks).all()
    organizations = {line_item.organization for line_item in line_items}
    if line_items.count() == 0:
        messages.error(request, "Please only select at least one line item")
        return None
    if len(organizations) > 1:
        messages.error(request, "Please select line items from the same organization")
        return None
    return redirect_with_url_params("admin:webapp_reassign_portal_line_item", pks=selected)


def reassign_portal_view(request: HttpRequest) -> HttpResponse:
    template = "admin/webapp/lineitem/reassign_portal.html"
    raw_pks = request.GET.get("pks", "")
    pks = raw_pks.split(",")
    line_items = LineItem.objects.active().filter(pk__in=pks).all()
    organization = line_items.first().organization if line_items.first() else None
    if request.method == "GET":
        return TemplateResponse(
            request,
            template,
            {
                "form": SelectPortalForm(organization=organization),
            },
        )
    if request.method == "POST":
        form = SelectPortalForm(organization=organization, data=request.POST)
        if form.is_valid():
            logger.info("Reassigning portal", pks=pks, request=request.POST, form=form.cleaned_data)
            portal = form.cleaned_data["portal"]
            if not isinstance(portal, Portal):
                raise TypeError

            with transaction.atomic():
                for line_item in line_items:
                    line_item.merged_portal_credential.portal = portal
                    line_item.merged_portal_credential.portal_credential.portal = portal
                    line_item.merged_portal_credential.save()
                    line_item.merged_portal_credential.portal_credential.save()
                    messages.success(
                        request, f"Portal reassigned successfully MPC ({line_item.merged_portal_credential.pk})"
                    )
                    messages.success(
                        request,
                        f"Portal reassigned successfully PC ({
                            line_item.merged_portal_credential.portal_credential.pk
                        })",
                    )
            return HttpResponseRedirect(reverse("admin:webapp_lineitem_changelist") + f"?portal__id__exact={portal.pk}")

    return HttpResponse("Invalid request method", status=405)


@admin.action(description="Hide from user")
def mark_not_visible(
    modeladmin: "LineItemAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: QuerySet,
) -> None:
    pks = {str(pk) for pk in queryset.values_list("pk", flat=True)}
    line_items = LineItem.objects.filter(pk__in=pks)
    line_items.update(is_visible=False)
    messages.success(request, f"Line items marked as hidden ({', '.join(pks)})")


@admin.action(description="Show to user")
def mark_visible(
    modeladmin: "LineItemAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: QuerySet,
) -> None:
    pks = {str(pk) for pk in queryset.values_list("pk", flat=True)}
    line_items = LineItem.objects.filter(pk__in=pks)
    line_items.update(is_visible=True)
    messages.success(request, f"Line items marked as visible ({', '.join(pks)})")


@admin.action(description="Mark as not backfilled")
def mark_not_backfilled(
    modeladmin: "LineItemAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: QuerySet,
) -> None:
    pks = {str(pk) for pk in queryset.values_list("pk", flat=True)}
    line_items = LineItem.objects.filter(pk__in=pks)
    line_items.update(is_backfilled=False)
    messages.success(request, f"Line items marked as not backfilled ({', '.join(pks)})")


@admin.action(description="Mark as fully backflled")
def mark_backfilled(
    modeladmin: "LineItemAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: QuerySet,
) -> None:
    pks = {str(pk) for pk in queryset.values_list("pk", flat=True)}
    line_items = LineItem.objects.filter(pk__in=pks)
    line_items.update(is_backfilled=True)
    messages.success(request, f"Line items marked as backfilled ({', '.join(pks)})")


@admin.action(description="Reset Merged Portal Credential")
def reset_merged_portal_credential_action(
    modeladmin: "LineItemAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: QuerySet,
) -> None:
    raw_pks = queryset.values_list("pk", flat=True)
    line_items = LineItem.objects.filter(pk__in=raw_pks)
    if line_items.count() == 0:
        messages.error(request, "Please select at least one line item")
        return
    for line_item in line_items:
        if line_item.created_by is None:
            messages.error(request, f"Line item {line_item.pk} has no created_by user")
            continue
        line_item.unlink_and_reset_merged_credential(line_item.created_by)
        messages.success(
            request,
            f"Line item {line_item.pk} has been reset and unlinked from the merged portal credential",
        )


@admin.register(LineItem)
class LineItemAdmin(admin.ModelAdmin):
    actions = (
        reset_merged_portal_credential_action,
        reassign_portal_action,
        mark_visible,
        mark_not_visible,
        mark_backfilled,
        mark_not_backfilled,
    )
    list_display = (
        "merged_portal_credential",
        "is_visible",
        "is_backfilled",
        "organization",
        "investing_entity",
        "investment",
        "last_retrieval",
    )
    show_facets = admin.ShowFacets.ALWAYS
    list_filter = (
        ("organization__name", dropdown_filter(AllValuesFieldListFilter, "Organization")),
        "is_visible",
        "is_backfilled",
        ("investing_entity__legal_name", dropdown_filter(AllValuesFieldListFilter, "Investing Entity")),
        ("investment__legal_name", dropdown_filter(AllValuesFieldListFilter, "Investment")),
        ("investing_entity__client__legal_name", dropdown_filter(AllValuesFieldListFilter, "Client")),
        (
            "last_retrieval__manager",
            dropdown_filter(AllValuesAndNoneFieldListFilter, "Portal Name"),
        ),
        (
            "merged_portal_credential__portal__name",
            dropdown_filter(AllValuesAndNoneFieldListFilter, "User Portal Name"),
        ),
    )
    search_fields = (
        "organization__name",
        "investing_entity__legal_name",
        "investment__legal_name",
        "investing_entity__client__legal_name",
    )
    ordering = ("-organization", "investing_entity")

    def get_urls(self) -> list[URLPattern]:
        return [
            path(
                "reassign_portal_for_line_item",
                self.admin_site.admin_view(reassign_portal_view),
                name="webapp_reassign_portal_line_item",
            ),
            *super().get_urls(),
        ]
