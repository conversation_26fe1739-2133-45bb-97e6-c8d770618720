{% load customtags %}
{% load static %}
{% if not upcoming_capital_call.processed_doc.has_been_viewed %}
    {% alias "bg-secondary-orange-beige font-semibold" as background %}
{% else %}
    {% alias "bg-white" as background %}
{% endif %}
<tr class="border-b {{ background }} h-[47px]"
    {# djlint:off #}
    _="
    on openedProcessedDocPdf[pk=='{{ upcoming_capital_call.processed_doc.pk }}'] from document
        remove .bg-secondary-orange-beige remove .font-semibold add .bg-white
    end
    "
    {# djlint:on #}
    >
    <td class="px-3 py-3 text-[#343D36] font-inter text-[14px] date-cell">{{ upcoming_capital_call.due_date }}</td>
    <td class="px-3 py-3 text-[#343D36] font-inter text-[14px]">{{ upcoming_capital_call.entity }}</td>
    <td class="px-3 py-3 text-[#343D36] font-inter text-[14px]">{{ upcoming_capital_call.investment }}</td>
    <td class="px-3 py-3 text-[#343D36] font-inter text-[14px]">
        <div class="flex items-center gap-x-2">
            <a href="{% url 'pdf_modal' %}{% urlparams processed_doc_pk=upcoming_capital_call.processed_doc.pk %}"
                target="_blank"
                title="{{ upcoming_capital_call.processed_doc.name }}"
                {# djlint:off #}
               _="
                    on click or auxclick
                        trigger openedProcessedDocPdf(pk:'{{ upcoming_capital_call.processed_doc.pk }}')
                    end
                "
                {# djlint:on #}
                >
                <img height="20"
                     width="20"
                     src="{% static 'assets/images/pdf.svg' %}"
                     alt="pdf icon"
                     class="inline-block" />
                <span>{{ upcoming_capital_call.amount|format_as_currency }}</span>
            </a>
        </div>
    </td>
    <td class="px-3 py-3 text-[#343D36] font-inter text-[14px]">
        <input type="hidden"
               name="capital_call_document_pk"
               value="{{ upcoming_capital_call.processed_doc.capital_call_document.pk }}">
        <div class="relative flex items-center w-full">
            <select class="rounded-md border px-2 py-1 text-[14px] text-[#343D36] font-medium font-sans border-1 {% if upcoming_capital_call.processed_doc.capital_call_document.status == 'executed' %} border-[#689757] bg-[#EAF4EA] {% else %} border-[#3B82F6] bg-[#EAF1FB] {% endif %} focus:outline-none focus:ring-0"
                    data-capital-call-id="{{ upcoming_capital_call.processed_doc.capital_call_document.pk }}"
                    data-current-status="{{ upcoming_capital_call.processed_doc.capital_call_document.status }}"
                    hx-post="{% url 'update_capital_call_status' %}"
                    hx-target="closest tr"
                    hx-swap="outerHTML"
                    hx-trigger="change"
                    hx-include="closest td"
                    _="on htmx:afterRequest[detail.xhr.getResponseHeader('HX-Trigger')?.includes('removeRow')] remove closest tr"
                    name="status">
                {% for value, label in capital_call_status_choices %}
                    <option value="{{ value }}"
                            {% if upcoming_capital_call.processed_doc.capital_call_document.status == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                {% endfor %}
            </select>
        </div>
    </td>
</tr>
