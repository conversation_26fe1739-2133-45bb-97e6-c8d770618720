<div id="notifications-card"
     hx-post="{% url 'mark_documents_viewed' %}"
     hx-trigger="openedProcessedDocPdf from:document delay:200ms"
     hx-push-url="false"
     hx-swap="outerHTML"
     class="flex flex-col lg:flex-row items-start lg:items-center justify-between bg-gray-100 p-4 rounded-lg shadow mb-4 gap-4 lg:gap-0">
    <div class="text-lg lg:text-2xl font-semibold text-gray-800">
        Unread Documents: <span id="notifications-count" class="font-bold">{{ notifications }}</span>
    </div>
    <div class="text-sm text-gray-600 w-full lg:w-auto">
        <p class="mb-1 lg:mb-0">
            Total line items: <span class="font-semibold text-[#689757]">{{ total_line_items }}</span>
        </p>
        <p>
            Last notice received: <span class="font-semibold text-[#689757]">
            {% if last_notice_received is None %}
                -
            {% else %}
                {{ last_notice_received }}
            {% endif %}
        </span>
    </p>
</div>
</div>
