import pytest
from phonenumbers import NumberParseException

from webapp.models.portal import Portal, SMSMessage
from webapp.models.user import BridgeUser
from webapp.repository.sms_repository import InvalidMessageError, SMSRepository

pytestmark = pytest.mark.django_db


@pytest.fixture
def sms_repository() -> SMSRepository:
    """Create an SMS repository instance without a user number."""
    return SMSRepository()


@pytest.fixture
def sms_repository_with_user(request) -> SMSRepository:
    """Create an SMS repository instance with a user number."""
    user_phone_number = getattr(request, "param", "+15852223333")
    return SMSRepository(user_phone_number=user_phone_number)


class TestSMSRepository:
    def test_validate_phone_number(self, sms_repository: SMSRepository) -> None:
        """Test phone number format validation."""
        assert sms_repository._validate_phone_number("+15852223333")  # noqa: SLF001
        assert sms_repository._validate_phone_number("+441234567891")  # noqa: SLF001
        assert not sms_repository._validate_phone_number("invalid")  # noqa: SLF001
        assert not sms_repository._validate_phone_number("123456789")  # noqa: SLF001
        assert not sms_repository._validate_phone_number("")  # noqa: SLF001

    @pytest.mark.skip(reason="need to create a user with email")
    def test_receive_message_as_webhook_valid(self, sms_repository: SMSRepository) -> None:
        """Test receiving a valid webhook message."""
        message = sms_repository.receive_message_as_webhook(
            email="<EMAIL>", from_number="+15852223333", body="Your verification code is 123456"
        )
        assert message is not None
        assert str(message.user_phone_number) == "+15852223333"
        assert message.body == "Your verification code is 123456"

    def test_receive_message_as_webhook_invalid_phone(self, sms_repository: SMSRepository) -> None:
        """Test receiving a webhook message with invalid phone number."""
        with pytest.raises(InvalidMessageError, match="Phone number is not valid"):
            sms_repository.receive_message_as_webhook(
                user_phone_number="+15852223333",
                portal_phone_number="invalid",
                body="Your code is 123456",
                email="<EMAIL>",
                portal_name="Test Portal",
            )

    def test_get_most_recent_messages(self, admin_user: BridgeUser) -> None:
        """
        Test retrieving most recent messages.
        """
        _ = Portal.objects.create(
            name="Test Portal",
            portal_login_url="https://test.com",
            user=admin_user,
            organization=admin_user.organization,
        )
        mock_messages = [
            SMSMessage.objects.create(user=admin_user, user_phone_number="+15852223333", body="Code 123456"),
            SMSMessage.objects.create(user=admin_user, user_phone_number="+15852223333", body="Code 789012"),
        ]
        sms_repository = SMSRepository(user_phone_number="+15852223333")
        messages = sms_repository.get_most_recent_messages(limit=1)
        assert list(messages) == [mock_messages[1]]

    @pytest.mark.parametrize("sms_repository_with_user", ["+15852223333"], indirect=True)
    def test_get_most_recent_code(self, sms_repository_with_user: SMSRepository, admin_user: BridgeUser) -> None:
        """Test retrieving the most recent code for a user."""
        # Create test messages
        SMSMessage.objects.create(user=admin_user, user_phone_number="+15852223333", body="Old code 123456")
        recent_message = SMSMessage.objects.create(
            user=admin_user, user_phone_number="+15852223333", body="New code 789012"
        )

        # Get the most recent code
        code = sms_repository_with_user.get_most_recent_code()

        # Verify the correct code was returned
        assert code == "789012"

        # Test with a message that doesn't contain a code
        recent_message.body = "No code here"
        recent_message.save()

        # Should return None when no code is found
        assert sms_repository_with_user.get_most_recent_code() is None

    def test_normalize_phone_number_valid(self, sms_repository: SMSRepository) -> None:
        """Test phone number normalization with valid numbers."""
        # Test US number
        assert sms_repository.normalize_phone_number("+15852223333") == "+15852223333"
        # Test with formatting
        assert sms_repository.normalize_phone_number("(*************") == "+15852223333"
        # Test with country code
        assert sms_repository.normalize_phone_number("+44 1234 567890") == "+441234567890"

    def test_normalize_phone_number_invalid_format(self, sms_repository: SMSRepository, monkeypatch) -> None:
        """Test phone number normalization with invalid format (NumberParseException)."""

        # Mock PhoneNumber.from_string to raise NumberParseException
        def mock_from_string(*args: object, **kwargs: object) -> None:
            raise NumberParseException(0, "Invalid number")

        from phonenumber_field.phonenumber import PhoneNumber

        monkeypatch.setattr(PhoneNumber, "from_string", mock_from_string)

        # Should return the original string when normalization fails
        invalid_number = "not-a-number"
        assert sms_repository.normalize_phone_number(invalid_number) == invalid_number

    def test_normalize_phone_number_value_error(self, sms_repository: SMSRepository, monkeypatch) -> None:
        """Test phone number normalization with ValueError."""

        # Mock PhoneNumber.from_string to raise ValueError
        def mock_from_string(*args: object, **kwargs: object) -> None:
            raise ValueError("Invalid number")  # noqa: EM101

        from phonenumber_field.phonenumber import PhoneNumber

        monkeypatch.setattr(PhoneNumber, "from_string", mock_from_string)

        # Should return the original string when normalization fails
        invalid_number = "123"
        assert sms_repository.normalize_phone_number(invalid_number) == invalid_number
