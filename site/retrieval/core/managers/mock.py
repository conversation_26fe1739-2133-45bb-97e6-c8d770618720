import asyncio
import random
import uuid
from collections.abc import AsyncGenerator

from django.conf import settings
from django.utils import timezone
from webapp.models import DocumentType
from webapp.models.portal import MFAType

from retrieval.core.strategy import DocumentHash, RawDocumentTuple, RetrievalManager


class MockTestManagerForDev(RetrievalManager):
    def __init__(self, *args, **kwargs) -> None:  # noqa: ANN002, ANN003
        super().__init__(*args, **kwargs)
        self.has_entered_otp = False
        self.has_entered_creds = False

    async def delay(self, seconds: int) -> None:
        to_sleep = 0
        if not settings.DEBUG:
            to_sleep = 1
            to_sleep = random.randint(seconds - 1, seconds + 2)  # noqa: S311 # nosec
        await asyncio.sleep(to_sleep)

    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        return portal_login_url is not None

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:  # noqa: ARG002
        await self.delay(3)
        self.has_entered_creds = True

    async def _check_has_otp(self) -> bool:
        await self.delay(2)
        return not self.has_entered_otp and self.has_entered_creds

    async def _send_otp(self) -> None:
        pass

    async def _check_login_successful(self) -> bool:
        await self.delay(1)
        return self.has_entered_creds and self.has_entered_otp

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        await self.delay(3)
        self.has_entered_otp = True

    async def _wait_for_otp(self) -> str | None:
        mfa_type = await self.get_multi_factor_authentication_type()
        if mfa_type == MFAType.LIVE_LOGIN:
            return await super()._wait_for_otp()
        await self.delay(5)
        return "123456"

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        for i in range(5):
            data = f"mock{i}.pdf"
            now = timezone.now()
            await self.delay(2)
            yield RawDocumentTuple(data, now, DocumentType.UNKNOWN, doc_hash=self._doc_hash(data))

    def _doc_hash(self, _: str) -> DocumentHash:
        return DocumentHash(str(uuid.uuid4()), -1, "mock")

    @classmethod
    def is_demo(cls) -> bool:
        return True
