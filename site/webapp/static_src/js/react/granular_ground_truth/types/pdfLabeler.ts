import { JSONSchema } from "./jsonSchema";

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface BoxData {
  id: number;
  pageNumber: number;
  boundingBox: BoundingBox;
  content: string;
  schema?: JSONSchema;
  isGroundTruth?: boolean;
  imageBinary?: string;
  element?: HTMLDivElement;
  data?: any;
  path?: string;
  isValid?: boolean;
  isSaved?: boolean;
  needsSync?: boolean;
  targetUri?: string;
}

export interface SelectionData {
  boundingBox: BoundingBox;
  imageBinary: string;
  content: string;
}
