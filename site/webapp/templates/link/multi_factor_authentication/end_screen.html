{% comment %} TODO better message/success/error handling {% endcomment %}
{% load static %}
<div class="w-full max-w-lg max-h-full">
    <!-- Modal content -->
    <div class="relative">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-4 md:p-5 rounded-t dark:border-gray-600">
            <div class="p-2 bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
                <div class="relative flex-col justify-start items-start flex">
                    <div class="relative">
                        <img src="{% static 'assets/images/tick_mark.svg' %}"
                             width="38"
                             height="20"
                             alt="closeButton">
                    </div>
                </div>
            </div>
            <button _="on click trigger closeModal"
                    type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:tex t-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                <img src="{% static 'assets/images/close.svg' %}"
                     width="16"
                     height="19"
                     alt="closeButton">
                <span class="sr-only">Close modal</span>
            </button>
        </div>
        <!-- Modal body -->
        <div class="px-4 py-2">
            {% if success %}
                <div class="text-[#343d36] text-lg font-semibold font-inter leading-[27px] pb-2">Success!</div>
                <div class="text-[#616863] text-sm font-normal font-inter leading-[21px] pb-2">
                    You have successfully stored your Multi-Factor Authentication details for the {{ merged_portal_credential.portal.name }} portal.
                </div>
                <div class="text-[#616863] text-sm font-bold font-inter leading-[21px]">
                    We are working on validating your credentials, please check back shortly.
                </div>
            {% else %}
                <div class="text-red-500 text-lg font-semibold font-inter leading-[27px]">
                    Unfortunately some error occured. Please reach out for support.
                </div>
                <div class="max-h-[400px] overflow-auto mt-2 p-2 rounded-lg w-11/12 mx-auto">
                    <div class="text-[#616863] text-sm font-normal font-inter leading-[21px]">
                        Please make sure you're uploading PNG or JPG format and that the QR code is clear and readable. We only support time-based OTPs (TOTP) for Authenticator Multi-Factor Authentication.
                    </div>
                </div>
            {% endif %}
        </div>
        <!-- Modal footer -->
        <div class="flex items-center p-4 md:p-4 pr-4 border-gray-200 rounded-b dark:border-gray-600">
            <button _="on click trigger closeModal"
                    class="self-stretch h-11 w-full px-4 py-2 bg-[#924f34] rounded-lg shadow-inner justify-center items-center gap-1.5 inline-flex">
                <div id="refresh"
                     class="text-white text-base font-semibold font-inter leading-normal">Continue</div>
            </button>
        </div>
    </div>
</div>
