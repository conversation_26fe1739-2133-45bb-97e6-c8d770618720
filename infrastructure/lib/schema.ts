import { Construct } from "constructs";
import * as cdk from "aws-cdk-lib";
import * as ssm from "aws-cdk-lib/aws-ssm";

export interface SharedProps extends cdk.StackProps {
  awsAccount: string;
  awsRegion: string;
  deploymentTarget: string;
  hostedZoneId: string;
  domainName: string;
}

export class SharedStack extends cdk.Stack {
  sharedProps: SharedProps;
  constructor(scope: Construct, id: string, props: SharedProps) {
    super(scope, id, props);
    this.sharedProps = props;
  }
}

function getParameterName(
  stack: SharedStack,
  obj: CdkOutputtable,
  field: string,
): string {
  const stack_name = obj.constructor.name;
  return `/bridge/infra/${stack.sharedProps.deploymentTarget}/${stack_name}/${field}`;
}

abstract class CdkOutputtable {
  [key: string]: string | string[] | Function;

  toCdkOutput(stack: SharedStack): void {
    for (const field of Object.keys(this)) {
      if (typeof this[field] === "function") {
        continue; // Skip methods
      }
      const output = this[field];
      const parameterName = getParameterName(stack, this, field);
      if (Array.isArray(output)) {
        new ssm.StringParameter(stack, `${field}Parameter`, {
          parameterName: parameterName,
          stringValue: output.join(","),
        });
      } else if (typeof output === "string") {
        new ssm.StringParameter(stack, `${field}Parameter`, {
          parameterName: parameterName,
          stringValue: output,
        });
      } else {
        throw new Error(
          `Property ${field} must be a string or an array of strings`,
        );
      }
    }
  }

  static fromCdkOutput<T extends CdkOutputtable>(
    stack: SharedStack,
    type: new () => T,
  ): T | undefined {
    const instance = new type();
    for (const field of Object.keys(instance)) {
      if (typeof instance[field] === "function") {
        continue; // Skip methods
      }
      const initialValue = instance[field];
      const parameterName = getParameterName(stack, instance, field);
      const value = ssm.StringParameter.valueFromLookup(stack, parameterName);
      if (value.startsWith("dummy")) {
        console.log("Returned a dummy value for ", parameterName);
        return undefined;
      }
      if (Array.isArray(initialValue)) {
        (instance as any)[field] = value.split(",");
      } else if (typeof initialValue === "string") {
        (instance as any)[field] = value;
      } else {
        throw new Error(
          `Property ${field} must be a string or an array of strings`,
        );
      }
    }
    return instance;
  }
}

// ConfigStackOutputs
class ConfigStackOutputsProps {
  public AwsAccount: string = "";
  public AwsRegion: string = "";
  public DeploymentTarget: string = "";
  public HostedZoneId: string = "";
  public DomainName: string = "";
  public DjangoSecretArn: string = "";
  public BulkUploadsS3Path: string = "";
  public EmailDocsS3Path: string = "";
  public ProcessedDocsS3Path: string = "";
  public RetrievalLogsS3Path: string = "";
  public EmailS3Path: string = "";
  public VantaAuditorRoleArn: string = "";
  public GitHubActionsRoleArn: string = "";
  public MsftEmailOauthSecretArn: string = "";
  public DevTwilioSecretArn: string = "";
  public SlackSecretArn: string = "";
  public PythonContainerRepositoryName: string = "";
  public UserBucketName: string = "";
  public SftpBucketName: string = "";
  public DatabaseName: string = "";
  public UserDataKmsName: string = "";
  public EmailTopicName: string = "";
  public EmailWebhookPath: string = "";
  public EmailWebhookUsername: string = "";
}

export class ConfigStackOutputs extends CdkOutputtable {
  public AwsAccount: string = "";
  public AwsRegion: string = "";
  public DeploymentTarget: string = "";
  public HostedZoneId: string = "";
  public DomainName: string = "";
  public DjangoSecretArn: string = "";
  public BulkUploadsS3Path: string = "";
  public EmailDocsS3Path: string = "";
  public ProcessedDocsS3Path: string = "";
  public RetrievalLogsS3Path: string = "";
  public EmailS3Path: string = "";
  public VantaAuditorRoleArn: string = "";
  public GitHubActionsRoleArn: string = "";
  public MsftEmailOauthSecretArn: string = "";
  public DevTwilioSecretArn: string = "";
  public SlackSecretArn: string = "";
  public PythonContainerRepositoryName: string = "";
  public UserBucketName: string = "";
  public SftpBucketName: string = "";
  public DatabaseName: string = "";
  public UserDataKmsName: string = "";
  public EmailTopicName: string = "";
  public EmailWebhookPath: string = "";
  public EmailWebhookUsername: string = "";
  constructor(props?: ConfigStackOutputsProps) {
    super();
    if (props) {
      Object.assign(this, props);
    }
  }
}

// NetworkStackOutputs
class NetworkStackOutputsProps {
  public VpcId: string = "";
  public VpcAvailabilityZones: string[] = [];
  public VpcPublicSubnetIds: string[] = [];
  public VpcPublicRouteTableIds: string[] = [];
  public VpcCidrBlock: string = "";
}

export class NetworkStackOutputs extends CdkOutputtable {
  public VpcId: string = "";
  public VpcAvailabilityZones: string[] = [];
  public VpcPublicSubnetIds: string[] = [];
  public VpcPublicRouteTableIds: string[] = [];
  public VpcCidrBlock: string = "";

  constructor(props?: NetworkStackOutputsProps) {
    super();
    if (props) {
      Object.assign(this, props);
    }
  }
}

// StorageStackOutputs
class StorageStackOutputsProps {
  public EmailConfigurationSetName: string = "";
  public EmailSendAddress: string = "";
  public DBCredentialsSecretArn: string = "";
  public DBInstanceIds: string[] = [];
  public DBClusterArn: string = "";
  public S3BucketName: string = "";
  public RedisSecretArn: string = "";
  public RedisClusterName: string = "";
  public UserDataKmsArn: string = "";
  public RedisClusterPrimaryEndPointAddress: string = "";
  public RedisClusterPrimaryEndPointPort: string = "";
  public RedisSecurityGroupId: string = "";
}

export class StorageStackOutputs extends CdkOutputtable {
  public EmailConfigurationSetName: string = "";
  public EmailSendAddress: string = "";
  public DBCredentialsSecretArn: string = "";
  public DBInstanceIds: string[] = [];
  public DBClusterArn: string = "";
  public S3BucketName: string = "";
  public RedisSecretArn: string = "";
  public RedisClusterName: string = "";
  public UserDataKmsArn: string = "";
  public RedisClusterPrimaryEndPointAddress: string = "";
  public RedisClusterPrimaryEndPointPort: string = "";
  public RedisSecurityGroupId: string = "";

  constructor(props?: StorageStackOutputsProps) {
    super();
    if (props) {
      Object.assign(this, props);
    }
  }
}

// ClusterStackOutputs
class ClusterStackOutputsProps {
  public ClusterName: string = "";
  public ExecutionRoleArn: string = "";
}

export class ClusterStackOutputs extends CdkOutputtable {
  public ClusterName: string = "";
  public ExecutionRoleArn: string = "";
  constructor(props?: ClusterStackOutputsProps) {
    super();
    if (props) {
      Object.assign(this, props);
    }
  }
}

// CeleryServiceStackOutputs
class CeleryServiceStackOutputsProps {
  public CeleryTaskRoleArn: string = "";
}

export class CeleryServiceStackOutputs extends CdkOutputtable {
  public CeleryTaskRoleArn: string = "";
  constructor(props?: CeleryServiceStackOutputsProps) {
    super();
    if (props) {
      Object.assign(this, props);
    }
  }
}

// SiteServiceStackOutputs
class SiteServiceStackOutputsProps {
  public LoadBalancerName: string = "";
}

export class SiteServiceStackOutputs extends CdkOutputtable {
  public LoadBalancerName: string = "";
  constructor(props?: SiteServiceStackOutputsProps) {
    super();
    if (props) {
      Object.assign(this, props);
    }
  }
}

// MonitoringStackOutputsProps
class MonitoringStackOutputsProps {}

export class MonitoringStackOutputs extends CdkOutputtable {
  constructor(props?: MonitoringStackOutputsProps) {
    super();
    if (props) {
      Object.assign(this, props);
    }
  }
}

// SFTP

// StorageStackOutputs
class SFTPOutputsProps {
  public SftpStaticIpAddresses: string[] = [];
  public SftpServerId: string = "";
}

export class SFTPOutputs extends CdkOutputtable {
  public SftpStaticIpAddresses: string[] = [];
  public SftpServerId: string = "";

  constructor(props?: SFTPOutputsProps) {
    super();
    if (props) {
      Object.assign(this, props);
    }
  }
}

// AllStackOutputs
// Remember to update ./site/bridge_project/cdk_schema.py class
export class AllStackOutputs {
  // StackName to StackOutput Type
  public ConfigStack?: ConfigStackOutputs;
  public NetworkStack?: NetworkStackOutputs;
  public StorageStack?: StorageStackOutputs;
  public ClusterStack?: ClusterStackOutputs;
  public CeleryServiceStack?: CeleryServiceStackOutputs;
  public SiteServiceStack?: SiteServiceStackOutputs;
  public MonitoringStack?: MonitoringStackOutputs;
}
