import React, { useState } from "react";
import { JSONSchema } from "../../types/jsonSchema";
import Form from "@rjsf/core";
import { RJSFSchema, WidgetProps, RegistryWidgetsType } from "@rjsf/utils";
import type { IChangeEvent } from "@rjsf/core";
import validator from "@rjsf/validator-ajv8";

// Custom number widget with comma formatting
const CustomNumberWidget = (props: WidgetProps) => {
  const {
    value,
    onChange,
    onBlur,
    onFocus,
    id,
    placeholder,
    disabled,
    readonly,
    required,
    schema,
  } = props;

  const formatNumberWithCommas = (num: string | number) => {
    if (num === null || num === undefined || num === "") return "";
    const numStr = num.toString().replace(/,/g, "");
    if (isNaN(Number(numStr))) return num;
    if (numStr.endsWith(".") || numStr.endsWith(".0")) {
      return numStr; // Don't format if it ends with a dot
    }
    return Number(numStr).toLocaleString();
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = event.target.value.replace(/,/g, "");
    if (rawValue.endsWith(".") || rawValue.endsWith(".0")) {
      onChange(rawValue);
    } else if (rawValue === "") {
      onChange(undefined);
    } else if (!isNaN(Number(rawValue))) {
      onChange(Number(rawValue));
    }
  };

  return (
    <input
      type="text"
      id={id}
      value={formatNumberWithCommas(value || "")}
      onChange={handleChange}
      onBlur={onBlur && ((event) => onBlur(id, event.target.value))}
      onFocus={onFocus && ((event) => onFocus(id, event.target.value))}
      placeholder={placeholder}
      disabled={disabled}
      readOnly={readonly}
      required={required}
      className="form-control"
    />
  );
};

const customWidgets: RegistryWidgetsType = {
  commaNumberWidget: CustomNumberWidget,
};

interface LabelSelectorFormProps {
  schema: RJSFSchema | null;
  data: any | null;
  setData: (data: any | null) => void;
}

const LabelSelectorForm: React.FC<LabelSelectorFormProps> = ({
  schema,
  data,
  setData,
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(data === null);

  // Function to generate uiSchema that applies comma formatting to number fields
  const generateUiSchemaWithNumberFormatting = (schema: RJSFSchema): any => {
    const uiSchema: any = {
      "ui:options": {
        classNames: "bg-white text-black",
      },
    };

    // Recursively apply number widget to number/integer fields
    const applyNumberWidgetToSchema = (schema: any, currentUiSchema: any) => {
      if (schema.type === "number" || schema.type === "integer") {
        currentUiSchema["ui:widget"] = "commaNumberWidget";
      } else if (schema.type === "object" && schema.properties) {
        Object.keys(schema.properties).forEach((key) => {
          if (!currentUiSchema[key]) currentUiSchema[key] = {};
          applyNumberWidgetToSchema(
            schema.properties[key],
            currentUiSchema[key],
          );
        });
      } else if (schema.type === "array" && schema.items) {
        if (!currentUiSchema.items) currentUiSchema.items = {};
        applyNumberWidgetToSchema(schema.items, currentUiSchema.items);
      }
    };

    if (schema) {
      applyNumberWidgetToSchema(schema, uiSchema);
    }

    return uiSchema;
  };

  const handleFormSubmit = (
    data: IChangeEvent<any>,
    event: React.FormEvent<any>,
  ) => {
    setData(data.formData);
    setIsEditing(false);
  };
  return (
    <div className="mb-2">
      <label className="block text-xs font-medium mb-1 text-gray-300">
        Label:
      </label>
      {schema && isEditing && (
        <Form
          schema={schema}
          validator={validator}
          formData={data}
          onSubmit={handleFormSubmit}
          className="bg-white text-black p-4 rounded"
          uiSchema={generateUiSchemaWithNumberFormatting(schema)}
          widgets={customWidgets}
        />
      )}
      {schema && !isEditing && (
        <div className="mt-4 p-2 bg-gray-700 rounded">
          <h3 className="text-sm font-semibold text-gray-200">
            Submitted Data:
          </h3>
          <button
            onClick={() => setIsEditing(true)}
            className="inline-flex items-center px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded shadow transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-400"
          >
            Edit
          </button>
          <pre className="text-xs text-gray-300 whitespace-pre-wrap">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default LabelSelectorForm;
