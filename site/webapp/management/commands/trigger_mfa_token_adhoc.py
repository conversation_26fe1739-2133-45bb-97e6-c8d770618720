import json
from argparse import Argument<PERSON>arser
from pathlib import Path

import structlog
from django.conf import settings
from django.core.management.base import BaseCommand
from django_extensions.management.utils import signalcommand
from retrieval.core import registry
from retrieval.tasks import send_mfa_notification_email

from webapp.models.portal import MergedPortalCredential, Portal
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser
from webapp.services.emails import _make_microsoft_session, add_or_update_rule, delete_existing_rule, get_existing_rules

logger = structlog.get_logger(__name__)
SHOULD_EMAIL_USER = False


class Command(BaseCommand):
    help = "Recovers and manages forwarding rules"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("-u", "--user", type=str)
        parser.add_argument("-p", "--portal", type=str)

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002, C901, PLR0912, PLR0915
        user = kwargs.get("user")
        portal_name = kwargs.get("portal")
        if user is None or portal_name is None:
            logger.error("User and Portal name are required to proceed")
            return

        user = BridgeUser.objects.get(email=user)
        portal = Portal.objects.get(name=portal_name, organization=user.organization)
        msft_session = None
        message_rule = None
        rule_id = None
        merged_portal_credential = None
        try:
            merged_portal_credential_all = MergedPortalCredential.objects.for_user(user).filter(portal=portal).all()
            if len(merged_portal_credential_all) == 0:
                logger.error("No merged portal credential found", email=user.email, portal=portal_name)
                merged_portal_credential = None
            if merged_portal_credential_all is not None and len(merged_portal_credential_all) > 1:
                logger.error("Multiple merged portal credentials found", email=user.email, portal=portal_name)
                merged_portal_credential = None
            if merged_portal_credential_all is not None:
                merged_portal_credential = merged_portal_credential_all[0]
                retrieval = Retrieval.create(
                    user=user,
                    merged_portal_credential=merged_portal_credential,
                )
                msft_session = _make_microsoft_session(merged_portal_credential)
                manager_cls = registry.RetrievalRegistry.get_retrieval_manager_for_portal(
                    user, merged_portal_credential.portal
                )
                default_otp_rules = {
                    "senderContains": ["bridgeinvest.io"],
                }
                otp_rules = manager_cls.email_otp_rules() if manager_cls is not None else default_otp_rules
                if len(otp_rules) == 0:
                    otp_rules = default_otp_rules
                logger.info("OTP rules", otp_rules=otp_rules)
                display_name = f"Bridge - Capture MFA {merged_portal_credential.portal.name} - {retrieval.pk}"
                message_rule = {
                    "displayName": display_name,
                    "sequence": 1,
                    "conditions": otp_rules,
                    "actions": {
                        "forwardTo": [
                            {
                                "emailAddress": {
                                    "address": f"mfa+{retrieval.pk}@{settings.DOMAIN_NAME}",
                                },
                            },
                        ],
                        "stopProcessingRules": False,
                    },
                    "isEnabled": True,
                }
                existing_rules = get_existing_rules(msft_session)
                with Path(f"{user.username}_init_rules.json").open("w") as f:
                    json.dump(existing_rules, f, indent=4)
                logger.info(message_rule)
        except ValueError:
            logger.exception("Error creating retrieval")

        try:
            if msft_session is None or message_rule is None:
                logger.error("No session or message rule found")
                logger.info("Skipping adding rule")
            else:
                input("Press Enter to add the rule crtl+c to skip....")
                rule_id = add_or_update_rule(msft_session, message_rule)
                existing_rules = get_existing_rules(msft_session)
                with Path(f"{user.username}_post_add_rules.json").open("w") as f:
                    json.dump(existing_rules, f, indent=4)
                input("Press Enter to add move on...")
        except KeyboardInterrupt as e:
            logger.exception("Interrupted!", exc_info=e)
            logger.info("Skipped adding rule")

        to_email = user.email
        if not SHOULD_EMAIL_USER and merged_portal_credential is not None:
            to_email = merged_portal_credential.multi_factor_authentication.receiving_email.email
            logger.info("Using the receiving email", to_email=to_email)
        else:
            logger.info("Using the users email", to_email=to_email)
        try:
            input("Press Enter to send the notification email, crtl+c to skip...")
            send_mfa_notification_email.apply_async(
                kwargs={
                    "portal_id": portal.id,
                    "user_id": user.pk,
                    "to_email": to_email,
                }
            )
            input("Press Enter to add move on...")
        except KeyboardInterrupt as e:
            logger.exception("Interrupted!", exc_info=e)
            logger.info("Skipped sending notification email")

        try:
            if rule_id is None or msft_session is None:
                logger.error("No rule id found")
                logger.info("Skipping deleting rule")
                return
            input("Press Enter to delete the rule...")
            delete_existing_rule(msft_session, rule_id)
            existing_rules = get_existing_rules(msft_session)
            with Path(f"{user.username}_post_delete_rules.json").open("w") as f:
                json.dump(existing_rules, f, indent=4)
            input("Press Enter to add move on...")
        except KeyboardInterrupt as e:
            logger.exception("Interrupted!", exc_info=e)
            logger.info("Skipped deleteing rule")
        logger.info(rule_id)
