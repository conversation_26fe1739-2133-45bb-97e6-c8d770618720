# Generated by Django 5.1.8 on 2025-04-11 17:25

from django.db import migrations, models


class Migration(migrations.Migration):
    atomic = True

    dependencies = [
        ('webapp', '0029_remove_smsmessage_from_user_number_and_more'),
    ]

    def backfill_raw_documents(apps, schema_editor):
        RawDocument = apps.get_model('webapp', 'rawdocument')
        for r in RawDocument.objects.all():
            r.original_posted_date = r.posted_date
            r.original_document_type = r.original_document_type
            gt_pds = r.processed_documents.filter(is_visible=True, is_ground_truth=True).order_by("-updated_at").all()
            for pd in gt_pds:
                pd.is_correct = True
                pd.save()
            r.save()

    def reverse_func(apps, schema_editor):
        pass

    operations = [
        migrations.AddField(
            model_name='processeddocument',
            name='raw_prediction_values',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='rawdocument',
            name='original_document_type',
            field=models.CharField(choices=[('cc', 'Capital Call'), ('dn', 'Distribution Notice'), ('as', 'Account Statement'), ('iu', 'Investment Update'), ('fs', 'Financial Statements'), ('tx', 'Tax'), ('lg', 'Legal'), ('ot', 'Other'), ('un', 'Unknown')], default='un', max_length=2),
        ),
        migrations.AddField(
            model_name='processeddocument',
            name='is_correct',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='rawdocument',
            name='original_posted_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.RunPython(backfill_raw_documents, reverse_func),
    ]
