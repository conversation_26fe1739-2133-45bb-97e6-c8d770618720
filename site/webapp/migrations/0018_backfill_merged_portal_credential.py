from django.db import migrations
import structlog
from webapp.models.portal import MFAType
logger = structlog.get_logger(__name__)

def get_line_item_mfa(li):
    if hasattr(li, 'multi_factor_authentication'):
        return li.multi_factor_authentication
    return None

def get_line_item_pc(li):
    if hasattr(li, 'portal_credential'):
        return li.portal_credential
    return None

class Migration(migrations.Migration):
    atomic = True

    dependencies = [
        ('webapp', '0017_zipjob'),
    ]

    def backfill_line_items_with_mpc_and_pc_and_mfa(apps, schema_editor):
        LineItems = apps.get_model('webapp', 'lineitem')
        MergedPortalCredentials = apps.get_model('webapp', 'mergedportalcredential')
        MultiFactorAuthentication = apps.get_model('webapp', 'multifactorauthentication')
        PortalCredential = apps.get_model('webapp', 'portalcredential')

        # Get all line items without MPCs
        total_line_items = LineItems.objects.count()
        line_items_without_mpc = LineItems.objects.filter(merged_portal_credential__isnull=True)
        logger.info(f"There are {total_line_items} Line Items and {line_items_without_mpc.count()} Line Items without MPCs")

        for li in line_items_without_mpc.iterator():
            user = li.created_by
            portal = li.portal

            # Add MPC
            merged_portal_credential = MergedPortalCredentials.objects.create(
                created_by=user,
                updated_by=user,
                organization=user.organization,
                portal=portal,
            )

            # Add MFA to MPC if it exists
            multi_factor_authentication = get_line_item_mfa(li)
            if multi_factor_authentication is None:
                multi_factor_authentication = MultiFactorAuthentication.objects.create(
                    created_by=user,
                    updated_by=user,
                    organization=user.organization,
                    multi_factor_authentication_type=MFAType.EMAIL,
                    receiving_email=li.receiving_email,
                    line_item=li,
                )
            merged_portal_credential.multi_factor_authentications.add(multi_factor_authentication)
            merged_portal_credential.save()

            # Add PC to MPC if it exists
            portal_credential = get_line_item_pc(li)
            if portal_credential is None:
                portal_credential = PortalCredential.objects.create(
                    created_by=user,
                    updated_by=user,
                    organization=user.organization,
                    portal=portal,
                    username="",
                    line_item=li,
                )
            merged_portal_credential.portal_credentials.add(portal_credential)
            merged_portal_credential.save()

            li.merged_portal_credential = merged_portal_credential
            li.save()

        logger.info(f"Successfully created {line_items_without_mpc.count()} MPCs")

    def reverse_func(apps, schema_editor):
        pass

    operations = [
        migrations.RunPython(backfill_line_items_with_mpc_and_pc_and_mfa, reverse_func),
    ]