import structlog
from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse

from webapp.forms import CredentialsFlowForm
from webapp.models.portal import MergedPortalCredential, MFAType, PortalCredential
from webapp.views.utils import add_hx_trigger_header, get_merged_portal_credential_from_url_params

logger = structlog.get_logger(__name__)


@login_required
def render_end_screen(
    request: HttpRequest, merged_portal_credential: MergedPortalCredential, message: str
) -> HttpResponse:
    template = "link/credentials/end_screen.html"
    context = {
        "merged_portal_credential": merged_portal_credential,
        "message": message,
    }
    return TemplateResponse(
        request,
        template,
        context,
        headers=add_hx_trigger_header(
            {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}}
        ),
    )


@login_required
def modal(request: HttpRequest) -> HttpResponse:
    template = "modal.html"
    merged_portal_credential = get_merged_portal_credential_from_url_params(request)
    logger.info("mpc", merged_portal_credential_pk=merged_portal_credential.portal_credential.is_complete)
    default_initial_template = "link/credentials/welcome_screen.html"
    modal_include_template = request.GET.get("modal_include_template", default_initial_template)

    if request.method == "GET":
        return TemplateResponse(
            request,
            template,
            {
                "merged_portal_credential": merged_portal_credential,
                "modal_include_template": modal_include_template,
            },
        )
    return HttpResponse("Invalid request method", status=405)


@login_required
def manage(request: HttpRequest) -> HttpResponse:
    template = "link/credentials/enter_credentials.html"
    merged_portal_credential = get_merged_portal_credential_from_url_params(request)
    context = {
        "merged_portal_credential": merged_portal_credential,
        "MFAType": MFAType,
    }

    if request.method == "GET":
        context["form"] = CredentialsFlowForm()
        return TemplateResponse(request, template, context)  # type: ignore[arg-type]

    if request.method == "POST":
        form = CredentialsFlowForm(request.POST)
        if not form.is_valid():
            return HttpResponse("Invalid form data", status=400)

        portal_credential = PortalCredential.create(
            user=request.user,
            portal=merged_portal_credential.portal,
            username=form.data["user_name"],
            password=form.data["portal_password"],
        )
        merged_portal_credential.update_merged_credential_portal_credential(portal_credential)

        return render_end_screen(
            request,
            merged_portal_credential,
            (
                f"You have successfully stored your encrypted credentials "
                f"for the {merged_portal_credential.portal.name} portal."
            ),
        )

    if request.method == "DELETE":
        with transaction.atomic():
            portal_credential = PortalCredential.create(
                user=request.user,
                portal=merged_portal_credential.portal,
                username="",
            )
            merged_portal_credential.update_merged_credential_portal_credential(portal_credential)

        return render_end_screen(
            request,
            merged_portal_credential,
            (
                f"You have successfully deleted your encrypted credentials "
                f"for the {merged_portal_credential.portal.name} portal."
            ),
        )

    return HttpResponse("Invalid request method", status=405)
