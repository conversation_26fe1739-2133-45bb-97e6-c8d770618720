from typing import Annotated, Literal

import structlog
from pydantic import BaseModel, Field
from pydantic.config import ConfigDict

logger = structlog.get_logger(__name__)


LoginError = Annotated[Literal["login_error"], <PERSON>(description="scrape failed while trying to login, or otp blocked")]

PopUpError = Annotated[
    Literal["pop_up_error"], <PERSON>(description="encountered an unexpected pop up resulting in a locator timeout")
]

PortalDownError = Annotated[Literal["portal_down_error"], <PERSON>(description="navigation to portal website failed")]

CeleryTimeoutError = Annotated[
    Literal["celery_timeout_error"],
    <PERSON>(
        description="scrapes are running on celery which has a 3600s timeout. this is the likely error if a retrieval was last updated around 3600s after it was created"  # noqa: E501
    ),
]

UnknownError = Annotated[
    Literal["unknown_error"], Field(description="use only when no other specific error types can be justified")
]

ErrorType = LoginError | PopUpError | PortalDownError | CeleryTimeoutError | UnknownError


class ErrorSummary(BaseModel):
    """
    Based on the context given, analyze what caused this webscrape error.
    Provide a one-sentence summary of the error and classify the error according to its type.
    """

    # setting the class title to be used as the tool name
    model_config = ConfigDict(title="get_error_summary")

    summary: str = Field(description="A one-sentence summary of the error trace")
    error_type: ErrorType = Field(description="The categorized type of the error from the ErrorType")

    @property
    def readable_error_type(self) -> str:
        return " ".join(self.error_type.split("_")).title()
