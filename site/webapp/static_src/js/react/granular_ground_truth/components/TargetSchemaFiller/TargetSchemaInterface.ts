import { JSONSchema } from "../../types/jsonSchema";
import { BoxData } from "../../types/pdfLabeler";

export interface TargetSchemaProps {
  schema: JSONSchema;
  is_required: boolean;
  path: string;
  labeledBoxes: BoxData[];
  targetSchema: JSONSchema;
  hideLabels: boolean;
  overlays: Map<number, HTMLElement | null>;
  scale: number;
  currentPageNumber: number;
  onSaveTargetSchema: (schema: BoxData | null) => void;
  onDeleteBox: (boxes: BoxData[]) => void;
  onLabelBox: (boxes: BoxData[]) => void;
  extractTextFromBoundingBox: (
    box: {
      x: number;
      y: number;
      width: number;
      height: number;
    },
    pageNumber?: number,
  ) => Promise<{ content: string }>;
}
