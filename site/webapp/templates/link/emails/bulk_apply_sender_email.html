{% load customtags static %}
<form hx-post="{% url 'confirm_email_modal' %}{% urlparams merged_portal_credential_pk=mpc.pk %}{% if line_item %}&line_item_pk={{ line_item.pk }}{% endif %}"
      hx-vals='{"sender_emails": [{% for sender_email in sender_emails %} "{{ sender_email }}" {% if not forloop.last %},{% endif %}{% endfor %}]}'
      hx-swap="outerHTML"
      hx-target="#modal-container">
    <div class="mb-4">
        <input type="checkbox" name="select-all" id="select-all-checkbox">
        <label class="text-xs ml-2" for="select-all-checkbox">Select all</label>
    </div>
    <div class="flex flex-col gap-4 overflow-auto max-h-60 pl-4">
        <div class="cursor-not-allowed opacity-50">
            <input type="checkbox"
                   name="{{ line_item.pk }}"
                   id="{{ line_item.pk }}"
                   value="{{ line_item.pk }}"
                   class="disabled:cursor-not-allowed"
                   checked
                   disabled>
            <label class="text-xs ml-2 disabled:cursor-not-allowed"
                   for="{{ line_item.pk }}">{{ line_item }}</label>
        </div>
        {% for investment_line_item in investment_line_items %}
            <div>
                <input type="checkbox"
                       name="investment_line_item|{{ investment_line_item.pk }}"
                       id="investment_line_item|{{ investment_line_item.pk }}"
                       value="{{ investment_line_item.pk }}">
                <label class="text-xs ml-2"
                       for="investment_line_item|{{ investment_line_item.pk }}">{{ investment_line_item }}</label>
            </div>
        {% endfor %}
    </div>
    <div id="button-containers" class="flex flex-col gap-4 mt-6">
        <button type="submit"
                name="submit"
                value="bulk_apply_sender_email"
                class="w-full px-4 py-2.5 bg-[#924f34] text-md font-semibold text-white rounded-lg hover:bg-[#8a5f47] transition-colors">
            Continue
        </button>
    </div>
</form>
<script>
    (function() {
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const itemCheckboxes = document.querySelectorAll('input[name^="investment_line_item|"]');

        // Skip if elements don't exist (prevents errors on re-runs)
        if (!selectAllCheckbox || itemCheckboxes.length === 0) {
            return;
        }

        // Select All functionality
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });

        // Individual checkbox change handler
        itemCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateSelectAllState();
            });
        });

        // Function to update Select All checkbox state
        function updateSelectAllState() {
            const checkedBoxes = document.querySelectorAll('input[name^="investment_line_item|"]:checked');
            const totalBoxes = itemCheckboxes.length;

            if (checkedBoxes.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedBoxes.length === totalBoxes) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        // Initialize the select all state on page load
        updateSelectAllState();
    })();
</script>
