import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON><PERSON>ator
from pathlib import Path

import structlog
from dateutil import parser
from playwright.async_api import Locator
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
    retry_until_success,
)

logger = structlog.get_logger(__name__)


@register_strategy
class EgnytePortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "egnyte.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return []

    async def _enter_credentials(self, username: str, password: str) -> None:
        username_locator = self.page.get_by_placeholder("Enter your e-mail or username")
        # When selecting remember me, just the username is remembered.
        if await username_locator.count() and await username_locator.is_visible():
            logger.info("Username field found", username_locator=await username_locator.count())
            await username_locator.click()
            await asyncio.sleep(1)
            await username_locator.fill(username)
            await asyncio.sleep(1)
            await self.page.locator("button.set-username-btn").click()
            await asyncio.sleep(2)
        else:
            logger.info("Username field not found", username_locator=await username_locator.count())
        await self.page.get_by_placeholder("Enter password").click()
        await asyncio.sleep(1)
        await self.page.get_by_placeholder("Enter password").fill(password)
        await asyncio.sleep(1)
        await self.page.get_by_text("Keep me logged in").set_checked(checked=True)
        await asyncio.sleep(1)
        await self.page.locator("button#loginBtn").click()

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        locators = [
            self.page.get_by_placeholder("Enter your e-mail or username"),
            self.page.get_by_placeholder("Enter password"),
            self.page.get_by_text("Keep me logged in"),
        ]
        return all([await locator.count() == 0 for locator in locators])

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        async def can_retrieve() -> bool:
            return await self.page.locator("div.folder-tree").count() > 0

        await retry_until_success(can_retrieve, num_retries=5)
        await self.__expand_folders()
        folder_tree = self.page.locator("div.folder-tree")
        folders_with_docs = folder_tree.locator("li:not(:has(button))")
        seen = set()
        for folder in await folders_with_docs.all():
            await folder.click()
            await asyncio.sleep(5)
            rows = await self._create_rows_enumerator(seen)
            while rows:
                row_locator, metadata, doc_name, doc_size, ref_last_modified_date, updated_by, folder_path = rows.pop(0)
                if await self.should_download_document(metadata):
                    doc = await self.__download_doc(
                        row_locator, metadata, ref_last_modified_date, self.__document_mapper(doc_name, folder_path)
                    )
                    if doc is not None:
                        yield doc

                else:
                    logger.info("Skipping document", doc_name=doc_name)

    async def __expand_folders(self) -> None:
        more_buttons_exist = True
        folder_tree = self.page.locator("div.folder-tree")
        if await folder_tree.count() > 0:
            while more_buttons_exist:
                buttons = folder_tree.locator('button[aria-expanded="false"]')
                buttons_count = await buttons.count()
                if buttons_count == 0:
                    more_buttons_exist = False
                    break
                await buttons.first.click()
                await asyncio.sleep(1)
        else:
            logger.exception("Cannot find folder tree")
            raise ValueError

    async def _create_rows_enumerator(self, seen: set[str]) -> list[tuple[str, str, datetime.datetime, str, str]]:
        rows = await self.page.locator("div[role='rowgroup'] div[role='row']").all()
        res = []
        for row_locator in rows:
            cells = row_locator.locator("div[role='cell']")
            raw_doc_locator = cells.nth(1)
            raw_doc_inner_divs_name = raw_doc_locator.locator("div.name")
            doc_name = (await raw_doc_inner_divs_name.inner_text()).strip()
            doc_size = (await cells.nth(3).inner_text()).strip()
            raw_last_modified_date = (await cells.nth(4).inner_text()).strip()
            ref_last_modified_date = parser.parse(raw_last_modified_date)
            updated_by = (await cells.nth(5).inner_text()).strip()
            folder_path = await self.__get_folder_path()
            metadata = json.dumps(
                {
                    "Item name": doc_name,
                    "Size": doc_size,
                    "Date modified": raw_last_modified_date,
                    "Updated by": updated_by,
                    "Folder path": folder_path,
                }
            )
            if metadata in seen:
                continue
            seen.add(metadata)
            res.append((row_locator, metadata, doc_name, doc_size, ref_last_modified_date, updated_by, folder_path))
        logger.info(
            "Recreating rows enumerator",
            length_new_rows=len(res),
            length_seen_rows=len(seen),
            length_available_rows=len(rows),
        )
        return res

    async def __get_folder_path(self) -> str:
        tooltip = self.page.locator("nav.breadcrumbs")
        await tooltip.hover()
        await asyncio.sleep(1)
        tippy_content = self.page.locator("div.tippy-content")
        deepst_div = tippy_content.locator("div").last
        children_spans = deepst_div.locator(":scope > *")
        spans_count = await children_spans.count()
        folder_path = ""
        for i in range(spans_count):
            span_text = (await children_spans.nth(i).inner_text()).strip()
            folder_path += span_text
        folder_path = folder_path.replace(" /", "/")
        return str(folder_path)

    async def __download_doc(
        self, row_locator: Locator, metadata: str, ref_last_modified_date: datetime.datetime, doc_type: str
    ) -> RawDocumentTuple:
        await row_locator.hover()
        async with self.page.expect_download() as download_info:
            await self.page.locator("div[data-tid='inline-download']").click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.warning("Unsupported file format", file_name_suffix=file_name.suffix)
            raise ValueError
        file_name.unlink()
        return RawDocumentTuple(
            name=download.suggested_filename,
            date=ref_last_modified_date,
            doc_type=doc_type,
            content=content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    def __document_mapper(self, doc_name: str, folder_path: str) -> str:
        mapping = {
            "Annual Meeting Materials": DocumentType.INVESTMENT_UPDATE,
            "Capital Account Statements and Distribution Notices": DocumentType.ACCOUNT_STATEMENT,
            "K-1s": DocumentType.TAX,
            "Quarterly Financials": DocumentType.INVESTMENT_UPDATE,
        }
        folder_path_strings = folder_path.split("/")
        for folder_str in folder_path_strings[::-1]:
            if (
                folder_str == "Capital Account Statements and Distribution Notices"
                and "Distribution Notice" in doc_name
            ):
                return DocumentType.DISTRIBUTION_NOTICE

            if folder_str in mapping:
                return mapping[folder_str]

        return DocumentType.UNKNOWN
