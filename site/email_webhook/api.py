import base64
import json
import time

import boto3
import botocore.client
import botocore.exceptions
import requests
import structlog
from cryptography import x509
from cryptography.exceptions import InvalidSignature
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import padding
from django.conf import settings
from django.http import HttpRequest, HttpResponse
from ninja.security import HttpBasic<PERSON>uth

from .tasks import route_email_async


class SubscriptionConfirmationError(Exception):
    pass


class EmailFormattingError(Exception):
    pass


class S3BucketNotFoundError(Exception):
    pass


# TODO: make this more secure in production, security thru obscurity should be OK for now...
class BasicAuth(HttpBasicAuth):
    def authenticate(self, request: HttpRequest, username: str, password: str) -> str | None:  # noqa: ARG002
        if username == "bridge" and password == "snswebhook":  # noqa: S105 # nosec
            return username
        return None


logger = structlog.get_logger(__name__)

from ninja import Field, Router, Schema

router = Router(auth=BasicAuth())


class SnsMessageSchema(Schema):
    message_type: str = Field(..., alias="Type")
    message_id: str = Field(..., alias="MessageId")
    topic_arn: str = Field(..., alias="TopicArn")
    message: str = Field(..., alias="Message")
    timestamp: str = Field(..., alias="Timestamp")
    signiture_version: str = Field(..., alias="SignatureVersion")
    signature: str = Field(..., alias="Signature")
    signing_cert_url: str = Field(..., alias="SigningCertURL")

    # Schema on subscription for new subscriber on init
    token: str | None = Field(None, alias="Token")
    subscribe_url: str | None = Field(None, alias="SubscribeURL")

    # # Schema on email
    unsubscribe_url: str | None = Field(None, alias="UnsubscribeURL")
    subject: str | None = Field(None, alias="Subject")


@router.post("/sns-webhook")
def sns_webhook(request: HttpRequest, sns_message: SnsMessageSchema) -> HttpResponse:
    logger.info("Processing message", sns_message=sns_message)
    try:
        sign_message(json.loads(request.body.decode("utf-8")))
    except InvalidSignature:
        return HttpResponse("Invalid signature", status=403)

    if sns_message.message_type == "SubscriptionConfirmation":
        # Confirm subscription
        confirm_subscription(sns_message)
        return HttpResponse("Subscription confirmed", status=200)

    if sns_message.message_type == "Notification":
        # Process the notification
        process_email(sns_message)
        return HttpResponse("Notification received", status=200)

    return HttpResponse(status=400)


def does_s3_exist(s3: botocore.client.BaseClient, bucket: str, key: str) -> bool:
    try:
        s3.head_object(Bucket=bucket, Key=key)
    except botocore.exceptions.ClientError as e:
        if e.response["Error"]["Code"] == "404":
            return False
        raise
    else:
        return True


bucket = settings.AWS_STORAGE_BUCKET_NAME


def process_email(sns_message: SnsMessageSchema) -> None:
    """
    Process the SNS notification. This is where you handle the business logic.
    """
    message = json.loads(sns_message.message)
    mail = message.get("mail", None)
    if mail is None:
        raise EmailFormattingError
    message_id = mail["messageId"]
    if message_id is None:
        raise EmailFormattingError
    s3 = boto3.client("s3")
    # TODO: export prefix as variable in CDK
    email_path = f"inbound_emails/{message_id}"
    does_exists = does_s3_exist(s3, bucket, email_path)
    start = time.time()
    logger.info("Checking for s3", email_path=email_path)
    if not does_exists and time.time() - start < 60:  # noqa: PLR2004
        time.sleep(1)
        does_exists = does_s3_exist(s3, bucket, email_path)
    if not does_exists:
        raise S3BucketNotFoundError
    logger.info("Processing message id", message_id=message_id)
    route_email_async.apply_async(task_id=email_path, kwargs={"email_s3_bucket": bucket, "email_s3_key": email_path})


def confirm_subscription(sns_message: SnsMessageSchema) -> None:
    """
    Confirm SNS subscription by accessing the Token provided by SNS.
    """
    if sns_message.subscribe_url is None:
        raise SubscriptionConfirmationError
    response = requests.get(sns_message.subscribe_url, timeout=10)
    if response.status_code == 200:  # noqa: PLR2004
        logger.info("Subscription confirmed", response=response)
    else:
        raise SubscriptionConfirmationError


def raw_message_to_string(sns_raw_payload: dict) -> str | None:
    payload_type = sns_raw_payload.get("Type")
    if payload_type in ["SubscriptionConfirmation", "UnsubscribeConfirmation"]:
        fields = ["Message", "MessageId", "SubscribeURL", "Timestamp", "Token", "TopicArn", "Type"]
    elif payload_type == "Notification":
        fields = ["Message", "MessageId", "Subject", "Timestamp", "TopicArn", "Type"]
    else:
        return None

    # Build the string to be signed.
    string_to_sign = ""
    for field in fields:
        field_value = sns_raw_payload.get(field)
        if not isinstance(field_value, str):
            return None
        string_to_sign += field + "\n" + field_value + "\n"
    return string_to_sign


# Pieced together from: https://github.com/boto/boto3/issues/2508
def sign_message(sns_raw_payload: dict) -> bool:
    cert_url = sns_raw_payload["SigningCertURL"]
    get_cert_req = requests.get(cert_url, timeout=10)
    get_cert_req.raise_for_status()
    cert = x509.load_pem_x509_certificate(get_cert_req.content)

    message_signature = sns_raw_payload["Signature"]
    # decode the signature from base64.
    decoded_signature = base64.b64decode(message_signature)

    message_sig_version = sns_raw_payload["SignatureVersion"]
    signature_hash = hashes.SHA1() if message_sig_version == "1" else hashes.SHA256()  # noqa: S303 # nosec
    string_to_sign = raw_message_to_string(sns_raw_payload)
    if string_to_sign is None:
        raise InvalidSignature

    # verify the signature value with cert, if the signature is not valid, it will raise `InvalidSignature`
    cert.public_key().verify(  # type: ignore[attr-defined]
        decoded_signature,
        string_to_sign.encode(),
        padding=padding.PKCS1v15(),  # type: ignore[attr-defined]
        algorithm=signature_hash,  # type: ignore[attr-defined]
    )

    return True
