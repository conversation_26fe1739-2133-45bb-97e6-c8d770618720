import json
import uuid
from pathlib import Path
from typing import Any

import structlog
from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin.filters import AllValuesFieldListFilter
from django.core.cache import cache
from django.db import models
from django.db.models import OuterRef, Subquery
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.template.response import TemplateResponse
from django.urls import URLPattern, path, reverse
from django.utils import timezone
from django.utils.html import format_html
from django_ses.views import csrf_exempt
from ml_app.tasks.doc_vault_tasks import multi_process_doc_vault
from ml_app.tasks.ingestion import ingest_and_backfill_processed_doc, ingest_processed_doc
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, save_document

from webapp.admin.granular_ground_truth import (
    extracted_data_view,
    rawdocument_get_doc_chunks_view,
    rawdocument_granular_ground_truth_view,
    schemas_view,
    submit_target_document_view,
)
from webapp.admin.utils import AllValuesAndNoneFieldListFilter, dropdown_filter
from webapp.models import (
    BridgeUser,
    CapitalCallDocumentFact,
    DistributionNoticeDocumentFact,
    DocumentType,
    InvestmentUpdateDocumentFact,
    LineItem,
    MergedPortalCredential,
    Portal,
    ProcessedDocument,
    RawDocument,
    Retrieval,
)
from webapp.models.documents import REQUIRED_SUB_TYPE_DOCUMENTS, CurrencyType, SubDocumentType
from webapp.views.utils import redirect_with_url_params

logger = structlog.get_logger(__name__)

BUCKET = settings.AWS_STORAGE_BUCKET_NAME


class MultipleFileInput(forms.ClearableFileInput):
    allow_multiple_selected = True


class MultipleFileField(forms.FileField):
    def __init__(self, *args, **kwargs) -> None:  # noqa: ANN002, ANN003
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data: Any, initial: Any = None) -> list[Any]:  # noqa: ANN401
        single_file_clean = super().clean
        if isinstance(data, list | tuple):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = [single_file_clean(data, initial)]
        return result


class FileFieldForm(forms.Form):
    file_field = MultipleFileField()


class GroundTruth(forms.Form):
    def __init__(self, *args, raw_doc: RawDocument | None = None, **kwargs) -> None:  # noqa: ANN002, ANN003
        super().__init__(*args, **kwargs)
        line_items_field = self.fields["line_items"]
        if type(line_items_field) is not forms.ModelMultipleChoiceField:
            raise ValueError
        if raw_doc is None:
            line_items_field.queryset = LineItem.objects.none()
        elif raw_doc.retrieval is None:
            line_items_field.queryset = (
                LineItem.objects.filter(
                    organization=raw_doc.organization,
                    merged_portal_credential__portal__portal_type=Portal.PortalType.EMAIL_BASED,
                    deleted_at__isnull=True,
                )
                .order_by(
                    "investing_entity__client__legal_name", "investing_entity__legal_name", "investment__legal_name"
                )
                .all()
            )
        else:
            line_items_field.queryset = (
                raw_doc.retrieval.line_items.filter(deleted_at__isnull=True)
                .order_by(
                    "investing_entity__client__legal_name", "investing_entity__legal_name", "investment__legal_name"
                )
                .all()
            )

    document_type = forms.ChoiceField(choices=DocumentType.choices, label="Ground Truth Document Type", required=True)
    sub_document_type = forms.ChoiceField(
        choices=[*SubDocumentType.choices, ("", "---------")], label="Sub Document Type", required=False
    )
    line_items: "forms.ModelChoiceField[LineItem]" = forms.ModelMultipleChoiceField(
        queryset=None, widget=forms.CheckboxSelectMultiple
    )
    has_been_viewed = forms.BooleanField(required=False, label="Mark document as read")
    posted_date = forms.DateField()
    effective_date = forms.DateField()
    capital_call_due_date = forms.DateField(required=False)
    capital_call_amount = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={"class": "currency_input"}),
    )
    currency = forms.ChoiceField(choices=CurrencyType.choices, label="Currency", required=False)
    distribution_amount = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={"class": "currency_input"}),
    )
    investment_update_invested = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={"class": "currency_input"}),
    )
    investment_update_total_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={"class": "currency_input"}),
    )
    investment_update_unfunded = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={"class": "currency_input"}),
    )
    investment_update_committed_capital = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={"class": "currency_input"}),
    )
    investment_update_realized_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={"class": "currency_input"}),
    )
    investment_update_unrealized_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={"class": "currency_input"}),
    )
    document_summary = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={"rows": 10}),
    )
    is_document_summary_approved = forms.BooleanField(required=False, label="Approve summary for display")

    def is_valid(self) -> bool:  # noqa: C901 PLR0912
        # Perform your custom validation logic here
        valid = super().is_valid()
        dollar_fields = [
            "capital_call_amount",
            "distribution_amount",
            "investment_update_invested",
            "investment_update_total_value",
            "investment_update_unfunded",
            "investment_update_committed_capital",
            "investment_update_realized_value",
            "investment_update_unrealized_value",
        ]
        fields_needed = {
            DocumentType.CAPITAL_CALL: [
                ("capital_call_due_date", True),
                ("capital_call_amount", True),
            ],
            DocumentType.DISTRIBUTION_NOTICE: [
                ("distribution_amount", True),
            ],
            DocumentType.INVESTMENT_UPDATE: [],
            DocumentType.FINANCIAL_STATEMENTS: [],
            DocumentType.ACCOUNT_STATEMENT: [
                ("investment_update_invested", True),
                ("investment_update_total_value", True),
                ("investment_update_unfunded", False),
                ("investment_update_committed_capital", True),
                ("investment_update_realized_value", True),
                ("currency", True),
                ("investment_update_unrealized_value", False),
            ],
            DocumentType.TAX: [],
            DocumentType.LEGAL: [],
            DocumentType.OTHER: [],
        }
        if valid:
            doc_type = self.cleaned_data["document_type"]
            if doc_type == DocumentType.UNKNOWN:
                self.add_error("document_type", "This field is required cannot be unknown")
                valid = False
            if doc_type in fields_needed:
                other_fields = [field for d, fields in fields_needed.items() if d != doc_type for field in fields]
                for field, required in fields_needed[doc_type]:
                    data = self.cleaned_data.get(field, None)
                    if not data and required:
                        self.add_error(field, "This field is required")
                        valid = False
                for field, _ in other_fields:
                    cleaned_field = self.cleaned_data.get(field, None)
                    if cleaned_field:
                        # Ignore the default value for currency if not needed.
                        if field == "currency" and cleaned_field == "USD":
                            continue
                        self.add_error(field, "This field is not required")
                        valid = False
            for field in dollar_fields:
                if self.cleaned_data.get(field, None):
                    try:
                        value = float(self.cleaned_data[field].replace(",", "").replace("$", ""))
                        self.cleaned_data[field] = value
                    except ValueError:
                        self.add_error(field, "This field must be a number")
                        valid = False
                else:
                    self.cleaned_data[field] = None

            if doc_type in REQUIRED_SUB_TYPE_DOCUMENTS:
                sub_document_type = self.cleaned_data.get("sub_document_type", None)
                if (
                    not sub_document_type
                    or sub_document_type not in ProcessedDocument.get_sub_document_types_for_document_type(doc_type)
                ):
                    valid_sub_types = ProcessedDocument.get_sub_document_types_for_document_type(doc_type)
                    self.add_error(
                        "sub_document_type",
                        f"Choose a valid sub document type from this list"
                        f": {', '.join(str(SubDocumentType(sub_type).label) for sub_type in valid_sub_types)}",
                    )
                    valid = False
            else:
                self.cleaned_data["sub_document_type"] = ""

        return valid


def rawdocument_label_view(request: HttpRequest, pk: str) -> HttpResponse:  # noqa: C901
    template = "admin/webapp/rawdocument/label.html"
    raw_doc = get_object_or_404(RawDocument, pk=pk)
    previous_ground_truth = raw_doc.processed_documents.order_by("-created_at").all()
    if raw_doc.metadata is None:
        metadata = {}
    elif isinstance(raw_doc.metadata, str):
        try:
            metadata = json.loads(raw_doc.metadata)
        except json.JSONDecodeError:
            metadata = {}
    else:
        metadata = raw_doc.metadata
    logger.info(request.user)
    ignore = ["checkbox_id", "is_unread", "raw_document_date", "raw_published_date", "raw_due_date"]
    for k in ignore:
        metadata.pop(k, None)
    if "document_type" in metadata:
        try:
            metadata["document_type"] = DocumentType(metadata["document_type"]).label
        except ValueError:
            metadata["document_type"] = f"Unknown bad value ({metadata['document_type']})"

    next_raw_doc = (
        RawDocument.objects.exclude(pk__in=[raw_doc.pk])
        .exclude(has_ground_truth=True)
        .filter(document_type=raw_doc.document_type, organization=raw_doc.organization)
        .order_by("?")
        .first()
    )
    context = {
        "DocumentType": DocumentType,
        "SubDocumentType": SubDocumentType,
        "raw_doc": raw_doc,
        "raw_doc_file_type": Path(raw_doc.name).suffix.lower(),
        "raw_doc_metadata": metadata,
        "next_raw_doc": next_raw_doc,
        "previous_ground_truth": previous_ground_truth,
        "video_link": None if raw_doc.retrieval is None else raw_doc.retrieval.get_presigned_video_url(),
    }
    if request.method == "POST":
        gt_form = GroundTruth(request.POST, raw_doc=raw_doc)
        context["gt_form"] = gt_form
        logger.info("Unclean form", gt_form_data=gt_form.data, errors=gt_form.errors)
        if gt_form.is_valid():
            logger.info("Cleaned form", gt_form_cleaned_data=gt_form.cleaned_data)
            pds = ground_truth_process_document(
                raw_document=raw_doc, cleaned_data=gt_form.cleaned_data, labeled_by=request.user
            )
            if len(pds) == 0:
                messages.error(request, "Document not labeled")
                return TemplateResponse(request, template, context)
            logger.info("Example Processed Document", example_processed_doc=pds[0])
            messages.success(request, "Document labeled")
            return HttpResponseRedirect(reverse("admin:webapp_rawdocument_label", args=[raw_doc.pk]))
        messages.error(request, "Document not labeled")
        return TemplateResponse(request, template, context)

    if request.method == "GET":
        has_gt = raw_doc.processed_documents.exists()
        all_gt = raw_doc.processed_documents.order_by("-created_at").all()
        gt = all_gt.first()
        line_items = [pd.line_item for pd in all_gt]
        fill_posted_date = None
        fill_posted_date = gt.posted_date if has_gt and gt is not None else raw_doc.posted_date
        initial = {
            "line_items": line_items,
            "posted_date": fill_posted_date,
        }
        if gt is not None:
            initial = {
                "line_items": line_items,
                "has_been_viewed": raw_doc.retrieval.is_backfill if raw_doc.retrieval is not None else False,
                "posted_date": fill_posted_date,
                "document_type": gt.document_type if has_gt else DocumentType.UNKNOWN,
                "sub_document_type": gt.sub_document_type if has_gt else "",
                "effective_date": gt.effective_date if has_gt else None,
                "capital_call_due_date": gt.capital_call_document.capital_call_due_date
                if has_gt and gt.capital_call_document
                else None,
                "currency": gt.investment_update_document.currency
                if has_gt and gt.investment_update_document
                else CurrencyType.USD,
                "capital_call_amount": gt.capital_call_document.amount if has_gt and gt.capital_call_document else None,
                "distribution_amount": gt.distribution_notice_document.amount
                if has_gt and gt.distribution_notice_document
                else "",
                "investment_update_invested": gt.investment_update_document.invested
                if has_gt and gt.investment_update_document
                else "",
                "investment_update_total_value": gt.investment_update_document.total_value
                if has_gt and gt.investment_update_document
                else "",
                "investment_update_unfunded": gt.investment_update_document.unfunded
                if has_gt and gt.investment_update_document
                else "",
                "investment_update_committed_capital": gt.investment_update_document.committed_capital
                if has_gt and gt.investment_update_document
                else "",
                "investment_update_realized_value": gt.investment_update_document.realized_value
                if has_gt and gt.investment_update_document
                else "",
                "investment_update_unrealized_value": gt.investment_update_document.unrealized_value
                if has_gt and gt.investment_update_document
                else "",
                "document_summary": gt.document_summary if has_gt and gt.document_summary else "",
                "is_document_summary_approved": gt.is_document_summary_approved if has_gt else False,
            }

        context["gt_form"] = GroundTruth(raw_doc=raw_doc, initial=initial)

        return TemplateResponse(request, template, context)
    return HttpResponse("Invalid request method", status=405)


def ground_truth_mark_visible(raw_document: RawDocument) -> None:  # noqa: C901
    processed_documents = raw_document.processed_documents.order_by("-created_at").all()
    li_grouped: dict[str, list[ProcessedDocument]] = {}
    for pd in processed_documents:
        li_pk = str(pd.line_item.pk)
        if li_pk not in li_grouped:
            li_grouped[li_pk] = []
        li_grouped[li_pk].append(pd)
        pd.is_visible = False
        pd.is_wrong = True
        pd.is_correct = False
        pd.save()

    visible_docs: dict[str, ProcessedDocument] = {}
    for li_pk, pds in li_grouped.items():
        visible = None
        has_been_viewed = False
        first_viewed_date = None
        for pd in pds:
            # Mark the first ground truth as visible.
            if pd.is_ground_truth and visible is None:
                visible = pd
            if has_been_viewed:
                has_been_viewed = pd.has_been_viewed
                first_viewed_date = pd.first_viewed_date
        # otherwised mark the first processed document as visible.
        if visible is None:
            visible = pds[0]
        visible.is_visible = True
        visible.is_wrong = False
        visible.is_correct = True
        if has_been_viewed:
            visible.has_been_viewed = has_been_viewed
            visible.first_viewed_date = first_viewed_date
        visible.save()
        visible_docs[li_pk] = visible
    non_visible = processed_documents.filter(is_visible=False)
    for other_pd in non_visible:
        if any(is_same_pd_values(pd, other_pd) for pd in visible_docs.values()):
            other_pd.is_wrong = False
            other_pd.is_correct = True
            other_pd.save()


def is_same_pd_values(pd1: ProcessedDocument, pd2: ProcessedDocument) -> bool:
    # TODO: need to add more checks for validating correctness
    return (
        pd1.effective_date == pd2.effective_date
        and pd1.posted_date == pd2.posted_date
        and pd1.line_item == pd2.line_item
        and pd1.document_type == pd2.document_type
        and is_same_investment_document(pd1.investment_update_document, pd2.investment_update_document)
        and is_same_capital_call_document(pd1.capital_call_document, pd2.capital_call_document)
        and is_same_disibution_notice_document(pd1.distribution_notice_document, pd2.distribution_notice_document)
    )


def is_same_investment_document(
    iudf1: InvestmentUpdateDocumentFact | None, iudf2: InvestmentUpdateDocumentFact | None
) -> bool:
    if (iudf1 is None) and (iudf2 is None):
        return True
    if (iudf1 is None) or (iudf2 is None):
        return False
    return (
        iudf1.invested == iudf2.invested
        and iudf1.total_value == iudf2.total_value
        and iudf1.unfunded == iudf2.unfunded
        and iudf1.committed_capital == iudf2.committed_capital
        and iudf1.realized_value == iudf2.realized_value
        and iudf1.realized_value == iudf2.realized_value
        and iudf1.currency == iudf2.currency
    )


def is_same_capital_call_document(ccdf1: CapitalCallDocumentFact | None, ccdf2: CapitalCallDocumentFact | None) -> bool:
    if (ccdf1 is None) and (ccdf2 is None):
        return True
    if (ccdf1 is None) or (ccdf2 is None):
        return False
    return ccdf1.capital_call_due_date == ccdf2.capital_call_due_date and ccdf1.amount == ccdf2.amount


def is_same_disibution_notice_document(
    dndf1: DistributionNoticeDocumentFact | None, dndf2: DistributionNoticeDocumentFact | None
) -> bool:
    if (dndf1 is None) and (dndf2 is None):
        return True
    if (dndf1 is None) or (dndf2 is None):
        return False
    return dndf1.amount == dndf2.amount


def ground_truth_process_document(
    raw_document: RawDocument, cleaned_data: dict[str, Any], labeled_by: BridgeUser
) -> list[ProcessedDocument]:
    process_document_version = -1
    required = ["line_items", "document_type", "posted_date", "effective_date"]
    for r in required:
        if r not in cleaned_data:
            logger.error("Missing required field", required_field=r)
            raise ValueError
    line_items: list[LineItem] = cleaned_data["line_items"]
    document_type = cleaned_data["document_type"]
    sub_document_type = cleaned_data["sub_document_type"]
    if document_type == DocumentType.UNKNOWN:
        logger.info("Document is unknown, skipping", raw_document_id=raw_document.pk)

    if (
        document_type == DocumentType.CAPITAL_CALL
        and "capital_call_due_date" in cleaned_data
        and "capital_call_amount" in cleaned_data
    ):
        capital_call_facts = {
            "capital_call_due_date": cleaned_data["capital_call_due_date"],
            "amount": cleaned_data["capital_call_amount"],
        }
    else:
        capital_call_facts = None

    if document_type == DocumentType.DISTRIBUTION_NOTICE and "distribution_amount" in cleaned_data:
        distribution_notice_facts = {
            "amount": cleaned_data["distribution_amount"],
        }
    else:
        distribution_notice_facts = None
    account_statement_required_fields = [
        "currency",
        "investment_update_invested",
        "investment_update_total_value",
        "investment_update_unfunded",
        "investment_update_committed_capital",
        "investment_update_realized_value",
        "investment_update_unrealized_value",
    ]
    account_statement_has_required_fields = all(field in cleaned_data for field in account_statement_required_fields)
    if document_type == DocumentType.ACCOUNT_STATEMENT and account_statement_has_required_fields:
        investment_update_facts = {
            "currency": cleaned_data["currency"],
            "invested": cleaned_data["investment_update_invested"],
            "total_value": cleaned_data["investment_update_total_value"],
            "unfunded": cleaned_data["investment_update_unfunded"],
            "committed_capital": cleaned_data["investment_update_committed_capital"],
            "realized_value": cleaned_data["investment_update_realized_value"],
            "unrealized_value": cleaned_data["investment_update_unrealized_value"],
        }
    else:
        investment_update_facts = None
    has_been_viewed = False
    if "has_been_viewed" in cleaned_data:
        logger.info("Has been viewed field found", has_been_viewed=cleaned_data["has_been_viewed"])
        has_been_viewed = cleaned_data["has_been_viewed"] == "on" or cleaned_data["has_been_viewed"] is True

    return ProcessedDocument.create(
        raw_document=raw_document,
        line_items=line_items,
        document_type=document_type,
        sub_document_type=sub_document_type,
        posted_date=cleaned_data["posted_date"],
        effective_date=cleaned_data["effective_date"],
        process_document_version=process_document_version,
        process_document_source="ground_truth",
        labeled_by=labeled_by,
        is_visible=True,
        is_ground_truth=True,
        has_been_viewed=has_been_viewed,
        distribution_notice=distribution_notice_facts,
        investment_update=investment_update_facts,
        capital_call=capital_call_facts,
        document_summary=cleaned_data.get("document_summary"),
        is_document_summary_approved=cleaned_data.get("is_document_summary_approved", False),
    )


def get_line_items(raw_doc: RawDocument) -> models.QuerySet[LineItem]:
    if raw_doc is None or raw_doc.retrieval is None or raw_doc.retrieval.merged_portal_credential is None:
        return LineItem.objects.none()
    return raw_doc.retrieval.line_items.order_by(
        "investing_entity__client__legal_name", "investing_entity__legal_name", "investment__legal_name"
    ).all()


def bulk_label_view(request: HttpRequest) -> HttpResponse:  # noqa: C901, PLR0912, PLR0915
    if request.method == "GET":
        template = "admin/webapp/rawdocument/bulk_label.html"
        s_id = request.GET.get("session_post_data", None)
        pks = request.session[s_id].split(",") if s_id is not None else request.GET.get("pks", "").split(",")
        task_id = request.GET.get("task_id", "")
        counter = {}
        if task_id:
            for pk in pks:
                res = cache.get(f"prediction_task_{task_id}_{pk}")
                if res is None:
                    res = "started"
                if res not in counter:
                    counter[res] = 0
                counter[res] += 1
        logger.info("Bulk label", pks=pks, task_id=task_id)
        previous_effective_date_subquery = (
            ProcessedDocument.objects.filter(raw_retreival_document=OuterRef("pk"))
            .order_by("-created_at")
            .values("effective_date")[:1]
        )

        raw_docs_objs = (
            RawDocument.objects.filter(pk__in=pks)
            .select_related("retrieval", "retrieval__merged_portal_credential")
            .prefetch_related(
                "processed_documents",
                "processed_documents__line_item",
                "retrieval__line_items",
                "retrieval__line_items__investing_entity",
                "retrieval__line_items__investment",
                "retrieval__line_items__investing_entity__client",
            )
            #   "processed_documents__raw_retrieval_document")
            .annotate(previous_effective_date=Subquery(previous_effective_date_subquery))
            .order_by(
                "document_type",
                "posted_date",
            )
            .all()
        )

        def get_metadata(raw_doc: RawDocument) -> dict[str, Any]:
            if raw_doc.metadata:
                return json.loads(raw_doc.metadata) if isinstance(raw_doc.metadata, str) else raw_doc.metadata
            return {}

        raw_docs = [
            {
                "name": raw_doc.name,
                "posted_date": raw_doc.posted_date,
                "document_type": raw_doc.document_type,
                "pk": raw_doc.pk,
                "is_backfill": raw_doc.retrieval.is_backfill if raw_doc.retrieval else False,
                "metadata": get_metadata(raw_doc),
                "line_items": sorted(
                    raw_doc.retrieval.line_items.all(),
                    key=lambda li: (
                        li.investing_entity.client.legal_name
                        if li.investing_entity and li.investing_entity.client
                        else "",
                        li.investing_entity.legal_name if li.investing_entity else "",
                        li.investment.legal_name if li.investment else "",
                    ),
                )
                if raw_doc.retrieval
                else [],
                "previous_line_items": {pd.line_item.pk for pd in raw_doc.processed_documents.all()},
                "previous_effective_date": raw_doc.previous_effective_date,
                "choice_document_type": DocumentType.choices,
            }
            for raw_doc in raw_docs_objs
        ]
        return TemplateResponse(
            request,
            template,
            {
                "counter": counter,
                "raw_docs": raw_docs[:400],
                "DocumentType": DocumentType,
                "SubDocumentType": SubDocumentType,
            },
        )
    if request.method == "POST":
        template = "admin/webapp/rawdocument/bulk_label.html"
        logger.info("Bulk label", post=request.POST)
        docs_to_save: dict[str, tuple[dict[str, str | list[LineItem]], RawDocument]] = {}
        raw_docs_models = {}
        li_models = {}
        for key, value in request.POST.lists():
            split = key.split("|")
            if len(split) != 2:  # noqa: PLR2004
                continue
            raw_doc_pk, field = split
            raw_docs_models[raw_doc_pk] = None
            if field == "line_items":
                for v in value:
                    li_models[v] = None
        previous_effective_date_subquery = (
            ProcessedDocument.objects.filter(raw_retreival_document=OuterRef("pk"))
            .order_by("-created_at")
            .values("effective_date")[:1]
        )

        raw_doc_objs = (
            RawDocument.objects.filter(pk__in=raw_docs_models.keys())
            .select_related("retrieval", "retrieval__merged_portal_credential")
            .prefetch_related(
                "processed_documents",
                "processed_documents__line_item",
                "retrieval__line_items",
                "retrieval__line_items__investing_entity",
                "retrieval__line_items__investment",
                "retrieval__line_items__investing_entity__client",
            )
            .annotate(previous_effective_date=Subquery(previous_effective_date_subquery))
            .order_by("document_type", "posted_date")
            .all()
        )
        line_item_objs = LineItem.objects.filter(pk__in=li_models.keys()).all()
        for raw_doc in raw_doc_objs:
            raw_docs_models[str(raw_doc.pk)] = raw_doc
        for li in line_item_objs:
            li_models[str(li.pk)] = li

        for key, value in request.POST.lists():
            split = key.split("|")
            if len(split) != 2:  # noqa: PLR2004
                continue
            raw_doc_pk, field = split
            raw_doc = raw_docs_models[raw_doc_pk]
            if raw_doc_pk not in docs_to_save:
                docs_to_save[raw_doc_pk] = ({}, raw_doc)
            if field == "line_items":
                docs_to_save[raw_doc_pk][0][field] = [li_models[v] for v in value if v in li_models]
            elif field == "sub_document_type":
                if raw_doc.document_type not in REQUIRED_SUB_TYPE_DOCUMENTS:
                    docs_to_save[raw_doc_pk][0][field] = ""
                else:
                    docs_to_save[raw_doc_pk][0][field] = value[0]
            else:
                docs_to_save[raw_doc_pk][0][field] = value[0]

        logger.info("Docs to save", docs_to_save=docs_to_save)
        for data, raw_doc in docs_to_save.values():
            if raw_doc is None:
                continue
            try:
                ground_truth_process_document(raw_document=raw_doc, cleaned_data=data, labeled_by=request.user)
                messages.success(request, f"Document labeled, {raw_doc.name}", raw_doc.name)
            except Exception as e:  # noqa: BLE001
                messages.error(request, f"ERROR: {raw_doc.name}: {e}", raw_doc.name)
        for raw_doc in raw_docs_models.values():
            raw_doc.refresh_from_db()

        def get_metadata(raw_doc: RawDocument) -> dict[str, Any]:
            if raw_doc.metadata:
                return json.loads(raw_doc.metadata) if isinstance(raw_doc.metadata, str) else raw_doc.metadata
            return {}

        raw_docs = [
            {
                "name": raw_doc.name,
                "posted_date": raw_doc.posted_date,
                "document_type": raw_doc.document_type,
                "pk": raw_doc.pk,
                "metadata": get_metadata(raw_doc),
                "is_backfill": raw_doc.retrieval.is_backfill if raw_doc.retrieval else False,
                "line_items": sorted(
                    raw_doc.retrieval.line_items.all(),
                    key=lambda li: (
                        li.investing_entity.client.legal_name
                        if li.investing_entity and li.investing_entity.client
                        else "",
                        li.investing_entity.legal_name if li.investing_entity else "",
                        li.investment.legal_name if li.investment else "",
                    ),
                )
                if raw_doc.retrieval
                else [],
                "previous_line_items": {pd.line_item.pk for pd in raw_doc.processed_documents.all()},
                "previous_effective_date": raw_doc.previous_effective_date,
                "choice_document_type": DocumentType.choices,
            }
            for raw_doc in raw_docs_models.values()
            if raw_doc is not None
        ]
        return TemplateResponse(
            request, template, {"raw_docs": raw_docs, "DocumentType": DocumentType, "SubDocumentType": SubDocumentType}
        )
    return HttpResponse("Invalid request method", status=405)


def raw_doc_upload_view(request: HttpRequest) -> HttpResponse:
    template = "admin/webapp/rawdocument/raw_doc_upload.html"
    raw_pk = request.GET.get("pk", "")
    merged_portal_credential = MergedPortalCredential.objects.get(pk=raw_pk)
    if merged_portal_credential.created_by is None:
        messages.error(request, "Database is corrupt (no user for this merged portal credential)")
        return HttpResponse("Invalid request ", status=400)

    user = merged_portal_credential.created_by

    if request.method == "GET":
        form = FileFieldForm()
        return TemplateResponse(request, template, {"form": form})
    if request.method == "POST":
        form = FileFieldForm(request.POST, request.FILES)
        if not form.is_valid():
            logger.error("Form is invalid", form=form.errors)
            return TemplateResponse(request, template, {"form": form})

        manual_retrieval_folder = (
            Path("manual_retrieval") / str(timezone.now().strftime("%Y-%m-%d_%H-%M-%S")) / "raw_documents"
        )
        retrieval = Retrieval.create(
            user=user,
            merged_portal_credential=merged_portal_credential,
            s3_format_version=-1,
            s3_key=manual_retrieval_folder,
            s3_bucket=BUCKET,
            manager="ManualRetrievalV1",
            exists_in_s3=True,
            retrieval_status=Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
        )

        new_docs = []
        for file in form.cleaned_data["file_field"]:
            doc_hash = DocumentHash(str(uuid.uuid4()), 1, "ManualUpload")
            doc = RawDocumentTuple(
                name=file.name,
                date=timezone.now(),
                doc_type=DocumentType.UNKNOWN,
                content=file.read(),
                raw_metadata="{}",
                doc_hash=doc_hash,
            )
            doc_id, _ = save_document(user, doc, retrieval=retrieval)
            retrieval.increment_documents_retrieved()
            new_docs.append(doc_id)
        retrieval.update_login_status(Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL)
        return redirect_with_url_params("admin:webapp_rawdocument_bulk_label", pks=",".join(new_docs))
    return HttpResponse("Invalid request method", status=405)


@admin.action(description="Bulk Label")
def bulk_label_action(
    modeladmin: "RawDocumentModelAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> HttpResponseRedirect:
    serialized = json.dumps(request.GET)
    selected = ",".join([str(pk) for pk in queryset.values_list("pk", flat=True)])
    return redirect_with_url_params("admin:webapp_rawdocument_bulk_label", pks=selected, go_back=serialized)


@admin.action(description="Predict Doc Vault V1")
def predict_v1(
    modeladmin: "RawDocumentModelAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> HttpResponseRedirect:
    serialized = json.dumps(request.GET)
    raw_doc_pks = queryset.values_list("pk", flat=True)
    selected = ",".join([str(pk) for pk in raw_doc_pks])
    res = multi_process_doc_vault.apply_async(kwargs={"raw_document_ids": list(map(str, raw_doc_pks))}, queue="medium")
    return redirect_with_url_params(
        "admin:webapp_rawdocument_bulk_label", pks=selected, go_back=serialized, task_id=res.task_id
    )


@admin.action(description="Mark Duplicate")
def mark_duplicates(
    modeladmin: "RawDocumentModelAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> None:
    duplicates = {str(pk) for pk in queryset.values_list("pk", flat=True)}
    for rd in RawDocument.objects.filter(pk__in=duplicates):
        rd.is_duplicate = True
        rd.save()
        messages.success(request, f"Document Marked as duplicate ({rd.name})")


@admin.action(description="Upload Similar Documents")
def upload_document(
    modeladmin: "RawDocumentModelAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> HttpResponseRedirect | None:
    serialized = json.dumps(request.GET)
    raw_pks = queryset.values_list("pk", flat=True)
    raw_docs = RawDocument.objects.filter(pk__in=raw_pks).all()
    merged_portal_credentials = {
        raw_doc.retrieval.merged_portal_credential
        for raw_doc in raw_docs
        if raw_doc.retrieval is not None and raw_doc.retrieval.merged_portal_credential is not None
    }
    if len(merged_portal_credentials) != 1:
        messages.error(request, "Documents must be from the same portal / organization")
        return None
    merged_portal_credential = merged_portal_credentials.pop()
    if merged_portal_credential.created_by is None:
        messages.error(request, "Database is corrupt (no user for this merged portal credential)")
        return None

    return redirect_with_url_params(
        "admin:webapp_rawdocument_raw_doc_upload", pk=merged_portal_credential.pk, go_back=serialized
    )


@admin.action(description="Sync State with Processed Documents")
def sync_state_with_processed_doc(
    modeladmin: "RawDocumentModelAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> HttpResponseRedirect | None:
    raw_pks = queryset.values_list("pk", flat=True)
    raw_docs = RawDocument.objects.filter(pk__in=raw_pks).all()
    for raw_doc in raw_docs:
        messages.success(request, f"Synced state for {raw_doc.name}")
        try:
            raw_doc.sync_state_with_processed_doc()
        except Exception:
            logger.exception("Failed to sync state for raw document", raw_doc_id=raw_doc.pk)
            messages.error(request, f"Failed to sync state for {raw_doc.name}")


@admin.action(description="Index Document")
def index_document(
    modeladmin: "RawDocumentModelAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> None:
    raw_pks = queryset.values_list("pk", flat=True)
    raw_docs = RawDocument.objects.filter(pk__in=raw_pks).all()
    for rd in raw_docs:
        ingest_processed_doc.apply_async(kwargs={"raw_document_ids": [str(rd.pk)]})
        messages.success(request, f"Document Scheduled for indexing ({rd.name})")


@csrf_exempt
def index_document_view(
    request: HttpRequest,
    pk: str,
) -> HttpResponse:
    raw_doc = get_object_or_404(RawDocument, pk=pk)
    ingest_processed_doc.apply_async(kwargs={"raw_document_ids": [str(raw_doc.pk)]})
    messages.success(request, f"Document Scheduled for indexing ({raw_doc.name})")
    return HttpResponse("success", status=200)


@csrf_exempt
def index_and_backfill_document_view(
    request: HttpRequest,
    pk: str,
) -> HttpResponse:
    raw_doc = get_object_or_404(RawDocument, pk=pk)
    ingest_and_backfill_processed_doc.apply_async(kwargs={"raw_document_ids": [str(raw_doc.pk)]})
    messages.success(request, f"Document Scheduled for indexing ({raw_doc.name})")
    return HttpResponse("success", status=200)


@csrf_exempt
def generate_summary_view(request: HttpRequest, pk: str) -> HttpResponse:
    from django.http import JsonResponse
    from ml_app.core.pdf_summarizer import summarize_raw_document

    if request.method != "POST":
        return JsonResponse({"success": False, "error": "Only POST method allowed"})

    raw_doc = get_object_or_404(RawDocument, pk=pk)

    try:
        summary_data = summarize_raw_document(str(raw_doc.pk))

        return JsonResponse(
            {
                "success": True,
                "summary": summary_data.get("summary", ""),
            }
        )
    except Exception as e:
        logger.exception("Failed to generate summary", raw_doc_id=pk)
        return JsonResponse({"success": False, "error": str(e)})


@admin.register(RawDocument)
class RawDocumentModelAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "posted_date",
        "created_at",
        "organization__name",
        "investing_entity",
        "investment",
        "portal",
        "document_type",
        "has_ground_truth",
        "has_been_processed",
        "has_extracted_numbers",
        "is_duplicate",
        "docs",
        "label",
        "has_granular_ground_truth",
        "granular_ground_truth",
    )
    list_filter = (
        "document_type",
        "has_been_processed",
        "has_ground_truth",
        "has_extracted_numbers",
        ("organization__name", dropdown_filter(AllValuesFieldListFilter, "Organization")),
        (
            "processed_documents__line_item__investing_entity__legal_name",
            dropdown_filter(AllValuesFieldListFilter, "(Labeled) Line Item Entity"),
        ),
        (
            "processed_documents__line_item__investment__legal_name",
            dropdown_filter(AllValuesFieldListFilter, "(Labeled) Line Item Investment"),
        ),
        (
            "retrieval__manager",
            dropdown_filter(AllValuesAndNoneFieldListFilter, "Portal Name"),
        ),
        (
            "retrieval__merged_portal_credential__portal__name",
            dropdown_filter(AllValuesAndNoneFieldListFilter, "User Portal Name"),
        ),
        "is_duplicate",
    )
    search_fields = ("name", "organization__name", "document_type", "metadata")
    ordering = ("-created_at", "name")
    raw_id_fields = ("retrieval",)
    show_facets = admin.ShowFacets.ALWAYS
    actions = (
        bulk_label_action,
        mark_duplicates,
        upload_document,
        predict_v1,
        sync_state_with_processed_doc,
        index_document,
    )

    @admin.display
    def has_granular_ground_truth(self, obj: RawDocument) -> bool:
        return obj.raw_extractions.filter(is_current_target_schema_validated=True).exists()

    has_granular_ground_truth.boolean = True

    def portal(self, obj: RawDocument) -> str:
        if obj.retrieval is None:
            return "Email (No Retrieval)"
        return format_html(
            '<a href="{}">{}</a>',
            reverse("admin:webapp_retrieval_change", args=[obj.retrieval.pk]) if obj.retrieval else "#",
            obj.retrieval.manager if obj.retrieval else " - ",
        )

    def investing_entity(self, obj: RawDocument) -> str:
        if obj.processed_documents is None:
            return " - "
        first = obj.processed_documents.order_by("-created_at").first()
        if first is None:
            return " - "
        return first.line_item.investing_entity.legal_name

    def investment(self, obj: RawDocument) -> str:
        if obj.processed_documents is None:
            return " - "
        first = obj.processed_documents.order_by("-created_at").first()
        if first is None:
            return " - "
        return first.line_item.investment.legal_name

    def docs(self, obj: RawDocument) -> str:
        return format_html("<a href='{}?q={}'>View</a>", reverse("admin:webapp_processeddocument_changelist"), obj.name)

    def get_urls(self) -> list[URLPattern]:
        return [
            path(
                "<pk>/label",
                self.admin_site.admin_view(rawdocument_label_view),
                name="webapp_rawdocument_label",
            ),
            path(
                "<pk>/granular_ground_truth",
                self.admin_site.admin_view(rawdocument_granular_ground_truth_view),
                name="webapp_rawdocument_granular_ground_truth",
            ),
            path(
                "<pk>/get_doc_chunks",
                self.admin_site.admin_view(rawdocument_get_doc_chunks_view),
                name="webapp_rawdocument_get_doc_chunks",
            ),
            path(
                "schemas",
                self.admin_site.admin_view(schemas_view),
                name="webapp_rawdocument_schemas",
            ),
            path(
                "<pk>/extracted_data",
                self.admin_site.admin_view(extracted_data_view),
                name="webapp_rawdocument_extracted_data",
            ),
            path(
                "<pk>/submit_target_document",
                self.admin_site.admin_view(submit_target_document_view),
                name="webapp_rawdocument_submit_target_document",
            ),
            path(
                "<pk>/index_document",
                self.admin_site.admin_view(index_document_view),
                name="webapp_rawdocument_index_document",
            ),
            path(
                "<pk>/index_and_backfill_document",
                self.admin_site.admin_view(index_and_backfill_document_view),
                name="webapp_rawdocument_index_and_backfill_document",
            ),
            path(
                "<pk>/generate_summary",
                self.admin_site.admin_view(generate_summary_view),
                name="webapp_rawdocument_generate_summary",
            ),
            path(
                "bulk_label",
                self.admin_site.admin_view(bulk_label_view),
                name="webapp_rawdocument_bulk_label",
            ),
            path(
                "raw_doc_upload",
                self.admin_site.admin_view(raw_doc_upload_view),
                name="webapp_rawdocument_raw_doc_upload",
            ),
            *super().get_urls(),
        ]

    def label(self, obj: RawDocument) -> str:
        url = reverse("admin:webapp_rawdocument_label", args=[obj.pk])
        return format_html('<a href="{}">📝</a>', url)

    def granular_ground_truth(self, obj: RawDocument) -> str:
        url = reverse("admin:webapp_rawdocument_granular_ground_truth", args=[obj.pk])
        return format_html('<a href="{}">📝</a>', url)
