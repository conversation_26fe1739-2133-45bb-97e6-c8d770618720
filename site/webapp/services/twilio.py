import structlog
from django.conf import settings
from twilio.base.exceptions import TwilioRestException
from twilio.rest import Client

TWILIO_ACCOUNT_SID = settings.TWILIO_ACCOUNT_SID
TWILIO_AUTH_TOKEN = settings.TWILIO_AUTH_TOKEN
TWILIO_VERIFY_SERVICE_SID = settings.TWILIO_VERIFY_SERVICE_SID

# Twilio error codes
TWILIO_ERROR_INVALID_PHONE = 60200
TWILIO_ERROR_MAX_SEND_ATTEMPTS = 60204
TWILIO_ERROR_LANDLINE_UNSUPPORTED = 60205
TWILIO_ERROR_MAX_VERIFY_ATTEMPTS = 60202

logger = structlog.get_logger(__name__)


class TwilioInvalidPhoneNumberError(Exception):
    """
    An error that occurs when a phone number is invalid or not formatted correctly.
    """


class TwilioMaxVerificationAttemptsReachedError(Exception):
    """
    An error that occurs when the max number of verification attempts (5) is reached within a 10 minute window.
    """


class TwilioMaxSendAttemptsReachedError(Exception):
    """
    An error that occurs when the max number of send attempts (5) is reached within a 10 minute window.
    """


class TwilioGeneralError(Exception):
    """
    A generic error that occurs when the Twilio returns a non-specific error.
    """


class TwilioLandlineNotSupportedError(Exception):
    """
    An error that occurs when a landline number is not supported.
    """


class TwilioClient:
    """
    A client for the Twilio API.
    """

    def __init__(self) -> None:
        """
        Initialize the Twilio client
        """
        self.client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN).verify.services(TWILIO_VERIFY_SERVICE_SID)

    def send_token_sms(self, to: str) -> bool:
        """
        Send an SMS token.
        """
        try:
            result = self.client.verifications.create(to=to, channel="sms")
        except TwilioRestException as e:
            if e.code == TWILIO_ERROR_INVALID_PHONE:
                logger.exception("Twilio Client Error: Invalid phone number")
                raise TwilioInvalidPhoneNumberError from e
            if e.code == TWILIO_ERROR_MAX_SEND_ATTEMPTS:
                logger.exception("Twilio Client Error: Max send attempts reached")
                raise TwilioMaxSendAttemptsReachedError from e
            if e.code == TWILIO_ERROR_LANDLINE_UNSUPPORTED:
                logger.exception("Twilio Client Error: Landline number not supported")
                raise TwilioLandlineNotSupportedError from e
            logger.exception("Twilio Client Error: An uncaught error occurred")
            raise TwilioGeneralError from e

        except Exception:
            logger.exception("Twilio Client Error: An unexpected non-Twilio error occurred")
            raise
        else:
            return result.status == "pending"

    def verify_phone_number(self, to: str, code: str) -> dict:
        """
        Verify a phone number.
        """
        try:
            verification = self.client.verification_checks.create(to=to, code=code)
        except TwilioRestException as e:
            if e.code == TWILIO_ERROR_INVALID_PHONE:
                logger.exception("Twilio Client Error: Invalid phone number")
                raise TwilioInvalidPhoneNumberError from e
            if e.code == TWILIO_ERROR_MAX_VERIFY_ATTEMPTS:
                logger.exception("Twilio Client Error: Invalid code")
                raise TwilioMaxVerificationAttemptsReachedError from e
            logger.exception("Twilio Client Error: An uncaught error occurred")
            raise TwilioGeneralError from e
        except Exception:
            logger.exception("Twilio Client Error: An unexpected non-Twilio error occurred")
            raise
        else:
            return verification.status == "approved"
