{% load customtags static %}
<form hx-post="{% url 'emails_setup_integration' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
      hx-swap="outerHTML"
      hx-target="#modal">
    <div class="ModalButtonWrapper w-full flex-col justify-start items-start gap-3 inline-flex">
        <button type="submit"
                name="submit"
                value="use_exisiting"
                class="text-white text-base font-semibold font-inter leading-normal Buttons self-stretch h-11 px-4 py-2 bg-[#924f34] rounded-lg  shadow-inner justify-center items-center gap-1.5 inline-flex">
            Use existing Email Plug-In
        </button>
        {% if receiving_email.email_provider_bridge_managed != 'ns' %}
            <button type="submit"
                    name="submit"
                    value="automatic_setup"
                    class="text-[#924f34] text-base font-semibold font-inter leading-normal Buttons self-stretch w-full h-11 px-4 py-2.5 bg-white rounded-lg  shadow-inner border border-[#924f34] justify-center items-center gap-1.5 inline-flex">
                Update Email Plug-In
            </button>
        {% endif %}
        <button type="submit"
                name="submit"
                value="skip_edit"
                class="text-[#924f34] text-base font-semibold font-inter leading-normal Buttons self-stretch w-full h-11 px-4 py-2.5 bg-white rounded-lg  shadow-inner border border-[#924f34] justify-center items-center gap-1.5 inline-flex">
            Update to Manual Setup
        </button>
    </div>
</form>
