{% load static %}
<div class="h-[72px] px-8 py-4 justify-between items-center inline-flex w-full">
    <form class="grow shrink basis-0 h-9 justify-start items-center gap-1.5 flex"
          hx-trigger="change"
          hx-target="#insight_dynamic"
          hx-post="{% url 'insight_dynamic' %}"
          hx-swap="outerHTML">
        <div class="w-[84px] text-[#343d36] text-lg font-semibold font-['Inter'] leading-tight">Filter by:</div>
        <div class="relative">
            <select class="px-2.5 py-2 bg-white rounded-lg border border-secondary-light-olive text-[#343d36] text-sm font-semibold font-['Inter'] leading-tight w-full"
                    name="entity">
                <option value="__all__" selected>Select Entity</option>
                {% for op in option_entity %}<option value="{{ op }}">{{ op }}</option>{% endfor %}
            </select>
        </div>
        <div class="relative">
            <select class="px-2.5 py-2 bg-white rounded-lg border border-secondary-light-olive text-[#343d36] text-sm font-semibold font-['Inter'] leading-tight w-full"
                    name="asset_class">
                <option value="__all__" selected>Select Asset Class</option>
                {% for op in option_asset_class %}<option value="{{ op }}">{{ op }}</option>{% endfor %}
            </select>
        </div>
        <div class="relative">
            <select class="px-2.5 py-2 bg-white rounded-lg border border-secondary-light-olive text-[#343d36] text-sm font-semibold font-['Inter'] leading-tight w-full"
                    name="geography">
                <option value="__all__" selected>Select Geography</option>
                {% for op in option_geography %}<option value="{{ op }}">{{ op }}</option>{% endfor %}
            </select>
        </div>
        <div class="relative ">
            <select class="px-2.5 py-2 bg-white rounded-lg border border-secondary-light-olive text-[#343d36] text-sm font-semibold font-['Inter'] leading-tight w-full"
                    name="sector">
                <option value="__all__" selected>Select Sector</option>
                {% for op in option_sector %}<option value="{{ op }}">{{ op }}</option>{% endfor %}
            </select>
        </div>
        <div class="relative ">
            <select class="px-2.5 py-2 bg-white rounded-lg border border-secondary-light-olive text-[#343d36] text-sm font-semibold font-['Inter'] leading-tight w-full"
                    name="sub_sector">
                <option value="__all__" selected>Select Sub-Sector</option>
                {% for op in option_sub_sector %}<option value="{{ op }}">{{ op }}</option>{% endfor %}
            </select>
        </div>
    </form>
    {% static download_url as static_download_url %}
    <a id="insights_download"
       target="_blank"
       class="hidden"
       href="{{ static_download_url }}"
       download='Bridge_Insights_Export.xlsx'></a>
    <button class="justify-start items-center gap-3 flex"
            onclick="document.getElementById('insights_download').click()">
        <div class="flex-col justify-start items-start gap-2 inline-flex">
            <div class="px-3.5 py-2.5 bg-[#4b554d] rounded-lg border border-secondary-light-olive justify-center items-center gap-1 inline-flex">
                <div class="w-4 h-4 relative">
                    <img height="18"
                         width="18"
                         src="{% static 'assets/images/download_icon.svg' %}"
                         alt="download icon" />
                </div>
                <div class="px-0.5 justify-center items-center flex">
                    <div class="text-white text-sm font-semibold font-['Inter'] leading-tight">Export</div>
                </div>
            </div>
        </div>
    </button>
</div>
