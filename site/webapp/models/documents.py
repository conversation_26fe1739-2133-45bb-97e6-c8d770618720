import csv
import os
import tempfile
import typing
import urllib.parse
from collections.abc import Iterator
from contextlib import contextmanager
from decimal import Decimal
from pathlib import Path

import boto3
import structlog
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models.signals import post_delete
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _

if typing.TYPE_CHECKING:
    from webapp.models.extraction import DocChunk, RawExtraction
    from webapp.models.line_item import LineItem

logger = structlog.get_logger(__name__)
import datetime

from webapp.models.core import AbstractBaseModel
from webapp.models.user import BridgeUser


class DocumentType(models.TextChoices):
    CAPITAL_CALL = "cc", _("Capital Call")
    DISTRIBUTION_NOTICE = "dn", _("Distribution Notice")
    ACCOUNT_STATEMENT = "as", _("Account Statement")
    INVESTMENT_UPDATE = "iu", _("Investment Update")
    FINANCIAL_STATEMENTS = "fs", _("Financial Statements")
    TAX = "tx", _("Tax")
    LEGAL = "lg", _("Legal")
    OTHER = "ot", _("Other")
    UNKNOWN = "un", _("Unknown")


class SubDocumentType(models.TextChoices):
    AUDITED_FINANCIAL_STATEMENTS = "audited_fs", _("Audited Financial Statements")
    UNAUDITED_FINANCIAL_STATEMENTS = "unaudited_fs", _("Unaudited Financial Statements")
    QUARTERLY_LETTER = "quarterly_letter", _("Quarterly Letter")
    ANNUAL_LETTER = "annual_letter", _("Annual Letter")
    INVESTMENT_MEMO = "investment_memo", _("Investment Memo")
    TAX_ESTIMATES = "tax_estimates", _("Tax Estimates")
    TAX_FINAL = "tax_final", _("Tax Final")
    OTHER = "other", _("Other")


DOCUMENT_TYPE_SUB_TYPE_MAPPING = {
    DocumentType.FINANCIAL_STATEMENTS: [
        SubDocumentType.AUDITED_FINANCIAL_STATEMENTS,
        SubDocumentType.UNAUDITED_FINANCIAL_STATEMENTS,
        SubDocumentType.OTHER,
    ],
    DocumentType.INVESTMENT_UPDATE: [
        SubDocumentType.QUARTERLY_LETTER,
        SubDocumentType.ANNUAL_LETTER,
        SubDocumentType.INVESTMENT_MEMO,
        SubDocumentType.OTHER,
    ],
    DocumentType.TAX: [
        SubDocumentType.TAX_ESTIMATES,
        SubDocumentType.TAX_FINAL,
        SubDocumentType.OTHER,
    ],
}

# Document types that require a sub_document_type
REQUIRED_SUB_TYPE_DOCUMENTS = {
    DocumentType.FINANCIAL_STATEMENTS,
    DocumentType.INVESTMENT_UPDATE,
    DocumentType.TAX,
}


class RawDocument(AbstractBaseModel):
    document_type = models.CharField(
        max_length=2,
        choices=DocumentType.choices,
        default=DocumentType.UNKNOWN,
    )
    md5 = models.CharField(max_length=255)
    doc_hash = models.CharField(max_length=255, blank=True)
    doc_hash_version = models.IntegerField(default=0)
    doc_hash_source = models.CharField(max_length=255, blank=True)
    # MAKE SOME THINGS IMMUTABLE.
    name = models.CharField(max_length=255)
    s3_key = models.CharField(max_length=1024, blank=True)
    s3_bucket = models.CharField(max_length=255, blank=True)
    exists_in_s3 = models.BooleanField(default=False)
    posted_date = models.DateField()
    process_document_version = models.IntegerField(default=0)
    has_been_processed = models.BooleanField(default=False)
    has_extracted_numbers = models.BooleanField(default=False)
    has_ground_truth = models.BooleanField(default=False)
    is_duplicate = models.BooleanField(default=False)
    metadata = models.JSONField(null=True)
    original_document_type = models.CharField(max_length=2, choices=DocumentType.choices, default=DocumentType.UNKNOWN)
    original_posted_date = models.DateField()

    retrieval = models.ForeignKey(
        "Retrieval",
        on_delete=models.CASCADE,
        related_name="raw_documents",
        blank=True,
        null=True,
    )
    email_intake = models.ForeignKey(
        "EmailIntake",
        on_delete=models.CASCADE,
        related_name="raw_documents",
        null=True,
    )
    processed_documents: models.QuerySet["ProcessedDocument"]
    doc_chunks: models.QuerySet["DocChunk"]
    raw_extractions: models.QuerySet["RawExtraction"]

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        # TODO: if retrieval exists, we should get the line items from there; if forwarding rule exists, we should get the line items from there  # noqa: E501
        return []

    def __str__(self) -> str:
        if self.retrieval is None:
            return f"Document {self.name} from {self.email_intake}"
        return f"Document from {self.retrieval}"

    @property
    def signed_url(self) -> str | None:
        s3 = boto3.client("s3", region_name=os.environ.get("AWS_REGION", "us-east-1"))
        if not self.s3_bucket or not self.s3_key:
            return None
        content_type = extract_content_type(self)
        # TODO: throw a cache on this bitch
        return s3.generate_presigned_url(
            ClientMethod="get_object",
            Params={
                "Bucket": self.s3_bucket,
                "Key": self.s3_key,
                "ResponseContentType": content_type,
                "ResponseContentDisposition": f'filename="{urllib.parse.quote(self.name, safe="")}"',
            },
            ExpiresIn=3600,  # one hour in seconds, increase if needed
        )

    def get_doc_bytes(self) -> bytes | None:
        s3 = boto3.client("s3")
        if not self.s3_bucket or not self.s3_key:
            return None
        try:
            obj = s3.get_object(Bucket=self.s3_bucket, Key=self.s3_key)
            return obj["Body"].read()
        except Exception as e:
            logger.exception("Error getting document bytes", error=str(e), bucket=self.s3_bucket, key=self.s3_key)
            return None

    @contextmanager
    def get_doc_file(self) -> Iterator[Path | None]:
        content = self.get_doc_bytes()
        if content is None:
            yield None
            return
        with tempfile.NamedTemporaryFile(delete=True) as tmp_file:
            tmp_file.write(content)
            tmp_file.flush()
            yield Path(tmp_file.name)

    def sync_state_with_processed_doc(self) -> None:
        self.refresh_from_db()
        exemplar_processed_doc_gt = self.processed_documents.filter(is_visible=True).first()
        exemplar_processed_doc = self.processed_documents.first()
        if not self.processed_documents.exists():
            self.posted_date = self.original_posted_date
            self.document_type = self.original_document_type
            self.has_been_processed = False
            self.has_extracted_numbers = False
            self.has_ground_truth = False
            self.save()
            return
        if exemplar_processed_doc_gt is not None:
            exemplar_processed_doc = exemplar_processed_doc_gt
        self.posted_date = exemplar_processed_doc.posted_date
        self.document_type = exemplar_processed_doc.document_type
        self.has_been_processed = True
        self.has_extracted_numbers = exemplar_processed_doc.has_extracted_numbers
        self.has_ground_truth = exemplar_processed_doc.is_ground_truth
        self.save()


class ProcessedDocument(AbstractBaseModel):
    raw_retreival_document = models.ForeignKey(
        "RawDocument",
        on_delete=models.CASCADE,
        related_name="processed_documents",
        blank=True,
        null=True,
    )
    # TODO: should we turn this into a Many to Many field?
    #  Right now we "duplicate" data if one document is associated with many line items.
    line_item = models.ForeignKey(
        "webapp.LineItem",
        on_delete=models.CASCADE,
        related_name="processed_documents",
    )
    document_type = models.CharField(
        max_length=2,
        choices=DocumentType.choices,
        default=DocumentType.UNKNOWN,
    )
    sub_document_type = models.CharField(
        max_length=32,
        choices=SubDocumentType.choices,
        blank=True,
    )
    md5 = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    process_document_source = models.CharField(max_length=255, blank=True)
    process_document_version = models.IntegerField()
    posted_date = models.DateField()
    effective_date = models.DateField()
    content_type = models.CharField(max_length=255)
    has_extracted_numbers = models.BooleanField(default=False)
    is_ground_truth = models.BooleanField(default=False)
    is_visible = models.BooleanField(default=False)
    is_wrong = models.BooleanField(default=False)
    is_correct = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    labeled_by = models.ForeignKey(
        BridgeUser, on_delete=models.SET_NULL, related_name="tagged_documents", blank=True, null=True
    )

    s3_key = models.CharField(max_length=1024, blank=True)
    s3_bucket = models.CharField(max_length=255, blank=True)
    exists_in_s3 = models.BooleanField(default=False)

    has_been_viewed = models.BooleanField(default=False)
    first_viewed_date = models.DateTimeField(blank=True, null=True)

    raw_prediction_values = models.JSONField(null=True, blank=True)

    document_summary = models.TextField(blank=True, null=True)  # noqa DJ001
    is_document_summary_approved = models.BooleanField(default=False)

    capital_call_document: "models.OneToOneField[CapitalCallDocumentFact|None, ProcessedDocument]" = (
        models.OneToOneField(
            "webapp.CapitalCallDocumentFact",
            on_delete=models.CASCADE,
            related_name="processed_document",
            blank=True,
            null=True,
        )
    )
    # NOTE: this is legacy naming, this acutally should be "AccountStatementDocumentFact"
    investment_update_document: "models.OneToOneField[InvestmentUpdateDocumentFact|None, ProcessedDocument]" = (
        models.OneToOneField(
            "webapp.InvestmentUpdateDocumentFact",
            on_delete=models.CASCADE,
            related_name="processed_document",
            blank=True,
            null=True,
        )
    )
    distribution_notice_document: "models.OneToOneField[DistributionNoticeDocumentFact|None, ProcessedDocument]" = (
        models.OneToOneField(
            "webapp.DistributionNoticeDocumentFact",
            on_delete=models.CASCADE,
            related_name="processed_document",
            blank=True,
            null=True,
        )
    )

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_item"]

    def clean(self) -> None:
        super().clean()
        if self.document_type and self.document_type in REQUIRED_SUB_TYPE_DOCUMENTS:
            if not self.sub_document_type:
                msg = f'Sub document type is required for document type "{self.document_type}"'
                raise ValidationError({"sub_document_type": msg})
            allowed_sub_types = DOCUMENT_TYPE_SUB_TYPE_MAPPING.get(self.document_type, [])
            if self.sub_document_type not in allowed_sub_types:
                msg = (
                    f'Sub document type "{self.sub_document_type}" is not valid for document '
                    f'type "{self.document_type}". Allowed types: {", ".join(allowed_sub_types)}'
                )
                raise ValidationError({"sub_document_type": msg})

    @classmethod
    def get_sub_document_types_for_document_type(cls, document_type: str) -> list[str]:
        return DOCUMENT_TYPE_SUB_TYPE_MAPPING.get(document_type, [])

    @classmethod
    def requires_sub_document_type(cls, document_type: str) -> bool:
        return document_type in REQUIRED_SUB_TYPE_DOCUMENTS

    def __str__(self) -> str:
        return f"Name {self.name} Document from {self.line_item}"

    @property
    def signed_url(self) -> str | None:
        s3 = boto3.client("s3")
        if not self.s3_bucket or not self.s3_key:
            return None
        content_type = self.content_type
        # TODO: throw a cache on this bitch
        return s3.generate_presigned_url(
            ClientMethod="get_object",
            Params={
                "Bucket": self.s3_bucket,
                "Key": self.s3_key,
                "ResponseContentType": content_type,
                "ResponseContentDisposition": f'filename="{urllib.parse.quote(self.name)}"',
            },
            ExpiresIn=3600,  # one hour in seconds, increase if needed
        )

    @classmethod
    def create(  # noqa: C901, PLR0912, PLR0913, PLR0915
        cls,
        raw_document: "RawDocument",
        line_items: list["LineItem"],
        document_type: str,
        sub_document_type: str,
        posted_date: datetime.datetime | str,
        effective_date: datetime.datetime | str,
        process_document_version: int,
        process_document_source: str,
        labeled_by: BridgeUser | None,
        *,
        is_visible: bool,
        is_ground_truth: bool,
        has_been_viewed: bool = False,
        hide_all_other_line_items: bool = False,
        hide_line_items: list["LineItem"] | None = None,
        distribution_notice: dict[str, typing.Any] | None = None,
        capital_call: dict[str, typing.Any] | None = None,
        investment_update: dict[str, typing.Any] | None = None,
        document_summary: str | None = None,
        is_document_summary_approved: bool = False,
    ) -> list["ProcessedDocument"]:
        import mimetypes

        if sum([distribution_notice is not None, capital_call is not None, investment_update is not None]) > 1:
            raise ValueError
        if hide_all_other_line_items and hide_line_items is not None:
            raise ValueError

        s3 = boto3.client("s3")
        res = []
        with transaction.atomic():
            for line_item in line_items:
                processed_docs_path = Path("processed_docs") / str(line_item.pk) / str(process_document_version)
                processed_docs_info = s3.list_objects_v2(
                    Bucket=raw_document.s3_bucket, Prefix=str(processed_docs_path)
                ).get("Contents", [])
                etags = {x.get("ETag", "")[0:-1]: x.get("Key", "") for x in processed_docs_info}
                existing_key = etags.get(raw_document.md5, None)
                content_type = extract_content_type(raw_document)
                file_extensions = [".pdf", ".xlsx", ".xls", ".doc", ".docx", ".zip", ".csv"]
                processed_doc_name = raw_document.name
                if all(ext not in raw_document.name.lower() for ext in file_extensions):
                    if content_type == "application/octet-stream":
                        # Hide processed docs that don't have a proper file type
                        is_visible = False
                    else:
                        extension = mimetypes.guess_extension(content_type)
                        if extension:
                            processed_doc_name = raw_document.name + extension
                        else:
                            # Hide processed docs that don't have a proper file type
                            is_visible = False
                if existing_key is None:
                    s3_key = str(processed_docs_path / str(raw_document.id))
                    s3_bucket = raw_document.s3_bucket
                    s3.copy(
                        {
                            "Bucket": raw_document.s3_bucket,
                            "Key": raw_document.s3_key,
                        },
                        s3_bucket,
                        s3_key,
                    )
                else:
                    s3_key = existing_key
                    s3_bucket = raw_document.s3_bucket
                processed_document = ProcessedDocument.objects.create(
                    user=raw_document.created_by,
                    md5=raw_document.md5,
                    line_item=line_item,
                    raw_retreival_document=raw_document,
                    name=processed_doc_name,
                    document_type=document_type,
                    sub_document_type=sub_document_type,
                    posted_date=posted_date,
                    effective_date=effective_date,
                    content_type=content_type,
                    process_document_version=process_document_version,
                    process_document_source=process_document_source,
                    has_been_viewed=has_been_viewed,
                    is_visible=is_visible,
                    is_ground_truth=is_ground_truth,
                    is_correct=is_ground_truth,
                    labeled_by=labeled_by,
                    s3_key=s3_key,
                    s3_bucket=s3_bucket,
                    exists_in_s3=True,
                    document_summary=document_summary,
                    is_document_summary_approved=is_document_summary_approved,
                )
                if document_type == DocumentType.CAPITAL_CALL and capital_call is not None:
                    capital_call_fact = CapitalCallDocumentFact.objects.create(
                        user=processed_document.created_by,
                        md5=processed_document.md5,
                        line_item=line_item,
                        **capital_call,
                    )
                    processed_document.capital_call_document = capital_call_fact
                    processed_document.has_extracted_numbers = True

                if document_type == DocumentType.DISTRIBUTION_NOTICE and distribution_notice is not None:
                    distribution_notice_fact = DistributionNoticeDocumentFact.objects.create(
                        user=processed_document.created_by,
                        md5=processed_document.md5,
                        line_item=line_item,
                        **distribution_notice,
                    )
                    processed_document.distribution_notice_document = distribution_notice_fact
                    processed_document.has_extracted_numbers = True

                if document_type == DocumentType.ACCOUNT_STATEMENT and investment_update is not None:
                    investment_update_fact = InvestmentUpdateDocumentFact.objects.create(
                        user=processed_document.created_by,
                        md5=processed_document.md5,
                        line_item=line_item,
                        **investment_update,
                    )
                    processed_document.investment_update_document = investment_update_fact
                    processed_document.has_extracted_numbers = True
                line_item.save()
                processed_document.save()
                res.append(processed_document)

            # mark visiblity correctly for displaying
            processed_documents = raw_document.processed_documents.order_by("-created_at").all()
            # TODO: Delete old line items that are not in the new list or pass as a param
            li_grouped: dict[str, list[ProcessedDocument]] = {}
            for pd in processed_documents:
                li_pk = str(pd.line_item.pk)
                if li_pk not in li_grouped:
                    li_grouped[li_pk] = []
                li_grouped[li_pk].append(pd)
                pd.save()

            visible_docs: list[ProcessedDocument] = []
            non_visible_docs: list[ProcessedDocument] = []

            line_item_pks = [str(li.pk) for li in line_items]
            hide_line_items_pks = [str(li.pk) for li in hide_line_items] if hide_line_items is not None else []
            for li_pk, pds in li_grouped.items():
                if (hide_all_other_line_items and li_pk not in line_item_pks) or li_pk in hide_line_items_pks:
                    logger.debug(
                        "Marking all as non visible",
                        line_item_pk=li_pk,
                        line_item_pks=line_item_pks,
                        pds=pds,
                        hide_all_other_line_items=hide_all_other_line_items,
                        hide_line_items_pks=hide_line_items_pks,
                    )
                    non_visible_docs.extend(pds)
                else:
                    visible = None
                    for pd in pds:
                        # Mark the first ground truth as visible.
                        # We do this so we can predict documents even after they have ground truth.
                        # Currently, this means we would hide the new prediction. Ideally, we should
                        # add previous ground truths from the same document to the context window.
                        # or have some sort of "merge" functinon.
                        # TL;DR: likely we should remove this code path, keeping it for "safety"
                        # "safety" defined as not showing users predicted stuff after a GT was done.
                        if pd.is_ground_truth and pd.is_visible and visible is None:
                            visible = pd
                            break
                    # otherwised mark the last created processed document that is visible as visible.
                    # recall, the main purpose is mostly marking other docs as not visible.
                    if visible is None:
                        for pd in pds:
                            if pd.is_visible:
                                visible = pd
                                break
                    # This is the case where we have no visible documents for a line item, but we had
                    # previously predicted values. This could come from a wrong prediction of a line item
                    # as an example.
                    if visible is not None:
                        visible_docs.append(visible)
                        pds.remove(visible)
                    non_visible_docs.extend(pds)

            logger.debug("Visibility grouping", visible_docs=visible_docs, non_visible_docs=non_visible_docs)
            for pd in visible_docs:
                pd.is_visible = True
                if is_ground_truth:
                    pd.is_wrong = False
                    pd.is_correct = True
                pd.save()

            for other_pd in non_visible_docs:
                other_pd.is_visible = False
                if any(pd.is_same(other_pd) for pd in visible_docs):
                    other_pd.is_wrong = False
                    other_pd.is_correct = True
                other_pd.save()
        raw_document.sync_state_with_processed_doc()
        return res

    def is_same(self, other_pd: "ProcessedDocument") -> bool:
        # TODO: need to add more checks for validating correctness
        return (
            self.effective_date == other_pd.effective_date
            and self.posted_date == other_pd.posted_date
            and self.line_item == other_pd.line_item
            and self.document_type == other_pd.document_type
            and self.is_same_investment_document(other_pd)
            and self.is_same_capital_call_document(other_pd)
            and self.is_same_disibution_notice_document(other_pd)
        )

    def is_same_investment_document(self, other_pd: "ProcessedDocument") -> bool:
        iudf1 = self.investment_update_document
        iudf2 = other_pd.investment_update_document

        if (iudf1 is None) and (iudf2 is None):
            return True
        if (iudf1 is None) or (iudf2 is None):
            return False
        return (
            iudf1.invested == iudf2.invested
            and iudf1.total_value == iudf2.total_value
            and iudf1.unfunded == iudf2.unfunded
            and iudf1.committed_capital == iudf2.committed_capital
            and iudf1.realized_value == iudf2.realized_value
            and iudf1.realized_value == iudf2.realized_value
            and iudf1.currency == iudf2.currency
        )

    def is_same_capital_call_document(self, other_pd: "ProcessedDocument") -> bool:
        ccdf1 = self.capital_call_document
        ccdf2 = other_pd.capital_call_document
        if (ccdf1 is None) and (ccdf2 is None):
            return True
        if (ccdf1 is None) or (ccdf2 is None):
            return False
        return ccdf1.capital_call_due_date == ccdf2.capital_call_due_date and ccdf1.amount == ccdf2.amount

    def is_same_disibution_notice_document(self, other_pd: "ProcessedDocument") -> bool:
        dndf1 = self.distribution_notice_document
        dndf2 = other_pd.distribution_notice_document
        if (dndf1 is None) and (dndf2 is None):
            return True
        if (dndf1 is None) or (dndf2 is None):
            return False
        return dndf1.amount == dndf2.amount


@receiver(post_delete, sender=ProcessedDocument)
def sync_on_delete_processed_document(sender: models.Model, instance: ProcessedDocument, using: str, **kwargs) -> None:  # noqa: ARG001, ANN003
    try:
        if instance.raw_retreival_document is not None:
            instance.raw_retreival_document.sync_state_with_processed_doc()
    except RawDocument.DoesNotExist:
        return


def extract_content_type(raw_doc: RawDocument) -> str:
    import mimetypes

    import filetype

    mime_type, _ = mimetypes.guess_type(raw_doc.name)
    if mime_type:
        return mime_type
    file_bytes = raw_doc.get_doc_bytes()
    kind = filetype.guess(file_bytes)
    if kind:
        return kind.mime
    return "application/octet-stream"


class CapitalCallStatus(models.TextChoices):
    PENDING = "pending", _("Pending")
    EXECUTED = "executed", _("Executed")


class CapitalCallDocumentFact(AbstractBaseModel):
    line_item = models.ForeignKey(
        "LineItem",
        on_delete=models.CASCADE,
        related_name="capital_call_documents",
    )
    processed_document: ProcessedDocument
    md5 = models.CharField(max_length=255)
    capital_call_due_date = models.DateField()
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    status = models.CharField(max_length=20, choices=CapitalCallStatus.choices, default=CapitalCallStatus.PENDING)

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_item"]

    def __str__(self) -> str:
        if hasattr(self, "processed_document"):
            return f"Calital Call Document for {self.processed_document} - {self.line_item}"
        return "Capital Call Document NO PROCESSED DOC"


class DistributionNoticeDocumentFact(AbstractBaseModel):
    line_item = models.ForeignKey(
        "LineItem",
        on_delete=models.CASCADE,
        related_name="distribution_notice_documents",
    )
    processed_document: ProcessedDocument
    md5 = models.CharField(max_length=255)
    amount = models.DecimalField(max_digits=15, decimal_places=2)

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_item"]

    def __str__(self) -> str:
        if hasattr(self, "processed_document"):
            return f"Distribution Notice Document for {self.processed_document} - {self.line_item}"
        return "Distribution Notice Document NO PROCESSED DOC"


class CurrencyType(models.TextChoices):
    USD = "USD", _("US Dollar")
    AUD = "AUD", _("Australian Dollar")
    EUR = "EUR", _("Euro")
    NZD = "NZD", _("New Zealand Dollar")
    GBP = "GBP", _("British Pound")
    BRL = "BRL", _("Brazilian Real")
    CAD = "CAD", _("Canadian Dollar")
    CNY = "CNY", _("Chinese Yuan")
    DKK = "DKK", _("Danish Krone")
    HKD = "HKD", _("Hong Kong Dollar")
    INR = "INR", _("Indian Rupee")
    JPY = "JPY", _("Japanese Yen")
    MYR = "MYR", _("Malaysian Ringgit")
    MXN = "MXN", _("Mexican Peso")
    NOK = "NOK", _("Norwegian Krone")
    ZAR = "ZAR", _("South African Rand")
    SGD = "SGD", _("Singapore Dollar")
    KRW = "KRW", _("South Korean Won")
    LKR = "LKR", _("Sri Lankan Rupee")
    SEK = "SEK", _("Swedish Krona")
    CHF = "CHF", _("Swiss Franc")
    TWD = "TWD", _("Taiwan Dollar")
    THB = "THB", _("Thai Baht")
    VEB = "VEB", _("Venezuelan Bolivar")


class CurrencyRates:
    def __init__(self) -> None:
        self.rates: dict[str, dict[datetime.date, Decimal]] = self.load_rates()
        self.start_date = datetime.date(2000, 1, 1)
        self.end_date = datetime.date(2025, 6, 13)

    def load_rates(self) -> dict[str, dict[datetime.date, Decimal]]:
        exchange_rates: dict[str, dict[datetime.date, Decimal]] = {}
        csv_path = Path(settings.BASE_DIR) / "webapp/static/data" / "frb-currency-rates-20000101-20250613.csv"
        with csv_path.open(mode="r") as f:
            for _ in range(3):
                next(f)

            origin_countries = next(f).strip().split(",")
            origin_countries = [country.strip("'\" ") for country in origin_countries[1:]]

            for _ in range(2):
                next(f)

            reader = csv.reader(f)

            for country in origin_countries:
                exchange_rates[country] = {}

            for row in reader:
                date_str = row[0]
                date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()  # noqa: DTZ007

                for country, rate in zip(origin_countries, row[1:], strict=False):
                    # Do not include ND values
                    if rate != "ND":
                        exchange_rates[country][date] = Decimal(rate)

        return exchange_rates

    def get_rate(self, currency: CurrencyType | str, date: datetime.date) -> Decimal | None:
        if currency not in self.rates:
            return None

        if date < self.start_date or date > self.end_date:
            return None

        # Get the closest previous date's rate
        if date not in self.rates[currency]:
            previous_date = date - datetime.timedelta(days=1)
            while previous_date >= self.start_date and previous_date not in self.rates[currency]:
                previous_date = previous_date - datetime.timedelta(days=1)

            return self.rates[currency][previous_date] if previous_date >= self.start_date else None

        return self.rates[currency][date]


# TODO: Should all get migrated onto a model if we automate current rate retrieval.
currency_rates = CurrencyRates()


def convert_currency_to_usd(
    amount: Decimal | None, currency: CurrencyType | str, date: datetime.date
) -> Decimal | None:
    if currency == CurrencyType.USD:
        return amount
    rate = currency_rates.get_rate(currency, date)
    if not rate or amount is None:
        return None
    # i.e. 100 USD * (1 / 1.4 USD/CAD) = ~71 CAD, rates are in USD/currency
    res = (amount * (Decimal("1.0") / rate)).quantize(Decimal("0.01"))
    logger.debug("currency convert", amount=amount, date=date, rate=rate, res=res, currency=currency)
    return res


def get_current_rate(currency: CurrencyType | str, date: datetime.date) -> Decimal | None:
    if currency == CurrencyType.USD:
        return Decimal(1)
    return currency_rates.get_rate(currency, date)


class InvestmentUpdateDocumentFact(AbstractBaseModel):
    # NOTE: this is legacy naming, this should be "AccountStatementDocumentFact"
    line_item = models.ForeignKey(
        "LineItem",
        on_delete=models.CASCADE,
        related_name="investment_update_documents",
    )
    processed_document: ProcessedDocument
    md5 = models.CharField(max_length=255)
    invested = models.DecimalField(max_digits=15, decimal_places=2)
    total_value = models.DecimalField(max_digits=15, decimal_places=2)
    unfunded = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    # These fields are marked as optional currently, but only for migration purposes.
    # Need to have a generic way of adding new fields and marking them as required.
    # Possibly introducting the concept of an "invalid" fact:
    # - A fact that was created previously but is now missing required fields
    committed_capital = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    realized_value = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    # This is a true optional field
    unrealized_value = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    currency = models.CharField(max_length=3, choices=CurrencyType.choices, default=CurrencyType.USD)

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_item"]

    def __str__(self) -> str:
        if hasattr(self, "processed_document"):
            return f"Investment Update Document for {self.processed_document} - {self.line_item}"
        return "Investment Update Document NO PROCESSED DOC"

    @property
    def invested_usd(self) -> Decimal | None:
        if self.currency == CurrencyType.USD:
            return self.invested
        return convert_currency_to_usd(self.invested, self.currency, self.processed_document.effective_date)

    @property
    def total_value_usd(self) -> Decimal | None:
        if self.currency == CurrencyType.USD:
            return self.total_value
        return convert_currency_to_usd(self.total_value, self.currency, self.processed_document.effective_date)

    @property
    def unfunded_value_usd(self) -> Decimal | None:
        if self.currency == CurrencyType.USD:
            return self.unfunded
        return convert_currency_to_usd(self.unfunded, self.currency, self.processed_document.effective_date)

    @property
    def committed_capital_usd(self) -> Decimal | None:
        if self.currency == CurrencyType.USD:
            return self.committed_capital
        return convert_currency_to_usd(self.committed_capital, self.currency, self.processed_document.effective_date)

    @property
    def realized_value_usd(self) -> Decimal | None:
        if self.currency == CurrencyType.USD:
            return self.realized_value
        return convert_currency_to_usd(self.realized_value, self.currency, self.processed_document.effective_date)

    @property
    def unrealized_value_usd(self) -> Decimal | None:
        if self.currency == CurrencyType.USD:
            return self.unrealized_value
        return convert_currency_to_usd(self.unrealized_value, self.currency, self.processed_document.effective_date)

    @property
    def daily_currency_rate_usd_to_currency(self) -> Decimal | None:
        rate = get_current_rate(self.currency, self.processed_document.effective_date)
        if rate is None:
            return None
        return (Decimal("1.0") / rate).quantize(Decimal("0.0001"))

    @property
    def daily_currency_rate_currency_to_usd(self) -> Decimal | None:
        rate = get_current_rate(self.currency, self.processed_document.effective_date)
        if rate is None:
            return None
        return (rate).quantize(Decimal("0.0001"))
