{% load static widget_tweaks customtags %}
{% if user.is_manager and line_items|length == 0 %}
    {% alias "w-full min-h-32 pb-12 border-b border-gray-300" as container_class %}
{% else %}
    {% alias "hidden w-full min-h-32 pb-12 border-b border-gray-300" as container_class %}
{% endif %}
<div id="upload-container" class="{{ container_class }}">
    <div class="columns-1 gap-0 justify-evenly font-medium px-9">
        <div onclick="document.getElementById('id_xslx_file').click()"
             class="w-full h-48 pl-2 py-3 border rounded-md bg-white cursor-pointer">
            <!-- Loading Indicator -->
            <div id="loading-spinner"
                 class="hidden flex flex-col justify-center items-center h-full">
                {% comment %} style="width: 300px; height: 300px" {% endcomment %}
                <lottie-player src="{% static 'assets/images/bridge_green_txt_loading_animation.json' %}" class="w-36 h-36" background="transparent" speed="1" loop autoplay></lottie-player>
                {% comment %} <div class="loader border-t-4 border-blue-500 rounded-full w-8 h-8 animate-spin"></div> {% endcomment %}
                <span class="ml-2">Uploading...</span>
            </div>
            {% url 'bulk_upload' as bulk_upload_url %}
            {% with "on change remove .hidden from #loading-spinner then add .hidden to #upload-content then wait 2s then add .hidden to #upload-container" as hs %}
                {% render_field form.xslx_file class+="hidden" hx-trigger="change" hx-encoding="multipart/form-data" hx-post=bulk_upload_url _=hs %}
            {% endwith %}
            <!-- Content shown when not loading -->
            <div id="upload-content"
                 class="flex flex-col items-center justify-center pt-7 w-full">
                <img src="{% static 'assets/images/upload_btn.svg' %}"
                     class="h-11"
                     height="44px"
                     width="44px"
                     alt="Upload button" />
                <div class="TextAndSupportingText h-[52px] flex-col justify-start items-center gap-3 inline-flex">
                    <div class="Action self-stretch justify-center items-start gap-1 inline-flex font-medium text-[#414141] text-2xl font-inter">
                        <div class="Button justify-start items-start flex">
                            <div class="ButtonBase justify-center items-center gap-2 flex">
                                <div onclick="document.getElementById('id_xslx_file').click()"
                                     class="Text  leading-tight">Click to upload</div>
                            </div>
                        </div>
                        <div class="Text leading-tight">or drag and drop</div>
                    </div>
                    <div class="SupportingText self-stretch text-center font-medium text-[#414141] text-2xl font-inter leading-tight">
                        Completed Investment Template
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% comment %} TODO: have a generic spot for messages to notify the client of success / failure {% endcomment %}
