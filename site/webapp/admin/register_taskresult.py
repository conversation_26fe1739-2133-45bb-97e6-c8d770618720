import structlog
from bridge_project import celery_app
from django.contrib import admin, messages
from django.utils.safestring import mark_safe
from django_celery_results.admin import TaskResultAdmin
from django_celery_results.models import TaskResult

logger = structlog.get_logger(__name__)


# TODO: figure out type annotations for this one
def retry_task(modeladmin, request, queryset):  # noqa: ANN001,ARG001, ANN201
    msg = ""
    for task_res in queryset:
        if task_res.status != "FAILURE":
            msg += f'{task_res.task_id} => Skipped. Not in "FAILURE" State<br>'
            continue
        try:
            meta = celery_app.backend.get_task_meta(task_res.task_id)
            logger.info(meta)
            task = celery_app.tasks[meta["task_name"]]
            # HACK: Celery for None args and kwargs doesn't serialize
            if meta["args"] == "()":
                meta["args"] = ()
            if meta["kwargs"] == "{}":
                meta["kwargs"] = {}
            task.apply_async(args=meta["args"], kwargs=meta["kwargs"])
            msg += f"{task_res.task_id} => Successfully sent to queue for retry.<br>"
        except Exception as ex:  # noqa: BLE001
            msg += f"{task_res.task_id} => Unable to process. Error: {ex}<br>"
    messages.error(request, mark_safe(msg))  # noqa: S308 # nosec


retry_task.short_description = "Retry Task"  # type: ignore[attr-defined]


class CustomTaskResultAdmin(TaskResultAdmin):
    actions = [retry_task]  # noqa: RUF012


admin.site.unregister(TaskResult)
admin.site.register(TaskResult, CustomTaskResultAdmin)
