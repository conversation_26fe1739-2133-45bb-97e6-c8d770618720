{% load customtags static %}
{% for top_obj in table_data %}
    <tr>
        <td data-value="parent" class="pr-4 py-3 font-bold text-[#343D36]">
            <div class="flex items-center justify-between w-full">
                <a class="pl-[20px] min-w-[150px] whitespace-nowrap overflow-hidden text-ellipsis"
                   href="{% url 'vault' %}?{{ top_obj.type }}={{ top_obj.name | urlencode }}">{{ top_obj.name }}</a>
                <div class="flex items-center gap-3">
                    <!-- Toggle Button -->
                    <div _="on click toggle .border-b then trigger toggleRow(top_obj:'{{ top_obj.name|slugify }}', sub_obj:'__all__', is_closed:me.classList.contains('border-b'))"
                         class="cursor-pointer h-[22px] px-1.5 py-0.5 bg-white rounded-md shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border border-[#eceee8] flex justify-center items-center gap-1">
                        <img src="{% static 'assets/images/dropdown/arrow.svg' %}"
                             class="transform transition-transform rotate-0"
                             _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='__all__'] from elsewhere toggle .rotate-180 end"
                             width="16"
                             height="16"
                             alt="expand arrow">
                        <div class="text-center text-[#4b554d] text-xs font-medium font-['Inter'] leading-[18px]">{{ top_obj.length }}</div>
                    </div>
                    <!-- Add Note Button -->
                    <button class="flex items-right"
                            hx-get="{% url 'add_note' %}?name={{ top_obj.name|urlencode }}&type={{ top_obj.type }}&object_id={{ top_obj.object_id }}"
                            hx-target="body"
                            hx-swap="beforeend">
                        <div class="h-[22px] px-1.5 py-0.5 bg-white rounded-md shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border border-[#eceee8] flex justify-center items-center gap-1">
                            <img src="{% static 'assets/images/pen.svg' %}"
                                 width="16"
                                 height="16"
                                 alt="edit">
                        </div>
                    </button>
                </div>
            </div>
        </td>
        <td class="px-4 py-3  text-left text-sm font-semibold text-gray-600 whitespace-nowrap border">
            <span class="hidden "
                  _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='__all__'] from elsewhere toggle .hidden"
                  data-value=" {{ top_obj_sums|lookup:top_obj.name|lookup:"committed_capital_usd" }}  ">
                {{ top_obj_sums|lookup:top_obj.name|lookup:"committed_capital_usd"|format_as_currency }}
            </span>
        </td>
        <td class="px-4 py-3 text-left text-sm font-semibold text-gray-600 whitespace-nowrap border">
            <span class="hidden "
                  _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='__all__'] from elsewhere toggle .hidden"
                  data-value=" {{ top_obj_sums|lookup:top_obj.name|lookup:"invested_usd" }}">
                {{ top_obj_sums|lookup:top_obj.name|lookup:"invested_usd"|format_as_currency }}
            </span>
        </td>
        <td class="px-4 py-3 text-left text-sm font-semibold text-gray-600 whitespace-nowrap border">
            <span class="hidden "
                  _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='__all__'] from elsewhere toggle .hidden"
                  data-value=" {{ top_obj_sums|lookup:top_obj.name|lookup:"total_value_usd" }}">
                {{ top_obj_sums|lookup:top_obj.name|lookup:"total_value_usd"|format_as_currency }}
            </span>
        </td>
        <td class="px-4 py-3 text-left text-sm font-semibold text-gray-600 whitespace-nowrap border">
            <span class="hidden "
                  _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='__all__'] from elsewhere toggle .hidden"
                  data-value=" {{ top_obj_sums|lookup:top_obj.name|lookup:"realized_value_usd" }}">
                {{ top_obj_sums|lookup:top_obj.name|lookup:"realized_value_usd"|format_as_currency }}
            </span>
        </td>
        <td class="px-4 py-3 text-left text-sm font-semibold text-gray-600 whitespace-nowrap border">
            <span class="hidden"
                  _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='__all__'] from elsewhere toggle .hidden"
                  data-value=" {{ top_obj_sums|lookup:top_obj.name|lookup:"unrealized_value_usd" }}">
                {{ top_obj_sums|lookup:top_obj.name|lookup:"unrealized_value_usd"|format_as_currency }}
            </span>
        </td>
        <td class="border" colspan="5"></td>
    </tr>
    {% for sub_obj in top_obj.values %}
        {% if sub_obj.is_hidden %}
            {% alias "hidden" as init_hidden_state %}
        {% else %}
            {% alias "" as init_hidden_state %}
        {% endif %}
        <tr class="bg-gray-100 border-t {{ init_hidden_state }}"
            _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}{{ init_hidden_state }}' and sub_obj=='__all__'] from elsewhere toggle .hidden">
            {% comment %} HACK: we append hidden to the state so this event listener never triggers when hidden to prevent showing invisible rows {% endcomment %}
            <td class="px-4 py-3 text-[#343D36]">
                <div class="flex items-center justify-between w-full">
                    <a class="pl-[10px] min-w-[150px] whitespace-nowrap overflow-hidden text-ellipsis"
                       href="{% url 'vault' %}?{{ top_obj.type }}={{ top_obj.name | urlencode }}&{{ sub_obj.type }}={{ sub_obj.name | urlencode }}">
                        {{ sub_obj.name }}
                    </a>
                    <div class="flex items-left gap-3">
                        <!-- Toggle Button -->
                        <div _="on click toggle .closed then trigger toggleRow(top_obj:'{{ top_obj.name|slugify }}', sub_obj:'{{ sub_obj.name|slugify }}')"
                             class="cursor-pointer h-[22px] px-1.5 py-0.5 bg-white rounded-md shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border border-[#eceee8] flex justify-center items-center gap-1">
                            <img src="{% static 'assets/images/dropdown/arrow.svg' %}"
                                 class="transition-transform transform rotate-0"
                                 _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='{{ sub_obj.name|slugify }}'] from elsewhere toggle .rotate-180"
                                 width="16"
                                 height="16"
                                 alt="expand arrow">
                            <div class="text-center text-[#4b554d] text-xs font-medium font-['Inter'] leading-[18px]">{{ sub_obj.length }}</div>
                        </div>
                        <!-- Add Note Button -->
                        <button class="flex items-right"
                                hx-get="{% url 'add_note' %}?name={{ sub_obj.name|urlencode }}&type={{ sub_obj.type }}&object_id={{ sub_obj.object_id }}"
                                hx-target="body"
                                hx-swap="beforeend">
                            <div class="h-[22px] px-1.5 py-0.5 bg-white rounded-md shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border border-[#eceee8] flex justify-center items-center gap-1">
                                <img src="{% static 'assets/images/pen.svg' %}"
                                     width="16"
                                     height="16"
                                     alt="edit">
                            </div>
                        </button>
                    </div>
                </div>
            </td>
            <td class="px-4 py-3  text-left text-sm text-gray-600 whitespace-nowrap border">
                <span data-value=" {{ sub_obj_sums|lookup:sub_obj.name|lookup:"committed_capital_usd" }}  "
                      _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='{{ sub_obj.name|slugify }}'] from elsewhere toggle .hidden"
                      class="hidden">
                    {{ sub_obj_sums|lookup:sub_obj.name|lookup:"committed_capital_usd"|format_as_currency }}
                </span>
            </td>
            <td class="px-4 py-3 text-left text-sm text-gray-600 whitespace-nowrap border">
                <span data-value=" {{ sub_obj_sums|lookup:sub_obj.name|lookup:"invested_usd" }} "
                      _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='{{ sub_obj.name|slugify }}'] from elsewhere toggle .hidden"
                      class="hidden">{{ sub_obj_sums|lookup:sub_obj.name|lookup:"invested_usd"|format_as_currency }}</span>
            </td>
            <td class="px-4 py-3 text-left text-sm text-gray-600 whitespace-nowrap border">
                <span data-value=" {{ sub_obj_sums|lookup:sub_obj.name|lookup:"total_value_usd" }} "
                      _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='{{ sub_obj.name|slugify }}'] from elsewhere toggle .hidden"
                      class="hidden">{{ sub_obj_sums|lookup:sub_obj.name|lookup:"total_value_usd"|format_as_currency }}</span>
            </td>
            <td class="px-4  py-3 text-left text-sm text-gray-600 whitespace-nowrap border">
                <span class="hidden"
                      _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='{{ sub_obj.name|slugify }}'] from elsewhere toggle .hidden"
                      data-value=" {{ sub_obj_sums|lookup:sub_obj.name|lookup:"realized_value_usd" }} ">
                    {{ sub_obj_sums|lookup:sub_obj.name|lookup:"realized_value_usd"|format_as_currency }}
                </span>
            </td>
            <td class="px-4 py-3 text-left text-sm font-semibold text-gray-600 whitespace-nowrap border">
                <span data-value=" {{ sub_obj_sums|lookup:sub_obj.name|lookup:"unrealized_value_usd" }} "
                      _="on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='{{ sub_obj.name|slugify }}'] from elsewhere toggle .hidden"
                      class="hidden">{{ sub_obj_sums|lookup:sub_obj.name|lookup:"unrealized_value_usd"|format_as_currency }}</span>
            </td>
            <td colspan="5" class="border-l"></td>
        </tr>
        {% for leaf_obj in sub_obj.values %}
            {% alias leaf_obj.value as investment %}
            {# djlint:off #}
            <tr class="border bg-white"
                _="
                on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='{{ sub_obj.name|slugify }}'] from elsewhere toggle .hidden toggle .sub_obj_hidden end
                on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='__all__' and is_closed] from elsewhere add .hidden end
                on toggleRow[top_obj=='{{ top_obj.name|slugify }}' and sub_obj=='__all__' and not is_closed and not me.classList.contains('sub_obj_hidden')] from elsewhere remove .hidden end
            ">
            {# djlint:on#}
            <td class="px-4 italic py-3  text-[#343D36] items-center justify-between align-middle ">
                <div class="flex items-center justify-between w-full">
                    <a class="pl-[20px] min-w-[150px] whitespace-nowrap overflow-hidden text-ellipsis"
                       href="{% url 'vault' %}?{{ top_obj.type }}={{ top_obj.name | urlencode }}&{{ sub_obj.type }}={{ sub_obj.name | urlencode }}&{{ leaf_obj.type }}={{ leaf_obj.name | urlencode }}">
                        {{ leaf_obj.name }}
                    </a>
                    <button class="w-1/8 flex items-center"
                            hx-get="{% url 'add_note' %}?name={{ leaf_obj.name|urlencode }}&type={{ leaf_obj.type }}&object_id={{ leaf_obj.object_id }}"
                            hx-target="body"
                            hx-swap="beforeend">
                        <div class="h-[22px] px-1.5 py-0.5 bg-white rounded-md shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border border-[#eceee8] flex justify-center items-center gap-1">
                            <img src="{% static 'assets/images/pen.svg' %}"
                                 width="16"
                                 height="16"
                                 alt="edit">
                        </div>
                    </button>
                </div>
            </td>
            <td class="editable-col px-4 py-3 italic text-left text-sm text-gray-600 whitespace-nowrap border-l">
                <div class="flex items-center gap-1">
                    <span class="display_value">{{ investment.committed_capital_usd|format_as_currency }}</span>
                    {% if investment.currency != "USD" %}
                        <div class="group relative inline-block">
                            <div class="w-4 h-4 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold cursor-help hover:bg-gray-500">
                                i
                            </div>
                            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap pointer-events-none">
                                <div>Ref. doc: {{ investment.committed_capital|format_as_currency:investment.currency }}</div>
                                <div>
                                    Rate: {{ investment.daily_currency_rate_usd_to_currency }} USD to {{ investment.currency }} ({{ investment.account_statements_processed_doc.effective_date }})
                                </div>
                                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900">
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <input type="text"
                    {% if investment.account_statements_processed_doc %}
                        {# djlint:off #}
                        value="{{ investment.committed_capital }}"
                        name="{{ investment.account_statements_processed_doc.pk }}|committed_capital"
                        data-original="{{ investment.committed_capital }}" 
                        data-currency="{{ investment.currency }}"
                        class="currency_input edit_value hidden border px-1 py-0.5 w-full text-sm text-gray-800"
                    {% else %}
                        disabled
                        class="edit_value hidden border px-1 py-0.5 w-full text-sm text-gray-800 bg-gray-200"
                        {# djlint:on #}
                    {% endif %}
                    >
                </input>
            </td>
            <td class="editable-col px-4 italic py-3 text-left text-sm text-gray-600 border-l">
                <div class="flex items-center gap-1">
                    <span class="display_value">{{ investment.invested_usd|format_as_currency }}</span>
                    {% if investment.currency != "USD" %}
                        <div class="group relative inline-block">
                            <div class="w-4 h-4 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold cursor-help hover:bg-gray-500">
                                i
                            </div>
                            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap pointer-events-none">
                                <div>Ref. doc: {{ investment.invested|format_as_currency:investment.currency }}</div>
                                <div>
                                    Rate: {{ investment.daily_currency_rate_usd_to_currency }} USD to {{ investment.currency }} ({{ investment.account_statements_processed_doc.effective_date }})
                                </div>
                                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900">
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <input type="text"
                    {% if investment.account_statements_processed_doc %}
                        {# djlint:off #}
                        value="{{ investment.invested }}"
                        name="{{ investment.account_statements_processed_doc.pk }}|invested"
                        data-original="{{ investment.invested }}" 
                        data-currency="{{ investment.currency }}"
                        class="currency_input edit_value hidden border px-1 py-0.5 w-full text-sm text-gray-800"
                    {% else %}
                        disabled
                        class="edit_value hidden border px-1 py-0.5 w-full text-sm text-gray-800 bg-gray-200"
                        {# djlint:on #}
                    {% endif %}
                    >
                </input>
            </td>
            <td class="editable-col px-4 italic py-3 text-left text-sm text-gray-600 border-l">
                <div class="flex items-center gap-1">
                    <span class="display_value">{{ investment.total_value_usd|format_as_currency }}</span>
                    {% if investment.currency != "USD" %}
                        <div class="group relative inline-block">
                            <div class="w-4 h-4 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold cursor-help hover:bg-gray-500">
                                i
                            </div>
                            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap pointer-events-none">
                                <div>Ref. doc: {{ investment.total_value|format_as_currency:investment.currency }}</div>
                                <div>
                                    Rate: {{ investment.daily_currency_rate_usd_to_currency }} USD to {{ investment.currency }} ({{ investment.account_statements_processed_doc.effective_date }})
                                </div>
                                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900">
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <input type="text"
                    {% if investment.account_statements_processed_doc %}
                        {# djlint:off #}
                        value="{{ investment.total_value }}"
                        name="{{ investment.account_statements_processed_doc.pk }}|total_value"
                        data-original="{{ investment.total_value }}" 
                        data-currency="{{ investment.currency }}"
                        class="currency_input edit_value hidden border px-1 py-0.5 w-full text-sm text-gray-800"
                    {% else %}
                        disabled
                        class="edit_value hidden border px-1 py-0.5 w-full text-sm text-gray-800 bg-gray-200"
                        {# djlint:on #}
                    {% endif %}
                    >
                </input>
            </td>
            <td class="editable-col px-4 italic py-3 text-left text-sm text-gray-600 border-l">
                <div class="flex items-center gap-1">
                    <span class="display_value">{{ investment.realized_value_usd|format_as_currency }}</span>
                    {% if investment.currency != "USD" %}
                        <div class="group relative inline-block">
                            <div class="w-4 h-4 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold cursor-help hover:bg-gray-500">
                                i
                            </div>
                            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap pointer-events-none">
                                <div>Ref. doc: {{ investment.realized_value|format_as_currency:investment.currency }}</div>
                                <div>
                                    Rate: {{ investment.daily_currency_rate_usd_to_currency }} USD to {{ investment.currency }} ({{ investment.account_statements_processed_doc.effective_date }})
                                </div>
                                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900">
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <input type="text"
                    {% if investment.account_statements_processed_doc %}
                        {# djlint:off #}
                        value="{{ investment.realized_value }}"
                        name="{{ investment.account_statements_processed_doc.pk }}|realized_value"
                        data-original="{{ investment.realized_value }}" 
                        data-currency="{{ investment.currency }}"
                        class="currency_input edit_value hidden border px-1 py-0.5 w-full text-sm text-gray-800"
                    {% else %}
                        disabled
                        class="edit_value hidden border px-1 py-0.5 w-full text-sm text-gray-800 bg-gray-200"
                        {# djlint:on #}
                    {% endif %}
                    >
                </input>
            </td>
            <td class="editable-col px-4 italic py-3 text-left text-sm text-gray-600 border-l">
                <div class="flex items-center gap-1">
                    <span class="display_value">{{ investment.unrealized_value_usd|format_as_currency }}</span>
                    {% if investment.currency != "USD" %}
                        <div class="group relative inline-block">
                            <div class="w-4 h-4 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold cursor-help hover:bg-gray-500">
                                i
                            </div>
                            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap pointer-events-none">
                                <div>Ref. doc: {{ investment.unrealized_value|format_as_currency:investment.currency }}</div>
                                <div>
                                    Rate: {{ investment.daily_currency_rate_usd_to_currency }} USD to {{ investment.currency }} ({{ investment.account_statements_processed_doc.effective_date }})
                                </div>
                                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900">
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <input type="text"
                    {% if investment.account_statements_processed_doc %}
                        {# djlint:off #}
                        value="{{ investment.unrealized_value }}"
                        name="{{ investment.account_statements_processed_doc.pk }}|unrealized_value"
                        data-original="{{ investment.unrealized_value }}" 
                        data-currency="{{ investment.currency }}"
                        class="currency_input edit_value hidden border px-1 py-0.5 w-full text-sm text-gray-800"
                    {% else %}
                        disabled
                        class="edit_value hidden border px-1 py-0.5 w-full text-sm text-gray-800 bg-gray-200"
                        {# djlint:on #}
                    {% endif %}
                    >
                </input>
            </td>
            <td class="px-4 py-3 text-center text-sm text-gray-600 border-l">
                <div class="items-center">
                    <span class="display_value">{{ investment.account_statements_processed_doc.effective_date|format_to_mdy }}</span>
                </div>
                <input type="text"
                    disabled
                    value="{{ investment.account_statements_processed_doc.effective_date|format_to_mdy }}"
                    class="edit_value hidden border items-center px-1 py-0.5 w-full text-sm text-gray-800 bg-gray-200"
                    {# djlint:on #}
                    >
                </input>
            </td>
            <td class="px-4 py-3 text-left text-sm font-semibold text-gray-600 whitespace-nowrap border-l">
                {% include "dashboard/investment_details/pdf_link.html" with title=investment.capital_calls_display has_been_viewed=investment.capital_calls_has_been_viewed processed_doc_pk=investment.capital_calls_processed_doc.pk %}
            </td>
            <td class="px-4 py-3 text-left text-sm font-semibold text-gray-600 whitespace-nowrap">
                {% include "dashboard/investment_details/pdf_link.html" with title=investment.distribution_notices_display has_been_viewed=investment.distribution_notices_has_been_viewed processed_doc_pk=investment.distribution_notices_processed_doc.pk %}
            </td>
            <td class="px-4 py-3 text-left text-sm font-semibold text-gray-600">
                {% include "dashboard/investment_details/pdf_link.html" with title=investment.account_statements_display has_been_viewed=investment.account_statements_has_been_viewed processed_doc_pk=investment.account_statements_processed_doc.pk %}
            </td>
            <td class="px-4 py-3 text-left text-sm font-semibold text-gray-600">
                {% include "dashboard/investment_details/pdf_link.html" with title=investment.investment_updates_documents_display has_been_viewed=investment.investment_updates_has_been_viewed processed_doc_pk=investment.investment_updates_processed_doc.pk %}
            </td>
        </tr>
    {% endfor %}
{% endfor %}
{% endfor %}
