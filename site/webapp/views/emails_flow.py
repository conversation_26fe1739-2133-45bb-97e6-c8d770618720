import json
from pathlib import Path
from typing import Any

import msal
import requests
import structlog
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from retrieval.tasks.universal_login_task import universal_login_task

from webapp.forms import EmailMPCForm
from webapp.models.emails import (
    BridgeEmail,
    CustomerEmailCredential,
    EmailRetrieval,
    UserForwardingRule,
)
from webapp.models.investment_details import Client, InvestingEntity, Investment
from webapp.models.line_item import LineItem
from webapp.models.portal import MergedPortalCredential, Portal
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser
from webapp.services.emails import (
    add_or_update_rule,
    create_or_update_credential_and_forwarding_rules_msft,
    make_microsoft_session_with_customer_email_credential,
)
from webapp.views.utils import (
    add_hx_trigger_header,
    get_line_item_from_url_params,
    get_merged_portal_credential_from_url_params,
    redirect_with_url_params,
)

CLIENT_ID = settings.OAUTH_SECRET.get("CLIENT_ID", None)
CLIENT_SECRET = settings.OAUTH_SECRET.get("CLIENT_SECRET", None)
AUTHORITY = "https://login.microsoftonline.com/common"  # Use to control the account that the user authenticates with
logger = structlog.get_logger(__name__)


def make_msft_request(url: str, access_token: str, method: str = "post", json: dict | None = None) -> dict:
    # Make the API request to create the message rule
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    response = requests.request(method=method, url=url, headers=headers, json=json, timeout=10)
    return response.json()


def get_msft_email(access_token: str) -> dict:
    # Make the API request to create the message rule
    return make_msft_request("https://graph.microsoft.com/v1.0/me", access_token, method="get")


def generate_msft_oauth_link(
    request: HttpRequest, receiving_email: CustomerEmailCredential, mpc: MergedPortalCredential
) -> str:
    if "ms_flow" in request.session and request.session["ms_flow_mpc_pk"] == str(mpc.pk):
        ms_flow: dict[str, str] | None = request.session.get("ms_flow")
        if ms_flow is None:
            raise ValueError
        auth_uri = ms_flow.get("auth_uri")
        if auth_uri is None:
            raise ValueError
        return auth_uri
    request.session["ms_flow_mpc_pk"] = str(mpc.pk)
    app = msal.PublicClientApplication(
        client_id=CLIENT_ID,
        authority=AUTHORITY,
    )

    flow = app.initiate_auth_code_flow(
        scopes=["MailboxSettings.ReadWrite", "User.Read"],
        login_hint=receiving_email.email,
    )
    request.session["ms_flow"] = flow
    return flow["auth_uri"]


def msft_identity_association(request: HttpRequest) -> HttpResponse:  # noqa: ARG001
    return HttpResponse(
        json.dumps(
            {
                "associatedApplications": [
                    {
                        "applicationId": CLIENT_ID,
                    },
                ],
            },
        ),
        content_type="application/json",
    )


@csrf_exempt
def msft_redirect_uri(request: HttpRequest) -> HttpResponse:  # noqa: C901
    if "admin_consent" in request.GET:
        tenant_id = request.GET.get("tenant", None)
        error = request.GET.get("error", None)
        logger.info("Admin consent callback", get=request.GET)
        if error:
            logger.error("Admin consent error", get=request.GET)
            return HttpResponse(
                f"Admin consent failed: {request.GET.get('error_description', 'Unknown error')}", status=400
            )

        if tenant_id:
            logger.info("Admin consent successful", tenant_id=tenant_id)
            # Store tenant-level approval or inform the user/admin as needed
            return HttpResponse("Admin consent granted successfully. Users can now use the app.", status=200)

        logger.error("Admin consent callback missing tenant ID")
        return HttpResponse("Admin consent failed: Missing tenant ID.", status=400)

    ms_flow = request.session.get("ms_flow", None)
    if ms_flow is not None:
        del request.session["ms_flow"]
    mpc_pk = request.session.get("ms_flow_mpc_pk", None)
    if mpc_pk is not None:
        del request.session["ms_flow_mpc_pk"]

    if mpc_pk is None or ms_flow is None:
        logger.error("Session not set correctly", mpc_pk=mpc_pk, ms_flow=ms_flow, get=request.GET)
        return HttpResponse("Failed to get session data", status=400)

    mpc = MergedPortalCredential.objects.for_user(request.user).get(id=mpc_pk)  # type: ignore[attr-defined]
    is_email_based = mpc.portal.portal_type == Portal.PortalType.EMAIL_BASED
    receiving_email = (
        mpc.user_forwarding_rule.receiving_email if is_email_based else mpc.multi_factor_authentication.receiving_email
    )
    logger.info("Microsoft callback data", ms_flow=ms_flow)

    cache = msal.SerializableTokenCache()
    app = msal.ConfidentialClientApplication(
        client_id=CLIENT_ID,
        client_credential=CLIENT_SECRET,
        authority=AUTHORITY,
        token_cache=cache,
    )
    result = app.acquire_token_by_auth_code_flow(
        auth_code_flow=ms_flow,
        auth_response=request.GET,
    )
    cache_json = cache.serialize()
    access_token = result.get("access_token", None)

    if access_token is None:
        logger.error("Failed to get access token", result=result)
        return redirect_with_url_params(
            "link_dashboard",
            hx_trigger=json.dumps(
                {
                    "popupEmailModalError": {
                        "merged_portal_credential_pk": str(mpc.pk),
                        # TODO: need to propogate error messages cleanly....
                        "error": "Failed to get access token",
                    },
                    "changeMergedPortalCredentialTable": None,
                }
            ),
        )

    email = get_msft_email(access_token)["mail"]

    if email != receiving_email.email:
        logger.warning("Email mismatch", email=email, receiving_email=receiving_email.email)
        # TODO: do we need to create this if the emails are not equal?
        # Doing it for now incase something goes wrong.
        create_or_update_credential_and_forwarding_rules_msft(receiving_email.email, cache_json, request.user)

    # Create or update
    create_or_update_credential_and_forwarding_rules_msft(email, cache_json, request.user)

    mpc.refresh_from_db()
    if mpc.is_complete:
        user = mpc.created_by
        if user is None:
            logger.error("No user found for merged portal credential", merged_portal_credential_pk=mpc.pk)
        retrieval = Retrieval.create(user, mpc, is_user_login_validation_retrieval=True)
        retrieval_id = str(retrieval.id)
        task = universal_login_task.apply_async(kwargs={"retrieval_id": retrieval_id})
        retrieval.task_id = task.id
        retrieval.save()

    return redirect_with_url_params(
        "link_dashboard",
        hx_trigger=json.dumps(
            {
                "popupEmailModalSuccess": {"merged_portal_credential_pk": str(mpc.pk)},
                "changeMergedPortalCredentialRow": {"merged_portal_credential_pk": str(mpc.pk)},
            }
        ),
    )


def update_or_create_mpc_with_cec_and_user_forwarding_rule(
    user: BridgeUser, email_provider: str, email: str, mpc: MergedPortalCredential | None = None
) -> MergedPortalCredential:
    with transaction.atomic():
        receiving_customer_email, _ = CustomerEmailCredential.objects.get_or_create(
            user=user,
            email=email,
            defaults={
                # NOTE: if CEC already exists, we are not updating the provider field.
                # It doesn't really make sense for a CEC to be the same email but the provider changes.
                # Honestly we should probably just parse out the provider from the email domain
                # instead of letting our users choose.
                "email_provider_raw": email_provider,
                "email_provider_customer_managed": CustomerEmailCredential.parse_email_provider_customer_managed(
                    email_provider
                ),
                "email_provider_bridge_managed": CustomerEmailCredential.parse_email_provider_bridge_managed(
                    email_provider
                ),
            },
        )
        bridge_email = BridgeEmail.get_organization_bridge_email_by_user(user)
        user_forwarding_rule, _ = UserForwardingRule.objects.get_or_create(
            user=user,
            receiving_email=receiving_customer_email,
            bridge_email=bridge_email,
        )
        if mpc is None:
            portal, _ = Portal.objects.update_or_create(
                user=user,
                name=user_forwarding_rule.receiving_email.email,
                defaults={
                    "portal_login_url": "",
                    "portal_type": Portal.PortalType.EMAIL_BASED,
                },
            )
            return MergedPortalCredential.create(
                user=user,
                portal=portal,
                user_forwarding_rule=user_forwarding_rule,
            )
        mpc.portal.name = user_forwarding_rule.receiving_email.email
        mpc.portal.save()
        mpc.user_forwarding_rule = user_forwarding_rule
        mpc.save()
        return mpc


@login_required
def add_sender_email_field(request: HttpRequest) -> HttpResponse:
    sender_emails_count = request.GET.get("sender_emails_count")
    logger.info("Add sender email field", sender_emails_count=sender_emails_count)
    return TemplateResponse(request, "link/emails/sender_email_input.html")


@login_required
def onboard_or_edit_email_inbox(
    request: HttpRequest,
    email_inbox_form: EmailMPCForm,
    template_context: dict[str, Any],
    exisiting_mpc: MergedPortalCredential | None = None,
) -> HttpResponse:
    template = "modal.html"
    modal_include_template = "link/emails/email_modal.html"
    form_include_template = "link/emails/email_inbox_form.html"
    if request.method == "GET":
        return TemplateResponse(
            request,
            template,
            {
                "merged_portal_credential_pk": template_context.get("merged_portal_credential_pk"),
                "email_providers": CustomerEmailCredential.EmailProvider,
                "modal_include_template": modal_include_template,
                "modal_header_text": template_context.get("modal_header_text"),
                "modal_description_text": template_context.get("modal_description_text"),
                "form_include_template": form_include_template,
                "form": email_inbox_form,
            },
        )
    if request.method == "POST":
        if not email_inbox_form.is_valid():
            return TemplateResponse(
                request,
                template,
                {
                    "merged_portal_credential_pk": template_context.get("merged_portal_credential_pk"),
                    "email_providers": CustomerEmailCredential.EmailProvider,
                    "modal_include_template": modal_include_template,
                    "modal_header_text": template_context.get("modal_header_text"),
                    "modal_description_text": template_context.get("modal_description_text"),
                    "form_include_template": form_include_template,
                    "form": email_inbox_form,
                },
            )
        receiver_email = email_inbox_form.cleaned_data["email"]
        receiver_email_provider = email_inbox_form.cleaned_data["email_provider"]
        logger.info("receiver_email", receiver_email=receiver_email, receiver_email_provider=receiver_email_provider)
        mpc = update_or_create_mpc_with_cec_and_user_forwarding_rule(
            user=request.user,
            email_provider=receiver_email_provider,
            email=receiver_email,
            mpc=exisiting_mpc,
        )
        # this checks for when user is editing to an email that is already bridge managed (i.e. same email is setup for an MFA)  # noqa: E501
        # if its a new CEC email_integration_status would be unknown
        # existing CEC could be customer managed, in which case we ask them to set up as a new email integration
        if (
            mpc.user_forwarding_rule.receiving_email.email_integration_status
            == CustomerEmailCredential.EmailIntegrationStatus.BRIDGE_MANAGED_FILTERS
        ):
            form_include_template = "link/emails/edit_email_integration.html"
            context = {
                "mpc": mpc,
                "modal_include_template": modal_include_template,
                "modal_sub_header_text": template_context.get("modal_sub_header_text"),
                "modal_header_icon": True,
                "modal_description_text": "This email integration is already setup. Would you like to update it?",
                "form_include_template": form_include_template,
                "receiving_email": mpc.user_forwarding_rule.receiving_email,
            }
            headers = add_hx_trigger_header({"changeMergedPortalCredentialTable": None})
            return TemplateResponse(request, template, context, headers=headers)
        description_text = (
            "Select your preferred method for setting up email integration with Bridge."
            if mpc.user_forwarding_rule.receiving_email.email_provider_bridge_managed != "ns"
            else "We only support a few providers right now. Please message support if you would like to see more."
        )
        form_include_template = "link/emails/begin_email_integration.html"
        context = {
            "mpc": mpc,
            "modal_include_template": modal_include_template,
            "modal_sub_header_text": template_context.get("modal_sub_header_text"),
            "modal_header_icon": True,
            "modal_description_text": description_text,
            "form_include_template": form_include_template,
            "receiving_email": mpc.user_forwarding_rule.receiving_email,
        }
        headers = add_hx_trigger_header({"changeMergedPortalCredentialTable": None})
        return TemplateResponse(request, template, context, headers=headers)
    return HttpResponse(status=405)


@login_required
def onboard_new_email_inbox(request: HttpRequest) -> HttpResponse:
    template_context = {
        "modal_header_text": "Onboard new email inbox",
        "modal_description_text": "Please enter the e-mail address that you want to onboard.",
        "modal_sub_header_text": "Begin Email Integration",
    }
    if request.method == "GET":
        onboard_email_form = EmailMPCForm()
    if request.method == "POST":
        onboard_email_form = EmailMPCForm(request.POST)
    return onboard_or_edit_email_inbox(request, onboard_email_form, template_context, None)


@login_required
def edit_setup(request: HttpRequest) -> HttpResponse:
    mpc = get_merged_portal_credential_from_url_params(request)
    initial_form = {
        "email": mpc.user_forwarding_rule.receiving_email.email,
        "email_provider": mpc.user_forwarding_rule.receiving_email.email_provider_raw,
    }
    template_context = {
        "modal_header_text": "Edit Existing Email Inbox",
        "modal_description_text": f"Current email: {mpc.user_forwarding_rule.receiving_email.email}",
        "modal_sub_header_text": "Edit Email Integration",
        "merged_portal_credential_pk": mpc.pk,
    }
    if request.method == "GET":
        onboard_email_form = EmailMPCForm(initial=initial_form)
    if request.method == "POST":
        onboard_email_form = EmailMPCForm(request.POST, initial=initial_form)
    return onboard_or_edit_email_inbox(request, onboard_email_form, template_context, mpc)


@login_required
def setup_integration(request: HttpRequest) -> HttpResponse:
    template = "modal.html"
    if request.method == "POST":
        mpc = get_merged_portal_credential_from_url_params(request)
        if mpc.user_forwarding_rule is None and mpc.multi_factor_authentication is None:
            raise ValueError
        receiving_email = (
            mpc.user_forwarding_rule.receiving_email
            if mpc.user_forwarding_rule
            else mpc.multi_factor_authentication.receiving_email
        )
        if request.POST.get("submit") == "automatic_setup":
            microsoft_auth_uri = generate_msft_oauth_link(request, receiving_email, mpc)
            logger.info("microsoft_auth_uri", microsoft_auth_uri=microsoft_auth_uri)
            modal_include_template = "link/emails/automatic_setup_modal.html"
            return TemplateResponse(
                request,
                template,
                {
                    "modal_include_template": modal_include_template,
                    "microsoft_auth_uri": microsoft_auth_uri,
                },
            )
        if request.POST.get("submit") == "manual_setup":
            CustomerEmailCredential.objects.update_or_create(
                user=request.user,
                email=receiving_email.email,
                defaults={"email_integration_status": CustomerEmailCredential.EmailIntegrationStatus.CUSTOMER_MANAGED},
            )
            modal_include_template = "link/emails/email_modal.html"
            form_include_template = "link/emails/manual_setup.html"
            bridge_email = BridgeEmail.get_organization_bridge_email_by_user(request.user)
            return TemplateResponse(
                request,
                template,
                {
                    "mpc": mpc,
                    "modal_include_template": modal_include_template,
                    "modal_sub_header_text": "Please forward relevant emails to",
                    "modal_header_icon": True,
                    "modal_description_text_highlighted": bridge_email.email,
                    "form_include_template": form_include_template,
                },
            )
        if request.POST.get("submit") == "use_exisiting":
            modal_include_template = "link/emails/email_modal.html"
            form_include_template = "link/emails/edit_integration_end.html"
            return TemplateResponse(
                request,
                template,
                {
                    "modal_include_template": modal_include_template,
                    "modal_sub_header_text": "Email setup updated to inbox",
                    "modal_header_icon": True,
                    "modal_description_text_highlighted": mpc.user_forwarding_rule.receiving_email.email,
                    "form_include_template": form_include_template,
                },
            )
    return HttpResponse(status=405)


@login_required
def email_integration_end(request: HttpRequest) -> HttpResponse:
    mpc_pk = request.GET.get("merged_portal_credential_pk", None)
    logger.info("email_integration_end", mpc_pk=mpc_pk)
    if mpc_pk:
        template = "modal.html"
        modal_include_template = "link/emails/email_modal.html"
        mpc = get_merged_portal_credential_from_url_params(request)
        is_email_based = mpc.portal.portal_type == Portal.PortalType.EMAIL_BASED
        if is_email_based and mpc.user_forwarding_rule.email_retrievals.exists():
            form_include_template = "link/emails/edit_integration_end.html"
            return TemplateResponse(
                request,
                template,
                {
                    "modal_include_template": modal_include_template,
                    "modal_sub_header_text": "Email setup updated to inbox",
                    "modal_header_icon": True,
                    "modal_description_text_highlighted": mpc.user_forwarding_rule.receiving_email.email,
                    "form_include_template": form_include_template,
                },
            )
        form_include_template = "link/emails/integration_complete.html"
        return TemplateResponse(
            request,
            template,
            {
                "mpc": mpc,
                "modal_include_template": modal_include_template,
                "modal_sub_header_text": "Email setup complete for",
                "modal_header_icon": True,
                "modal_description_text_highlighted": mpc.user_forwarding_rule.receiving_email.email
                if is_email_based
                else mpc.multi_factor_authentication.receiving_email.email,
                "form_include_template": form_include_template,
            },
        )
    template = "modal.html"
    modal_include_template = "link/emails/integration_error_modal.html"
    return TemplateResponse(
        request,
        template,
        {
            "modal_include_template": modal_include_template,
            "error_message": "Email integration unsuccessful. Please reach out to a member of our team for assistance.",
        },
    )


@login_required
def email_integration_failed(request: HttpRequest) -> HttpResponse:
    mpc_pk = request.GET.get("merged_portal_credential_pk", None)
    logger.info("email_integration_failed", mpc_pk=mpc_pk)
    template = "modal.html"
    modal_include_template = "link/emails/integration_error_modal.html"
    return TemplateResponse(
        request,
        template,
        {
            "modal_include_template": modal_include_template,
            "error_message": "Email integration unsuccessful. Please reach out to a member of our team for assistance.",
        },
    )


@login_required
def add_line_item(request: HttpRequest) -> HttpResponse:
    template = "modal.html"
    modal_include_template = "link/emails/email_modal.html"
    form_include_template = "link/emails/add_line_item.html"
    existing_sender_emails = [""]
    mpc = get_merged_portal_credential_from_url_params(request)
    logger.info("adding line item to mpc", mpc=mpc)
    headers = add_hx_trigger_header({"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": mpc.pk}})
    return TemplateResponse(
        request,
        template,
        {
            "mpc": mpc,
            "modal_include_template": modal_include_template,
            "modal_header_text": "Onboard new line item",
            "modal_description_text": 'Please enter, the "line-item" (client / entity / investment) that you receive e-mail notices about.',  # noqa: E501
            "form_include_template": form_include_template,
            "sender_emails": existing_sender_emails,
            "field_count": len(existing_sender_emails),
        },
        headers=headers,
    )


@login_required
def edit_line_item(request: HttpRequest) -> HttpResponse:
    template = "modal.html"
    modal_include_template = "link/emails/email_modal.html"
    mpc = get_merged_portal_credential_from_url_params(request)
    line_item = get_line_item_from_url_params(request)
    if mpc.portal.portal_type == Portal.PortalType.EMAIL_BASED:
        existing_sender_emails = line_item.email_retrieval.sender_emails or [""]
        return TemplateResponse(
            request,
            template,
            {
                "mpc": mpc,
                "line_item": line_item,
                "modal_include_template": modal_include_template,
                "modal_header_text": "Edit line item",
                "form_include_template": "link/emails/add_line_item.html",
                "sender_emails": existing_sender_emails,
                "field_count": len(existing_sender_emails),
            },
        )
    return TemplateResponse(
        request,
        template,
        {
            "mpc": mpc,
            "line_item": line_item,
            "modal_include_template": "link/line_items/edit_line_item.html",
        },
    )


@login_required
def confirm_email_modal(request: HttpRequest) -> HttpResponse:
    mpc = get_merged_portal_credential_from_url_params(request)
    logger.info("confirm_email_modal", mpc=mpc)
    if request.method == "POST":
        sender_emails_raw = request.POST.getlist("sender_emails")
        sender_emails = [email.strip() for email in sender_emails_raw if email.strip()]
        line_item_pk = request.GET.get("line_item_pk", None)
        logger.info("confirm_email_modal POST", mpc=mpc, sender_email=sender_emails, line_item_pk=line_item_pk)
        if line_item_pk is None:
            with transaction.atomic():
                # TODO: form validation on these fields?
                client_name = request.POST.get("client")
                entity_name = request.POST.get("investing_entity")
                investment_name = request.POST.get("investment")
                client, _ = Client.objects.get_or_create(user=request.user, legal_name=client_name)
                investing_entity, _ = InvestingEntity.objects.update_or_create(
                    user=request.user,
                    legal_name=entity_name,
                    defaults={
                        "client": client,
                    },
                )
                investment, _ = Investment.objects.update_or_create(
                    user=request.user, legal_name=investment_name, create_defaults={"managing_firm_name": ""}
                )
                line_item, _ = LineItem.objects.update_or_create(
                    user=request.user,
                    investing_entity=investing_entity,
                    investment=investment,
                    defaults={
                        "merged_portal_credential": mpc,
                    },
                )
        else:
            line_item = get_line_item_from_url_params(request)
            if (
                request.POST.get("submit") != "bulk_apply_sender_email" and line_item.investment.line_items.count() > 1
            ):  # show bulk apply sender email modal
                return bulk_apply_sender_email_modal(request, sender_emails)
        # update the new added line item or replace sender_emails for the selected edit line item
        with transaction.atomic():
            EmailRetrieval.objects.update_or_create(
                user=request.user,
                line_item=line_item,
                defaults={
                    "sender_emails": sender_emails,
                    "user_forwarding_rule": mpc.user_forwarding_rule,
                },
            )
        # bulk add to other line items instead of replace
        if request.POST.get("submit") == "bulk_apply_sender_email":
            line_items_pk_to_bulk_apply = []
            bulk_add_sender_emails = request.POST.getlist("sender_emails")
            for key, value in request.POST.items():
                if key.startswith("investment_line_item|"):
                    line_item_id = value
                    line_items_pk_to_bulk_apply.append(line_item_id)
            line_items_to_bulk_apply = LineItem.objects.filter(pk__in=line_items_pk_to_bulk_apply)
            logger.info("bulk_apply_sender_email", line_items_to_bulk_apply=line_items_to_bulk_apply)
            with transaction.atomic():
                for line_item_to_bulk_apply in line_items_to_bulk_apply:
                    email_retrieval = EmailRetrieval.objects.for_user(request.user).get(
                        line_item=line_item_to_bulk_apply,
                    )
                    logger.info(
                        "bulk_apply_sender_email individual", email_retrieval=email_retrieval, pk=email_retrieval.pk
                    )
                    existing_sender_emails = email_retrieval.sender_emails or []
                    email_retrieval.sender_emails = list(set(existing_sender_emails + bulk_add_sender_emails))
                    email_retrieval.user_forwarding_rule = mpc.user_forwarding_rule
                    email_retrieval.save()
        if (
            mpc.user_forwarding_rule.receiving_email.email_integration_status
            == CustomerEmailCredential.EmailIntegrationStatus.BRIDGE_MANAGED_FILTERS
        ):
            message_rule = mpc.user_forwarding_rule.update_forwarding_rule_with_email_retrievals_sender_emails()

            msft_session = make_microsoft_session_with_customer_email_credential(
                mpc.user_forwarding_rule.receiving_email
            )
            rule_id = add_or_update_rule(msft_session, message_rule)
            logger.info("forwarding rule created", rule_id=rule_id)

        template = "link/emails/final_step.html"
        return TemplateResponse(
            request,
            template,
            context={
                "line_item": line_item,
                "main_message": "Final step: Upload Historical Documents"
                if line_item_pk is None
                else "Line Item Updated",
                "sub_message": "You can upload historical documents received by either manually bulk uploading or bulk forwarding.",  # noqa: E501
            },
            headers=add_hx_trigger_header({"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": mpc.pk}}),
        )
    return HttpResponse(status=405)  # Method not allowed


def bulk_apply_sender_email_modal(request: HttpRequest, sender_emails: list) -> HttpResponse:
    mpc = get_merged_portal_credential_from_url_params(request)
    line_item = get_line_item_from_url_params(request)
    logger.info("bulk_apply_sender_email", mpc=mpc, line_item=line_item)
    template = "link/emails/email_modal.html"
    form_include_template = "link/emails/bulk_apply_sender_email.html"
    investment_line_items = line_item.investment.line_items.all().exclude(pk=line_item.pk)
    return TemplateResponse(
        request,
        template,
        {
            "mpc": mpc,
            "line_item": line_item,
            "modal_header_text": "Edit line item",
            "modal_description_text": "Do you want to apply these manager / sender emails to the following line items?",
            "form_include_template": form_include_template,
            "sender_emails": sender_emails,
            "investment_line_items": investment_line_items,
        },
    )


@login_required
def bulk_upload_historic(request: HttpRequest) -> HttpResponse:
    template = "labeling_modal.html"
    line_item = get_line_item_from_url_params(request)
    logger.info("bulk_upload_historic", line_item=line_item)
    s3_parent_path = Path("user_upload_retrieval") / str(timezone.now().strftime("%Y-%m-%d_%H-%M-%S"))
    modal_include_template = "vault/modal/upload.html"
    return TemplateResponse(
        request,
        template,
        {
            "line_item": line_item,
            "modal_include_template": modal_include_template,
            "s3_parent_path": s3_parent_path,
        },
    )


@login_required
def bulk_forward(request: HttpRequest) -> HttpResponse:
    template = "link/emails/bulk_forward.html"
    return TemplateResponse(
        request,
        template,
        context={
            "main_message": "Bulk Forward",
            "sub_message": f"Please forward emails / documents to {request.user.organization.name.lower().replace(' ', '-')}@app.bridgeinvest.io",  # noqa: E501
            "close_button_text": "Done",
        },
    )
