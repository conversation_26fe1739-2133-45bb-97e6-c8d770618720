import structlog
from django.db import models
from django.db.models import Q, QuerySet
from django.http import Http404, HttpRequest
from ninja import Query, Router
from ninja.pagination import LimitOffsetPagination, paginate
from webapp.models import (
    CapitalCallDocumentFact,
    Client,
    DistributionNoticeDocumentFact,
    InvestingEntity,
    Investment,
    InvestmentUpdateDocumentFact,
    LineItem,
    ProcessedDocument,
)

from public_api.auth import APIKeyAuth
from public_api.filters import (
    CashFlowFilterSchema,
    DocumentFilterSchema,
    EntityFilterSchema,
    LineItemFilterSchema,
    ValuationFilterSchema,
)
from public_api.schemas import (
    CashFlowSchema,
    ClientSchema,
    DocumentSchema,
    DocumentSchemaWithS3,
    InvestingEntitySchema,
    InvestmentSchema,
    LineItemSchema,
    ValuationSchema,
)

logger = structlog.get_logger(__name__)


###########################################
# Filter Schemas for API filtering
###########################################


# API Metadata
api_description = """
# Bridge Investment API
Welcome to the Bridge Investment Platform API. This API provides access to investment data,
portfolio entities, documents, cash flows, and valuation information.

## Authentication
All API endpoints require an API key for authentication.
Include your API key in the request header under `X-Bridge-API-Key`.


## Response Format
All responses are returned in JSON format and follow a consistent structure:
- Single resource endpoints return the resource object directly
- Collection endpoints return paginated results with metadata
"""

# Set up API router with authentication and custom metadata
router = Router(
    auth=[APIKeyAuth()],
    tags=[
        {"name": "Clients", "description": "Endpoints for managing client data"},
        {"name": "Investing Entities", "description": "Endpoints for managing investing entities like LLCs and trusts"},
        {"name": "Investments", "description": "Endpoints for managing investment funds and opportunities"},
        {"name": "Line Items", "description": "Endpoints for managing relationships between entities and investments"},
        {"name": "Documents", "description": "Endpoints for accessing investment documents"},
        {"name": "Analytics", "description": "Endpoints for analyzing cash flows and valuations"},
    ],
)


# Custom pagination configuration
class CustomPagination(LimitOffsetPagination):
    """Pagination class with configurable page size"""


@router.get(
    "/clients",
    response=list[ClientSchema],
    summary="List all clients",
    description="""
    Retrieve a list of all clients accessible to the authenticated user.

    This endpoint returns basic information about each client including their
    legal name and any notes. Results are paginated for performance.
    """,
    tags=["Clients"],
    operation_id="list_clients",
)
@paginate(CustomPagination)
def get_clients(request: HttpRequest) -> list[ClientSchema]:
    """Get a paginated list of all clients."""
    return Client.objects.for_user(request.user).order_by("legal_name")


@router.get(
    "/clients/{client_id}",
    response=ClientSchema,
    summary="Get client details",
    description="""
    Retrieve detailed information about a specific client.

    This endpoint returns the complete client record including id, legal name, and notes.
    """,
    tags=["Clients"],
    operation_id="get_client",
)
def get_client(request: HttpRequest, client_id: str) -> ClientSchema:
    """Get a specific client by ID."""
    queryset = Client.objects.for_user(request.user)
    try:
        return queryset.get(id=client_id)
    except queryset.model.DoesNotExist as e:
        msg = "No object matches the given query."
        raise Http404(msg) from e


@router.get(
    "/entities",
    response=list[InvestingEntitySchema],
    summary="List all investing entities",
    description="""
    Retrieve a list of all investing entities accessible to the authenticated user.

    This endpoint returns basic information about each investing entity including
    their legal name, type, and associated client. Results are paginated for performance.

    You can optionally filter entities by client_id using the query parameter.
    """,
    tags=["Investing Entities"],
    operation_id="list_entities",
)
@paginate(CustomPagination)
def get_entities(request: HttpRequest, filters: Query[EntityFilterSchema]) -> list[InvestingEntitySchema]:
    """Get a paginated list of investing entities, optionally filtered by client."""
    return filters.filter(
        InvestingEntity.objects.for_user(request.user).select_related("client").order_by("legal_name")
    )


@router.get(
    "/entities/{entity_id}",
    response=InvestingEntitySchema,
    summary="Get investing entity details",
    description="""
    Retrieve detailed information about a specific investing entity.

    This endpoint returns the complete entity record including id, legal name,
    entity type, client association, and notes.
    """,
    tags=["Investing Entities"],
    operation_id="get_entity",
)
def get_entity(request: HttpRequest, entity_id: str) -> InvestingEntity:
    """Get a specific investing entity by ID."""
    queryset = InvestingEntity.objects.for_user(request.user).select_related("client")
    try:
        return queryset.get(id=entity_id)
    except queryset.model.DoesNotExist as e:
        msg = "No object matches the given query."
        raise Http404(msg) from e


@router.get(
    "/investments",
    response=list[InvestmentSchema],
    summary="List all investments",
    description="""
    Retrieve a list of all investments accessible to the authenticated user.

    This endpoint returns basic information about each investment including
    their legal name and managing firm. Results are paginated for performance.
    """,
    tags=["Investments"],
    operation_id="list_investments",
)
@paginate(CustomPagination)
def get_investments(request: HttpRequest) -> QuerySet:
    """Get a paginated list of all investments."""
    return Investment.objects.for_user(request.user).order_by("legal_name")


@router.get(
    "/investments/{investment_id}",
    response=InvestmentSchema,
    summary="Get investment details",
    description="""
    Retrieve detailed information about a specific investment.

    This endpoint returns the complete investment record including id, legal name,
    managing firm, and notes.
    """,
    tags=["Investments"],
    operation_id="get_investment",
)
def get_investment(request: HttpRequest, investment_id: str) -> Investment:
    """Get a specific investment by ID."""
    queryset = Investment.objects.for_user(request.user)
    try:
        return queryset.get(id=investment_id)
    except queryset.model.DoesNotExist as e:
        msg = "No object matches the given query."
        raise Http404(msg) from e


@router.get(
    "/line-items",
    response=list[LineItemSchema],
    summary="List all line items",
    description="""
    Retrieve a list of all line items accessible to the authenticated user.

    Line items represent the relationship between investing entities and investments.
    This endpoint returns information about which entities have invested in which funds.
    Results are paginated for performance.

    You can optionally filter line items by client_id, entity_id, or investment_id.
    """,
    tags=["Line Items"],
    operation_id="list_line_items",
)
@paginate(CustomPagination)
def get_line_items(
    request: HttpRequest,
    filters: Query[LineItemFilterSchema],
) -> list[LineItemSchema]:
    """Get a paginated list of line items, with optional filtering."""
    return filters.filter(
        LineItem.objects.for_user(request.user)
        .select_related("investing_entity", "investment", "investing_entity__client")
        .order_by("investing_entity__legal_name", "investment__legal_name")
    )


@router.get(
    "/line-items/{line_item_id}",
    response=LineItemSchema,
    summary="Get line item details",
    description="""
    Retrieve detailed information about a specific line item.

    This endpoint returns the complete line item record including id, associated
    investing entity, investment, and status information.
    """,
    tags=["Line Items"],
    operation_id="get_line_item",
)
def get_line_item(request: HttpRequest, line_item_id: str) -> LineItem:
    """Get a specific line item by ID."""
    queryset = LineItem.objects.for_user(request.user).select_related(
        "investing_entity", "investment", "investing_entity__client"
    )
    try:
        return queryset.get(id=line_item_id)
    except queryset.model.DoesNotExist as e:
        msg = "No object matches the given query."
        raise Http404(msg) from e


@router.get(
    "/documents",
    response=list[DocumentSchema],
    summary="List all documents",
    description="""
    Retrieve a list of all processed documents accessible to the authenticated user.

    This endpoint returns information about documents such as capital calls,
    distribution notices, and account statements. Results are paginated for performance.

    You can optionally filter documents by line_item_id, document_type, and date range.
    """,
    tags=["Documents"],
    operation_id="list_documents",
)
@paginate(CustomPagination)
def get_documents(
    request: HttpRequest,
    filters: Query[DocumentFilterSchema],
) -> list[DocumentSchema]:
    """Get a paginated list of documents, with optional filtering."""
    return filters.filter(
        ProcessedDocument.objects.for_user(request.user)
        .filter(is_visible=True)
        .select_related(
            "line_item", "line_item__investing_entity", "line_item__investment", "line_item__investing_entity__client"
        )
        .order_by("-posted_date", "name")
    )


@router.get(
    "/documents/{document_id}",
    response=DocumentSchemaWithS3,
    summary="Get document details",
    description="""
    Retrieve detailed information about a specific document.

    This endpoint returns the complete document record including id, name, type,
    dates, and associated line item.
    """,
    tags=["Documents"],
    operation_id="get_document",
)
def get_document(request: HttpRequest, document_id: str) -> DocumentSchemaWithS3:
    """Get a specific document by ID."""
    queryset = ProcessedDocument.objects.for_user(request.user).select_related(
        "line_item", "line_item__investing_entity", "line_item__investment", "line_item__investing_entity__client"
    )
    try:
        return queryset.get(id=document_id, is_visible=True)
    except queryset.model.DoesNotExist as e:
        msg = "No object matches the given query."
        raise Http404(msg) from e


@router.get(
    "/cash-flows",
    response=list[CashFlowSchema],
    summary="Get cash flow data",
    description="""
    Retrieve cash flow data for investments, including capital calls and distributions.

    This endpoint combines data from capital call and distribution notice documents
    to provide a comprehensive view of money moving into and out of investments.
    Results are paginated for performance.

    You can optionally filter cash flows by client_id, entity_id, investment_id, and date range.
    """,
    tags=["Analytics"],
    operation_id="get_cash_flows",
)
@paginate(CustomPagination)
def get_cash_flows(request: HttpRequest, filters: Query[CashFlowFilterSchema]) -> list[CashFlowSchema]:
    """
    Get cash flow data with optional filtering.
    """
    union_all_schema = [
        "processed_document__id",
        "processed_document__name",
        "processed_document__md5",
        "processed_document__posted_date",
        "processed_document__document_type",
        "processed_document__line_item__id",
        "processed_document__line_item__investing_entity__id",
        "processed_document__line_item__investing_entity__legal_name",
        "processed_document__line_item__investing_entity__client__id",
        "processed_document__line_item__investing_entity__client__legal_name",
        "processed_document__line_item__investment__id",
        "processed_document__line_item__investment__legal_name",
        "processed_document__line_item__investment__managing_firm_name",
        "flow_type",
        "amount",
        "flow_date",
    ]
    capital_calls_qs = (
        CapitalCallDocumentFact.objects.for_user(request.user)
        .select_related(
            "processed_document",
            "processed_document__line_item",
        )
        .filter(Q(processed_document__is_visible=True) & filters.get_filter_expression())
        .annotate(
            flow_type=models.Value("Capital Call", output_field=models.CharField()),
            flow_date=models.F("capital_call_due_date"),
        )
        .values(*union_all_schema)
        .filter(Q(amount__isnull=False))
    )

    distributions_qs = (
        DistributionNoticeDocumentFact.objects.for_user(request.user)
        .select_related(
            "processed_document",
            "processed_document__line_item",
        )
        .filter(Q(processed_document__is_visible=True) & filters.get_filter_expression())
        .annotate(
            flow_type=models.Value("Distributions", output_field=models.CharField()),
            flow_date=models.F("processed_document__effective_date"),
        )
        .values(*union_all_schema)
        .filter(Q(amount__isnull=False))
    )
    return capital_calls_qs.union(distributions_qs, all=True).order_by("-flow_date")


@router.get(
    "/valuations",
    response=list[ValuationSchema],
    summary="Get investment valuations",
    description="""
    Retrieve the current value of investments.

    This endpoint provides valuation data from the most recent account statements
    for each investment, including committed capital, invested amount, and total value
    (both realized and unrealized). Results are paginated for performance.

    You can optionally filter valuations by client_id, entity_id, investment_id, and as-of date.
    """,
    tags=["Analytics"],
    operation_id="get_valuations",
)
@paginate(CustomPagination)
def get_valuations(request: HttpRequest, filters: Query[ValuationFilterSchema]) -> list[ValuationSchema]:
    """Get investment valuation data with optional filtering using a single ORM call."""
    return (
        InvestmentUpdateDocumentFact.objects.for_user(request.user)
        .select_related(
            "processed_document",
            "processed_document__line_item",
        )
        .filter(Q(processed_document__is_visible=True) & filters.get_filter_expression())
        .order_by("-processed_document__effective_date")
        .filter(
            Q(committed_capital__isnull=False)
            & Q(invested__isnull=False)
            & Q(total_value__isnull=False)
            & Q(realized_value__isnull=False)
            & Q(unrealized_value__isnull=False)
            & Q(currency__isnull=False)
        )
    )
