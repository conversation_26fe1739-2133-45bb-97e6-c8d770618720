{% load customtags static %}
<div id="modal-container">
    <div class="relative w-full max-w-lg max-h-full">
        <!-- Modal content -->
        <div class="relative w-[600px]"></div>
        <div class="font-inter relative rounded-lg p-4">
            <!-- Modal header -->
            <div class="flex items-center justify-between rounded-t dark:border-gray-600">
                <button _="on click trigger closeModal"
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                    <img src="{% static 'assets/images/close.svg' %}"
                         class="w-3 h-3"
                         width="12"
                         height="12"
                         alt="closeButton">
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form hx-post="{% url 'line_item_confirm_edit' %}{% urlparams merged_portal_credential_pk=mpc.pk %}&line_item_pk={{ line_item.pk }}"
                  hx-swap="outerHTML"
                  hx-target="#modal-container">
                <div id="line-items-disabled-inputs" class="flex flex-col gap-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Edit Line Item</h3>
                        <div class="flex items-center">
                            <button class="flex flex-row gap-1 flex-1 px-4 py-2.5 border text-xs text-[#343d36] rounded-md shadow-xs hover:bg-[#f7f4f2] transition-colors"
                                    type="button"
                                    hx-post="{% url 'line_item_delete' %}{% urlparams merged_portal_credential_pk=mpc.pk line_item_pk=line_item.pk %}"
                                    hx-target="#modal-container">
                                <img src="{% static 'assets/images/trash.svg' %}"
                                     width="14"
                                     height="14"
                                     alt="Delete">
                                <span class="flex items-center">Delete line item</span>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <label for="client_name"
                               class="w-32 text-left text-sm text-[#4b554d] font-medium">Client:</label>
                        <input type="text"
                               name="client_name"
                               id="client_name"
                               {% if line_item.investing_entity.client %}value="{{ line_item.investing_entity.client }}"{% endif %}
                               class="w-full px-4 py-2.5 border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                               required />
                    </div>
                    <div class="flex items-center gap-2">
                        <label for="entity_name"
                               class="w-32 text-left text-sm text-[#4b554d] font-medium">
                            Investing
                            Entity:
                        </label>
                        <input type="text"
                               name="entity_name"
                               id="entity_name"
                               {% if line_item.investing_entity.legal_name %}value="{{ line_item.investing_entity.legal_name }}"{% endif %}
                               class="w-full px-4 py-2.5 border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                               required />
                    </div>
                    <div class="flex items-center gap-2">
                        <label for="investment_name"
                               class="w-32 text-left text-sm text-[#4b554d] font-medium">Investment:</label>
                        <input type="text"
                               name="investment_name"
                               id="investment_name"
                               {% if line_item.investment.legal_name %}value="{{ line_item.investment.legal_name }}"{% endif %}
                               class="w-full px-4 py-2.5 border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                               required />
                    </div>
                </div>
                <button type="submit"
                        class="w-full mt-6 px-4 py-2.5 bg-[#924f34] text-md font-semibold text-white rounded-lg hover:bg-[#8a5f47] transition-colors">
                    Done
                </button>
            </form>
        </div>
    </div>
</div>
