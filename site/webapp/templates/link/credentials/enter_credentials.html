{% load customtags widget_tweaks static %}
<div id="modal-container-{{ merged_portal_credential.pk }}">
    <div class="relative w-[600px]"></div>
    <div id="modal-wrapper-{{ merged_portal_credential.pk }}"
         class="p-4 md:p-5 flex items-center justify-between rounded-t w-full">
        <div class=" p-2 bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
            <div class="relative flex-col justify-start items-start flex">
                <div class="relative">
                    <img src="{% static 'assets/images/fin_tag.svg' %}"
                         width="38"
                         height="20"
                         alt="closeButton">
                </div>
            </div>
        </div>
        <button _="on click trigger closeModal"
                type="button"
                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <img src="{% static 'assets/images/close.svg' %}"
                 width="16"
                 height="19"
                 alt="closeButton">
            <span class="sr-only">Close modal</span>
        </button>
    </div>
    <form class="px-3"
          hx-post="{% url 'credentials_manage' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
          hx-swap="outerHTML"
          hx-target="#modal-container-{{ merged_portal_credential.pk }}">
        {% if form.non_field_errors %}
            <div class="px-4 pb-4">
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
                     role="alert">
                    {% for error in form.non_field_errors %}<p>{{ error }}</p>{% endfor %}
                </div>
            </div>
        {% endif %}
        <div class="px-4 pb-4 space-y-4">
            <div class="text-[#343d36] text-lg font-semibold font-inter leading-[27px] modal-line-item">
                Portal Provider:
                <a class="underline"
                   href="{{ merged_portal_credential.portal.portal_login_url }}">{{ merged_portal_credential.portal.name }}</a>
            </div>
            {% if user.is_demo %}
                <div class="flex h-[50px] items-center justify-center">
                    <img alt="{{ merged_portal_credential.portal|cut:' '|lower }}"
                         width="120"
                         height="50"
                         class="rounded-md"
                         src="{% static 'assets/images/logos/' %}{{ merged_portal_credential.portal|cut:' '|lower }}.png">
                </div>
            {% endif %}
        </div>
        {% csrf_token %}
        {% for field in form.visible_fields %}
            {% if field.id_for_label == 'id_user' %}
                <div class="px-2 w-full">
                    <div class="flex items-center mb-4">
                        <label class="text-gray-700 font-medium pr-2 w-1/5"
                               for="{{ field.id_for_label }}">{{ field.label }}</label>
                        <div class="w-4/5 relative">
                            {{ field|attr:"class:w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400" }}
                            {% if field.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in field.errors %}<p>{{ error }}</p>{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% elif field.id_for_label == 'id_portal_password' %}
                <div class="px-2 w-full">
                    <div class="flex items-center mb-4">
                        <label class="text-gray-700 font-medium pr-4 w-1/5 relative"
                               for="{{ field.id_for_label }}">{{ field.label }}</label>
                        <div class="w-4/5 relative">
                            {{ field|attr:"class:w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400" }}
                            {% if field.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in field.errors %}<p>{{ error }}</p>{% endfor %}
                                </div>
                            {% endif %}
                            <button type="button"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600"
                                    tabindex="-1"
                                    onclick="togglePasswordVisibility('{{ field.id_for_label }}', 'toggleIcon{{ forloop.counter }}')">
                                <img id="toggleIcon{{ forloop.counter }}"
                                     src="{% static 'assets/images/hide_icon.svg' %}"
                                     alt="Toggle visibility"
                                     width="20"
                                     height="20">
                            </button>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="px-2 w-full">
                    <div class="flex items-center mb-4">
                        <label class="text-gray-700 font-medium pr-4 w-1/5 relative"
                               for="{{ field.id_for_label }}">{{ field.label }}</label>
                        <div class="w-4/5 relative">
                            {{ field|attr:"class:w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"|attr:"hx-trigger:load,keyup"|attr:"hx-post:/real_time_encryption"|attr:"hx-target:#encrypted_password"|attr:"hx-swap:innerHTML" }}
                            {% if field.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in field.errors %}<p>{{ error }}</p>{% endfor %}
                                </div>
                            {% endif %}
                            <button type="button"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600"
                                    tabindex="-1"
                                    onclick="togglePasswordVisibility('{{ field.id_for_label }}', 'toggleIcon{{ forloop.counter }}')">
                                <img id="toggleIcon{{ forloop.counter }}"
                                     src="{% static 'assets/images/hide_icon.svg' %}"
                                     alt="Toggle visibility"
                                     width="20"
                                     height="20">
                            </button>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
        <div id="encrypted_password">{% include "link/credentials/real_time_encryption.html" %}</div>
    </form>
</div>
