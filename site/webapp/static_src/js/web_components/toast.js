customElements.define(
  "bridge-toast",
  class extends HTMLElement {
    connectedCallback() {
      const template = document.getElementById("toast-template");
      Array.from(template.attributes).forEach((attr) => {
        if (!this.hasAttribute(attr.name) && attr.name !== "id") {
          this.setAttribute(attr.name, attr.value);
        }
      });
      const new_node = template.content.cloneNode(true);
      new_node.querySelector("span").textContent = this.dataset.message || "";
      new_node.querySelector("button").addEventListener("click", () => {
        this.close();
      });

      if (this.dataset.timeout) {
        setTimeout(() => {
          this.close();
        }, parseInt(this.dataset.timeout));
      }

      setTimeout(() => {
        this.show();
      }, 100);

      this.appendChild(new_node);
    }

    show() {
      this.classList.remove("opacity-0", "translate-y-full");
      this.classList.add("opacity-100", "translate-y-0");
    }

    hide() {
      this.classList.remove("opacity-100", "translate-y-0");
      this.classList.add("opacity-0", "translate-y-full");
    }

    close() {
      this.hide();
      setTimeout(() => {
        this.remove();
      }, 500);
    }
  },
);
