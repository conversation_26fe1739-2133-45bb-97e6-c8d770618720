import os
from argparse import ArgumentParser

import boto3
import structlog
from botocore.exceptions import ClientError
from django.core.management.base import BaseCommand
from django_extensions.management.utils import signalcommand

from webapp.models.user import BridgeUser
from webapp.services.emails import create_or_update_credential_and_forwarding_rules_msft

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Recovers and manages forwarding rules"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("-u", "--user", type=str)
        parser.add_argument("-e", "--email", type=str)
        parser.add_argument("-s", "--secret", type=str)

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002
        region_name = os.environ.get("AWS_REGION", "us-east-1")

        secret_arn = kwargs["secret"]
        user = BridgeUser.objects.get(username=kwargs["user"])
        session = boto3.session.Session()
        client = session.client(
            service_name="secretsmanager",
            region_name=region_name,
        )

        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=secret_arn,
            )
        except ClientError:
            logger.exception("Secret Client error", secret_arn=secret_arn, action="getting")
            raise
        else:
            secret = get_secret_value_response["SecretString"]

        create_or_update_credential_and_forwarding_rules_msft(email=kwargs["email"], cache_json=secret, user=user)

        self.stdout.write(
            self.style.SUCCESS(f'Successfully reapplied forwarding rules "{user.username}" email: "{kwargs["email"]}"'),
        )
