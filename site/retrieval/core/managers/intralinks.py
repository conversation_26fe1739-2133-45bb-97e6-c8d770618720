import asyncio
import datetime
import hashlib
import json
import re
from collections.abc import Async<PERSON>enerator
from pathlib import Path
from typing import override

import structlog
from bs4 import BeautifulSoup as bs  # noqa: N813
from dateutil import parser
from playwright.async_api import Locator, expect
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from webapp.models.documents import DocumentType
from webapp.models.portal import MFAType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class IntralinksPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "intralinks.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "Intralinks (Tailwind)"),
            ("<EMAIL>", "InvestorVision"),
            ("<EMAIL>", "Shore Capital (PL)"),
        ]

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        await self.page.get_by_placeholder("Email address").fill(username)
        await asyncio.sleep(1)
        await self.page.get_by_text("Remember me").click()
        await asyncio.sleep(1)
        await self.page.get_by_text("Next").click()
        await asyncio.sleep(2)
        await self.page.get_by_placeholder("Password").fill(password)
        await asyncio.sleep(1)
        await self.page.locator("#login-submit").click()
        await asyncio.sleep(2)

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return (
            sum(
                [
                    await self.page.locator("#code-auth-google").count(),
                    await self.page.get_by_placeholder("6-digit code").count(),
                    await self.page.locator("#code-sms").count(),
                ]
            )
            > 0
        )

    async def _send_otp(self) -> None:
        # Not used here as it is automatically sent, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(5)
        if (
            await self.page.locator("#code-sms").count() > 0
            and await self.get_multi_factor_authentication_type() == MFAType.SMS
        ):
            await asyncio.sleep(1)
            await self.page.locator("#code-sms").click()
            await asyncio.sleep(1)
        if (
            await self.page.locator("#code-auth-google").count() > 0
            and await self.get_multi_factor_authentication_type() == MFAType.AUTHENTICATOR
        ):
            await asyncio.sleep(1)
            await self.page.locator("#code-auth-google").click()
            await asyncio.sleep(1)

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.get_by_placeholder("6-digit code").fill(otp)
        await asyncio.sleep(1)
        await self.page.locator("#submit-button").click()
        await asyncio.sleep(30)

    @override
    async def _resend_otp(self) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(5)
        resend_button = self.page.locator("#resend-link")
        if await resend_button.count() != 1:
            logger.error("Could not find resend button")
            raise ValueError
        await resend_button.click(force=True)
        await asyncio.sleep(5)

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return (
            sum(
                [
                    await self.page.locator("#resend-link").count(),
                    await self.page.locator("#submit-button").count(),
                    await self.page.get_by_placeholder("6-digit code").count(),
                    await self.page.locator("#code-auth-google").count(),
                    await self.page.locator("#code-sms").count(),
                    await self.page.get_by_placeholder("Email address").count(),
                    await self.page.get_by_text("Remember me").count(),
                    await self.page.get_by_text("Next").count(),
                    await self.page.get_by_placeholder("Password").count(),
                    await self.page.locator("#login-submit").count(),
                ]
            )
            == 0
        )

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        try:
            await self.page.locator("#app_home").click()
            await asyncio.sleep(5)
        except Exception:
            logger.exception("Could not click on home button, possible from a different page")
        await self.page.reload(wait_until="load")
        await asyncio.sleep(5)
        exchanges = []
        for row in (
            await self.page.locator("[role='row']").filter(has_not=self.page.locator("[role='columnheader']")).all()
        ):
            name = (await row.locator("[columnindex='0']").inner_text()).strip()
            if len(name) > 0:
                exchanges.append(name)
        if len(exchanges) == 0:
            async for nested_file in self._non_exchange_retrieve():
                yield nested_file
        else:
            for exchange_name in exchanges:
                await self.page.locator("[role='row'] a").filter(has_text=exchange_name).click()
                await asyncio.sleep(5)
                await self.page.wait_for_load_state("networkidle", timeout=60000)
                try:
                    await expect(self.page.locator("h2").filter(has_text="Documents")).to_have_count(1, timeout=30000)
                except (PlaywrightTimeoutError, AssertionError):
                    logger.exception("Navigate to exchange timed out")
                    await self.page.reload(wait_until="load")
                    await asyncio.sleep(5)
                    await self.page.locator("#app_home").click()
                    await asyncio.sleep(5)
                    await self.page.reload(wait_until="load")
                    await asyncio.sleep(5)
                    logger.exception("Could not navigate to exchange", exchange_name=exchange_name)
                    continue
                async for nested_file in self._traverse_folder(exchange_name, "All Folders"):
                    yield nested_file
                # navigate back to home page
                await self.page.locator("#app_home").click()
                await asyncio.sleep(5)
                await self.page.reload(wait_until="load")
                await asyncio.sleep(5)

    async def _non_exchange_retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: C901, PLR0915
        if self.page is None:
            raise ManagerNotInitializedError
        try:
            await self.page.get_by_text("Documents").click()
        except Exception:
            logger.exception("Could not find Documents tab, possible from a different page")
        await asyncio.sleep(5)
        await self.page.wait_for_load_state("load")
        while await self.page.get_by_text("Show More").count() > 0:
            await self.page.get_by_text("Show More").click()
            await asyncio.sleep(1)
        soup = bs(markup=await self.page.content(), features="html.parser")
        table_body_list = soup.find_all("tbody")
        if len(table_body_list) != 1:
            logger.error("Expected exactly one tbody in the table", len_table_body_list=len(table_body_list))
            raise ValueError
        table_body = table_body_list[0]
        table_head_list = soup.find_all("thead")
        if len(table_head_list) != 1:
            logger.error("Expected exactly one thead in the table", len_table_head_list=len(table_head_list))
            raise ValueError
        table_head = table_head_list[0]

        headers = [th.text.strip() for th in table_head.find_all("th")[2:]]
        data_to_do = {}
        for row in table_body.find_all("tr"):
            checkbox_id = row.find_all("input")[0]["id"]
            data = [td.text.strip() for td in row.find_all("td")[2:]]
            raw_metadata = dict(zip(headers, data, strict=True))
            metadata = json.dumps(raw_metadata)
            if "Published" in raw_metadata:
                date = parser.parse(raw_metadata["Published"])
            elif "Effective" in raw_metadata:
                date = parser.parse(raw_metadata["Effective"])
            else:
                date = datetime.datetime.now()  # noqa: DTZ005
            doc_hash = await self.should_download_document(metadata)
            if doc_hash is None:
                continue
            data_to_do[checkbox_id] = (
                metadata,
                date,
                doc_hash,
                self._document_mapper(raw_metadata.get("Doc Type", "Unknown")),
            )

        for checkbox_id, (metadata, date, doc_hash, doc_type) in data_to_do.items():
            await self.page.locator(f"[id='{checkbox_id}']").check()
            await asyncio.sleep(1)  # Wait for the checkbox to be checked
            async with self.page.expect_download(timeout=45000) as download_info:
                await self.page.get_by_test_id("downloadDocuments-button").click()
                download = await asyncio.wait_for(download_info.value, 100)
            file_name = self.local_directory / download.suggested_filename
            await download.save_as(file_name)
            content = file_name.read_bytes()
            file_name.unlink()
            yield RawDocumentTuple(
                name=download.suggested_filename,
                date=date,
                doc_type=doc_type,
                content=content,
                raw_metadata=metadata,
                doc_hash=doc_hash,
            )
            await asyncio.sleep(1)
            await self.page.locator(f"[id='{checkbox_id}']").scroll_into_view_if_needed()
            await self.page.locator(f"[id='{checkbox_id}']").hover()
            await self.page.locator(f"[id='{checkbox_id}']").uncheck()
            await asyncio.sleep(1)

    async def _wait_for_spinner(self, wait_timeout: int = 60000, error_message: str = "Navigation timed out") -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        try:
            await expect(self.page.locator(".spinner")).to_have_count(0, timeout=wait_timeout)
        except PlaywrightTimeoutError as e:
            logger.exception(error_message)
            raise CouldNotFindHTMLElementError from e

    async def _traverse_folder(
        self,
        exchange_name: str,
        bread_crumbs: str,
    ) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        await self._wait_for_spinner(error_message=f"Navigate to folder {bread_crumbs} timed out")
        row_count = await self._get_total_count()
        seen: set[str] = set()
        row_id = 0
        while len(seen) < row_count and row_id < row_count:
            await self._scroll_to_row_id(row_id)
            row = (
                self.page.locator(f"[role='row'][row-id='{row_id}']")
                .filter(has_not=self.page.locator("[role='columnheader']"))
                .first  # there is a duplicate id for the last row for some reason
            )
            title = (await row.locator("[columnindex='4']").inner_text()).strip()
            file_type = (await row.locator("[columnindex='5']").inner_text()).strip()
            file_path = bread_crumbs + "/" + title
            if len(title) == 0 or file_path in seen:
                continue
            seen.add(file_path)
            if file_type == "Folder":
                await row.locator("[columnindex='4'] a").click()
                await self._wait_for_spinner(error_message=f"Navigate to folder {file_path} timed out")
                await asyncio.sleep(1)
                async for nested_file in self._traverse_folder(exchange_name, file_path):
                    yield nested_file
                last_folder = self.page.locator(".breadcrumb li a")
                try:
                    await expect(last_folder).not_to_have_count(0)
                    last_crumb = await last_folder.last.inner_text()
                    while await last_folder.count() > 0 and last_crumb == await last_folder.last.inner_text():
                        await last_folder.last.click()
                        await self._wait_for_spinner(error_message=f"Navigate to folder {bread_crumbs} timed out")
                        await asyncio.sleep(2)
                except PlaywrightTimeoutError as e:
                    error_message = f"Cannot find bread crumb {bread_crumbs}"
                    logger.exception(error_message)
                    raise CouldNotFindHTMLElementError from e
            else:
                effective_date = (await row.locator("[columnindex='12']").inner_text()).strip()
                doc_type = self._document_mapper(file_path)
                metadata = json.dumps(
                    {
                        "Document Name": title,
                        "Exchange Name": exchange_name,
                        "File Path": file_path,
                        "Effective Date": effective_date,
                        "Doc Type": doc_type,
                    }
                )
                if await self.should_download_document(metadata):
                    doc = await self._download_doc(row, metadata, effective_date=(effective_date), doc_type=doc_type)
                    await self._wait_for_spinner(error_message="File download timed out")
                    await asyncio.sleep(1)
                    if doc is not None:
                        yield doc
            row_id += 1

    async def _scroll_to_row_id(self, row_id: int) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(2)
        first_row_id_str = (
            await self.page.locator("[role='row']")
            .filter(has_not=self.page.locator("[role='columnheader']"))
            .first.get_attribute("row-id")
        )
        if first_row_id_str is None:
            raise CouldNotFindHTMLElementError
        try:
            first_row_id = int(first_row_id_str)
        except ValueError:
            msg = f"Cannot parse row-id '{first_row_id_str}' to int"
            raise ValueError(msg) from ValueError
        if first_row_id + 10 < row_id:
            try:
                await (
                    self.page.locator("[role='row']")
                    .filter(has_not=self.page.locator("[role='columnheader']"))
                    .nth(2)  # first row can be overscrolled under the header
                    .hover()
                )
            except PlaywrightTimeoutError:
                msg = f"hovering failed for row id '{first_row_id_str}'"
                logger.exception(msg)
            for _ in range(row_id - first_row_id):
                # the row height is 500px but scrolling by 5000 doesn't work, each scroll action scrolls one row
                await self.page.mouse.wheel(0, 500)

    async def _get_total_count(self) -> int:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.locator("[role='columnheader'] #columnCheckbox").click()
        items_selected_text = await self.page.locator(".bx--batch-summary__para span").inner_text()
        match = re.match(r"^(\d+)", items_selected_text)
        if match is None:
            raise CouldNotFindHTMLElementError
        await asyncio.sleep(2)
        await self.page.locator("[role='columnheader'] #columnCheckbox").click()
        return int(match.group(1))

    async def _download_doc(
        self, row_locator: Locator, metadata: str, effective_date: str, doc_type: str
    ) -> RawDocumentTuple | None:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        check_box = row_locator.locator("[columnindex='0'] [role='checkbox']")
        await check_box.click(force=True)
        await asyncio.sleep(1)
        download_button = self.page.locator("[type='button']").filter(has_text="Download")
        async with self.page.expect_download(timeout=30000) as download_info:
            await download_button.click()
        download = await download_info.value
        await asyncio.sleep(1)
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.warning("Unsupported file format: %s", file_name.suffix)
            return None
        file_name.unlink()
        return RawDocumentTuple(
            name=download.suggested_filename,
            date=self._parse_date(effective_date),
            doc_type=doc_type,
            content=content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    def _parse_date(self, effective_date: str) -> datetime.datetime:
        try:
            return parser.parse(effective_date)
        except (ValueError, TypeError, parser.ParserError):
            # some files have an empty effective date column, return today as a place holder
            return datetime.datetime.now()  # noqa: DTZ005

    def _document_mapper(self, file_path: str) -> DocumentType | str:
        file_path_lower = file_path.lower()
        keywords = {
            "capital call": DocumentType.CAPITAL_CALL,
            "distribution": DocumentType.DISTRIBUTION_NOTICE,
            "capital statement": DocumentType.ACCOUNT_STATEMENT,
            "update": DocumentType.INVESTMENT_UPDATE,
            "financial statement": DocumentType.FINANCIAL_STATEMENTS,
            "general": DocumentType.INVESTMENT_UPDATE,
            "tax": DocumentType.TAX,
            "legal": DocumentType.LEGAL,
            "statement": DocumentType.ACCOUNT_STATEMENT,
        }
        for keyword, doc_type in keywords.items():
            if keyword in file_path_lower:
                return doc_type
        return DocumentType.UNKNOWN
