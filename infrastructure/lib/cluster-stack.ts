import { Construct } from "constructs";
import {
  NetworkStackOutputs,
  ConfigStackOutputs,
  ClusterStackOutputs,
  SharedProps,
  SharedStack,
} from "./schema";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as cdk from "aws-cdk-lib";
import * as iam from "aws-cdk-lib/aws-iam";
import * as ecr from "aws-cdk-lib/aws-ecr";
import * as ec2 from "aws-cdk-lib/aws-ec2";

export class ClusterStack extends SharedStack {
  constructor(scope: Construct, id: string, props: SharedProps) {
    super(scope, id, props);

    // From network-stack
    const networkOutputs = NetworkStackOutputs.fromCdkOutput(
      this,
      NetworkStackOutputs,
    );
    const configOutputs = ConfigStackOutputs.fromCdkOutput(
      this,
      ConfigStackOutputs,
    );
    if (networkOutputs === undefined || configOutputs === undefined) {
      return;
    }
    const vpc = ec2.Vpc.fromVpcAttributes(this, "Vpc", {
      vpcId: networkOutputs.VpcId,
      availabilityZones: networkOutputs.VpcAvailabilityZones,
      publicSubnetIds: networkOutputs.VpcPublicSubnetIds,
      publicSubnetRouteTableIds: networkOutputs.VpcPublicRouteTableIds,
    });
    // Create ECS Cluster
    const cluster = new ecs.Cluster(this, "Cluster", {
      vpc: vpc,
    });
    // Define the ECR repository
    const pythonRepository = new ecr.Repository(this, "PythonRepository", {
      repositoryName: configOutputs.PythonContainerRepositoryName,
      encryption: ecr.RepositoryEncryption.AES_256,
      imageScanOnPush: true,
    });
    // Add a lifecycle rule to delete untagged images older than 30 days
    pythonRepository.addLifecycleRule({
      tagStatus: ecr.TagStatus.UNTAGGED,
      maxImageAge: cdk.Duration.days(7),
    });
    const executionRole = new iam.Role(this, "ExecutionRole", {
      assumedBy: new iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
      description: "IAM role for ECS task execution with necessary permissions",
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName(
          "service-role/AmazonECSTaskExecutionRolePolicy",
        ),
      ],
    });

    pythonRepository.grantPull(executionRole);

    new ClusterStackOutputs({
      ClusterName: cluster.clusterName,
      ExecutionRoleArn: executionRole.roleArn,
    }).toCdkOutput(this);
  }
}
