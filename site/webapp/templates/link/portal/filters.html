{% load static customtags %}
<div class="w-full flex justify-between items-center px-9 pt-10">
    <div class="text-[#34336] font-bold font-nanum text-[20px] leading-7">Onboarding Status</div>
    <div class="flex space-x-2">
        <!-- Portal Filter -->
        <div class="relative">
            <button id="dropdownSearchButton-portal"
                    type="button"
                    class="h-[37px] px-3 py-2 bg-white rounded-lg border border-[#d0d2bf] text-sm font-semibold flex items-center gap-2"
                    data-dropdown-toggle="dropdownSearch-portal">
                <img src="{% static 'assets/images/filter_black.svg' %}"
                     width="20"
                     height="21"
                     alt="Filter"
                     class="w-5 h-5">
                Portal / Inbox Filter
            </button>
            <div id="dropdownSearch-portal"
                 class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-70 mt-1"
                 data-dropdown>
                <ul class="h-48 px-3 py-2 overflow-y-auto text-sm text-gray-700">
                    {% for portal in portals %}
                        <li class="flex items-center px-2 py-1 rounded hover:bg-gray-100">
                            <input id="checkbox-item-portal-{{ portal.id }}"
                                   type="checkbox"
                                   name="portal"
                                   value="{{ portal.id }}"
                                   class="line_item_search w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                   hx-post="{% url 'line_item_search' %}"
                                   hx-trigger="change"
                                   hx-include=".line_item_search"
                                   hx-target="#portal_dashboard_table"
                                   hx-swap="outerHTML">
                            <label for="checkbox-item-portal-{{ portal.id }}"
                                   class=" ml-2 text-sm font-medium text-gray-900">{{ portal.name }}</label>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        <!-- Connection Status Filter -->
        <div class="relative">
            <button id="dropdownSearchButton-connection"
                    type="button"
                    class="h-[37px] px-3 py-2 bg-white rounded-lg border border-[#d0d2bf] text-sm font-semibold flex items-center gap-2"
                    data-dropdown-toggle="dropdownSearch-connection">
                <img src="{% static 'assets/images/filter_black.svg' %}"
                     alt="Filter"
                     width="20"
                     height="21"
                     class="w-5 h-5">
                Connection Status
            </button>
            <div id="dropdownSearch-connection"
                 class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-60 mt-1"
                 data-dropdown>
                <ul class="h-48 px-3 py-2 overflow-y-auto text-sm text-gray-700">
                    {% for status, label in ConnectionHealthStatus.items %}
                        <li class="flex items-center px-2 py-1 rounded hover:bg-gray-100">
                            <input id="checkbox-item-connection-{{ status }}"
                                   type="checkbox"
                                   name="connection_status"
                                   value="{{ status }}"
                                   class="line_item_search w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                   hx-post="{% url 'line_item_search' %}"
                                   hx-include=".line_item_search"
                                   hx-trigger="change"
                                   hx-target="#portal_dashboard_table"
                                   hx-swap="outerHTML">
                            <label for="checkbox-item-connection-{{ status }}"
                                   class="ml-2 text-sm font-medium text-gray-900">{{ label|capfirst }}</label>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>
