{% extends "settings/base_settings.html" %}
{% load static %}
{% block settings_content %}
    <div class="py-9 px-12">
        <div class="flex flex-col gap-2 justify-between">
            {% if entities %}
                <h1 class="text-2xl font-bold mb-6">Select your Entity:</h1>
                <div class="flex flex-col gap-2 w-4/12">
                    {% for entity in entities %}
                        <a href="{% url 'settings_entity' entity.id %}"
                           class="entity-button flex items-center gap-2 px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-sm rounded-md border border-transparent transition">
                            ✏️ {{ entity.legal_name }}
                        </a>
                    {% endfor %}
                </div>
            {% else %}
                <h1 class="text-2xl font-semibold">No entities found</h1>
            {% endif %}
        </div>
    </div>
{% endblock settings_content %}
