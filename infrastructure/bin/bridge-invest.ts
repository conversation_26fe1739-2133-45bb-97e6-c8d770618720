#!/usr/bin/env node
import "source-map-support/register";
import * as cdk from "aws-cdk-lib";
import { NetworkStack } from "../lib/network-stack";
import { StorageStack } from "../lib/storage-stack";
import { CeleryServiceStack } from "../lib/celery-service-stack";
import { SiteServiceStack } from "../lib/site-service-stack";
import { ClusterStack } from "../lib/cluster-stack";
import { ConfigStack } from "../lib/config-stack";
import { MonitoringStack } from "../lib/monitoring-stack";
import { SftpServerStack } from "../lib/sftp-stack";

const deployTargets: { [key: string]: { [key: string]: string } } = {
  demo: {
    deploymentTarget: "demo",
    awsAccount: "************",
    awsRegion: "us-east-1",
    domainName: "bridgeinvest.co",
    hostedZoneId: "Z05671591T7GRTZV7JAEQ",
  },
  prod: {
    deploymentTarget: "prod",
    awsAccount: "************",
    awsRegion: "us-east-1",
    domainName: "app.bridgeinvest.io",
    hostedZoneId: "Z05101163RY8ZZFJ9ECPS",
  },
};

const target = process.env.AWS_PROFILE || "demo";
const targetEnv = deployTargets[target];

const domainName = targetEnv.domainName;
const hostedZoneId = targetEnv.hostedZoneId;

const env = {
  account: targetEnv.awsAccount,
  region: targetEnv.awsRegion,
};

const app = new cdk.App();

const sharedProps = {
  env,
  deploymentTarget: targetEnv.deploymentTarget,
  awsAccount: targetEnv.awsAccount,
  awsRegion: targetEnv.awsRegion,
  domainName,
  hostedZoneId,
};
const configStack = new ConfigStack(app, "ConfigStack", sharedProps);

const networkStack = new NetworkStack(app, "NetworkStack", sharedProps);

const storageStack = new StorageStack(app, "StorageStack", sharedProps);
storageStack.addDependency(networkStack);
storageStack.addDependency(configStack);

const clusterStack = new ClusterStack(app, "ClusterStack", sharedProps);
clusterStack.addDependency(networkStack);
clusterStack.addDependency(configStack);

const celeryStack = new CeleryServiceStack(
  app,
  "CeleryServiceStack",
  sharedProps,
);
celeryStack.addDependency(configStack);
celeryStack.addDependency(storageStack);
celeryStack.addDependency(clusterStack);

const siteStack = new SiteServiceStack(app, "SiteServiceStack", sharedProps);
siteStack.addDependency(configStack);
siteStack.addDependency(storageStack);
siteStack.addDependency(clusterStack);

const monitoringStack = new MonitoringStack(
  app,
  "MonitoringStack",
  sharedProps,
);
monitoringStack.addDependency(storageStack);
monitoringStack.addDependency(siteStack);

const sftpStack = new SftpServerStack(app, "SftpServerStack", sharedProps);
sftpStack.addDependency(networkStack);
