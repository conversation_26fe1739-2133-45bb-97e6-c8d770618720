import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON><PERSON>ator
from typing import override
from zipfile import ZipFile

import structlog
from bs4 import BeautifulSoup as bs  # noqa: N813
from dateutil import parser
from django.utils import timezone
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.get_logger(__name__)


@register_strategy
class FundPanelPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "fundpanel.io" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "FundPanel"),
            ("<EMAIL>", "Fund Panel"),
            ("<EMAIL>", "FundPanel (Penny Jar)"),
        ]

    @classmethod
    def email_otp_rules(cls) -> dict:
        return {
            "senderContains": ["fundpanel.io"],
        }

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        await self.page.get_by_alt_text("Enter Email or Username").click()
        await self.page.get_by_alt_text("Enter Email or Username").fill(username)
        await asyncio.sleep(1)
        await self.page.get_by_alt_text("Enter Password").click()
        await self.page.get_by_alt_text("Enter Password").fill(password)
        await asyncio.sleep(1)
        await self.page.locator("input[alt='Login to FundPanel']").click()

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return await self.page.get_by_placeholder("Authentication Code").count() > 0

    @override
    async def _resend_otp(self) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(5)
        resend_button = self.page.locator("input[value='Resend Code']")
        if await resend_button.count() != 1:
            logger.error("Could not find resend button")
            raise ValueError
        await resend_button.click()
        await asyncio.sleep(5)

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.get_by_placeholder("Authentication Code").click()
        await self.page.get_by_placeholder("Authentication Code").fill(otp)
        # TODO: Do we need to click the button?
        await asyncio.sleep(10)
        setup_later_button = self.page.get_by_text("Setup Mobile Number Later")
        if await setup_later_button.count() > 0 and await setup_later_button.is_visible():
            logger.info("Found Setup Mobile Number Later button, clicking")
            await setup_later_button.click()
            await asyncio.sleep(10)
        else:
            logger.info("No Setup Mobile Number Later button found")

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return await self.page.get_by_text("Logout").count() > 0

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        async for raw_doc_tuple in self.__doc_iterator():
            if raw_doc_tuple is not None:
                yield raw_doc_tuple

    async def __doc_iterator(self) -> AsyncGenerator[RawDocumentTuple | None, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.goto("https://www.fundpanel.io/Home#/orgs")
        await asyncio.sleep(5)
        async for org in self._org_iterator():
            logger.info("Going to org", org=org)
            async for tab in self._tabs_iterator():
                logger.info("Going to tab", tab=tab)
                yield await self._download_export_all(org, tab)
                bulk_download_locator = self.page.get_by_text("Bulk Download")
                if await bulk_download_locator.count() > 0 and await bulk_download_locator.is_visible():
                    await bulk_download_locator.click()
                    await asyncio.sleep(1)
                    async for bulk_download_doc in self._bulk_download(org, tab):
                        yield bulk_download_doc
                    await self.page.locator("div.modal-header>button.close").click()
                    await asyncio.sleep(1)
                else:
                    logger.info("No Bulk Download button found")
        logger.info("Done with all orgs/tabs")

    async def _download_export_all(self, org: str, tab: str) -> RawDocumentTuple | None:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        export_locator = self.page.locator("button").filter(has_text="Export All").first
        if await export_locator.count() == 0 or not await export_locator.is_visible():
            logger.info("No Export All button found")
            return None

        psv, raw_date, date = await self._generic_table_to_psv()
        metadata = json.dumps(
            {
                "Fundpanel Org": org,
                "Fundpanel Tab": tab,
                "Table PSV": psv,
                "Raw Date": raw_date,
            }
        )

        if not await self.should_download_document(metadata):
            return None

        await asyncio.sleep(1)
        async with self.page.expect_download() as download_info:
            await self.page.locator("button").filter(has_text="Export All").first.click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = file_name.read_bytes()
        if date is None:
            date = timezone.now()
        file_name.unlink()
        return RawDocumentTuple(
            name=download.suggested_filename,
            date=date,
            doc_type=self._tab_name_document_mapper(tab),
            content=content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    async def _generic_table_to_psv(self) -> tuple[str, str | None, datetime.datetime | None]:
        if self.page is None:
            raise ManagerNotInitializedError
        soup = bs(markup=await self.page.content(), features="html.parser")
        table = soup.select("table")[0]
        table_str = []
        max_date = None
        raw_max_date = None
        table_str.append(",".join([th.text.strip() for th in table.select("th")]))
        for row in table.select("tr"):
            tds = row.select("td")
            if len(tds) == 0:
                continue
            texts = [td.text.strip() for td in tds]
            table_str.append("|".join(texts))
            max_date, raw_max_date = self._get_date_from_list(texts, max_date, raw_max_date)
        csv = "\n".join(table_str)
        return csv, raw_max_date, max_date

    async def _bulk_download(self, org: str, tab: str) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.get_by_text("Organize Documents within Folders").click()
        await asyncio.sleep(1)
        table_loc = self.page.locator("table")
        headers = []
        skip_first_n_cols = 1
        for x in (await table_loc.first.locator("thead>tr>th").all())[skip_first_n_cols:]:
            text_content_raw = await x.text_content()
            if text_content_raw is None:
                continue
            text_content = text_content_raw.strip()
            if text_content:
                headers.append(text_content)
        for row in await table_loc.first.locator("tbody>tr").all():
            metadata = {
                headers[idx]: t.strip()
                for idx, t in [
                    (i, await e.text_content())
                    for i, e in enumerate((await row.locator("td").all())[skip_first_n_cols:])
                ]
                if t is not None
            }
            metadata["Fundpanel Org"] = org
            metadata["Fundpanel Tab"] = tab
            json_metadata = json.dumps(metadata)
            if not await self.should_download_document(json_metadata):
                continue
            # Check the checkbox in the first column
            await row.locator("td").nth(0).click()
            await asyncio.sleep(1)
            yield await self._download_document(metadata, json_metadata)
            await self.page.get_by_text("Close", exact=True).last.click()
            await asyncio.sleep(1)
            # Uncheck the checkbox in the first column
            await row.locator("td").nth(0).click()
            await asyncio.sleep(1)

    async def _download_document(self, metadata: dict, json_metadata: str) -> RawDocumentTuple:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        async with self.page.expect_download() as download_info:
            await self.page.get_by_text("Download", exact=True).click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        zip_file_name = None
        with ZipFile(file_name, "r") as zf:
            zip_file_names = [zfn for zfn in zf.namelist() if not (zfn.endswith("/") or zfn.startswith("__MACOSX/"))]
            if len(zip_file_names) != 1:
                logger.info("Multiple files in zip", zip_file_names=zip_file_names)
                raise ValueError
            zip_file_name = zip_file_names[0]
            with zf.open(zip_file_name) as pdf_file:
                content = pdf_file.read()
        file_name.unlink()
        date, _ = self._get_date_from_list(list(metadata.values()))
        if date is None:
            date = timezone.now()
        return RawDocumentTuple(
            name=zip_file_name,
            date=date,
            doc_type=self._document_mapper(metadata),
            content=content,
            raw_metadata=json_metadata,
            doc_hash=self._doc_hash(json_metadata),
        )

    def _get_date_from_list(
        self, maybe_dates: list[str], max_date: datetime.datetime | None = None, raw_max_date: str | None = None
    ) -> tuple[datetime.datetime | None, str | None]:
        for maybe_date_text in maybe_dates:
            try:
                dt = parser.parse(maybe_date_text)
                if max_date is None or dt > max_date:
                    max_date = dt
                    raw_max_date = maybe_date_text
            except parser.ParserError:
                continue
        return max_date, raw_max_date

    def _document_mapper(self, metadata: dict) -> str:
        keywords = {
            "financial": (DocumentType.INVESTMENT_UPDATE, 2),
            "fund document": (DocumentType.INVESTMENT_UPDATE, 2),
            "tax": (DocumentType.TAX, 1),
            "k-1": (DocumentType.TAX, 1),
            "capital account statement": (DocumentType.ACCOUNT_STATEMENT, 1),
            "capital call": (DocumentType.CAPITAL_CALL, 1),
            "legal": (DocumentType.LEGAL, 1),
        }
        doc_types = []
        for keyword, (doc_type, priority) in keywords.items():
            for value in metadata.values():
                if keyword in value.lower():
                    doc_types.append((doc_type, priority))  # noqa: PERF401
        if len(doc_types) == 0:
            return DocumentType.UNKNOWN
        return sorted(doc_types, key=lambda x: x[1])[0][0]

    async def _org_iterator(self) -> AsyncGenerator[str, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.goto("https://www.fundpanel.io/Home#/orgs")
        await asyncio.sleep(5)
        titles = [await elem.get_attribute("title") for elem in await self.page.locator("img.orgLogo").all()]
        for title in titles:
            locator_selector = f"img.orgLogo[title='{title}']"
            locator = self.page.locator(locator_selector)
            if await locator.count() > 0 and await locator.is_visible():
                await locator.click()
                await asyncio.sleep(10)
            else:
                logger.error("Locator not found or not visible", locator_selector=locator_selector)
            if title is not None:
                yield title
            await self.page.goto("https://www.fundpanel.io/Home#/orgs")
            await asyncio.sleep(5)

    async def _tabs_iterator(self) -> AsyncGenerator[str, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        tabs = ["Capital Commitments", "Capital Calls", "Distributions", "Capital Accounts", "Documents"]
        more_tabs_locator = self.page.get_by_text("More Tabs")
        if await more_tabs_locator.count() > 0 and await more_tabs_locator.is_visible():
            await more_tabs_locator.click()
        else:
            logger.error("Locator not found or not visible", more_tabs_locator=more_tabs_locator)
        await asyncio.sleep(1)
        for tab in tabs:
            locator = self.page.get_by_text(tab, exact=True)
            if await locator.count() > 0 and await locator.is_visible():
                await locator.click()
                await asyncio.sleep(2)
                yield tab
                more_tabs_locator = self.page.get_by_text("More Tabs")
                if await more_tabs_locator.count() > 0 and await more_tabs_locator.is_visible():
                    await more_tabs_locator.click()
                await asyncio.sleep(1)
            else:
                logger.error("Locator not found or not visible", tab=tab)

    def _tab_name_document_mapper(self, tab_name: str) -> str:
        return {
            "Capital Commitments": DocumentType.ACCOUNT_STATEMENT,
            "Capital Calls": DocumentType.CAPITAL_CALL,
            "Distributions": DocumentType.DISTRIBUTION_NOTICE,
            "Capital Accounts": DocumentType.ACCOUNT_STATEMENT,
        }.get(tab_name, DocumentType.UNKNOWN)
