{% load static %}
<!-- Update investment details table container -->
<div id="client_dashboard_table">
    <!-- Section Header -->
    <div class="sticky top-0 z-20 min-w-full bg-base-beige px-9 py-4">
        <div class="flex flex-col">
            <div class="text-[#343d36] text-xl font-bold font-nanum leading-7 mb-2">Investment Detail</div>
            {% include "dashboard/investment_details/table_header_buttons.html" %}
        </div>
    </div>
    <div class="w-full px-5">
        <div class="bg-base-beige p-4 rounded-lg">
            <table class="w-full table-auto border relative">
                <!-- Sticky Header -->
                <thead class="top-[100px] sticky z-10 bg-base-beige transition-all duration-200">
                    <tr>
                        <th></th>
                        <th colspan="5"
                            class="px-4 py-3 text-center text-lg font-semibold bg-base-darker-olive-muted border text-white">
                            Investment Overview
                        </th>
                        <th colspan="1"
                            class="px-4 py-3 text-center text-lg font-semibold bg-gray-100 border">Reporting Date</th>
                        <th colspan="4"
                            class="px-4 py-3 text-center text-lg font-semibold bg-primary-olive border">
                            Document Portal
                        </th>
                    </tr>
                    <tr>
                        <th class="px-4 py-3 text-left text-md font-medium text-[#343D36] border whitespace-nowrap">Line Items</th>
                        <th class="px-4 py-3 text-left text-md font-medium text-[#343D36] border whitespace-nowrap">Committed</th>
                        <th class="px-4 py-3 text-left text-md font-medium text-[#343D36] border whitespace-nowrap">Invested</th>
                        <th class="px-4 py-3 text-left text-md font-medium text-[#343D36] border whitespace-nowrap">Total Value</th>
                        <th class="px-4 py-3 text-left text-md font-medium text-[#343D36] border whitespace-nowrap pr-4">Realized Value</th>
                        <th class="px-4 py-3 text-left text-md font-medium text-[#343D36] border whitespace-nowrap pr-4">Unrealized Value</th>
                        <th class="px-4 py-3 text-center text-md font-medium text-[#343D36] border whitespace-nowrap w-[180px]">As Of</th>
                        <th class="px-4 py-3 text-left text-md font-medium text-[#343D36] border whitespace-nowrap w-[180px]">
                            Capital Calls
                        </th>
                        <th class="px-4 py-3 text-left text-md font-medium text-[#343D36] border whitespace-nowrap w-[180px]">
                            Distribution Notices
                        </th>
                        <th class="px-4 py-3 text-left text-md font-medium text-[#343D36] border whitespace-nowrap w-[180px]">
                            Account Statements
                        </th>
                        <th class="px-4 py-3 text-left text-md font-medium text-[#343D36] border whitespace-nowrap w-[180px]">
                            Investment Updates
                        </th>
                    </tr>
                </thead>
                <tbody id="table-content">
                    {% include "dashboard/investment_details/table_body.html" %}
                </tbody>
            </table>
        </div>
    </div>
</div>
