{% extends "settings/base_settings.html" %}
{% load static %}
{% load customtags %}
{% load widget_tweaks %}
{% block settings_content %}
    <div class="py-9 px-12">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Manage Your Team</h1>
            <button id="show-invite-modal"
                    class="flex items-center gap-2 {% if not request.user.is_manager %}bg-gray-300 text-gray-500 cursor-not-allowed{% else %}bg-[#343d36] text-white hover:bg-[#2a312d]{% endif %} px-4 py-2 rounded-lg"
                    {% if not request.user.is_manager %}disabled{% else %} hx-get="{% url 'user_invite_modal' %}" hx-target="body" hx-swap="beforeend"{% endif %}>
                <img src="{% static 'assets/images/invite_users.svg' %}"
                     class="h-5 w-5 {% if not request.user.is_manager %}text-gray-500{% else %}text-white{% endif %} fill-current"
                     width="20"
                     height="20"
                     alt="Invite Users">
                Invite Users
            </button>
        </div>
        <div class="bg-white rounded-lg shadow">
            <div>
                <table class="w-full table-auto min-w-[800px]">
                    <thead class="bg-gray-50 border-b">
                        <tr>
                            <th class="px-4 py-3 text-left text-sm font-semibold text-gray-600 w-1/4">Name</th>
                            <th class="px-4 py-3 text-left text-sm font-semibold text-gray-600 w-1/4">Role</th>
                            <th class="px-4 py-3 text-left text-sm font-semibold text-gray-600 w-1/6">Entities</th>
                            <th class="px-4 py-3 text-left text-sm font-semibold text-gray-600 w-1/6">Investments</th>
                            <th class="px-4 py-3 text-center text-sm font-semibold text-gray-600 w-12">Action</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for user_in_organization in users_in_organization %}
                            {% with user_form=user_forms|lookup:user_in_organization.id %}
                                <tr id="user-row-{{ user_in_organization.id }}">
                                    <td colspan="5" class="px-4 py-4">
                                        <form method="post"
                                              action="{% url 'settings_organization' %}"
                                              id="user-settings-form-{{ user_in_organization.id }}">
                                            {% csrf_token %}
                                            <div class="flex items-center">
                                                <!-- Name -->
                                                <div class="w-1/4">
                                                    <div class="truncate">
                                                        <div class="text-sm font-medium text-gray-900 truncate">
                                                            {{ user_in_organization.first_name }} {{ user_in_organization.last_name }}
                                                        </div>
                                                        <div class="text-sm text-gray-500 truncate">{{ user_in_organization.email }}</div>
                                                    </div>
                                                </div>
                                                <!-- Role Dropdown and Hidden User ID -->
                                                <div class="w-1/4 px-4">
                                                    {{ user_form.user_id }}
                                                    {{ user_form.roles }}
                                                </div>
                                                <!-- Entity Dropdown -->
                                                <div class="w-1/6 px-4">
                                                    <button id="dropdownCheckboxButton-{{ user_in_organization.id }}-entities"
                                                            data-dropdown-toggle="dropdownCheckbox-{{ user_in_organization.id }}-entities"
                                                            class="border border-gray-300 rounded-md bg-white px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 inline-flex justify-between items-center w-full min-h-[38px] {% if user_form.entities.field.disabled %}opacity-50 cursor-not-allowed{% endif %}"
                                                            {% if user_form.entities.field.disabled %}disabled{% endif %}
                                                            type="button">
                                                        <span class="overflow-hidden text-ellipsis whitespace-nowrap flex-grow pr-2">Select Entities</span>
                                                        <img src="{% static 'assets/images/down_arrow.svg' %}"
                                                             class="h-3 w-3 text-gray-400 flex-shrink-0"
                                                             alt="down arrow"
                                                             height="20"
                                                             width="20">
                                                    </button>
                                                    <div id="dropdownCheckbox-{{ user_in_organization.id }}-entities"
                                                         class="z-10 hidden w-60 bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700 dark:divide-gray-600 border border-gray-300 max-h-60 overflow-y-auto">
                                                        <ul class="p-3 space-y-1 text-sm text-gray-700 dark:text-gray-200"
                                                            aria-labelledby="dropdownCheckboxButton-{{ user_in_organization.id }}-entities"
                                                            hx-get="{% url 'refresh_investment_dropdown' %}"
                                                            hx-trigger="change from:.flowbite-individual-checkbox"
                                                            hx-target="#dropdownCheckbox-{{ user_in_organization.id }}-investments"
                                                            hx-include="#user-settings-form-{{ user_in_organization.id }}, [name='entities']:checked"
                                                            hx-swap="innerHTML">
                                                            <li>
                                                                <div class="flex items-center px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                                                                    <input id="select-all-{{ user_in_organization.id }}-entities"
                                                                           type="checkbox"
                                                                           value=""
                                                                           class="flowbite-select-all-checkbox form-checkbox rounded-sm text-black border-black focus:ring-0 focus:outline-0 w-5 h-5 mr-2"
                                                                           {% if user_form.entities.field.disabled %}disabled{% endif %}>
                                                                    <label for="select-all-{{ user_in_organization.id }}-entities"
                                                                           class="w-full ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">
                                                                        Select All Entities
                                                                    </label>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <hr class="my-1 border-gray-200 dark:border-gray-600">
                                                            </li>
                                                            {% for checkbox in user_form.entities %}
                                                                <li>
                                                                    <div class="flex items-center px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                                                                        {{ checkbox.tag }}
                                                                        <label for="{{ checkbox.id_for_label }}"
                                                                               class="w-full ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">
                                                                            {{ checkbox.choice_label }}
                                                                        </label>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                                <!-- Investment Dropdown -->
                                                <div class="w-1/6 px-4">
                                                    <button id="dropdownCheckboxButton-{{ user_in_organization.id }}-investments"
                                                            data-dropdown-toggle="dropdownCheckbox-{{ user_in_organization.id }}-investments"
                                                            class="border border-gray-300 rounded-md bg-white px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 inline-flex justify-between items-center w-full min-h-[38px] {% if user_form.investments.field.disabled %}opacity-50 cursor-not-allowed{% else %}cursor-pointer{% endif %}"
                                                            {% if user_form.investments.field.disabled %}disabled{% endif %}
                                                            type="button">
                                                        <span class="overflow-hidden text-ellipsis whitespace-nowrap flex-grow pr-2">Select Investments</span>
                                                        <img src="{% static 'assets/images/down_arrow.svg' %}"
                                                             class="h-3 w-3 text-gray-400 flex-shrink-0"
                                                             alt="down arrow"
                                                             height="20"
                                                             width="20">
                                                    </button>
                                                    <div id="dropdownCheckbox-{{ user_in_organization.id }}-investments"
                                                         class="z-10 hidden w-60 bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700 dark:divide-gray-600 border border-gray-300 max-h-60 overflow-y-auto">
                                                        <ul class="p-3 space-y-1 text-sm text-gray-700 dark:text-gray-200"
                                                            aria-labelledby="dropdownCheckboxButton-{{ user_in_organization.id }}-investments">
                                                            <li>
                                                                <div class="flex items-center px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                                                                    <input id="select-all-{{ user_in_organization.id }}-investments"
                                                                           type="checkbox"
                                                                           value=""
                                                                           class="flowbite-select-all-checkbox form-checkbox rounded-sm text-black border-black focus:ring-0 focus:outline-0 w-5 h-5 mr-2"
                                                                           {% if user_form.investments.field.disabled %}disabled opacity-50 cursor-not-allowed{% endif %}>
                                                                    <label for="select-all-{{ user_in_organization.id }}-investments"
                                                                           class="w-full ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">
                                                                        Select All Investments
                                                                    </label>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <hr class="my-1 border-gray-200 dark:border-gray-600">
                                                            </li>
                                                            {% for checkbox in user_form.investments %}
                                                                <li>
                                                                    <div class="flex items-center px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                                                                        {{ checkbox.tag }}
                                                                        <label for="{{ checkbox.id_for_label }}"
                                                                               class="w-full ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">
                                                                            {{ checkbox.choice_label }}
                                                                        </label>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                                <!-- Save Button -->
                                                <div class="w-1/6 px-4 py-4">
                                                    <div class="flex items-center justify-center">
                                                        <button type="submit"
                                                                class="text-xs text-blue-600 hover:text-blue-800 font-medium ml-6 {% if user_form.entities.field.disabled or user_form.investments.field.disabled %}opacity-50 cursor-not-allowed{% endif %}"
                                                                hx-post="{% url 'settings_organization' %}"
                                                                hx-target="#save-status-{{ user_in_organization.id }}"
                                                                hx-swap="innerHTML"
                                                                {% if user_form.entities.field.disabled or user_form.investments.field.disabled %}disabled{% endif %}>
                                                            Save
                                                        </button>
                                                        <span id="save-status-{{ user_in_organization.id }}" class="text-xs ml-2"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </td>
                                </tr>
                                <!-- Permission History Rows -->
                                {% for history_entry in user_permission_history|lookup:user_in_organization.id %}
                                    <tr class="bg-gray-50 border-t border-gray-200 history-row-{{ history_entry.batch_id }}">
                                        <td class="px-4 py-3 text-xs text-gray-500 whitespace-nowrap">{{ history_entry.date|date:"M d, Y" }}</td>
                                        <td class="px-4 py-3 text-xs text-gray-500">
                                            {% for role in user_in_organization.roles.all %}
                                                {{ role }}
                                                {% if not forloop.last %},{% endif %}
                                            {% endfor %}
                                        </td>
                                        <td class="px-4 py-3 text-xs text-gray-500">
                                            {% if history_entry.entities %}
                                                <ul class="list-disc list-inside">
                                                    {% for entity in history_entry.entities %}<li>{{ entity.legal_name }}</li>{% endfor %}
                                                </ul>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td class="px-4 py-3 text-xs text-gray-500">
                                            {% if history_entry.investments %}
                                                <ul class="list-disc list-inside">
                                                    {% for investment in history_entry.investments %}<li>{{ investment.legal_name }}</li>{% endfor %}
                                                </ul>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td class="px-4 py-3 text-center">
                                            <div class="flex items-center justify-center">
                                                <button type="button"
                                                        hx-post="{% url 'delete_permission_batch' %}"
                                                        hx-vals='{"batch_id": "{{ history_entry.batch_id }}", "user_id": "{{ user_in_organization.id }}"}'
                                                        hx-confirm="Are you sure you want to delete this permission set? This action cannot be undone."
                                                        class="text-xs text-red-600 hover:text-red-800 font-medium {% if user_form.entities.field.disabled %}opacity-50 cursor-not-allowed{% endif %}"
                                                        {% if user_form.entities.field.disabled %}disabled{% endif %}>
                                                    Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% endwith %}
                        {% empty %}
                            <tr>
                                <td colspan="5" class="px-4 py-4 text-center text-gray-500">No team members found</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script>
        function updateSelectAllCheckbox(dropdownContent) {
            const selectAllCheckbox = dropdownContent.querySelector('.flowbite-select-all-checkbox');
            const individualCheckboxes = dropdownContent.querySelectorAll('.flowbite-individual-checkbox');

            if (!individualCheckboxes || individualCheckboxes.length === 0) return;

            // Update the 'Select All' checkbox state
            if (selectAllCheckbox) {
                const allChecked = Array.from(individualCheckboxes).every(cb => cb.checked);
                selectAllCheckbox.checked = allChecked;
            }
        }

        function setupDropdownLogic(container) {
            const selectAllCheckboxes = container.querySelectorAll('.flowbite-select-all-checkbox');
            const individualCheckboxes = container.querySelectorAll('.flowbite-individual-checkbox');

            // Handles case where the container itself is the dropdown menu
            let dropdownMenus;
            if (container instanceof Element && container.matches('[id^="dropdownCheckbox-"]')) {
                dropdownMenus = [container];
            } else {
                dropdownMenus = Array.from(container.querySelectorAll('[id^="dropdownCheckbox-"]'));
            }

            selectAllCheckboxes.forEach(selectAll => {
                selectAll.addEventListener('change', (event) => {
                    const dropdownContent = event.target.closest('[id^="dropdownCheckbox-"]');
                    if (!dropdownContent) return;
                    const individualCheckboxesInMenu = dropdownContent.querySelectorAll('.flowbite-individual-checkbox');
                    individualCheckboxesInMenu.forEach(checkbox => {
                        checkbox.checked = event.target.checked;
                    });
                    updateSelectAllCheckbox(dropdownContent);
                });
            });

            individualCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (event) => {
                    const dropdownContent = event.target.closest('[id^="dropdownCheckbox-"]');
                    if (!dropdownContent) return;
                    updateSelectAllCheckbox(dropdownContent);
                });
            });

            dropdownMenus.forEach(dropdownContent => {
                updateSelectAllCheckbox(dropdownContent);
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            setupDropdownLogic(document);

            // Show All Entities / All Investments for managers
            document.querySelectorAll('[name="roles"]').forEach(function(roleSelect) {
                if (roleSelect.value.includes('manager')) {
                    const form = roleSelect.closest('form');
                    if (form) {
                        const dropdownText = form.querySelector('[id^="dropdownCheckboxButton-"][id$="-entities"] span');
                        if (dropdownText) dropdownText.textContent = 'All Entities';
                        const dropdownTextInvestments = form.querySelector('[id^="dropdownCheckboxButton-"][id$="-investments"] span');
                        if (dropdownTextInvestments) dropdownTextInvestments.textContent = 'All Investments';
                    }
                }
            });

            // disable investments dropdown on load
            document.querySelectorAll('[id^="dropdownCheckboxButton-"][id$="-investments"]').forEach(function(btn) {
                btn.disabled = true;
                btn.style.opacity = 0.5;
                btn.style.cursor = 'not-allowed';
            });
        });

        document.body.addEventListener('htmx:afterSwap', (event) => {
            if (event.detail.elt) {
                setupDropdownLogic(event.detail.elt);
            }
        });

        function setupInvestmentsDropdownEnableLogic() {
            document.querySelectorAll('form[id^="user-settings-form-"]').forEach(function(form) {
                if (form.querySelector('[name="roles"]').value.includes('manager')) {
                    return;
                }
                const entityCheckboxes = form.querySelectorAll('input[name="entities"]');
                const investmentsButton = form.querySelector('[id^="dropdownCheckboxButton-"][id$="-investments"]');

                function updateInvestmentsButton() {
                    const anyChecked = Array.from(entityCheckboxes).some(cb => cb.checked);
                    if (investmentsButton) {
                        investmentsButton.disabled = !anyChecked;
                        investmentsButton.style.opacity = anyChecked ? 1 : 0.5;
                        investmentsButton.style.cursor = anyChecked ? '' : 'not-allowed';
                    }
                }
                entityCheckboxes.forEach(cb => {
                    cb.addEventListener('change', updateInvestmentsButton);
                });
                // Initial state
                updateInvestmentsButton();
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            setupInvestmentsDropdownEnableLogic();
        });

        document.body.addEventListener('htmx:afterSwap', function(event) {
            setupInvestmentsDropdownEnableLogic();
        });
    </script>
{% endblock settings_content %}
