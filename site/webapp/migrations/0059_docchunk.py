# Generated by Django 5.2.4 on 2025-07-23 20:36

import django.contrib.postgres.indexes
import django.contrib.postgres.search
import django.db.models.deletion
import pgvector.django.indexes
import pgvector.django.vector
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0058_bridgeuser_user_view_preferences'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocChunk',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('content', models.TextField(blank=True, null=True)),
                ('content_format', models.Char<PERSON>ield(default='text', max_length=256)),
                ('chunker_name', models.CharField(max_length=256)),
                ('parser_name', models.CharField(max_length=256)),
                ('ingestion_id', models.UUIDField()),
                ('page_number', models.IntegerField(default=0)),
                ('bbox_ymin', models.FloatField(default=0.0)),
                ('bbox_xmin', models.FloatField(default=0.0)),
                ('bbox_ymax', models.FloatField(default=0.0)),
                ('bbox_xmax', models.FloatField(default=0.0)),
                ('chunk_idx', models.IntegerField(default=-1)),
                ('chunk_length', models.IntegerField(default=0)),
                ('has_bounding_box', models.BooleanField(default=False)),
                ('embedding_titan_text_1024', pgvector.django.vector.VectorField(dimensions=1024)),
                ('search_vector', django.contrib.postgres.search.SearchVectorField(null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s', to='webapp.organization')),
                ('raw_document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='doc_chunks', to='webapp.rawdocument')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [pgvector.django.indexes.HnswIndex(ef_construction=64, fields=['embedding_titan_text_1024'], m=16, name='embedding_tt1024_cosine_idx', opclasses=['vector_cosine_ops']), django.contrib.postgres.indexes.GinIndex(fields=['search_vector'], name='webapp_docc_search__d15286_gin')],
            },
        ),
    ]
