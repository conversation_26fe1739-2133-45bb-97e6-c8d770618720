{% load customtags static %}
<div id="modal-container-{{ line_item.pk }}">
    <div class="w-full max-w-lg max-h-full">
        <!-- Modal content -->
        <div class="relative">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 rounded-t dark:border-gray-600">
                <div class="p-2 bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
                    <div class="Frame relative flex-col justify-start items-start flex">
                        <div class="Group relative">
                            <img alt="ImageIcon"
                                 width="38"
                                 height="20"
                                 src="{% static 'assets/images/email_icon.svg' %}">
                        </div>
                    </div>
                </div>
                <button _="on click trigger closeModal"
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                    <img src="{% static 'assets/images/close.svg' %}"
                         width="16"
                         height="19"
                         alt="closeButton">
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="px-4 py-2">
                <div class="text-[#343d36] text-lg font-semibold font-inter leading-[27px] pb-2">{{ main_message }}</div>
                <div class="text-[#616863] text-sm font-normal font-inter leading-[21px]">{{ sub_message }}</div>
            </div>
            <!-- Modal footer -->
            <div class="flex items-center p-4 border-gray-200 rounded-b dark:border-gray-600">
                <div class="flex flex-col gap-4 w-full">
                    <button type="button"
                            hx-post="{% url 'bulk_upload_historic' %}{% urlparams line_item_pk=line_item.pk %}"
                            hx-target="#modal"
                            hx-swap="outerHTML"
                            hx-trigger="click"
                            class="self-stretch h-11 w-full px-4 py-2 border text-base font-semibold border-[#924f34] text-[#924f34] rounded-lg hover:bg-[#f7f4f2] transition-colors">
                        Proceed to Bulk Upload
                    </button>
                    <button type="button"
                            hx-get="{% url 'bulk_forward' %}"
                            hx-target="#modal-container-{{ line_item.pk }}"
                            hx-swap="outerHTML"
                            hx-trigger="click"
                            class="self-stretch h-11 w-full px-4 py-2 bg-[#924f34] text-base font-semibold text-white rounded-lg hover:bg-[#8a5f47] transition-colors">
                        Proceed to Bulk Forward
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
