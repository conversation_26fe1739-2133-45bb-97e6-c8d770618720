<div id="sub-document-type-container">
    {% if valid_sub_document_types %}
        <div class="flex items-center gap-2">
            <label for="sub-document-type"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Sub Category:</label>
            <select name="sub_document_type"
                    id="sub-document-type"
                    class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                    {% if not valid_sub_document_types %}disabled{% endif %}>
                <option value="">Select Sub Document Type</option>
                {% for choice in valid_sub_document_types %}
                    <option value="{{ choice.value }}"
                            {% if choice.value == processed_doc.sub_document_type %}selected{% endif %}>
                        {{ choice.label }}
                    </option>
                {% endfor %}
            </select>
        </div>
    {% endif %}
</div>
