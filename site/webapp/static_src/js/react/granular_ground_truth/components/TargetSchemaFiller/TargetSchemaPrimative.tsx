import React, { useState, useRef, useEffect, MouseEventHandler } from "react";
import Moveable from "moveable";
import type { BoxData } from "../../types/pdfLabeler";
import { JSONSchema } from "../../types/jsonSchema";
import LabelSelectorForm from "./LabelSelectorForm";
import LabelBox from "./LabelBox";
import { Target } from "lucide-react";
import { TargetSchemaProps } from "./TargetSchemaInterface";

const TargetSchemaPrimative: React.FC<TargetSchemaProps> = ({
  schema,
  path,
  labeledBoxes,
  targetSchema,
  hideLabels,
  overlays,
  scale = 1.5,
  currentPageNumber,
  onDeleteBox,
  onLabelBox,
  extractTextFromBoundingBox,
}) => {
  const [isCreatingBox, setIsCreatingBox] = useState(false);
  const [activeBox, setActiveBox] = useState<HTMLDivElement | null>(null);
  const [moveable, setMoveable] = useState<Moveable | null>(null);
  const [boxData, setBoxData] = useState<BoxData | null>(null);
  const [isLabeled, setIsLabeled] = useState(false);
  const [boxCounter, setBoxCounter] = useState(0);
  const [isBoxVisible, setIsBoxVisible] = useState(true);

  const dragBoxRef = useRef<HTMLDivElement | null>(null);
  const isCreatingBoxRef = useRef(false);
  const dragStartRef = useRef<{ x: number; y: number } | null>(null);
  const isDraggingRef = useRef(false);

  if (typeof schema !== "object") {
    return <></>;
  }
  const overlay =
    overlays?.get(boxData?.pageNumber || currentPageNumber || 1) || null;
  // const overlayRef = useRef<HTMLDivElement | null>(overlays[boxData?.pageNumber || 1] || null);
  // useEffect(() => {
  //   if(!overlaysRef || !overlaysRef.current) return;
  //   localOverlayRef.current = overlaysRef.current[boxData?.pageNumber || 1] || null;
  // }, [overlaysRef?.current, boxData]);

  const createBox = () => {
    setIsCreatingBox(true);
    isCreatingBoxRef.current = true;
    document.body.style.cursor = "crosshair";

    // Add event listeners to the document
    document.addEventListener("mousedown", handleMouseDown);
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  const handleMouseDown = (e: MouseEvent) => {
    if (!isCreatingBoxRef.current) return;
    const target = e.target as HTMLElement;
    const clickedElement = target.closest("[data-page-number]") as HTMLElement;
    const pageNum = parseInt(clickedElement.dataset.pageNumber || "1");
    const targetOverlay = overlays.get(pageNum);
    if (!targetOverlay) return;
    const rect = targetOverlay.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    dragStartRef.current = { x, y };
    isDraggingRef.current = true;

    // Create initial drag box
    const dragBox = document.createElement("div");
    dragBox.className =
      "target-schema-box absolute border-2 border-dashed border-blue-400 bg-blue-900 bg-opacity-20 pointer-events-none z-[150]";
    dragBox.style.left = `${x}px`;
    dragBox.style.top = `${y}px`;
    dragBox.style.width = "0px";
    dragBox.style.height = "0px";

    targetOverlay.appendChild(dragBox);
    dragBoxRef.current = dragBox;
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDraggingRef.current || !dragStartRef.current || !dragBoxRef.current)
      return;

    const target = e.target as HTMLElement;
    const clickedElement = target.closest("[data-page-number]") as HTMLElement;
    const pageNum = parseInt(clickedElement.dataset.pageNumber || "1");
    const targetOverlay = overlays.get(pageNum);

    if (!targetOverlay) return;

    const rect = targetOverlay.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;

    const minX = Math.min(dragStartRef.current.x, currentX);
    const minY = Math.min(dragStartRef.current.y, currentY);
    const maxX = Math.max(dragStartRef.current.x, currentX);
    const maxY = Math.max(dragStartRef.current.y, currentY);

    dragBoxRef.current.style.left = `${minX}px`;
    dragBoxRef.current.style.top = `${minY}px`;
    dragBoxRef.current.style.width = `${maxX - minX}px`;
    dragBoxRef.current.style.height = `${maxY - minY}px`;
  };

  const handleMouseUp = (e: MouseEvent) => {
    if (!isDraggingRef.current || !dragStartRef.current || !dragBoxRef.current)
      return;

    const target = e.target as HTMLElement;
    const clickedElement = target.closest("[data-page-number]") as HTMLElement;
    const pageNum = parseInt(clickedElement.dataset.pageNumber || "1");
    const targetOverlay = overlays.get(pageNum);

    if (!targetOverlay) return;
    const rect = targetOverlay.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;

    const minX = Math.min(dragStartRef.current.x, currentX);
    const minY = Math.min(dragStartRef.current.y, currentY);
    const maxX = Math.max(dragStartRef.current.x, currentX);
    const maxY = Math.max(dragStartRef.current.y, currentY);

    const width = maxX - minX;
    const height = maxY - minY;

    // Only create box if it has meaningful size
    if (width > 10 && height > 10) {
      createMoveableBox(targetOverlay, minX, minY, width, height, pageNum);
    }

    cleanup();
  };

  const createMoveableBox = (
    overlayElement: Element,
    x: number,
    y: number,
    width: number,
    height: number,
    targetPageNumber: number,
    useScale = true,
  ) => {
    // Remove the drag box
    if (dragBoxRef.current) {
      dragBoxRef.current.remove();
      dragBoxRef.current = null;
    }
    // Create moveable box
    const boxElement = document.createElement("div");
    boxElement.className =
      "target-schema-box absolute border-2 border-dashed border-orange-400 bg-orange-900 bg-opacity-20 pointer-events-auto z-[100] cursor-move box-border";
    boxElement.style.left = `${x}px`;
    boxElement.style.top = `${y}px`;
    boxElement.style.width = `${width}px`;
    boxElement.style.height = `${height}px`;

    overlayElement.appendChild(boxElement);
    setActiveBox(boxElement);

    // Clean up existing moveable
    if (moveable) {
      moveable.destroy();
    }

    // Create new moveable
    const newMoveable = new Moveable(document.body, {
      target: boxElement,
      container: document.body,
      draggable: true,
      resizable: true,
      origin: true,
      keepRatio: false,
      edge: false,
      throttleDrag: 0,
      throttleResize: 0,
      throttleScale: 0,
      throttleRotate: 0,
    });
    const updateBoxData = () => {
      extractBoxData(boxElement, overlayElement, targetPageNumber, useScale);
    };
    newMoveable.on("drag", ({ target, left, top }) => {
      target.style.left = `${left}px`;
      target.style.top = `${top}px`;
      requestAnimationFrame(updateBoxData);
    });

    newMoveable.on("resize", ({ target, width, height, delta }) => {
      delta[0] && (target.style.width = `${width}px`);
      delta[1] && (target.style.height = `${height}px`);
      requestAnimationFrame(updateBoxData);
    });

    setMoveable(newMoveable);

    // Initial box data extraction
    updateBoxData();
  };

  const extractBoxData = async (
    boxElement: HTMLDivElement,
    overlayElement: Element,
    targetPageNumber: number,
    useScale = true,
  ) => {
    const boxRect = boxElement.getBoundingClientRect();
    const overlayRect = overlayElement.getBoundingClientRect();

    const relativeLeft = boxRect.left - overlayRect.left;
    const relativeTop = boxRect.top - overlayRect.top;
    const width = boxRect.width;
    const height = boxRect.height;

    const canvas = overlayElement.previousElementSibling as HTMLCanvasElement;

    // Use the dynamic scale from props
    const pdfLeft = relativeLeft / scale;
    const pdfTop = relativeTop / scale;
    const pdfWidth = width / scale;
    const pdfHeight = height / scale;

    try {
      // Create image binary of the selected area
      const croppedCanvas = document.createElement("canvas");
      croppedCanvas.width = width;
      croppedCanvas.height = height;
      const croppedCtx = croppedCanvas.getContext("2d");

      if (croppedCtx && canvas) {
        croppedCtx.drawImage(
          canvas,
          relativeLeft,
          relativeTop,
          width,
          height,
          0,
          0,
          width,
          height,
        );
      }

      const imageBinary = croppedCanvas.toDataURL("image/png");

      const { content } = await extractTextFromBoundingBox(
        {
          x: pdfLeft,
          y: pdfTop,
          width: pdfWidth,
          height: pdfHeight,
        },
        targetPageNumber,
      );

      const newBoxCounter = boxCounter + 1;
      setBoxCounter(newBoxCounter);

      console.log(
        `Creating new box on page ${targetPageNumber} with PDF coordinates:`,
        {
          x: pdfLeft,
          y: pdfTop,
          width: pdfWidth,
          height: pdfHeight,
        },
      );

      const newBoxData: BoxData = {
        id: newBoxCounter,
        pageNumber: targetPageNumber || 1, // Use actual current page number with fallback
        boundingBox: {
          x: parseFloat(pdfLeft.toFixed(2)),
          y: parseFloat(pdfTop.toFixed(2)),
          width: parseFloat(pdfWidth.toFixed(2)),
          height: parseFloat(pdfHeight.toFixed(2)),
        },
        content,
        imageBinary,
        element: boxElement,
        schema: schema,
        isSaved: false,
        data: null,
      };

      setBoxData(newBoxData);
    } catch (error) {
      console.error("Error extracting box data:", error);
    }
  };
  const cleanup = () => {
    setIsCreatingBox(false);
    isCreatingBoxRef.current = false;
    dragStartRef.current = null;
    document.body.style.cursor = "default";

    // Remove event listeners
    document.removeEventListener("mousedown", handleMouseDown);
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);

    // Clean up drag box
    if (dragBoxRef.current) {
      dragBoxRef.current.remove();
      dragBoxRef.current = null;
    }
  };

  const handleLabelBox = (labeledBox: BoxData) => {
    // Clean up moveable and active box
    setIsLabeled(true);
    if (moveable) {
      moveable.destroy();
      setMoveable(null);
    }

    createFixedBoxFromBoxData(labeledBox);

    if (labeledBox) {
      onLabelBox([labeledBox]);
    }
  };

  useEffect(() => {
    if (labeledBoxes && labeledBoxes.length > 0) {
      // && !boxData) {
      const targetUri =
        typeof targetSchema === "object" && targetSchema.$id
          ? targetSchema.$id
          : null;
      const prevBoxData = labeledBoxes.filter(
        (b) => b.targetUri === targetUri && b.path === path,
      );
      if (prevBoxData.length === 0) return;
      if (prevBoxData.length > 1) {
        console.log("ERROR", prevBoxData);
      }
      setBoxData(prevBoxData[0]);
      handleLabelBox(prevBoxData[0]);
      createFixedBoxFromBoxData(prevBoxData[0]);
    }
  }, [labeledBoxes, targetSchema]);

  useEffect(() => {
    setActiveBox(null);
  }, [currentPageNumber]);

  const handleDeleteBox = () => {
    if (activeBox) {
      activeBox.remove();
    }

    if (moveable) {
      moveable.destroy();
      setMoveable(null);
    }

    setActiveBox(null);
    setBoxData(null);
    if (onDeleteBox && boxData) {
      onDeleteBox([boxData]);
    }
  };

  const cancelBox = () => {
    if (activeBox) {
      activeBox.remove();
    }

    if (moveable) {
      moveable.destroy();
      setMoveable(null);
    }

    setActiveBox(null);
    setBoxData(null);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
      if (moveable) {
        moveable.destroy();
      }
    };
  }, []);

  const createFixedBox = (boxElement: HTMLElement) => {
    if (boxElement) {
      boxElement.className =
        "target-schema-box absolute border-2 border-solid border-green-400 bg-green-900 bg-opacity-20 pointer-events-auto cursor-pointer z-[100] box-border";

      // Add label display
      const labelElement = document.createElement("div");
      labelElement.className =
        "box-label absolute -top-6 left-0 bg-green-600 text-white py-0.5 px-2 text-xs rounded whitespace-nowrap pointer-events-none";
      labelElement.textContent =
        typeof schema === "object" && schema.$id
          ? schema.$id
          : "Unknown schema";
      boxElement.appendChild(labelElement);
    }
  };

  const createMoveableBoxFromBoxData = function (boxData: BoxData | null) {
    if (!overlay) return;
    if (boxData) {
      if (!overlay) {
        cleanup();
        return;
      }

      createMoveableBox(
        overlay,
        boxData.boundingBox.x * scale,
        boxData.boundingBox.y * scale,
        boxData.boundingBox.width * scale,
        boxData.boundingBox.height * scale,
        false,
      );
    }
  };

  function handleEditBox(): void {
    createMoveableBoxFromBoxData(boxData);
  }

  function hideShowBox(): void {
    if (activeBox) {
      const newVisibility = !isBoxVisible;
      setIsBoxVisible(newVisibility);

      if (newVisibility) {
        activeBox.classList.remove("hidden");
      } else {
        activeBox.classList.add("hidden");
      }
    } else if (boxData) {
      // Create moveable box if no active box exists
      setIsBoxVisible(true);
      createFixedBoxFromBoxData(boxData);
    }
  }

  function createFixedBoxFromBoxData(lBoxData: BoxData | null) {
    if (!overlays || !lBoxData) return;

    // Get the overlay for the specific page the box belongs to
    const targetOverlay = overlay;
    if (!targetOverlay) {
      console.warn(`No overlay found for page ${lBoxData.pageNumber}`);
      return;
    }

    console.log(
      `Creating box on page ${lBoxData.pageNumber} with coordinates:`,
      lBoxData.boundingBox,
      `scale: ${scale}`,
    );

    const x = lBoxData.boundingBox.x * scale;
    const y = lBoxData.boundingBox.y * scale;
    const width = lBoxData.boundingBox.width * scale;
    const height = lBoxData.boundingBox.height * scale;

    const boxElement = document.createElement("div");
    boxElement.className =
      "target-schema-box absolute border-2 border-dashed border-orange-400 bg-orange-900 bg-opacity-20 pointer-events-auto z-[100] cursor-move box-border";
    boxElement.style.left = `${x}px`;
    boxElement.style.top = `${y}px`;
    boxElement.style.width = `${width}px`;
    boxElement.style.height = `${height}px`;

    targetOverlay.appendChild(boxElement);
    if (moveable) {
      try {
        moveable.destroy();
      } catch (error) {
        console.error("Error destroying moveable:", error);
      }
      setMoveable(null);
    }
    setActiveBox(boxElement);
    createFixedBox(boxElement);
  }

  useEffect(() => {
    console.log("WHAT", activeBox, hideLabels, boxData, overlay);
    if (hideLabels) {
      // Global hide: hide the box if it exists and clear activeBox reference
      if (activeBox) {
        activeBox.remove(); // Remove from DOM instead of just hiding
        setActiveBox(null); // Clear the reference
        setIsBoxVisible(false);
      }
      // Clean up moveable if it exists
      if (moveable) {
        try {
          moveable.destroy();
        } catch (error) {
          console.error("Error destroying moveable:", error);
        }
        setMoveable(null);
      }
    } else {
      // Global show: recreate box if we have boxData but no activeBox
      if (boxData && !activeBox) {
        setIsBoxVisible(true);
        createFixedBoxFromBoxData(boxData);
      }
    }
  }, [hideLabels]);

  return (
    <div className="mb-4">
      <div className="flex items-center gap-2 mb-2">
        <Target className="w-4 h-4" />
        <span className="text-sm font-medium">
          {schema.type} - {path}
        </span>
      </div>

      {/* Takes you to new box workflow, which then takes you to new edit box. */}
      {!boxData && (
        <button
          onClick={createBox}
          disabled={isCreatingBox || activeBox !== null}
          className={`px-3 py-1 rounded text-sm ${
            isCreatingBox || activeBox !== null
              ? "bg-gray-600 text-gray-400 cursor-not-allowed"
              : "bg-blue-600 hover:bg-blue-500 text-white"
          }`}
        >
          {isCreatingBox ? "Drag to create box..." : "Create New Box"}
        </button>
      )}

      {boxData && (
        <div className="mt-2">
          {/* Only shown in edit mode */}
          <LabelBox
            boxData={boxData}
            schema={schema}
            // schemas={schemas}
            currentPageNumber={boxData.pageNumber}
            onLabelBox={handleLabelBox}
            path={path}
          />
          <button
            onClick={cancelBox}
            className="mt-2 px-3 py-1 rounded text-sm bg-red-600 hover:bg-red-500 text-white"
          >
            Cancel
            {/* only available Edit mode */}
            {/* Discards state and reloads from the database. */}
          </button>
          <button
            onClick={hideShowBox}
            className={`mt-2 px-3 py-1 rounded text-sm ${"bg-red-600 hover:bg-red-500 text-white"}`}
          >
            {isBoxVisible ? "Hide" : "Show"}
          </button>
          <button
            onClick={handleDeleteBox}
            className="mt-2 px-3 py-1 rounded text-sm bg-red-600 hover:bg-red-500 text-white"
          >
            {/* Available in edit mode only. */}
            Delete
          </button>
          <button
            onClick={handleEditBox}
            className="mt-2 px-3 py-1 rounded text-sm bg-red-600 hover:bg-red-500 text-white"
          >
            {/* Takes you to edit mode */}
            Edit Bounding Box
          </button>
        </div>
      )}
    </div>
  );
};

export default TargetSchemaPrimative;
