# Bridge Repo 

Python Django repo for powering the bridge website

# Onboarding checklist
* ~~Have a github account~~
* Have a slack account
* Have an gmail account
* Have an AWS account for the demo account (************)
* Have an AWS Account for the prod account (************)
* Create an access key for both [here](https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_access-keys.html)
* Name "prod" account as "prod" profile and "demo" account as "demo" profile, with "demo" account as "default" profile in `~/.aws/credentials`
* Install docker and rip it!

# AWS Setup
TODO: one day use [temp credentials](https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_temp.html)

1. Download the AWS Commandline [here](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html) 

2. Generate "Local Code" credentials using [this guide](https://docs.aws.amazon.com/sdkref/latest/guide/access-iam-users.html)

3. (optional) If you skipped a step and ran docker, please delete the credentials folder (docker mounts that don't exist become folders)
```
cat ~/.aws/credentials # should be a folder and get an error, otherwise ignore
rm -rf ~/.aws/credentials # careful! double check
```

4. Set up demo credentials with:
```
# for the account: ************
# region should be "us-east-1" (no quotes)
aws configure --profile default
aws configure --profile demo
```

5. Set up prod credentials with:
```
# for the account: ************
# region should be "us-east-1" (no quotes)
aws configure --profile prod
```

6. IMPORTANT: please validate your settings making sure the `prod` keys are not your default.
```
cat ~/.aws/credentials
```

# Docker environment
First install [docker desktop](https://docs.docker.com/engine/install/)

```
docker compose up --build
```

To change you running environment, use `SITE_INIT_ACTION` and `AWS_PROFILE`
The default values are `SITE_INIT_ACTION=reset` and `AWS_PROFILE=default`
`SITE_INIT_ACTION=reset`, deleted the DB, runs migrations, and creates users.

Below example does no init action:
```
SITE_INIT_ACTION=none docker compose up --build
```

Below would just run migrations:
```
SITE_INIT_ACTION=migrate docker compose up --build
```

If you want to test closer to production (no watchers, debug False, prod images, no volume mounts), run:
```
docker compose -f docker-compose.yml -f docker-compose.likeprod.yml up --build
```


# Login using:
Demo:
```
username: <EMAIL>
password: asdf
```
*important* Tom Jones is a special account where everything that happens during this users session is deleted on logout. AKA everything in the "demo" organization gets deleted when you click the log out button. It cleans up inbox rules as well.

Admin:
```
username: <EMAIL>
password: asdf
```

# Linting and formatting
```
./lint.py --fix
```

Mission 0 mypy/pyright errors command:
```
./lint.py --fix --python-types; grep -Ff <(git ls-files -m) lint.log
```

# Creating the local environment

Please only set up a local environment to ensure you have PyLance LSP working for type checking and autocomplete. All development should be done via docker compose.

Current UV Version.
```
grep "ghcr.io/astral-sh/uv" Dockerfile | awk -F: '{print $2}' | awk '{print $1}'
```

[How to install UV](https://docs.astral.sh/uv/getting-started/installation/):
```
UV_VERSION=$(grep "ghcr.io/astral-sh/uv" Dockerfile | awk -F: '{print $2}' | awk '{print $1}') curl -LsSf https://astral.sh/uv/$UV_VERSION/install.sh | less
UV_VERSION=$(grep "ghcr.io/astral-sh/uv" Dockerfile | awk -F: '{print $2}' | awk '{print $1}') curl -LsSf https://astral.sh/uv/$UV_VERSION/install.sh | sh
```

Or if you installed UV already installed:
```
uv self update $(grep "ghcr.io/astral-sh/uv" Dockerfile | awk -F: '{print $2}' | awk '{print $1}')
```

You can install a dev environment via
```
uv sync --dev
```

To install Playwright dependencies to do portal testing:
```
uv run -m patchright install --with-deps chromium
```


# Deploying to production
run from site directory
```
# RTFM
AWS_PROFILE=demo ./deploy.py --help

# Lists all deployments
AWS_PROFILE=demo ./deploy.py list

# "SSH" into a container running in production to run bash
AWS_PROFILE=demo ./deploy.py interactive SiteService
```

# How to run a retrieval adhoc
First SSH into the production container (above), then run:
```
./site/manage.py trigger_retrieval_adhoc -u "<EMAIL>" -p "CAZ Investor Flow"
```
Where the username and portal can be found from `known_user_portals`.

# Interacting locally with containers and psql

To interact with container locally for running `manage.py` (shorthand for `docker -it`)
```
./deploy.py interactive local
```

To interact with SQL directly:
```
docker ps # make sure container name is the same
docker exec -it bridge-python-db-1 bash
psql -U myuser mydatabase
```

# How to test things

## Uploading bulk excel files

The first thing you should try after logging in is creating a bulk excel upload.
Select the latest excel file from the root directory, and upload it on the link dashboard page.

If you're working on this feature, to debug excel parsing, you can run:
```
python ./site/webapp/services/bulk_upload.py
```

Validate using the admin UI. Ensure you do this process twice to account for updates.

## How to test production migrations locally.
1. Commit your changes in your feature branch.
2. (Optional) Dump the data from the server (remember to add `--run-in-prod`)
```
AWS_PROFILE=prod ./deploy.py interactive SiteService
./site/safe_ops.py --dump
```
3. Checkout main (or latest version of production code), and load the data.
```
AWS_PROFILE=prod SITE_INIT_ACTION=load docker compose up --build
```
4. Bring the service down
```
docker compose down
```
5. Checkout your feature branch 
6. (Optional) delete all deltas from your migrations folder
```
git restore --source=main site/webapp/migrations
```
7. Load your feature branch with the migration action
```
AWS_PROFILE=prod SITE_INIT_ACTION=migrate docker compose up --build
```
8. Validate your changes have been made correctly.

## running make migrations in docker container
1. interact with container locally
```
./deploy.py interactive local
```
2. creating new migrations based on changes to the model with detailed error messages
```
./site/manage.py makemigrations
```
3. apply migrations
```
./site/manage.py migrate
```
Migrations are not automatically applied. Remember to run migrate after code has been deployed by doing the above in the prod site container

## How to test email receving
1. Run the service. `docker compose up --build`
1. Setup an ngrok tunnel `AWS_PROFILE=demo ./ngrok.py`
1. Validate that the subscription has been confirmed in the logs locally.
1. Send a test email to `<EMAIL>` (use `app.bridgeinvest.io` if in `prod`)
1. Validate that you recieved log lines for the emails!

## How to run the demo
1. Run the service. `docker compose up --build`
1. Setup an nrok tunnel `AWS_PROFILE=demo ./ngrok.py` 
1. Log into the Tom Jones account on `http://localhost:8000`
1. Upload `Bridge_Investment_Template_241028.xslx` to the portal.
1. Fill in the credentials for Ayo/Alex (username and password can be anything, doesn't matter)
1. Select "Email Plugin" and "Outlook", you will need the `<EMAIL>` credentials, please ask for them to be shared.
1. Go thru the oauth approval flow for our email plugin with `<EMAIL>`
1. (optional) if someone else already tested this app, it's a partial flow with just `confirm`,  go to `https://account.microsoft.com/privacy/app-access` and delete all apps to test the flow fresh.
1. Upload any QR code image for Ayo's position (use `qr_test_code.png` in the root directory)
1. Click "Run Retrieval" for both Alex and Ayo.
1. Validate all documents are in Vault and Dashboard
1. Read all notification documents (make sure counter goes to 0)
1. Send an email to "<EMAIL>" with attachment `Bridge Buyout AP Fund I, LP_Quarterly Financial Summary Q2-2024 - Apax XI USD.pdf`, ask for a copy please.

## How to update the demo
1. Sometimes we need to update the demo. [Please reference this pr](https://github.com/BridgeInvestTech/bridge-python/pull/179)
1. First, we need to upload the documents referenced in the demo to S3. Start by running `bin/update_demo.py --local-file my_demo_file.zip --s3_folder static_demo_n --upload` (`--upload` is optional if you want to test the parsing of the zip).
1. Next, change the `site/retrieval/core/managers/demo/demo_data.csv` to have the new demo data.
1. Next, possibly change `get_demo_line_item` in `email_parse.py` to reflect the line item that will be updated over email. Sync w Ayo for the latest document they're sending over email.
1. Finally, (almost done I promise `:)`), head over to `site/webapp/static/data` and update all the export documents and the CSVs that power insights. Ask Ayo/Alex for a copy of the new values. Validate they work by downloading the documents and checking out insights. All of the `xslx` files are for downloading for the user, all of the `csv` documents are for powering insights. There's two spots we download documents, insights and dashboard. There's three states of a download, pre-retrieval (0 docs in the vault, empty dashboard/vault/insights), post-retrieval (there's N documents in the vault), and post-email (there's N+1 documents in the vault). IMPORTANT make sure you update this N value in: `site/webapp/templates/dashboard/investment_details/table.html`, `site/webapp/templates/insight/top/top.html`, and `site/webapp/views/insights.py` to make sure you're referencing the correct documents. We use the > 0 logic for the `pre-email` / `post-retrieval` documents in case someone clicks the export button before we've processed all the emails. TODO: make this N update easier with a better signal than the count.

## How to test invitations
1. Go to the Django admin portal.
1. Click "users"
1. Add a new user
1. Validate that the email has been sent

## How to load data for the 8 Line item excel file
1. Run the service, log into Tom Jones
1. Upload `Bridge_Investment_Template_8_positions_250115.xslx` to the portal
1. IMPORTANT: Please use a different login name when putting in each credential (I use "asdf1"..."asdf8" as example), this is because we don't have a great way of associating line items to the demo documents in the demo document processing task.
1. Fill out the rest of the details for the email / authenticator options, values do not matter.


## How to do a manual retrieval
1. A manual retrieval is one where someone has logged into the portal and has download the documents zipped up, and a reference excel file.
1. First upload to S3 `ENVIRONMENT=dev AWS_PROFILE=demo ./site/manage.py process_manual_retrieval -x ~/Downloads/Allvue\ Red\ Cell\ \(1\).xlsx -z ~/Downloads/Sheries\ Docs.zip`
1. Then test it locally: `AWS_PROFILE=demo USE_LOCALHOST=1 ENVIRONMENT=dev ...` with the printed command.
1. The printed command needs to be run in the service environment e.g. `AWS_PROFILE=demo ./deploy.py interactive SiteService`
1. Then run the printed command in production e.g.: `./site/manage.py process_manual_retrieval -z "s3://bridge-demo-userdata-bucket/manual_retrieval/2025-01-24_19-17-40/Sheries_Docs.zip" -x "s3://bridge-demo-userdata-bucket/manual_retrieval/2025-01-24_19-17-40/Allvue_Red_Cell_(1).xlsx"`


## How to test email MFA
1. Run the service. `SITE_INIT_ACTION=load AWS_PROFILE=prod USE_PROD_OAUTH_SECRET=1 docker compose up --build`
1. `USE_PROD_OAUTH_SECRET` ensures that we use the same Microsoft OAuth provider as production so we have permissions to setup rules on their inbox.
1. `USE_PROD_OAUTH_SECRET` is not necessary if a customer has set up manual forwarding rules, (so far only UCLA)
1. Setup an ngrok tunnel `AWS_PROFILE=prod ./bin/ngrok.py`, what this does is hook into our SNS service to forward to an ngrok endpoint, which then forwards to your localhost, allowing your localhost to listen to new emails that are sent. This will trigger our email webhook on your lacal machine.
1. Validate that the subscription has been confirmed in the logs locally.
1. Send a test email to `<EMAIL>` (use `app.bridgeinvest.io` if in `prod`), and validate that you see this email coming thru in the logs.
1. Check the Retrieval object, and make sure its in the `PENDING_LOGIN` or `BLOCKED_LOGIN_OTP` state. Use `await manager.update_login_status(Retrieval.RetrievalStatus.BLOCKED_LOGIN_OTP)` to update as needed.
1. Finally, trigger the MFA via a login! Please make note of the s3 email ID that gets sent, look for the `Processing message id` log line.
1. If there's a parsing error, you can rerun the email parsing logic (like OTP extraction) via `USE_LOCALHOST=1 AWS_PROFILE=prod ./bin/parse_emails.py --key {s3_key_from_above_logs}`. This also prints out useful extracted email stuff like the full body of the email that was sent. You can iterate on the email OTP extraction logic or general routing via this command line utility.
1. If you'd like to try out setting up the rules manually (aka setting rules up outside of `def login` you can run `USE_LOCALHOST=1 AWS_PROFILE=prod USE_PROD_OAUTH_SECRET=1 ./site/manage.py trigger_mfa_token_adhoc -u <EMAIL> -p "Fund Panel"` where the username and portal are the customer's username and portal. This will 1. set up the rule, 2. send an email to the customer, 3. destroy the rule. There is a prompt to do or skip the step so by default, if you run this code, it will only read the rules of the user using the OAuth secret. Check for `*_rules.json` files that will be saved on your computer representing the users current rules. This validates that the rules can be setup correctly.
1. REMEMBER: every login attempt triggers an MFA email to our users and then we send an email notifying it was us who were accessing their portal. Please be mindful of this when developing email portals. Happy scraping `:)` 


# Xserver
To run xserver (on my machine on mac at least :P)

This is used for developing portal integrations.

If you are using XQuartz, you may need to go into XQuartz->Settings->Security and enable "Allow network connections from clients".

```
xhost + 127.0.0.1
export DISPLAY=:0
```

# graph
```
python manage.py graph_models -a -o myapp_models.png
```

# MLflow command
```
uv run mlflow server --host 127.0.0.1 --port 1337
```

# Increase LLM Bedrock Quotas

1. [Choose a model here](https://************.us-east-1.console.aws.amazon.com/bedrock/home?region=us-east-1#/inference-profiles)
1. [Inspect Quotas here](https://************.us-east-1.console.aws.amazon.com/servicequotas/home/<USER>/bedrock/quotas)
1. [Example ticket corredpondence here](https://************.support.console.aws.amazon.com/support/home#/case/?displayId=174483134500544)

# Design Philosophy

1. Prefer Python as the first language for attempting problems 
    * typescript for CDK the only exception currently
    * Prefer python even over bash to avoid complicated control flow, use click for cmdline
    * Minimize Javascript usage on the front end: Server Side Rendering + HTMX + Hyperscript + (maybe alpine?)
1. Monolith, Monorepo for now
1. Never format the code yourself, rely on an autoformatter (ruff)
1. Linting will be set to the max, always addressed or commented out case-by-case
1. All code must be linted/formatted before going into Main.
1. Prefer end to end and integrations tests over units and mocks
1. No secrets will be checked in, all secrets will be retrieved from a separate service at boot, namely Secrets Manager.

Some reading that informed my Django opinions:
* [Django for startups](https://alexkrupp.typepad.com/sensemaking/2021/06/django-for-startup-founders-a-better-software-architecture-for-saas-startups-and-consumer-apps.html#rule1)
* [Django views the right way](https://spookylukey.github.io/django-views-the-right-way/) 
