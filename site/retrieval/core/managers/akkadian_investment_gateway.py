import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON>enerator
from pathlib import Path

import structlog
from playwright.async_api import Locator
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class AkkadianInvestmentGatewayPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "akkadian.vc" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "Akkadian Investment Gateway"),
        ]

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        login_email_locator = self.page.locator("input[data-automation-id='input-login-email']")
        await login_email_locator.click()
        await asyncio.sleep(1)
        await login_email_locator.fill(username)
        await asyncio.sleep(1)
        login_pass_locator = self.page.locator("input[data-automation-id='input-login-password']")
        await login_pass_locator.click()
        await asyncio.sleep(1)
        await login_pass_locator.fill(password)
        await asyncio.sleep(1)
        login_button_locator = self.page.locator("wb-button[data-automation-id='btn-login']")
        await login_button_locator.click()

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return False

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return await self.page.get_by_text("Investment Offerings").count() > 0

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError

        await self.page.get_by_text("My Documents").click()
        await asyncio.sleep(2)

        # expand all subfolders
        while await self.page.locator("mat-icon").filter(has_text="expand_more").count():
            await self.page.locator("mat-icon").filter(has_text="expand_more").first.click()
            await asyncio.sleep(2)

        async for doc in self._download_folder_docs():
            yield doc

    async def _download_folder_docs(self) -> AsyncGenerator[RawDocumentTuple, None]:
        doc_folders = await self.page.locator(".documents_nav-link.is-child").all()
        for folder_locator in doc_folders:
            await folder_locator.click()
            await asyncio.sleep(2)
            # we are handling folder structures on the side panel
            # look for all table rows that is not a sub folder
            table_rows = await (
                self.page.locator(".documents_documents-table tbody tr:visible")
                .filter(has_not=self.page.locator(".child-folder-name"))
                .all()
            )
            for doc_row in table_rows:
                doc_name = await doc_row.locator(".mat-column-name").all_inner_texts()
                uploaded_date_raw = await doc_row.locator(".mat-column-uploadedDate").all_inner_texts()
                uploaded_date = datetime.datetime.strptime(uploaded_date_raw[0], "%m/%d/%y, %I:%M %p")  # noqa: DTZ007
                icon_text = await self.page.locator(".breadcrumbs .item mat-icon").all_text_contents()
                bread_crumb_arr = await self.page.locator(".breadcrumbs .item").all_text_contents()
                folder_structure = "/".join(item.replace(icon_text[0], "").strip() for item in bread_crumb_arr[1:])
                metadata = json.dumps({"File Name": doc_name[0], "Folder Structure": folder_structure})
                if await self.should_download_document(metadata):
                    await doc_row.locator(".mat-column-name").click()
                    await asyncio.sleep(2)
                    download_button_container = self.page.locator("div.download-container")
                    download_button = download_button_container.locator("a")
                    file_name, content = await self.__download_doc(download_button)
                    doc = RawDocumentTuple(
                        name=file_name,
                        # placeholder since there is no date information
                        date=uploaded_date or datetime.datetime.now(),  # noqa: DTZ005
                        doc_type=self.__document_mapper(folder_structure),
                        doc_hash=self._doc_hash(metadata),
                        content=content,
                        raw_metadata=metadata,
                    )
                    if doc is not None:
                        yield doc
                    # first breadcrumb
                    await self.page.locator(".breadcrumbs .clickable").first.click()
                    await asyncio.sleep(2)

    async def __download_doc(self, download_button: Locator) -> tuple[str, bytes]:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        async with self.page.expect_download() as download_info:
            await download_button.click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.error("Unsupported file format", file_name_suffix=file_name.suffix)
            raise ValueError
        file_name.unlink()
        await asyncio.sleep(1)
        return download.suggested_filename, content

    def __document_mapper(self, page_folder_structure: str | None) -> DocumentType | str:
        if page_folder_structure is None:
            return DocumentType.UNKNOWN
        mapping = {
            "Investor Updates and Financials": DocumentType.INVESTMENT_UPDATE,
            "Annual Disclosures": DocumentType.FINANCIAL_STATEMENTS,
        }

        for mapping_text in mapping:
            if mapping_text in page_folder_structure:
                return mapping.get(mapping_text, DocumentType.UNKNOWN)

        return DocumentType.UNKNOWN
