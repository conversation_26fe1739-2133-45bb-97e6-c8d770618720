import json
from pathlib import Path
from typing import Any

import numpy as np
import pandas as pd
import structlog
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse

from webapp.models.documents import ProcessedDocument

logger = structlog.get_logger(__name__)

POST_EMAIL_INSIGHTS_DF = pd.read_csv(Path(settings.BASE_DIR) / "webapp/static/data" / "post_email_insights.csv")
PRE_EMAIL_INSIGHTS_DF = pd.read_csv(Path(settings.BASE_DIR) / "webapp/static/data" / "pre_email_insights.csv")
ASPIRIANT_INSIGHTS_DF = pd.read_csv(Path(settings.BASE_DIR) / "webapp/static/data" / "mdk_insights.csv")
SUB_SECTOR = [
    "Services",
    "FinTech",
    "Retail",
    "Medical ",
    "Data & Analytics",
    "Business  Services",
    "Services & Tech",
    "Apparel",
]
SECTOR = ["Consumer", "Technology", "Services", "Industrials", "Healthcare"]
GEOGRAPHY = ["North America", "LatAm", "Asia", "Europe", "Africa", "Middle East", "Other"]
ASSET_CLASS = [
    "Buyout",
    "Venture Capital",
    "Growth Equity",
    "Direct Investment",
    "Real Estate",
    "Private Credit",
    "Hedge Fund",
    "Other",
]


@login_required
def insight_home(request: HttpRequest) -> HttpResponse:
    return TemplateResponse(request, "insight/insight.html", make_context(request, make_filters(request)))


@login_required
def insight_dynamic(request: HttpRequest) -> HttpResponse:
    return TemplateResponse(request, "insight/dynamic.html", make_context(request, make_filters(request)))


def make_filters(request: HttpRequest) -> dict[str, str]:
    return {
        "entity": request.POST.get("entity", "__all__"),
        "asset_class": request.POST.get("asset_class", "__all__"),
        "geography": request.POST.get("geography", "__all__"),
        "sector": request.POST.get("sector", "__all__"),
        "sub_sector": request.POST.get("sub_sector", "__all__"),
    }


def make_context(request: HttpRequest, filters: dict[str, str]) -> dict[str, Any]:
    ct = ProcessedDocument.objects.for_user(request.user).count()
    if request.user.is_demo_post_email:
        logger.info("insights", ct=ct, state="post_email")
        download_url = "data/post_email_insights.xlsx"
        _insights_df = POST_EMAIL_INSIGHTS_DF
    elif request.user.is_demo_has_docs:
        logger.info("insights", ct=ct, state="pre_email")
        download_url = "data/pre_email_insights.xlsx"
        _insights_df = PRE_EMAIL_INSIGHTS_DF
    else:
        logger.info("insights", ct=ct, state="empty")
        download_url = "data/empty_insights.xlsx"
        _insights_df = POST_EMAIL_INSIGHTS_DF[np.full(POST_EMAIL_INSIGHTS_DF.shape[0], fill_value=False)]
    if request.user.organization.name == "Aspiriant":
        logger.info("insights", ct=ct, state="aspiriant")
        download_url = "data/mdk_insights_export.xlsx"
        _insights_df = ASPIRIANT_INSIGHTS_DF

    mask = np.full(_insights_df.shape[0], fill_value=True)
    for key, value in filters.items():
        if value == "__all__":
            continue
        mask &= _insights_df[key] == value
    filtered_insights_df = _insights_df[mask]

    def get_percentage(df: pd.DataFrame, group_by_column: str, value_column: str) -> list[tuple[Any, Any]]:
        total_value = df[value_column].sum()
        perc = (
            np.rint(df.groupby(group_by_column)[value_column].sum() / total_value * 1000).astype(int).astype(float) / 10
        )
        return sorted(zip(perc.index, perc, strict=True), key=lambda x: x[1], reverse=True)

    asset_class = get_percentage(filtered_insights_df, "asset_class", "current_value")
    geography = get_percentage(filtered_insights_df, "geography", "current_value")
    sector = get_percentage(filtered_insights_df, "sector", "current_value")
    sub_sector = get_percentage(filtered_insights_df, "sub_sector", "current_value")

    other_asset_classes = set(filtered_insights_df["asset_class"].unique()) - set(ASSET_CLASS)
    other_geographies = set(filtered_insights_df["geography"].unique()) - set(GEOGRAPHY)
    other_sectors = set(filtered_insights_df["sector"].unique()) - set(SECTOR)
    other_sub_sectors = set(filtered_insights_df["sub_sector"].unique()) - set(SUB_SECTOR)

    option_asset_class = sorted(ASSET_CLASS + list(other_asset_classes)) if other_asset_classes else ASSET_CLASS.copy()
    option_geography = sorted(GEOGRAPHY + list(other_geographies)) if other_geographies else GEOGRAPHY.copy()
    option_sector = sorted(SECTOR + list(other_sectors)) if other_sectors else SECTOR.copy()
    option_sub_sector = sorted(SUB_SECTOR + list(other_sub_sectors)) if other_sub_sectors else SUB_SECTOR.copy()

    return {
        "processed_doc_ct": ct,
        "option_entity": list(_insights_df["entity"].unique()),
        "option_asset_class": option_asset_class,
        "option_geography": option_geography,
        "option_sector": option_sector,
        "option_sub_sector": option_sub_sector,
        "financial_data": filtered_insights_df.replace({np.nan: ""}).to_dict(orient="records"),
        "total_value": filtered_insights_df["current_value"].sum(),
        "invested_capital": filtered_insights_df["paid_in_capital"].sum(),
        "realized": filtered_insights_df["distributions"].sum(),
        "unrealized": (filtered_insights_df["current_value"] - filtered_insights_df["distributions"]).sum(),
        "download_url": download_url,
        "chart_data": json.dumps(
            {
                "asset_class": [["Category", "Percentage"], *asset_class],
                "geography": [["Category", "Percentage"], *geography],
                "sector": [["Category", "Percentage"], *sector],
                "sub_Sector": [["Category", "Percentage"], *sub_sector],
            }
        ),
    }
