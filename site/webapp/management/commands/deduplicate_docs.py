from argparse import ArgumentParser

import structlog
from django.core.management.base import BaseCommand
from django.db.models import QuerySet
from django_extensions.management.utils import signalcommand

from webapp.models.documents import ProcessedDocument, RawDocument
from webapp.models.portal import Portal
from webapp.models.user import BridgeUser

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Recovers and manages forwarding rules"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("-u", "--user", type=str)
        parser.add_argument("-p", "--portal", type=str)
        parser.add_argument("-t", "--technique", type=str)

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002
        user = kwargs.get("user")
        portal_name = kwargs.get("portal")
        technique = kwargs.get("technique", "md5")
        if technique is None:
            technique = "md5"
        if user is None or portal_name is None:
            logger.error("User and Portal name are required to proceed")
            return

        user_obj = BridgeUser.objects.get(email=user)
        portal = Portal.objects.get(name=portal_name, organization=user_obj.organization)
        docs = RawDocument.objects.filter(
            retrieval__merged_portal_credential__portal=portal, organization=user_obj.organization
        )
        logger.info("Starting", length_docs=len(docs), technique=technique)
        if technique == "md5":
            dedupe_by_field(docs, ["md5"])
        elif technique == "doc_hash":
            dedupe_by_field(docs, ["doc_hash"])
        elif technique == "name":
            dedupe_by_field(docs, ["name", "posted_date"])


def dedupe_by_field(
    docs: QuerySet[RawDocument], fields: list[str], order_by: list[str] | tuple[str] = ("-updated_at",)
) -> None:
    distinct = docs.distinct(*fields).values_list(*fields)
    logger.info("Number of distinct", distinct_count=distinct.count())
    for distinct_vals in distinct:
        duplicates = docs.filter(**dict(zip(fields, distinct_vals, strict=True))).order_by(*order_by)
        if len(duplicates) > 1:
            logger.info("Duplicates found", distinct_vals=distinct_vals, field=distinct_vals)
            root = duplicates[0]
            shift_pd_to_root(root, duplicates)
        else:
            logger.info("No duplicates found", distinct_vals=distinct_vals, field=distinct_vals)


def shift_pd_to_root(root: RawDocument, duplicates: QuerySet[RawDocument]) -> None:
    processed_docs: list[ProcessedDocument] = []
    for dup in duplicates:
        dup_processed_docs = dup.processed_documents.all()
        processed_docs.extend(dup_processed_docs)
    for pd in processed_docs:
        pd.raw_retreival_document = root
        pd.s3_key = root.s3_key
        pd.s3_bucket = root.s3_bucket
        pd.save()
        logger.info("Associated processed document", processed_document=pd.id, root_processed_document=root.id)
    dupes = duplicates.exclude(pk=root.pk)
    has_extracted_numbers = False
    has_been_processed = False
    has_ground_truth = False
    for dup in dupes:
        has_extracted_numbers |= dup.has_extracted_numbers
        has_been_processed |= dup.has_been_processed
        has_ground_truth |= dup.has_ground_truth
        dup.has_been_processed = False
        dup.has_ground_truth = False
        dup.has_extracted_numbers = False
        dup.is_duplicate = True
        dup.save()
        logger.info("Marked doc", dup_id=dup.id)
    root.has_been_processed = has_been_processed
    root.has_ground_truth = has_ground_truth
    root.has_extracted_numbers = has_extracted_numbers
    root.is_duplicate = False
    root.save()
