{% load static customtags widget_tweaks %}
<div id="modal-container-{{ merged_portal_credential.pk }}">
    <!-- Modal content -->
    <div class="relative">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-5 rounded-t dark:border-gray-600">
            <button _="on click trigger closeModal"
                    type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                <img src="{% static 'assets/images/close.svg' %}"
                     width="16"
                     height="19"
                     alt="closeButton">
                <span class="sr-only">Close modal</span>
            </button>
        </div>
        <!-- Modal body -->
        <div class="px-5 pb-4">
            <div class="text-[#343d36] text-lg font-semibold font-inter leading-[27px] pb-4">
                Portal provider: {{ merged_portal_credential.portal.name }}
            </div>
            {% if is_portal_supported %}
                <div class="text-[#616863] text-sm font-semibold font-inter leading-[21px]">We support this portal.</div>
            {% else %}
                <div class="text-[#616863] text-sm font-semibold font-inter leading-[21px]">
                    Bridge currently does not support this portal. We are actively working to support this portal; a member of our team will reach out to confirm once complete.
                </div>
            {% endif %}
        </div>
        <!-- Modal footer -->
        <div class="flex items-center p-4 border-gray-200 rounded-b dark:border-gray-600">
            {% if is_portal_supported %}
                <button hx-get="{% url 'multi_factor_authentication_establish_connection' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
                        hx-target="#modal-container-{{ merged_portal_credential.pk }}"
                        hx-swap="outerHTML"
                        class="Buttons self-stretch h-11 w-full px-4 py-2 bg-[#924f34] rounded-lg shadow-inner justify-center items-center gap-1.5 inline-flex">
                    <div class="text-white text-base font-semibold font-inter leading-normal">Establish Connection</div>
                </button>
            {% else %}
                <button _="on click trigger closeModal"
                        class="Buttons self-stretch h-11 w-full px-4 py-2 bg-[#924f34] rounded-lg shadow-inner justify-center items-center gap-1.5 inline-flex">
                    <div class="text-white text-base font-semibold font-inter leading-normal">Close</div>
                </button>
            {% endif %}
        </div>
    </div>
</div>
