import pytest
from django.core.exceptions import ValidationError

from webapp.models.user import BridgeUser, Organization, Role

pytestmark = pytest.mark.django_db


def test_create_normal_user(test_org: Organization, test_role: Role) -> None:
    """Test creating a normal user with valid data"""
    user = BridgeUser.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=str(test_org.id),
        roles=[str(test_role.id)],
        password="testpass123",  # noqa: S106
        contact_number="+1234567890",
    )

    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert user.first_name == "Test"
    assert user.last_name == "User"
    assert user.organization == test_org
    assert test_role in user.roles.all()
    assert user.invitation_token is not None
    assert not user.is_admin
    assert user.is_active
    assert user.check_password("testpass123")


def test_create_superuser(test_org: Organization, test_role: Role) -> None:
    """Test creating a superuser"""
    superuser = BridgeUser.objects.create_superuser(
        username="admin",
        email="<EMAIL>",
        first_name="Admin",
        last_name="User",
        organization=str(test_org.id),
        roles=[str(test_role.id)],
        password="adminpass123",  # noqa: S106
    )

    assert superuser.is_admin
    assert superuser.is_superuser
    assert superuser.is_staff


def test_user_str_representation(test_org: Organization, test_role: Role) -> None:
    """Test the string representation of a user"""
    user = BridgeUser.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=str(test_org.id),
        roles=[str(test_role.id)],
        password="testpass123",  # noqa: S106
    )

    assert str(user) == "testuser"


def test_is_demo_property(demo_org: Organization, test_org: Organization, test_role: Role) -> None:
    """Test the is_demo property"""
    demo_user = BridgeUser.objects.create_user(
        username="demouser",
        email="<EMAIL>",
        first_name="Demo",
        last_name="User",
        organization=str(demo_org.id),
        roles=[str(test_role.id)],
        password="demopass123",  # noqa: S106
    )

    regular_user = BridgeUser.objects.create_user(
        username="regularuser",
        email="<EMAIL>",
        first_name="Regular",
        last_name="User",
        organization=str(test_org.id),
        roles=[str(test_role.id)],
        password="regularpass123",  # noqa: S106
    )

    assert demo_user.is_demo
    assert not regular_user.is_demo


def test_invalid_contact_number(test_org: Organization, test_role: Role) -> None:
    """Test that invalid contact numbers are rejected"""
    user = BridgeUser.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=str(test_org.id),
        roles=[str(test_role.id)],
        password="testpass123",  # noqa: S106
        contact_number="invalid-number",
    )
    with pytest.raises(ValidationError):
        user.full_clean()


def test_create_user_without_email(test_org: Organization, test_role: Role) -> None:
    """Test that creating a user without email raises ValueError"""
    with pytest.raises(ValueError):  # noqa: PT011
        BridgeUser.objects.create_user(
            username="testuser",
            email="",
            first_name="Test",
            last_name="User",
            organization=str(test_org.id),
            roles=[str(test_role.id)],
            password="testpass123",  # noqa: S106
        )


def test_user_with_multiple_roles(test_org: Organization) -> None:
    """Test that a user can have multiple roles"""
    role1 = Role.objects.create(name=Role.RoleName.MANAGER)
    role2 = Role.objects.create(name=Role.RoleName.VIEWER)

    user = BridgeUser.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=str(test_org.id),
        roles=[str(role1.id), str(role2.id)],
        password="testpass123",  # noqa: S106
    )

    assert user.roles.count() == 2
    assert role1 in user.roles.all()
    assert role2 in user.roles.all()
