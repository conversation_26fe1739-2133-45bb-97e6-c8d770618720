{% extends "admin/base_site.html" %}
{% load customtags %}
{% block title %}
    {{ retrievals.0.manager }} - Bootstrap Backfill
{% endblock title %}
{% block content %}
    <form method="post"
          action="{% url 'admin:webapp_bootstrap_mpc' %}?pk={{ merged_portal_credential.pk }}">
        {% csrf_token %}
        <button type="submit" name="submit" value="refresh-reduce">STEP 1: REFRESH PREDICTION STATUS</button>
        <h1>Prediction Status</h1>
        <button type="button"
                data-js-do-hide-show
                data-js-do-hide-show-target="#prediction"></button>
        <div id="prediction">
            {% for retrieval in retrievals %}
                <table>
                    <thead>
                        <tr>
                            <td>Key</td>
                            <td>Value</td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Number of documents predicted</td>
                            <td>{{ state.id2messages|length }}</td>
                        </tr>
                    </tbody>
                </table>
            {% endfor %}
        </div>
        <br />
        <br />
        <h1>Retrieval</h1>
        <button type="button"
                data-js-do-hide-show
                data-js-do-hide-show-target="#retrieval"></button>
        <div id="retrieval">
            {% for retrieval in retrievals %}
                <table>
                    <thead>
                        <tr>
                            <td>Key</td>
                            <td>Value</td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Status</td>
                            <td>{% label_lookup label=retrieval.retrieval_status enum=RetrievalStatus %}</td>
                        </tr>
                        <tr>
                            <td>Documents Retrieved</td>
                            <td>{{ retrieval.number_documents_retrieved }}</td>
                        </tr>
                        <tr>
                            <td>Link</td>
                            <td>
                                <a href="{% url 'admin:webapp_retrieval_change' object_id=retrieval.pk %}">Link</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            {% endfor %}
        </div>
        <br />
        <br />
        <h1>Line Items</h1>
        <button type="button"
                data-js-do-hide-show
                data-js-do-hide-show-target="#line_items"></button>
        <div id="line_items">
            <h3>Real Line Items</h3>
            <table>
                <thead>
                    <td>Client</td>
                    <td>Entity</td>
                    <td>Investment</td>
                    <td>Link</td>
                    <td>Action</td>
                </thead>
                {% for li in real_line_items %}
                    <tr>
                        <td>
                            <input readonly
                                   class="hidden"
                                   type="text"
                                   value="{{ li.pk }}"
                                   name="real_line_items[{{ forloop.counter0 }}][pk]" />
                            <input readonly
                                   size="50"
                                   type="text"
                                   value="{{ li.investing_entity.client.legal_name }}"
                                   name="real_line_items[{{ forloop.counter0 }}][client_investor_name]" />
                        </td>
                        <td>
                            <input readonly
                                   size="75"
                                   type="text"
                                   value="{{ li.investing_entity.legal_name }}"
                                   name="real_line_items[{{ forloop.counter0 }}][investing_entity_name]" />
                        </td>
                        <td>
                            <input readonly
                                   size="75"
                                   type="text"
                                   value="{{ li.investment.legal_name }}"
                                   name="real_line_items[{{ forloop.counter0 }}][investment_name]" />
                        </td>
                        <td>
                            <a href="{% url 'admin:webapp_lineitem_change' object_id=li.pk %}">Link</a>
                        </td>
                        <td>
                            <button type="submit"
                                    name="submit"
                                    value="delete_real_line_item[{{ forloop.counter0 }}]">Delete</button>
                        </td>
                    </tr>
                {% endfor %}
            </table>
            <br />
            <br />
            <button type="submit" name="submit" value="predict_line_items">STEP 2: Predict Line Items</button>
            <br />
            <br />
            <h3>Predicted Line Items</h3>
            <table>
                <thead>
                    <td>Client</td>
                    <td>Entity</td>
                    <td>Investment</td>
                    <td>Action</td>
                </thead>
                {% for li in state.line_items.accurate_line_items %}
                    <tr>
                        <td>
                            <input type="text"
                                   size="50"
                                   value="{{ li.client_investor_name }}"
                                   name="good_line_items[{{ forloop.counter0 }}][client_investor_name]" />
                        </td>
                        <td>
                            <input type="text"
                                   size="75"
                                   value="{{ li.investing_entity_name }}"
                                   name="good_line_items[{{ forloop.counter0 }}][investing_entity_name]" />
                        </td>
                        <td>
                            <input type="text"
                                   size="75"
                                   value="{{ li.investment_name }}"
                                   name="good_line_items[{{ forloop.counter0 }}][investment_name]" />
                            <input type="hidden"
                                   name="good_line_items[{{ forloop.counter0 }}][line_item_confidence]"
                                   value="medium_confidence" />
                        </td>
                        <td>
                            <button type="submit"
                                    name="submit"
                                    value="save_line_item[{{ forloop.counter0 }}]">Save</button>
                            <button type="submit"
                                    name="submit"
                                    value="delete_line_item[{{ forloop.counter0 }}]">Delete</button>
                            <button type="submit"
                                    name="submit"
                                    value="promote_real_line_item[{{ forloop.counter0 }}]">STEP 3: Promote</button>
                        </td>
                    </tr>
                {% endfor %}
            </table>
            <br />
            <input type="text"
                   size="50"
                   name="new_line_items[client_investor_name]"
                   placeholder="Client" />
            <input type="text"
                   size="75"
                   name="new_line_items[investing_entity_name]"
                   placeholder="Entity" />
            <input type="text"
                   size="75"
                   name="new_line_items[investment_name]"
                   placeholder="Investment" />
            <input type="hidden"
                   name="new_line_items[line_item_confidence]"
                   value="medium_confidence" />
            <button type="submit" name="submit" value="add_line_item">Add Line Item</button>
            <br />
            <h3>Wrong Line Items</h3>
            <table>
                <thead>
                    <td>Client</td>
                    <td>Entity</td>
                    <td>Investment</td>
                    <td>Action</td>
                </thead>
                {% for li in state.line_items.wrong_line_items %}
                    <tr>
                        <td>
                            <input readonly
                                   size="50"
                                   type="text"
                                   value="{{ li.client_investor_name }}"
                                   name="wrong_line_items[{{ forloop.counter0 }}][client_investor_name]" />
                        </td>
                        <td>
                            <input readonly
                                   size="75"
                                   type="text"
                                   value="{{ li.investing_entity_name }}"
                                   name="wrong_line_items[{{ forloop.counter0 }}][investing_entity_name]" />
                        </td>
                        <td>
                            <input readonly
                                   size="75"
                                   type="text"
                                   value="{{ li.investment_name }}"
                                   name="wrong_line_items[{{ forloop.counter0 }}][investment_name]" />
                            <input type="hidden"
                                   name="wrong_line_items[{{ forloop.counter0 }}][line_item_confidence]"
                                   value="medium_confidence" />
                        </td>
                        <td>
                            <button type="submit"
                                    name="submit"
                                    value="promote_line_item[{{ forloop.counter0 }}]">Promote</button>
                            <button type="submit"
                                    name="submit"
                                    value="delete_permanently_line_item[{{ forloop.counter0 }}]">Perm Delete</button>
                        </td>
                    </tr>
                {% endfor %}
            </table>
        </div>
        <br />
        <br />
        <h1>Documents</h1>
        <button type="button"
                data-js-do-hide-show
                data-js-do-hide-show-target="#raw_docs"></button>
        <div id="raw_docs">
            <h3>Predict Shortcuts</h3>
            <button type="submit" name="submit" value="predict_documents">STEP 4: Predict All Documents</button>
            <h3>View Shortcuts</h3>
            <a href="{% url 'admin:webapp_rawdocument_changelist' %}{% urlparams organization__id__exact=merged_portal_credential.organization.id retrieval__merged_portal_credential__portal__name=merged_portal_credential.portal.name %}">
                View All Documents
            </a>
            <h3>Predicted Documents Links</h3>
            <table>
                <thead>
                    <td>Predicted Line Item</td>
                    <td>Predicted Doc Type</td>
                    <td>Link</td>
                </thead>
                <tbody>
                    {% for li, doc_dic in li2doc2pk.items %}
                        {% for doc_type, pks_li in doc_dic.items %}
                            <tr>
                                <td>{{ li }}</td>
                                <td>{{ doc_type }}</td>
                                <td>
                                    <a href="{% url 'admin:webapp_rawdocument_changelist' %}?id__in={{ pks_li|join:',' }}">
                                        View Documents ({{ pks_li|length }})
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <br />
        <br />
        <h1>State</h1>
        <button type="button"
                data-js-do-hide-show
                data-js-do-hide-show-hidden
                data-js-do-hide-show-target="#state"></button>
        <div id="state">
            <table>
                <thead>
                    <td>Key</td>
                    <td>Value</td>
                </thead>
                {% for key, value in state_items %}
                    <tr>
                        <td>{{ key }}</td>
                        <td>
                            <code>{{ value|tojson }}</code>
                        </td>
                    </tr>
                {% endfor %}
            </table>
        </div>
    </form>
    <script>
        document.querySelectorAll('[data-js-do-hide-show ]').forEach(function(el) {
            const show_text = el.getAttribute('data-js-do-hide-show-show-text') || 'Show';
            const hide_text = el.getAttribute('data-js-do-hide-show-hide-text') || 'Hide';
            const target = el.getAttribute('data-js-do-hide-show-target');
            const initial_state_hidden = el.hasAttribute('data-js-do-hide-show-hidden') ? true : false;
            document.querySelectorAll(target).forEach(function(target_el) {
                target_el.classList.toggle('hidden', initial_state_hidden);
            });
            el.innerHTML = initial_state_hidden ? show_text : hide_text;
            // target_el.innerHTML = target_el.classList.contains('hidden') ? show_text : hide_text;
            el.addEventListener('click', function() {
                document.querySelectorAll(target).forEach(function(target_el) {
                    target_el.classList.toggle('hidden');
                    el.innerHTML = target_el.classList.contains('hidden') ? show_text : hide_text;
                });
            });
        });
    </script>
{% endblock content %}
