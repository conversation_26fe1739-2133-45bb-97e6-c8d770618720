{% load customtags %}
<script type="text/javascript">
    var go_from_select = function(opt) {
        window.location = window.location.pathname + opt
    };
</script>
<style>
.custom-dropdown-form {
    width: 95%;
    margin-left: 2%;
}
</style>
<h3>By {{ title }}</h3>
<ul class="admin-filter-{{ title|cut:' ' }}">
    {% if choices|slice:"4:" %}
        <li>
            <select class="form-control custom-dropdown-form"
                    onchange="go_from_select(this.options[this.selectedIndex].value)">
                {% for choice in choices %}
                    {% if not choice.display|endswith:"(0)" %}
                        <option {% if choice.selected %}selected="selected"{% endif %}
                                value="{{ choice.query_string|iriencode }}">{{ choice.display }}</option>
                    {% endif %}
                {% endfor %}
            </select>
        </li>
    {% else %}
        {% for choice in choices %}
            <li {% if choice.selected %}class="selected"{% endif %}>
                <a href="{{ choice.query_string|iriencode }}">{{ choice.display }}</a>
            </li>
        {% endfor %}
    {% endif %}
</ul>
