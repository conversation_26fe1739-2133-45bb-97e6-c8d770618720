import structlog
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse
from django.urls import reverse

from webapp.models import Retrieval
from webapp.views.utils import (
    add_hx_trigger_header,
    get_merged_portal_credential_from_url_params,
)

logger = structlog.get_logger(__name__)


@login_required
def modal(request: HttpRequest) -> HttpResponse:
    merged_portal_credential = get_merged_portal_credential_from_url_params(request)

    if request.method == "GET":
        template = "modal.html"
        default_initial_template = "link/error_screen/error_modal.html"
        modal_include_template = request.GET.get("modal_include_template", default_initial_template)
        headers = add_hx_trigger_header(
            {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}}
        )
        if (
            merged_portal_credential.last_user_login_validation_retrieval is None
            or merged_portal_credential.last_user_login_validation_retrieval.user_login_status
            in {
                Retrieval.UserLoginStatus.NOT_STARTED,
                Retrieval.UserLoginStatus.SUCCESS_LOGGED_IN,
            }
        ):
            raise ValueError

        retrieval_user_login_status = merged_portal_credential.last_user_login_validation_retrieval.user_login_status
        error_url = None
        hx_select = None
        if retrieval_user_login_status == Retrieval.UserLoginStatus.INVALID_LOGIN_URL:
            error_message = "Invalid portal URL. Please edit the URL."
            error_url = reverse("add_portal") + f"?portal_pk={merged_portal_credential.portal.pk}"
            hx_select = "#modal-container-add-portal"
            is_user_fixable = True

        elif retrieval_user_login_status == Retrieval.UserLoginStatus.INVALID_CREDENTIALS:
            error_url = reverse("credentials_manage") + f"?merged_portal_credential_pk={merged_portal_credential.pk}"
            error_message = "You input the wrong credentials. Please enter your credentials."
            hx_select = f"#modal-container-{merged_portal_credential.pk}"
            is_user_fixable = True

        elif retrieval_user_login_status == Retrieval.UserLoginStatus.INVALID_MFA_TYPE:
            error_url = (
                reverse("multi_factor_authentication_modal")
                + f"?merged_portal_credential_pk={merged_portal_credential.pk}"
            )
            # TODO: should we mark which MFA type is available?
            hx_select = f"#modal-container-{merged_portal_credential.pk}"
            error_message = "The wrong MFA type was selected, please check your MFA settings."
            is_user_fixable = True

        elif retrieval_user_login_status == Retrieval.UserLoginStatus.MFA_NOT_RECEIVED:
            error_message = (
                "MFA Integration was not successful. A team member will reach out to help you resolve this issue."
            )
            is_user_fixable = False
        else:
            # We should not get to this state.
            raise ValueError

        if is_user_fixable and error_url is None and hx_select is None:
            # We should never get here. Fix code paths.
            raise ValueError

        return TemplateResponse(
            request,
            template,
            {
                "merged_portal_credential": merged_portal_credential,
                "modal_include_template": modal_include_template,
                "error_url": error_url,
                "error_message": error_message,
                "hx_select": hx_select,
                "is_user_fixable": is_user_fixable,
            },
            headers=headers,
        )
    return HttpResponse(status=405)
