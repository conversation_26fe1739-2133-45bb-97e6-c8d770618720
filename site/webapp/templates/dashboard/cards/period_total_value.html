{% load customtags %}
<div class="h-44 p-6 bg-white rounded-xl border border-[#eceee8] flex-col justify-start items-start gap-6 inline-flex">
    <div class="flex flex-row justify-between w-full">
        <div class="text-[#343d36] text-base font-semibold font-inter leading-normal">
            Total Value as of <span>
            {% if period_date is None %}
                -
            {% else %}
                {{ period_date|date:"F d, Y" }}
            {% endif %}
        </span>
    </div>
    <div class="flex items-center">
        <span class="text-[#679657] text-sm font-medium font-inter leading-[21px]">
            {% if period_investment_ct is None %}
                0
            {% else %}
                {{ period_investment_ct }}
            {% endif %}
        </span>
        <span class="text-[#616863] text-sm font-medium font-inter leading-[21px] ml-1">investments</span>
    </div>
</div>
<div class="self-stretch h-[76px] flex-col justify-start items-start gap-4 flex">
    {% if period_total_value is None %}
        {% alias "0" as clean_total_value %}
    {% else %}
        {% alias period_total_value as clean_total_value %}
    {% endif %}
    <div data-value="{{ clean_total_value }}"
         class="self-stretch text-[#343d36] text-[28px] font-extrabold font-nanum leading-[39.20px]">
        {{ clean_total_value|format_as_currency }}
    </div>
</div>
</div>
