{% load customtags %}
{% for row in financial_data %}
    <tr class="text-white">
        <!-- First column (Holding) -->
        <td class="TableCell border text-xs font-medium p-1 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">{{ row.holding }}</div>
        </td>
        <td class="TableCell border text-xs font-medium p-1 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">{{ row.fund }}</div>
        </td>
        <td class="TableCell border text-xs font-medium p-1 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">{{ row.entity }}</div>
        </td>
        <td class="TableCell border text-xsfont-medium p-3 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">{{ row.investment_year }}</div>
        </td>
        <td class="TableCell border text-xsfont-medium p-3 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">{{ row.asset_class }}</div>
        </td>
        <td class="TableCell border text-xsfont-medium p-3 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">{{ row.sector }}</div>
        </td>
        <td class="TableCell border text-xsfont-medium p-3 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">{{ row.sub_sector }}</div>
        </td>
        <td class="TableCell border text-xsfont-medium p-3 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">{{ row.geography }}</div>
        </td>
        <td class="TableCell border text-xsfont-medium p-3 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                {{ row.current_value|format_as_currency }}
            </div>
        </td>
        <td class="TableCell border text-xsfont-medium p-3 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                {{ row.distributions|format_as_currency }}
            </div>
        </td>
        <td class="TableCell border text-xsfont-medium p-3 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                {{ row.paid_in_capital|format_as_currency }}
            </div>
        </td>
        <td class="TableCell border text-xsfont-medium p-3 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">{{ row.dpi }}</div>
        </td>
        <td class="TableCell border text-xsfont-medium p-3 overflow-hidden">
            <div class="Text text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">{{ row.tvpi }}</div>
        </td>
    </tr>
{% endfor %}
