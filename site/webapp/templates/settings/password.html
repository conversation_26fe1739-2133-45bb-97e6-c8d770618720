{% extends "settings/base_settings.html" %}
{% load static %}
{% block settings_content %}
    <div class="py-9 px-12">
        {% if messages %}
            <div>
                {% for message in messages %}
                    {% if 'settings_password' in message.tags %}
                        <div class=" px-4 py-3 rounded mb-4 {% if 'success' in message.tags %} bg-green-100 border border-green-400 text-green-700 {% elif 'error' in message.tags %} bg-red-100 border border-red-400 text-red-700 {% endif %} ">
                            {{ message }}
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        {% endif %}
        <form method="post"
              action="{% url 'settings_password' %}"
              hx-trigger="keyup, load"
              hx-post="{% url 'password_validators' %}"
              hx-target="#password-reset-validators"
              hx-swap="innerHTML"
              class="w-full">
            {% csrf_token %}
            <div class="flex items-start space-x-2 pt-6">
                <label for="current_password"
                       class="w-1/5 text-base font-medium text-gray-800">Current Password</label>
                <div class="w-2/5">
                    <input type="password"
                           id="current_password"
                           name="current_password"
                           placeholder="Current Password"
                           class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none text-base" />
                    {% if form.current_password.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.current_password.errors|join:", " }}</p>
                    {% endif %}
                </div>
            </div>
            <div class="flex items-start space-x-2 pt-6">
                <label for="new_password" class="w-1/5 text-base font-medium text-gray-800">New Password</label>
                <div class="w-2/5 relative">
                    <!-- Password Input -->
                    <input type="password"
                           id="password1"
                           name="password1"
                           placeholder="New Password"
                           class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none" />
                    <!-- Visibility Toggle Icon -->
                    <button type="button"
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600"
                            tabindex="-1"
                            onclick="togglePasswordVisibility('password1', 'toggleIcon1')">
                        <img id="toggleIcon1"
                             src="{% static 'assets/images/hide_icon.svg' %}"
                             alt="Toggle visibility"
                             width="20"
                             height="20">
                    </button>
                    {% if form.new_password.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.new_password.errors|join:", " }}</p>
                    {% endif %}
                </div>
            </div>
            <div class="flex items-start space-x-2 pt-6">
                <label for="password2" class="w-1/5 text-base font-medium text-gray-800">Confirm Password</label>
                <div class="w-2/5 relative">
                    <!-- Password Input -->
                    <input type="password"
                           id="password2"
                           name="password2"
                           placeholder="Confirm Password"
                           class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none" />
                    <!-- Visibility Toggle Icon -->
                    <button type="button"
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600"
                            tabindex="-1"
                            onclick="togglePasswordVisibility('password2', 'toggleIcon2')">
                        <img id="toggleIcon2"
                             src="{% static 'assets/images/hide_icon.svg' %}"
                             alt="Toggle visibility"
                             width="20"
                             height="20">
                    </button>
                    {% if form.confirm_password.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.confirm_password.errors|join:", " }}</p>
                    {% endif %}
                </div>
            </div>
            <div class="pt-5" id="password-reset-validators"></div>
            <input hidden type="text" name="button_text" value="Change Password" />
        </form>
    </div>
{% endblock settings_content %}
