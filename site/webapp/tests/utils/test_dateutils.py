import datetime

from freezegun import freeze_time

from webapp.utils.dateutils import pretty_date


@freeze_time("2024-03-27 12:00:00")
def test_pretty_date() -> None:
    """Test pretty date formatting."""
    # Test "just now"
    now = datetime.datetime(2024, 3, 27, 12, 0, 0, tzinfo=datetime.UTC)
    assert pretty_date(now) == "just now"

    # Test seconds
    seconds_ago = datetime.datetime(2024, 3, 27, 11, 59, 30, tzinfo=datetime.UTC)
    assert pretty_date(seconds_ago) == "30 seconds ago"

    # Test minutes
    minute_ago = datetime.datetime(2024, 3, 27, 11, 59, 0, tzinfo=datetime.UTC)
    assert pretty_date(minute_ago) == "a minute ago"

    # Test hours
    hours_ago = datetime.datetime(2024, 3, 27, 10, 0, 0, tzinfo=datetime.UTC)
    assert pretty_date(hours_ago) == "2 hours ago"

    # Test yesterday
    yesterday = datetime.datetime(2024, 3, 26, 12, 0, 0, tzinfo=datetime.UTC)
    assert pretty_date(yesterday) == "Yesterday"

    # Test days
    days_ago = datetime.datetime(2024, 3, 23, 12, 0, 0, tzinfo=datetime.UTC)
    assert pretty_date(days_ago) == "4 days ago"
