import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON>enerator
from pathlib import Path

import structlog
from playwright.async_api import Locator, expect
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import Document<PERSON>ash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class GoldmanPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "goldman.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "<PERSON>"),
        ]

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        await self.page.get_by_placeholder("Username").count()
        await self.page.get_by_placeholder("Username").fill(username)
        await asyncio.sleep(2)
        await self.page.get_by_placeholder("Password").count()
        await self.page.get_by_placeholder("Password").fill(password)
        await asyncio.sleep(2)
        remember_username_locator = self.page.locator("[data-qa-comp='pwm-checkbox']")
        if not await remember_username_locator.locator("input").is_checked():
            await remember_username_locator.locator("span.text").filter(has_text="Remember username").click()
        await asyncio.sleep(2)
        await self.page.locator("button[type='submit']").click()
        await asyncio.sleep(5)

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(2)
        return await self.page.get_by_text("Verify Your Identity").count() > 0

    async def _send_otp(self) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.locator("span").filter(has_text="Text message").click()
        await asyncio.sleep(2)
        await self.page.get_by_text("Confirm").click()
        await asyncio.sleep(2)

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.get_by_placeholder("Identification Code").fill(otp)
        await self.page.get_by_text("Submit").click()

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return (
            await self.page.get_by_placeholder("Username").count()
            + await self.page.get_by_placeholder("Password").count()
            + await self.page.get_by_text("Verify Your Identity").count()
            == 0
        )

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        await self._navigate_to_and_load_all_docs()
        seen_row_ids = set()
        rows_container = self.page.locator("div.ag-center-cols-container")
        unseen_rows = await self._create_unseen_rows(rows_container, seen_row_ids)
        while unseen_rows:
            row_id = unseen_rows.pop(0)
            row = rows_container.locator(f"div[role='row'][row-index='{row_id}']")
            if await row.count() != 1 or not await row.is_visible():
                logger.info("Row not visible or distinct", row_id=row_id)
            await row.evaluate("""
            el => {
                const rect = el.getBoundingClientRect();
                window.scrollBy({ top: rect.top - window.innerHeight / 2,
                behavior: 'instant' });
            }
            """)
            await asyncio.sleep(2)
            row = rows_container.locator(f"div[role='row'][row-index='{row_id}']")
            await row.wait_for(state="visible")
            await row.hover()
            seen_row_ids.add(row_id)

            # only process documents (not folder or header rows)
            spans = await row.locator("span").all()
            span_attributes = [await span.get_attribute("class") for span in spans]
            if "icon--pdf" in span_attributes:
                row_metadata = await self._get_row_metadata(row)
                metadata = json.dumps(row_metadata)
                action_cell = row.locator("div[col-id='action']")
                action_cell_show_on_hover = action_cell.locator("div.show-on-hover-flex")
                download_button = action_cell_show_on_hover.locator("div[data-action='download']")
                try:
                    await download_button.wait_for(state="visible", timeout=2000)
                except:  # noqa: E722
                    logger.info("Download button not visible for row", row_id=row_id)
                    continue
                if await self.should_download_document(metadata):
                    try:
                        content = await self._download_doc(download_button)
                        doc_type = self._document_mapper(row_metadata["Doc Type"], row_metadata["Name"])
                        datetime_date = datetime.datetime.strptime(row_metadata["Date"], "%b %d, %Y")  # noqa: DTZ007
                        doc = RawDocumentTuple(
                            name=row_metadata["Name"],
                            date=datetime_date,
                            doc_type=doc_type,
                            content=content,
                            raw_metadata=metadata,
                            doc_hash=self._doc_hash(metadata),
                        )
                        if doc is not None:
                            yield doc
                    except Exception:
                        logger.exception("Could not download document for row", row_id=row_id)

            unseen_rows = await self._create_unseen_rows(rows_container, seen_row_ids)

    async def _navigate_to_and_load_all_docs(self) -> None:
        if self.page is None:
            raise ManagerNotInitializedError

        toast_locator = self.page.locator("[data-gs-pwm-component='toast']")
        if await toast_locator.count() > 0:
            await toast_locator.locator("button[aria-label='Close']").click()

        await self.page.locator("li.menu-item").filter(has_text="Documents").click()

        await asyncio.sleep(5)
        await self.page.wait_for_selector("div.docking-container", state="visible")

        # set filter for all documents and expand all folders
        clear_filters_locator = self.page.get_by_text("Clear filters")
        if await clear_filters_locator.count() > 0:
            await clear_filters_locator.click()
            await asyncio.sleep(5)
            await self.page.wait_for_selector("div.docking-container", state="visible")
        expand_all_locator = self.page.locator("span[data-original-title='Expand groups']")
        try:
            await expand_all_locator.wait_for(state="visible", timeout=30000)
            await expand_all_locator.click()
        except:
            logger.exception("Expand all folders button not found, many documents may not be visible")
            raise
        await asyncio.sleep(5)
        await self.page.wait_for_selector("div.docking-container", state="visible")

        # load all documents
        see_more_locator = self.page.get_by_text("See More")
        while True:
            try:
                await expect(see_more_locator).to_have_count(1, timeout=5000)
                await see_more_locator.click()
            except:  # noqa: E722
                logger.info("expanded all see more")
                break
        await self.page.evaluate("window.scrollTo(0, 0)")

        await asyncio.sleep(5)

    async def _create_unseen_rows(self, table: Locator, seen: set[str]) -> list[str]:
        row_ids = []
        for e in await table.locator("div[role='row']").all():
            if not await e.is_visible():
                continue
            row_id = await e.get_attribute("row-index")
            if row_id is None:
                continue
            if row_id not in seen:
                row_ids.append(row_id)
        row_ids.sort(key=int)
        return row_ids

    async def _get_row_metadata(self, row: Locator) -> dict[str, str]:
        row_metadata = {}
        legal_registration_cell = row.locator("div[col-id='legalRegistration']")
        action_cell = row.locator("div[col-id='action']")

        # legal registration cell metadata
        account_holder_cell_details = legal_registration_cell.locator("div.account-holder-cell__details")
        truncated_texts_1 = account_holder_cell_details.locator("div.text-truncate")
        name_raw = await truncated_texts_1.nth(0).inner_text()
        legal_registration_raw = await truncated_texts_1.nth(1).inner_text()
        sub_account_raw = await truncated_texts_1.nth(2).inner_text()
        row_metadata["Name"] = name_raw
        row_metadata["Legal Registration"] = legal_registration_raw
        row_metadata["Sub Account"] = sub_account_raw

        product_cell = truncated_texts_1.nth(3)
        if await product_cell.count() > 0:
            product_raw = await product_cell.inner_text()
            row_metadata["Product"] = product_raw

        # action cell metadata
        action_cell_hide_on_hover = action_cell.locator("div.hide-on-hover")
        truncated_texts_2 = action_cell_hide_on_hover.locator("div.text-truncate")
        doc_type_raw = await truncated_texts_2.nth(0).inner_text()
        date_raw = await truncated_texts_2.nth(1).inner_text()
        row_metadata["Doc Type"] = doc_type_raw
        row_metadata["Date"] = date_raw

        return row_metadata

    async def _download_doc(self, download_button: Locator) -> bytes:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        async with self.page.expect_download() as download_info:
            await download_button.click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        with Path.open(file_name, "rb") as pdf_file:
            content = pdf_file.read()
        file_name.unlink()
        return content

    def _document_mapper(self, document_type: str | None, name: str | None) -> DocumentType | str:
        if document_type is None:
            return DocumentType.UNKNOWN

        mapping = {
            "Funds": DocumentType.OTHER,
            "Confirmations & Prospectus": DocumentType.OTHER,
            "Statements": DocumentType.ACCOUNT_STATEMENT,
            "Tax": DocumentType.TAX,
        }

        fund_mapping = {
            "Capital": DocumentType.CAPITAL_CALL,
            "Financials": DocumentType.FINANCIAL_STATEMENTS,
            "Update": DocumentType.INVESTMENT_UPDATE,
            "Distribution": DocumentType.DISTRIBUTION_NOTICE,
        }

        if document_type == "Funds":
            for key in fund_mapping:  # noqa: PLC0206
                if key in name:
                    return fund_mapping[key]

        return mapping.get(document_type, DocumentType.UNKNOWN)
