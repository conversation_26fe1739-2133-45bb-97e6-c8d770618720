from django.db import migrations
import structlog
logger = structlog.get_logger(__name__)

class Migration(migrations.Migration):
    atomic = True

    dependencies = [
        ('webapp', '0018_backfill_merged_portal_credential'),
    ]

    def backfill_line_items_with_mpc_and_pc_and_mfa(apps, schema_editor):
        MergedPortalCredentials = apps.get_model('webapp', 'mergedportalcredential')

        def is_mpc_complete(mpc):
            # Assumes that all PCs are the same for MPCs, which I think is True at the moment
            portal_credentials = mpc.portal_credentials.all()
            multi_factor_authentications = mpc.multi_factor_authentications.all()

            if not portal_credentials:
                return False
            
            if not multi_factor_authentications:
                return False

            portal_credential = portal_credentials[0]
            return (
                portal_credential is not None
                and portal_credential.username != None
                and portal_credential.username != ""
            )

        def is_associated_with_one_line_item(mpc):
            return mpc.line_items.count() <= 1


        all_merged_portal_credentials = MergedPortalCredentials.objects.all()
        print(f"Found {all_merged_portal_credentials.count()} merged portal credentials")

        mpc_count = 0
        li_count = 0
        for old_mpc in all_merged_portal_credentials.iterator():
            if is_associated_with_one_line_item(old_mpc):
                logger.info("Skipping", mpc_id=old_mpc.id, reason="associated with one line item")
                continue    

            if is_mpc_complete(old_mpc):
                logger.info("Skipping", mpc_id=old_mpc.id, reason="complete")
                continue

            logger.info("Processing", mpc_id=old_mpc.id, line_items=old_mpc.line_items.count())
            mpc_count += 1

            # Need to create a new MPC for each line item except the first one
            old_mpc_line_items = old_mpc.line_items.all()[1:]

            for line_item in old_mpc_line_items:
                line_item_mfa = line_item.multi_factor_authentication if hasattr(line_item, 'multi_factor_authentication') else None
                line_item_pc = line_item.portal_credential if hasattr(line_item, 'portal_credential') else None

                new_mpc = MergedPortalCredentials.objects.create(
                    created_by=line_item.created_by,
                    updated_by=line_item.created_by,
                    organization=line_item.created_by.organization,
                    portal=line_item.portal,
                )

                if line_item_mfa:
                    new_mpc.multi_factor_authentications.add(line_item_mfa)

                if line_item_pc:
                    new_mpc.portal_credentials.add(line_item_pc)

                line_item.merged_portal_credential = new_mpc
                line_item.save()

                li_count += 1

        logger.info("Processed", mpc_count=mpc_count, li_count=li_count)

    def reverse_func(apps, schema_editor):
        pass

    operations = [
        migrations.RunPython(backfill_line_items_with_mpc_and_pc_and_mfa, reverse_func),
    ]