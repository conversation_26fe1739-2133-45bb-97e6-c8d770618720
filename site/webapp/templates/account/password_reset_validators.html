<div class="self-stretch flex-col justify-start items-start gap-3 flex">
    {% for validation in validation_results %}
        <div class="self-stretch justify-start items-center inline-flex">
            <div class="self-stretch justify-start items-center gap-2.5 flex">
                <div class="justify-center items-center flex">
                    {% if validation.is_valid %}
                        <div class="w-5 h-5 relative bg-[#4caf50] rounded-full"></div>
                    {% else %}
                        <div class="w-5 h-5 relative bg-[#d0d2bf] rounded-full"></div>
                    {% endif %}
                </div>
                <div class="text-[#4b554d] text-sm font-normal font-['Inter'] leading-tight">{{ validation.text }}</div>
            </div>
        </div>
    {% endfor %}
    {% if show_tos_checkbox %}
        <div class="Form w-[440px]">
            <div class="w-full flex flex-row items-start gap-2">
                <input id="tos_agreement"
                       type="checkbox"
                       name="tos_agreement"
                       {% if is_tos_checked %}checked{% endif %}
                       class="w-5 h-5 mt-1 shrink-0 border-gray-300 rounded focus:ring-primary" />
                <label for="tos_agreement"
                       class="text-sm text-[#343d36] font-medium font-['Inter'] leading-tight">
                    * I understand & agree to the
                    <a href="https://www.bridgeinvest.io/terms-of-service"
                       class="text-[#924f34] underline">Terms and Conditions</a>
                    and that my information will be used as described on this page and in the
                    <a href="https://www.bridgeinvest.io/privacy-policy"
                       class="text-[#924f34] underline">Privacy Policy</a>.
                </label>
            </div>
        </div>
    {% endif %}
    <button type="submit"
            class="Actions self-stretch h-11 flex-col justify-start items-start flex"
            {% if not all_valid %}disabled{% endif %}>
        <div class="ButtonsButton self-stretch px-4 py-2.5 {% if not all_valid %} bg-gray-400 border-gray-500 cursor-not-allowed {% else %} bg-[#924f34] border-[#924f34] cursor-pointer {% endif %} rounded-lg shadow border justify-center items-center gap-1.5 inline-flex">
            <div class="TextPadding px-0.5 justify-center items-center flex">
                <div class="Text {% if not all_valid %} text-gray-500 {% else %} text-white {% endif %} text-base font-semibold font-['Inter'] leading-normal">
                    {{ button_text }}
                </div>
            </div>
        </div>
    </button>
</div>
