import base64

# nosec because this is a toy view for example
from Crypto.Cipher import <PERSON><PERSON>  # nosec
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse


@login_required
def encrypt(request: HttpRequest) -> HttpResponse:
    template = "link/credentials/real_time_encryption.html"
    password = request.POST.get("portal_password", "").strip()
    confirm_password = request.POST.get("confirm_password", "").strip()
    error = None
    encrypted_password = None

    if request.method == "POST":
        # Always encrypt the password even if they don't match
        if password or confirm_password:
            encrypted_password = encrypt_password(confirm_password)

        # Set error if passwords don't match
        if password and confirm_password and password != confirm_password:
            error = "Passwords do not match."

        # Enable button only if passwords match and both are filled
        disable_button = not (password and confirm_password and password == confirm_password)
    else:
        # First page load - Disable button initially
        disable_button = True

    return TemplateResponse(
        request,
        template,
        {
            "encrypted_password": encrypted_password,
            "error": error,  # Only pass error if passwords don't match
            "disable_button": disable_button,
        },
    )


# Replace this with your actual encryption key
encryption_key = b"your-encryption-key-here"


def encrypt_password(password: str) -> str:
    # Create a new AES cipher
    cipher = AES.new(encryption_key, AES.MODE_GCM, nonce=b"your-nonce-here")

    # Encrypt the password
    ciphertext, tag = cipher.encrypt_and_digest(password.encode("utf-8"))

    # Encode the ciphertext in base64
    return base64.b64encode(ciphertext).decode("utf-8")
