{% load customtags %}
<div id="portal_dashboard_table"
     hx-post="{% url 'line_item_search' %}"
     hx-include=".line_item_search"
     hx-trigger="changeMergedPortalCredentialTable from:document"
     hx-swap="innerHTML">
    {% if merged_portal_credentials_with_line_items %}
        <div class="p-4 lg:p-9">
            <!-- Table scroll indicator for mobile -->
            <div class="lg:hidden mb-2 text-xs text-gray-500 text-center">
                ← Scroll horizontally to view all columns →
            </div>
            <div class="overflow-x-auto">
                <table class="table-fixed w-full min-w-[800px] text-sm text-left rtl:text-right border-b-2 border-secondary-orange-beige">
                    <thead>
                        <tr>
                            <th class="w-2/5 min-w-[200px] px-3 py-2 text-left font-inter text-sm lg:text-[16px] border-b-2 border-r-2 border-secondary-orange-beige">
                                Portal / Inbox
                            </th>
                            <th class="w-1/8 min-w-[120px] px-3 py-2 text-left font-inter text-sm lg:text-[16px] border-b-2 border-secondary-orange-beige">Credentials</th>
                            <th class="w-1/8 min-w-[140px] px-3 py-2 text-left font-inter text-sm lg:text-[16px] border-b-2 border-secondary-orange-beige">
                                <span class="hidden lg:inline">Multi-Factor Authentication</span>
                                <span class="lg:hidden">MFA</span>
                            </th>
                            <th class="w-1/8 min-w-[120px] px-3 py-2 text-left font-inter text-sm lg:text-[16px] border-b-2 border-secondary-orange-beige">
                                <span class="hidden lg:inline">Connection Status</span>
                                <span class="lg:hidden">Status</span>
                            </th>
                            <th class="w-1/4 min-w-[160px] px-3 py-2 text-center font-inter text-sm lg:text-[16px] border-b-2 border-l-2 border-secondary-orange-beige">
                                Retrieval
                            </th>
                        </tr>
                    </thead>
                    {% for merged_portal_credential_with_line_items in merged_portal_credentials_with_line_items %}
                        {% include "link/portal/merged_portal_credential.html" with merged_portal_credential_with_line_items=merged_portal_credential_with_line_items %}
                    {% endfor %}
                </table>
            </div>
        </div>
    {% else %}
        <p class="text-center text-gray-500 pt-12 px-4">No details available.</p>
    {% endif %}
</div>
