import json
from typing import TYPE_CHECKING, Any

import structlog
from django.db import models
from django.utils.translation import gettext_lazy as _

from webapp.models.user import BridgeUser

logger = structlog.get_logger(__name__)

from webapp.models.core import AbstractBaseModel, SecretAbstractBaseModel
from webapp.models.documents import RawDocument

if TYPE_CHECKING:
    from webapp.models.line_item import LineItem
    from webapp.models.portal import MergedPortalCredential, MultiFactorAuthentication


class CustomerEmailCredential(SecretAbstractBaseModel):
    # Only supported email providers listed here.
    class EmailProvider(models.TextChoices):
        OUTLOOK = "ou", _("Outlook")
        GMAIL = "gm", _("Gmail")
        YAHOO = "yh", _("Yahoo")
        ZOHO = "zh", _("Zoho")
        NOT_SUPPORTED = "ns", _("Not Supported")

    class EmailIntegrationStatus(models.TextChoices):
        NOT_STARTED = "ns", _("Not Started")
        CUSTOMER_MANAGED = "cm", _("Customer Managed")
        BRIDGE_MANAGED_FILTERS = "bf", _("Bridge Managed Filters")

    class EmailSecretFormat(models.TextChoices):
        MSAL_CACHE = "mc", _("MSAL Cache")
        NOT_SUPPORTED = "ns", _("Not Supported")

    line_items: "LineItem"
    email = models.EmailField()
    # User provided email provider
    email_provider_raw = models.CharField(max_length=255)
    # we will on a cadence go from raw to structured
    email_provider_customer_managed = models.CharField(
        max_length=2,
        choices=EmailProvider.choices,
        default=EmailProvider.NOT_SUPPORTED,
    )
    email_provider_bridge_managed = models.CharField(
        max_length=2,
        choices=EmailProvider.choices,
        default=EmailProvider.NOT_SUPPORTED,
    )
    email_integration_status = models.CharField(
        max_length=2,
        choices=EmailIntegrationStatus.choices,
        default=EmailIntegrationStatus.NOT_STARTED,
    )

    secret_format = models.CharField(
        max_length=2,
        choices=EmailSecretFormat.choices,
        default=EmailSecretFormat.NOT_SUPPORTED,
    )
    email_secret_format_version = models.IntegerField(default=0)
    multi_factor_authentications: "MultiFactorAuthentication"

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_items"]

    class Meta(SecretAbstractBaseModel.Meta):
        unique_together = ("organization", "email")

    def __str__(self) -> str:
        return self.email

    @classmethod
    def parse_email_provider_bridge_managed(cls, email_provider_raw: str) -> EmailProvider:
        if email_provider_raw.lower() in ["outlook", "ou"]:
            return cls.EmailProvider.OUTLOOK
        return cls.EmailProvider.NOT_SUPPORTED

    @classmethod
    def parse_email_provider_customer_managed(cls, email_provider_raw: str) -> EmailProvider:
        if email_provider_raw.lower() in ["outlook", "ou"]:
            return cls.EmailProvider.OUTLOOK
        if email_provider_raw.lower() in ["gmail", "gm"]:
            return cls.EmailProvider.GMAIL
        if email_provider_raw.lower() in ["yahoo", "yh"]:
            return cls.EmailProvider.YAHOO
        if email_provider_raw.lower() in ["zoho", "zh"]:
            return cls.EmailProvider.ZOHO
        return cls.EmailProvider.NOT_SUPPORTED


class BridgeEmail(AbstractBaseModel):
    # organization ID is always added for uniqueness to the string itself
    email = models.EmailField(unique=True)
    user_forwarding_rule: "UserForwardingRule"

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["user_forwarding_rule__email_retrievals__line_item"]

    def __str__(self) -> str:
        return self.email

    @classmethod
    def get_organization_bridge_email_by_user(cls, user: BridgeUser) -> "BridgeEmail":
        bridge_email, _ = BridgeEmail.objects.get_or_create(
            user=user,
            email=f"{user.organization.name.lower().replace(' ', '-')}@app.bridgeinvest.io",
        )
        return bridge_email


class UserForwardingRule(AbstractBaseModel):
    # forwarding rule for a CustomerEmailCredential
    bridge_email = models.ForeignKey(BridgeEmail, on_delete=models.CASCADE, related_name="user_forwarding_rule")
    receiving_email = models.ForeignKey(
        CustomerEmailCredential,
        on_delete=models.CASCADE,
        related_name="user_forwarding_rule",
    )
    # the forwarding rule as string
    forwarding_rule = models.TextField(blank=True)
    email_retrievals: "EmailRetrieval"
    merged_portal_credential: "MergedPortalCredential"

    def __str__(self) -> str:
        return f"User Forwarding Rule for {self.receiving_email}"

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["email_retrievals__line_item"]

    def update_forwarding_rule_with_email_retrievals_sender_emails(self) -> dict[str, Any]:
        all_emails = [
            email
            for retrieval in self.email_retrievals.all()
            for email in retrieval.sender_emails
            if email and email.strip()
        ]
        sender_emails = list(set(all_emails))
        display_name = f"Bridge - Auto Forwarding Rule for {self.pk}"
        message_rule = {
            "displayName": display_name,
            "sequence": 1,
            "conditions": {
                "senderContains": sender_emails,
            },
            "actions": {
                "forwardTo": [
                    {
                        "emailAddress": {
                            "address": self.bridge_email.email,
                        },
                    },
                ],
                "stopProcessingRules": False,
            },
            "isEnabled": True,
        }
        self.forwarding_rule = json.dumps(message_rule)
        self.save()
        return message_rule


class EmailRetrieval(AbstractBaseModel):
    # model to track sender emails for a line item
    line_item = models.OneToOneField("LineItem", on_delete=models.CASCADE, related_name="email_retrieval")
    sender_emails = models.JSONField(default=list)
    email_intakes: "EmailIntake"
    user_forwarding_rule = models.ForeignKey(
        UserForwardingRule,
        on_delete=models.CASCADE,
        related_name="email_retrievals",
    )

    def __str__(self) -> str:
        return f"Email Retrieval for {self.line_item} containing emails from {', '.join(self.sender_emails)}"

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_item"]


class EmailIntake(AbstractBaseModel):
    email_subject = models.CharField()
    to_email = models.EmailField()
    from_email = models.EmailField()
    all_emails = models.JSONField(default=list)
    received_date = models.DateTimeField()
    email_text_content = models.TextField(blank=True)
    s3_key = models.CharField(max_length=1024, blank=True)
    s3_bucket = models.CharField(max_length=255, blank=True)
    raw_documents: models.QuerySet[RawDocument]
    email_retrieval = models.ForeignKey(
        EmailRetrieval,
        on_delete=models.CASCADE,
        related_name="email_intakes",
        null=True,  # Allow null since not all intakes are retrievals
    )

    def __str__(self) -> str:
        return f"Email from {self.from_email} to {self.to_email} on {self.received_date}"

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return []
