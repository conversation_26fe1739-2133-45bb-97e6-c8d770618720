import base64
import json
from datetime import datetime
from pathlib import Path
from typing import Any, TypeVar

import boto3
import structlog
from botocore.exceptions import ClientError
from langchain.output_parsers import PydanticOutputParser
from langchain_core.prompts import PromptTemplate
from pydantic import BaseModel
from webapp.models.retrieval import Retrieval

from retrieval.tasks.retrieval_error_classes import ErrorSummary

logger = structlog.get_logger(__name__)


def get_retrieval_error_summary(retrieval: Retrieval, current_time: datetime, time_threshold: datetime) -> ErrorSummary:
    error_trace = get_retrieval_error_trace(retrieval.pk, current_time, time_threshold)
    screenshot = get_retrieval_screenshot(retrieval)
    if len(error_trace) == 0 and screenshot is None:
        return ErrorSummary(summary="no error logs or screenshot found", error_type="unknown_error")

    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": f"Retrieval status: {Retrieval.RetrievalStatus(retrieval.retrieval_status).label}; created at: {retrieval.created_at}; updated at: {retrieval.updated_at}; documents retrieved: {retrieval.number_documents_retrieved}",  # noqa: E501
                },
            ],
        }
    ]

    if len(error_trace):
        # do I even need to use PromptTemplate here? just json.dumps the error_trace ?
        error_text = json.dumps(error_trace)
        prompt_template = PromptTemplate(
            template="""
            Error Trace:
            ```
            {error_text}
            ```
            """,
            input_variables=["error_text"],
        )
        error_logs_message = prompt_template.format(error_text=error_text)
        messages.append(
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": error_logs_message},
                ],
            }
        )

    if screenshot is not None:
        messages.append(
            {
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "source": {
                            "type": "base64",
                            "media_type": "image/png",
                            "data": screenshot,
                        },
                    },
                    {"type": "text", "text": "Screenshot of the last frame of the scrape"},
                ],
            }
        )

    try:
        return structured_output_tool_call(messages, ErrorSummary)
    except Exception:
        logger.exception("error in get_retrieval_error_summary")
        return ErrorSummary(summary="error parsing error LLM respond", error_type="unknown_error")


def get_retrieval_error_trace(retrieval_pk: str, current_time: datetime, time_threshold: datetime) -> list:
    try:
        current_time_seconds = int(current_time.timestamp())
        time_threshold_seconds = int(time_threshold.timestamp())
        client = boto3.client("logs", region_name="us-east-1")
        response = client.start_query(
            logGroupName="CeleryServiceStack-CeleryLogGroup0C29692E-9UPbQUAwrr0Y",
            startTime=time_threshold_seconds,
            endTime=current_time_seconds,
            queryString=f"fields @timestamp, @message | filter retrieval_pk='{retrieval_pk}' and (level='error' or (level='info' and log_to_llm=true)) | sort @timestamp asc",  # noqa: E501
            limit=100,
        )
        query_id = response["queryId"]
        while True:
            results = client.get_query_results(queryId=query_id)
            if results["status"] in [
                "Complete",
                "Failed",
                "Cancelled",
                "Timeout",
                "Unknown",
            ]:
                parsed = results.get("results", [])
                errors_array = []
                for error_messages in parsed:
                    formatted_dict = {error_obj["field"]: error_obj["value"] for error_obj in error_messages}
                    time_stamp = formatted_dict["@timestamp"]
                    error_message = json.loads(formatted_dict["@message"])
                    exceptions_array = (
                        [
                            {"type": exception["exc_type"], "value": exception["exc_value"]}
                            for exception in error_message["exception"]
                        ]
                        if "exception" in error_message
                        else []
                    )
                    error_dict = {}
                    if time_stamp:
                        error_dict["timestamp"] = time_stamp
                    if "event" in error_message:
                        error_dict["event"] = error_message["event"]
                    if "retrieval_state" in error_message:
                        error_dict["retrieval_state"] = error_message["retrieval_state"]
                    if exceptions_array:
                        error_dict["exceptions"] = exceptions_array
                    errors_array.append(error_dict)
                return errors_array
    except Exception:
        logger.exception("An error occured getting retrieval error logs")
        return []


def get_retrieval_screenshot(retrieval: Retrieval) -> str | None:
    try:
        s3 = boto3.client("s3")
        bucket = retrieval.s3_bucket
        path = Path(retrieval.s3_key)
        obj = s3.get_object(Bucket=bucket, Key=str(path / "loggable/cleanup/screenshot.png"))
        content = obj["Body"].read()
        return base64.b64encode(content).decode("utf-8")
    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        if error_code in ["NoSuchKey", "NoSuchBucket"]:
            msg = f"no screenshot found in cleanup directory for {retrieval.pk}"
            logger.info(msg)
        return None
    except Exception:
        logger.exception("error in get_retrieval_screenshot")
        return None


PydanticModelT = TypeVar("PydanticModelT", bound=BaseModel)


def structured_output_tool_call(  # noqa: UP047
    messages: list[dict[str, Any]],
    model_class: type[PydanticModelT],
    model_id: str = "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
    max_tokens: int = 1024,
) -> PydanticModelT:
    try:
        bedrock_runtime = boto3.client(service_name="bedrock-runtime", region_name="us-east-1")
        input_schema = model_class.model_json_schema()
        tool_description = input_schema.get("description", "Call this tool based on user message")
        tool_name = input_schema.get("title", "")
        system_prompt = "You are a software engineer in charge of automatic document retrieval using python playwright. You are given data pertaining to a retrieval that did not succeed. Analyze and call the tool provided to summarize and categorize the error that was encountered. Not all errors are blocking, pay close attention to the timestamp of the errors and when the retrieval object was created / updated. find the most likely error that caused the retrieval to not succeed."  # noqa: E501
        body = json.dumps(
            {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": max_tokens,
                "system": system_prompt,
                "tools": [
                    {
                        "name": tool_name,
                        "description": tool_description,
                        "input_schema": input_schema,
                    }
                ],
                "messages": messages,
            }
        )
        response = bedrock_runtime.invoke_model(body=body, modelId=model_id)
        response_body = json.loads(response.get("body").read())
        logger.info("got structured_output_tool_call bedrock response body", response_body=response_body)
        try:
            parser = PydanticOutputParser(pydantic_object=ErrorSummary)
            tool_use_responses = [
                resp
                for resp in response_body.get("content", [{}])
                if isinstance(resp, dict) and resp.get("type") == "tool_use"
            ]
            model_response = json.dumps(tool_use_responses[0].get("input", {}))
            logger.info("received model response", model_response=model_response)
            if not model_response:
                logger.error("Empty model response received")
                return ErrorSummary(
                    summary="Failed to generate error summary due to empty LLM response", error_type="unknown_error"
                )
            return parser.parse(model_response)
        except (KeyError, IndexError):
            logger.exception("Unexpected response format: %s", response_body)
    except ClientError as err:
        message = err.response["Error"]["Message"]
        logger.exception("A client error occurred: %s", message)
    return ErrorSummary(summary="Failed to generate an error summary", error_type="unknown_error")
