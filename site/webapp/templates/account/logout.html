{% extends "base.html" %}
{% load static %}
{% block title %}
    Dashboard
{% endblock title %}
{% block webapp_content %}
    {% block extra_head %}
        {% include "components/bridge_header.html" %}
    {% endblock extra_head %}
    <div class="Container h-[636px] w-full px-8 flex flex-col justify-center items-center">
        <div class="Content w-4/5 flex flex-col justify-center items-center gap-20">
            <div class="HeaderAndForm w-full flex flex-col justify-center items-center gap-6">
                <div class="TextAndSupportingText text-center">
                    <h1 class="Text text-[#343d36] text-4xl text-start font-bold font-nanum">Log Out</h1>
                    <p class="Text font-inter">
                        {# This new line pattern ensures we have correct spacing for all these spans, otherwise we get/remove " " in weird places.#}
                        <span class="text-[#343d36]">Hi</span>
                        <span class="text-[#343d36] font-bold p-0">{{ user.get_full_name }},</span>
                        <span class="text-[#343d36]">are you sure you want to log out from Bridge?</span>
                    </p>
                </div>
                <div class="Actions flex justify-center items-center gap-4">
                    <a onclick="history.go(-1)">
                        <div class="ButtonsButton h-11 px-4 py-2.5 bg-[#4b554d] rounded-lg shadow border border-[#4b554d] justify-center items-center gap-1.5 inline-flex">
                            <div class="TextPadding px-0.5 justify-center items-center flex">
                                <div class="Text text-white text-base font-semibold font-['Inter'] leading-normal">Go back</div>
                            </div>
                        </div>
                    </a>
                    <form method="post" action="{% url 'account_logout' %}" hx-boost="false">
                        {% csrf_token %}
                        <button type="submit"
                                class="ButtonsButton h-11 px-4 py-2.5 bg-[#924f34] rounded-lg shadow border border-[#924f34] justify-center items-center gap-1.5 inline-flex">
                            <div class="TextPadding px-0.5 justify-center items-center flex">
                                <div class="Text text-white text-base font-semibold font-['Inter'] leading-normal">Yes</div>
                            </div>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock webapp_content %}
