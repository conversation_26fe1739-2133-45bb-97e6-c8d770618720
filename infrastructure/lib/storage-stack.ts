import * as cdk from "aws-cdk-lib";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as rds from "aws-cdk-lib/aws-rds";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import * as s3 from "aws-cdk-lib/aws-s3";
import { Construct } from "constructs";
import * as kms from "aws-cdk-lib/aws-kms";
import * as elasticache from "aws-cdk-lib/aws-elasticache";
import * as cloudtrail from "aws-cdk-lib/aws-cloudtrail";
import * as ses from "aws-cdk-lib/aws-ses";
import * as route53 from "aws-cdk-lib/aws-route53";
import * as subscriptions from "aws-cdk-lib/aws-sns-subscriptions";
import {
  NetworkStackOutputs,
  StorageStackOutputs,
  SharedProps,
  SharedStack,
  ConfigStackOutputs,
} from "./schema";
import * as sns from "aws-cdk-lib/aws-sns";
import * as actions from "aws-cdk-lib/aws-ses-actions";

export class StorageStack extends SharedStack {
  // readonly bucket: s3.Bucket;
  readonly action: actions.S3;

  constructor(scope: Construct, id: string, props: SharedProps) {
    super(scope, id, props);

    // From network-stack
    const networkOutputs = NetworkStackOutputs.fromCdkOutput(
      this,
      NetworkStackOutputs,
    );
    const configOutputs = ConfigStackOutputs.fromCdkOutput(
      this,
      ConfigStackOutputs,
    );
    if (networkOutputs === undefined || configOutputs === undefined) {
      return;
    }

    const trail = new cloudtrail.Trail(this, "CloudTrail");

    const vpc = ec2.Vpc.fromVpcAttributes(this, "Vpc", {
      vpcId: networkOutputs.VpcId,
      availabilityZones: networkOutputs.VpcAvailabilityZones,
      publicSubnetIds: networkOutputs.VpcPublicSubnetIds,
      publicSubnetRouteTableIds: networkOutputs.VpcPublicRouteTableIds,
    });

    // Create a security group for the Aurora cluster
    const dbSecurityGroup = new ec2.SecurityGroup(this, "DBSecurityGroup", {
      vpc: vpc,
      description: "Security group for Aurora DB cluster",
      allowAllOutbound: true,
    });

    // Allow inbound traffic on port 5432 from the VPC
    dbSecurityGroup.addIngressRule(
      ec2.Peer.ipv4(networkOutputs.VpcCidrBlock),
      ec2.Port.tcp(5432),
      "Allow inbound traffic on port 5432 from the VPC",
    );

    // Create a secret to store the database credentials
    const dbCredentialsSecretEnc = new secretsmanager.Secret(
      this,
      "DBCredentialsSecretEncrypted",
      {
        secretName: `Application-${id}-db-credentials-enc`,
        generateSecretString: {
          secretStringTemplate: JSON.stringify({ username: "postgres" }),
          generateStringKey: "password",
          excludeCharacters: '"@/\\',
        },
        description:
          "Database credentials for the Aurora Serverless v2 cluster",
      },
    );
    let db_props_enc: rds.DatabaseClusterProps = {
      engine: rds.DatabaseClusterEngine.auroraPostgres({
        version: rds.AuroraPostgresEngineVersion.VER_16_2,
      }),
      vpc: vpc,
      vpcSubnets: { subnetType: ec2.SubnetType.PUBLIC }, // TODO: move to private subnets eventually
      writer: rds.ClusterInstance.serverlessV2("writer"),
      enableDataApi: true,
      securityGroups: [dbSecurityGroup],
      serverlessV2MinCapacity: 0.5,
      serverlessV2MaxCapacity: 16,
      defaultDatabaseName: configOutputs.DatabaseName,
      // credentials: rds.Credentials.fromSecret(dbCredentialsSecretEnc),
      storageEncrypted: true,
      storageEncryptionKey: kms.Alias.fromAliasName(
        this,
        "DefaultRDSKMS",
        "alias/aws/rds",
      ),
      backup: { retention: cdk.Duration.days(7) },
    };
    if (props.deploymentTarget === "prod") {
      db_props_enc = {
        ...db_props_enc,
        deletionProtection: true,
      };
    }

    let snapshotIdentifier = "";
    if (props.deploymentTarget === "prod") {
      snapshotIdentifier =
        "arn:aws:rds:us-east-1:180294215839:cluster-snapshot:soc-snapshot-to-encrypt-database-241217-cdk-do-not-delete";
    } else if (props.deploymentTarget === "demo") {
      snapshotIdentifier =
        "arn:aws:rds:us-east-1:654654313761:cluster-snapshot:my-test-soc";
    }

    const dbClusterEnc = new rds.DatabaseClusterFromSnapshot(
      this,
      "DBInstanceFromSnapshot",
      {
        snapshotCredentials: rds.SnapshotCredentials.fromSecret(
          dbCredentialsSecretEnc,
        ),
        snapshotIdentifier: snapshotIdentifier,
        ...db_props_enc,
      },
    );

    let removalPolicy = cdk.RemovalPolicy.DESTROY;
    let versioned = false;
    let autoDeleteObjects = true;
    if (
      props.deploymentTarget === "prod" ||
      props.deploymentTarget === "demo"
    ) {
      removalPolicy = cdk.RemovalPolicy.RETAIN;
      versioned = true;
      autoDeleteObjects = false;
    }
    // Create an S3 bucket
    const s3Bucket = new s3.Bucket(this, "S3Bucket", {
      bucketName: configOutputs.UserBucketName,
      encryption: s3.BucketEncryption.S3_MANAGED,
      removalPolicy: removalPolicy,
      versioned: versioned,
      autoDeleteObjects: autoDeleteObjects,
    });
    // Add CORS policy to the S3 bucket
    s3Bucket.addCorsRule({
      allowedOrigins: ["*"],
      allowedMethods: [
        s3.HttpMethods.GET,
        s3.HttpMethods.PUT,
        s3.HttpMethods.POST,
      ],
      allowedHeaders: ["*"],
      exposedHeaders: ["Content-Disposition", "Content-Type"],
      maxAge: 3000,
    });

    const emailNotificationTopicS3 = new sns.Topic(
      this,
      "EmailNotificationsTopicS3",
      {
        topicName: configOutputs.EmailTopicName,
        displayName: configOutputs.EmailTopicName,
      },
    );
    const action = new actions.S3({
      bucket: s3Bucket,
      objectKeyPrefix: configOutputs.EmailS3Path,
      topic: emailNotificationTopicS3,
    });

    const webhookPassword = new secretsmanager.Secret(this, "WebhookPassword", {
      secretName: "Application-WebhookPassword",
      description: "Webhook Password",
      generateSecretString: {
        secretStringTemplate: JSON.stringify({}),
        generateStringKey: "password",
        passwordLength: 8,
        excludeNumbers: true,
        excludePunctuation: true,
      },
    });
    const webhookToken = webhookPassword.secretValueFromJson("password");
    // Subscribe the SNS topic to the HTTPS endpoint
    // TODO: uncomment this out after merging the emails2_241107 branch
    emailNotificationTopicS3.addSubscription(
      // Password found in site/email_webhook/api.py
      new subscriptions.UrlSubscription(
        `https://bridge:snswebhook@${configOutputs.DomainName}/${configOutputs.EmailWebhookPath}`,
      ),
      // new subscriptions.UrlSubscription(`https://${configOutputs.EmailWebhookUsername}:${webhookToken.unsafeUnwrap()}@${configOutputs.DomainName}/${configOutputs.EmailWebhookPath}`),
    );
    const emails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ];

    emails.forEach((email) => {
      emailNotificationTopicS3.addSubscription(
        new subscriptions.EmailSubscription(email),
      );
    });

    // Create a receipt rule to handle incoming emails
    // Make sure you activate this rule on the SES console
    const receiptRuleSet = new ses.ReceiptRuleSet(this, "ReceiptRuleSet", {});

    const receiptRule = new ses.ReceiptRule(this, "ReceiptRule", {
      ruleSet: receiptRuleSet,
      enabled: true,
      actions: [action],
    });

    const configurationSet = new ses.ConfigurationSet(
      this,
      "ConfigurationSet",
      {
        // This is the default name for django-ses plugin. If you change this, you need to update the SES configuration in the Django settings
        configurationSetName: "my-first-configuration-set",
      },
    );

    const identity = new ses.EmailIdentity(this, "HelloSendIdentity", {
      // NOTE you must have email forwarding set up first before you can verify this email address.
      identity: ses.Identity.email(`hello@${props.domainName}`),
      configurationSet: configurationSet,
    });

    const hostedZone = route53.HostedZone.fromHostedZoneAttributes(
      this,
      "HostedZone",
      {
        hostedZoneId: props.hostedZoneId,
        zoneName: props.domainName,
      },
    );

    // Create Route53 inbound mail record
    new route53.MxRecord(this, "MailRecord", {
      zone: hostedZone,
      recordName: props.domainName,
      values: [
        {
          priority: 10, // Priority of the MX record
          hostName: "inbound-smtp.us-east-1.amazonaws.com",
        },
      ],
    });
    // Create the Subnet Group
    // Create the Subnet Group for Redis using public subnets
    const subnetGroup = new elasticache.CfnSubnetGroup(
      this,
      "RedisSubnetGroup",
      {
        description: "Subnet group for Redis cluster",
        subnetIds: vpc.selectSubnets({ subnetType: ec2.SubnetType.PUBLIC })
          .subnetIds,
      },
    );

    // Create the Security Group
    const securityGroup = new ec2.SecurityGroup(this, "RedisSecurityGroup", {
      vpc: vpc,
      description: "Security group for Redis cluster",
      allowAllOutbound: true,
    });

    // Restrict inbound access to the VPC CIDR
    securityGroup.addIngressRule(
      ec2.Peer.ipv4(networkOutputs.VpcCidrBlock),
      ec2.Port.tcp(6379),
      "Allow Redis access from within VPC",
    );
    const redisSecret = new secretsmanager.Secret(this, "RedisSecret", {
      secretName: `Application-${id}-redis-credentials`,
      generateSecretString: {
        secretStringTemplate: JSON.stringify({ username: "redis" }),
        generateStringKey: "password",
        excludePunctuation: true,
        excludeCharacters: '"@/\\',
      },
    });
    const authToken = redisSecret.secretValueFromJson("password");

    // Create the ElastiCache Redis Cluster
    const redisCluster = new elasticache.CfnReplicationGroup(
      this,
      "RedisCluster",
      {
        replicationGroupDescription: "Redis cluster for celery",
        engine: "redis",
        cacheNodeType: "cache.t3.micro",
        cacheSubnetGroupName: subnetGroup.ref,
        securityGroupIds: [securityGroup.securityGroupId],
        automaticFailoverEnabled: true,
        numNodeGroups: 1,
        replicasPerNodeGroup: 1,
        transitEncryptionEnabled: true,
        atRestEncryptionEnabled: true,
        // TODO: not this I guess https://github.com/aws/aws-cdk/issues/16368
        authToken: authToken.unsafeUnwrap(),
      },
    );
    const userDataKms = new kms.Key(this, "userDataKms", {
      alias: `alias/${configOutputs.UserDataKmsName}`,
    });

    new StorageStackOutputs({
      EmailConfigurationSetName: configurationSet.configurationSetName,
      EmailSendAddress: identity.emailIdentityName,
      DBCredentialsSecretArn: dbCredentialsSecretEnc.secretArn,
      // DBCredentialsSecretArn: dbCredentialsSecret.secretArn,
      DBInstanceIds: dbClusterEnc.instanceIdentifiers,
      DBClusterArn: dbClusterEnc.clusterArn,
      S3BucketName: s3Bucket.bucketName,
      UserDataKmsArn: userDataKms.keyArn,
      RedisSecretArn: redisSecret.secretArn,
      RedisClusterName: redisCluster.ref,
      // https://theburningmonk.com/cloudformation-ref-and-getatt-cheatsheet/
      RedisClusterPrimaryEndPointAddress: redisCluster
        .getAtt("PrimaryEndPoint.Address")
        .toString(),
      RedisClusterPrimaryEndPointPort: redisCluster
        .getAtt("PrimaryEndPoint.Port")
        .toString(),
      RedisSecurityGroupId: securityGroup.securityGroupId,
    }).toCdkOutput(this);
  }
}
