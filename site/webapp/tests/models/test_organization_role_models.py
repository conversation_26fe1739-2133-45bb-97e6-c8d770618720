import pytest
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from webapp.models.user import BridgeUser, Organization, Role

pytestmark = pytest.mark.django_db


def test_organization_creation() -> None:
    """Test organization creation with valid data"""
    test_org = Organization.objects.create(name="testorg", description="Test Organization")
    assert test_org.name == "testorg"
    assert test_org.description == "Test Organization"
    assert test_org.id is not None
    assert test_org.created_at is not None
    assert test_org.updated_at is not None
    assert test_org.deleted_at is None
    assert str(test_org) == "testorg"


def test_organization_unique_name(test_org: Organization) -> None:
    assert test_org.name == "Test Org"
    """Test that organization names must be unique"""
    with pytest.raises(IntegrityError):
        Organization.objects.create(name="Test Org", description="Another organization with same name")


def test_organization_users_relationship(test_org: Organization, test_role: Role) -> None:
    """Test the relationship between organization and users"""
    user = BridgeUser.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=str(test_org.id),
        roles=[str(test_role.id)],
        password="testpass123",  # noqa: S106
    )

    assert user in test_org.bridgeuser.all()


def test_role_creation(test_role: Role) -> None:
    """Test role creation with valid data"""
    assert test_role.name == Role.RoleName.MANAGER
    assert test_role.description == Role.STANDARD_DESCRIPTIONS[Role.RoleName.MANAGER]
    assert test_role.id is not None
    assert test_role.created_at is not None
    assert test_role.updated_at is not None
    assert test_role.deleted_at is None


def test_role_str_representation(test_role: Role) -> None:
    """Test the string representation of a role"""
    assert test_role.name == Role.RoleName.MANAGER
    assert str(test_role) == Role.RoleName.MANAGER.label


def test_role_unique_name(test_role: Role) -> None:
    assert test_role.name == Role.RoleName.MANAGER
    """Test that role names must be unique"""
    with pytest.raises(ValidationError):
        Role.objects.create(name=Role.RoleName.MANAGER)


def test_role_users_relationship(test_org: Organization, test_role: Role) -> None:
    """Test the relationship between role and users"""
    user = BridgeUser.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=str(test_org.id),
        roles=[str(test_role.id)],
        password="testpass123",  # noqa: S106
    )

    assert user in test_role.bridgeuser.all()
