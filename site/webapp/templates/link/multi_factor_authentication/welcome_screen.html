{# djlint:off H008 #}
{% load customtags static widget_tweaks %}
<div id="modal-container-{{ merged_portal_credential.pk }}"
     class="relative w-full max-w-lg max-h-full">
    <!-- Modal content -->
    <div class="relative w-[600px]"></div>
    <div class="relative rounded-lg">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-4 md:p-5 rounded-t">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                <div class="Group w-[48px] h-[49px] left-[12.84px] top-[15.46px] absolute">
                    <img src="{% static 'assets/images/fin_tag.svg' %}"
                         width="48"
                         height="49"
                         alt="Encryption modal changes">
                </div>
            </h3>
            <button _="on click trigger closeModal"
                    type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                <img src="{% static 'assets/images/close.svg' %}"
                     class="w-3 h-3"
                     width="12"
                     height="12"
                     alt="closeButton">
                <span class="sr-only">Close modal</span>
            </button>
        </div>
        <div class="px-4 md:p-5">
            <div class="pt-3 text-[#343d36] text-lg font-semibold font-inter leading-[27px]">
                <p class="pb-6">Please select your authentication method.</p>
            </div>
            {% if form.non_field_errors %}
                <div class="mb-4">
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
                         role="alert">
                        {% for error in form.non_field_errors %}<p>{{ error }}</p>{% endfor %}
                    </div>
                </div>
            {% endif %}
            <div id="loading-spinner"
                 class="fixed inset-0 z-50 flex flex-col justify-center items-center bg-gray-500 bg-opacity-75 pointer-events-auto h-full w-full top-0 left-0 [&:not(.htmx-request)]:hidden">
                <lottie-player src="{% static 'assets/images/bridge_white_txt_loading_animation.json' %}" class="w-36 h-36" background="transparent" speed="1" loop autoplay></lottie-player>
                <span class="ml-2 text-white text-lg font-semibold">Saving...</span>
            </div>
            <form class="w-full"
                  hx-post="{% url 'multi_factor_authentication_modal' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
                  hx-swap='outerHTML'
                  hx-target="#modal-container-{{ merged_portal_credential.pk }}"
                  hx-indicator="#loading-spinner"
                  enctype='multipart/form-data'>
                {% csrf_token %}
                {% if merged_portal_credential.multi_factor_authentication.status == MFAStatus.SUCCESS %}
                    You have previously set up Multi-Factor Authentication. If you would like to change it, please edit it below.
                    <br />
                    <br />
                {% elif merged_portal_credential.multi_factor_authentication.status == MFAStatus.FAILED %}
                    Multi Factor Authentication failed. Please edit the credentials, and try again or reach out to supoprt.
                    <br />
                    <br />
                {% elif merged_portal_credential.multi_factor_authentication.status == MFAStatus.NOT_SUPPORTED %}
                    Multi Factor Authentication type is not supported currently. Please reach out to support.
                    <br />
                    <br />
                {% endif %}
                {% comment %} {% render_field form.authentication_optons %} {% endcomment %}
                <div class="w-full flex-col justify-start items-start gap-4 inline-flex">
                    <div for="{{ form.authentication_options.id_for_label }}"
                         class="w-full h-9 justify-start items-center inline-flex px-2">
                        <label class="text-gray-700 font-medium w-1/5">{{ form.authentication_options.label }}</label>
                        <div class="w-4/5">
                            {{ form.authentication_options|attr:"class:py-2 w-full bg-white rounded-lg border border-gray-300 justify-center items-center gap-2 flex text-gray-700 text-sm font-semibold "|attr:"_:on change or load trigger authChange(val:me.value) on .optional_mfa_inputs"|attr:"id:authentication-options" }}
                            {% if form.authentication_options.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in form.authentication_options.errors %}<p>{{ error }}</p>{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div _="on authChange(val) if val is '{{ MFAType.AUTHENTICATOR }}' show else hide end"
                         class='optional_mfa_inputs w-full'>
                        <div class="h-9 justify-start items-center inline-flex px-2 w-full">
                            <div class="text-gray-700 font-medium w-1/5">QR Code Image File</div>
                            <!-- Label for Custom Styled File Input -->
                            <label for="qr_image_file"
                                   class="px-2.5 py-2 bg-white rounded-lg border border-[#eceee8] justify-center items-center gap-1 flex cursor-pointer">
                                <div class="px-0.5 justify-center items-center flex">
                                    <div class="text-[#343d36] text-sm font-semibold font-inter leading-tight">Choose file</div>
                                </div>
                            </label>
                            <!-- Hidden File Input -->
                            <input onchange="updateFileNameDisplay()"
                                   type="file"
                                   id="qr_image_file"
                                   name="qr_image_file"
                                   class="hidden" />
                            <div id="file-name-display"
                                 class="flex px-2 opacity-75 text-[#343d36] text-sm font-normal font-inter leading-tight">
                                No file chosen
                            </div>
                            {% if form.qr_image_file.errors %}
                                <div class="text-red-600 text-sm mt-1">
                                    {% for error in form.qr_image_file.errors %}<p>{{ error }}</p>{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div _="on authChange(val) if val is '{{ MFAType.EMAIL }}' show else hide end"
                         class='optional_mfa_inputs w-full'>
                        <div class="px-2 w-full ">
                            <div class="flex items-center mb-4">
                                <div class="w-1/5 ">
                                    <label class="text-gray-700 font-medium pr-2"
                                           for="{{ form.email_provider.id_for_label }}">
                                        {{ form.email_provider.label }}
                                    </label>
                                </div>
                                <div class="w-4/5 ">
                                    {{ form.email_provider|attr:"class:w-full border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400" }}
                                    {% if form.email_provider.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in form.email_provider.errors %}<p>{{ error }}</p>{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="flex items-center mb-4 w-full ">
                                <div class="w-1/5 ">
                                    <label class="text-gray-700 font-medium pr-2"
                                           for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                                </div>
                                <div class="w-4/5 ">
                                    {{ form.email|attr:"class: w-full border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 ml-auto" }}
                                    {% if form.email.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in form.email.errors %}<p>{{ error }}</p>{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div _="on authChange(val) if val is '{{ MFAType.SMS }}' show else hide end"
                         class='optional_mfa_inputs w-full'>
                        <div class="px-2 w-full">
                            <div class="flex items-center mb-4">
                                <div class="w-1/5 ">
                                    <label class="text-gray-700 font-medium pr-2"
                                           for="{{ form.phone_number.id_for_label }}">
                                        {{ form.phone_number.label }}
                                    </label>
                                </div>
                                <div class="w-4/5 ">
                                    {{ form.phone_number|attr:"class:w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400 ml-auto" }}
                                    {% if form.phone_number.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in form.phone_number.errors %}<p>{{ error }}</p>{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="flex items-center mb-4 w-full ">
                                <div class="w-1/5 ">
                                    <label class="text-gray-700 font-medium pr-2"
                                           for="{{ form.phone_type.id_for_label }}">{{ form.phone_type.label }}</label>
                                </div>
                                <div class="w-4/5 ">
                                    {{ form.phone_type|attr:"class: w-full border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 ml-auto" }}
                                    {% if form.phone_type.errors %}
                                        <div class="text-red-600 text-sm mt-1">
                                            {% for error in form.phone_type.errors %}<p>{{ error }}</p>{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div _="on authChange(val) if val is '{{ MFAType.NONE }}' show else hide end"
                         class='optional_mfa_inputs'>
                        <span class="text-gray-700 font-medium pr-2">
                            No Multi-Factor Authentication (MFA) Method will be used, no additional steps required. If MFA required, please select another one.
                        </span>
                    </div>
                    <div _="on authChange(val) if val is '{{ MFAType.UNKNOWN }}' show else hide end"
                         class='optional_mfa_inputs'>
                        <span class="text-gray-700 font-medium pr-2">Unknown MFA, please select an Multi Factor Authentication method.</span>
                    </div>
                    <div class="w-full flex-col justify-start items-start gap-5 inline-flex py-2">
                        <div class="self-stretch justify-start items-start gap-3 inline-flex">
                            <button type="submit"
                                    class="grow shrink basis-0 h-11 px-4 py-2.5 bg-[#924f34] rounded-lg border border-[#924f34] justify-center items-center gap-1.5 flex">
                                <div class="px-0.5 justify-center items-center flex">
                                    <div class="text-white text-base font-semibold font-inter leading-normal">Continue</div>
                                </div>
                            </button>
                            <button _="on click trigger closeModal"
                                    type="button"
                                    class="grow shrink basis-0 h-11 px-4 py-2.5 rounded-lg border border-[#924f34] justify-center items-center gap-1.5 flex">
                                <div class="px-0.5 justify-center items-center flex">
                                    <div class="text-[#924f34] text-base font-semibold font-inter leading-normal">Close Popup</div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    function updateFileNameDisplay() {
        const fileInput = document.getElementById('qr_image_file');
        const fileNameDisplay = document.getElementById('file-name-display');
        const files = fileInput.files;

        if (files.length > 0) {
            fileNameDisplay.textContent = files[0].name;
        } else {
            fileNameDisplay.textContent = 'No file chosen';
        }
    }
</script>
