from collections.abc import Iterator
from typing import Any

import structlog
from django.conf import settings
from django.contrib.admin.filters import AllValuesFieldListFilter
from django.contrib.admin.views.main import ChangeList

logger = structlog.get_logger(__name__)

BUCKET = settings.AWS_STORAGE_BUCKET_NAME


class AllValuesAndNoneFieldListFilter(AllValuesFieldListFilter):
    def choices(self, changelist: ChangeList) -> Iterator[dict[str, Any]]:
        choices = super().choices(changelist)
        yield {
            "selected": self.lookup_val_isnull == "True",
            "query_string": changelist.get_query_string(
                {
                    self.lookup_kwarg_isnull: "True",
                },
                [self.lookup_kwarg],
            ),
            "display": "None",
        }
        yield from choices


def dropdown_filter(filter_cls: type[AllValuesFieldListFilter], title: str) -> type[AllValuesFieldListFilter]:
    class Wrapper(filter_cls):  # type: ignore[valid-type,misc]
        def __new__(cls, *args, **kwargs):  # noqa: ANN002, ANN003, ANN204
            instance = filter_cls(*args, **kwargs)
            instance.title = title
            instance.template = "admin/dropdown_filter.html"
            return instance

    return Wrapper
