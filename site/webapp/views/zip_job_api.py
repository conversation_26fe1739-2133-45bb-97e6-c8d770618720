import boto3
import structlog
from celery import current_app
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.template.response import TemplateResponse
from retrieval.tasks.create_zip import (
    process_zip_job_task,
)

from webapp.models.zip_object import Z<PERSON><PERSON><PERSON>, ZipJobStatus

logger = structlog.get_logger(__name__)


@login_required
def multi_pdf_download_zip(request: HttpRequest) -> HttpResponse:
    """View to initiate ZIP file creation."""
    query_string = request.META.get("QUERY_STRING", "")
    if request.method == "POST":
        template = "vault/zip_files/zip_button.html"
        context = {}
        query_string = request.META.get("QUERY_STRING", "")
        page = query_string.split("page=")[-1]
        if page == "vault_download":
            docs = request.POST.getlist("processed_doc_pks", [])
            is_excel_export = False
        elif page == "dashboard_export":
            docs = request.POST.getlist("selected_line_items", [])
            is_excel_export = True
            context["button_class"] = (
                "h-11 px-3 mt-4 w-full py-2.5 bg-[#924f34] rounded-lg border border-[#924f34] justify-center items-center gap-1.5 inline-flex opacity-50"  # noqa: E501
            )

        if not docs:
            return HttpResponse(status=400)

        # Create a new ZIP job
        job = ZipJob.objects.create(
            user=request.user,
            initial_document_count=len(docs),
        )

        # Start the Celery task
        job_id = str(job.pk)
        task = process_zip_job_task.delay(request.user.id, docs, job_id, is_excel_export=is_excel_export)
        job.task_id = task.task_id
        job.save()
        request.session["zip_job_id"] = job_id
        messages.info(request, "Your ZIP file is being created...")
        context["button_text"] = "Downloading..."

        return TemplateResponse(request, template, context=context)

    return HttpResponse(status=405)


@login_required
def zip_status(request: HttpRequest) -> JsonResponse:
    """Checks if a ZIP file is ready for download and returns its status."""
    if request.method == "GET":
        job_id = request.GET.get("job_id")
        zip_job = ZipJob.objects.for_user(request.user).get(pk=job_id)
        celery_task_status = current_app.AsyncResult(zip_job.task_id)
        if celery_task_status.state not in ["SUCCESS", "STARTED"]:
            zip_job.status = ZipJobStatus.FAILED
            zip_job.error_message = celery_task_status.result
            zip_job.save()
            messages.error(
                request,
                "There was an error creating the ZIP file. Please try again.",
            )

        logger.info(
            "ZipJob status",
            job_id=job_id,
            status=zip_job.status,
            task_id=zip_job.task_id,
            celery_task_status=celery_task_status.state,
        )
        if zip_job.status != ZipJobStatus.PROCESSING:
            request.session["zip_job_id"] = None
        download_url = None
        if zip_job.status == ZipJobStatus.COMPLETED:
            messages.success(
                request,
                "Your ZIP file is ready and downloading!",
            )
            download_url = boto3.client("s3").generate_presigned_url(
                ClientMethod="get_object",
                Params={
                    "Bucket": zip_job.s3_bucket,
                    "Key": zip_job.s3_key,
                },
                ExpiresIn=300,
            )
        return TemplateResponse(
            request,
            "vault/zip_files/zip_status.html",
            {
                "zip_job": zip_job,
                "ZipJobStatus": ZipJobStatus,
                "download_url": download_url,
            },
        )
    return HttpResponse(status=405)
