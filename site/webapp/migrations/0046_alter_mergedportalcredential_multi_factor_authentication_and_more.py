# Generated by Django 5.2.1 on 2025-06-05 23:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0045_rename_receiving_email_userforwardingrule_bridge_email_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='mergedportalcredential',
            name='multi_factor_authentication',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='merged_portal_credential', to='webapp.multifactorauthentication'),
        ),
        migrations.AlterField(
            model_name='mergedportalcredential',
            name='portal_credential',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='merged_portal_credential', to='webapp.portalcredential'),
        ),
    ]
