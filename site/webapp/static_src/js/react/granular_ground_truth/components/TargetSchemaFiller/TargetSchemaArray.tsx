import React, { useEffect, useState } from "react";
import type { BoxData } from "js/react/granular_ground_truth/types/pdfLabeler";
import {
  getArrayItemsSchema,
  removeIdFields,
} from "js/react/granular_ground_truth/lib/schemaEditor";
import { TargetSchemaProps } from "./TargetSchemaInterface";
import TargetSchema from "./TargetSchema";
import Ajv, { JSONSchemaType } from "ajv";
import addFormats from "ajv-formats";

const ajv = new Ajv({
  strictSchema: "log",
});

addFormats(ajv);

const TargetSchemaArray: React.FC<TargetSchemaProps> = ({
  schema,
  path,
  labeledBoxes,
  targetSchema,
  hideLabels,
  currentPageNumber,
  overlays,
  onDeleteBox,
  onLabelBox,
  extractTextFromBoundingBox,
}) => {
  if (typeof schema !== "object") {
    return <></>;
  }
  const boxData = React.useRef<BoxData | null>(null);
  const [sboxData, setBoxData] = React.useState<BoxData | null>(null);
  const [creatingNew, setCreatingNew] = useState(false);

  const cleanedSchema = removeIdFields(schema);
  const validate = ajv.compile(cleanedSchema);

  const onLabelBoxLocal = (boxes: BoxData[]) => {
    const box = boxes[boxes.length - 1];
    setCreatingNew(false);
    if (box.path && box.data) {
      const paths = box.path.split(".");
      const field = paths[paths.length - 1];
      if (boxData.current) {
        let index = null;
        const regex = `^${path.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\[(\\d+)\\]$`;
        const match = box.path.match(new RegExp(regex));
        if (match) {
          index = parseInt(match[1], 10);
        }
        let new_data = [];
        if (
          index !== null &&
          index >= 0 &&
          index < boxData.current.data.length
        ) {
          new_data = [...boxData.current.data];
          new_data[index] = box.data;
        } else {
          new_data = [...boxData.current.data, box.data];
        }
        const new_box = {
          id: -1,
          pageNumber: -1,
          // TODO: merge bounding boxes.
          boundingBox: box.boundingBox,
          content: "",
          schema: schema,
          data: new_data,
          isValid: validate(new_data),
          isSaved: true,
          path: path,
        };

        boxData.current = new_box;
        boxes.push(new_box);
        onLabelBox(boxes);
        setBoxData(new_box);
      } else {
        const new_data = [box.data];
        const new_box = {
          id: -1,
          pageNumber: -1,
          // TODO: merge bounding boxes.
          boundingBox: box.boundingBox,
          content: "",
          schema: schema,
          data: new_data,
          isValid: validate(new_data),
          // Should isSaved be true here?
          isSaved: true,
          path: path,
        };
        boxData.current = new_box;
        boxes.push(new_box);
        onLabelBox(boxes);
        setBoxData(new_box);
      }
    }
  };
  const onDeleteBoxLocal = (boxes: BoxData[]) => {
    if (onDeleteBox) {
      onDeleteBox(boxes);
    }
  };
  const array_schema = getArrayItemsSchema(schema);
  if (!array_schema) {
    return <></>;
  }
  useEffect(() => {
    if (labeledBoxes && labeledBoxes.length > 0) {
      // && !boxData) {
      const targetUri =
        typeof targetSchema === "object" && targetSchema.$id
          ? targetSchema.$id
          : null;
      const prevBoxData = labeledBoxes.filter(
        (b) => b.targetUri === targetUri && b.path?.startsWith(path + "["),
      );
      if (prevBoxData.length === 0) return;
      const new_data = [];
      for (const box of prevBoxData) {
        new_data.push(box.data);
      }
      const new_box = {
        id: -1,
        pageNumber: -1,
        // TODO: merge bounding boxes.
        boundingBox: prevBoxData[0].boundingBox,
        content: "",
        schema: schema,
        data: new_data,
        isValid: validate(new_data),
        // Should isSaved be true here?
        isSaved: true,
        path: path,
      };
      boxData.current = new_box;
      setBoxData(new_box);
    }
  }, [labeledBoxes, targetSchema]);
  return (
    <div>
      {sboxData?.data.map((item, index) => (
        <TargetSchema
          key={index}
          schema={array_schema}
          path={`${path}[${index}]`}
          labeledBoxes={labeledBoxes}
          targetSchema={targetSchema}
          hideLabels={hideLabels}
          overlays={overlays}
          is_required={false}
          currentPageNumber={currentPageNumber}
          onDeleteBox={onDeleteBoxLocal}
          onLabelBox={onLabelBoxLocal}
          extractTextFromBoundingBox={extractTextFromBoundingBox}
        />
      ))}
      {creatingNew && (
        <TargetSchema
          schema={array_schema}
          path={`${path}[${sboxData?.data.length || 0}]`}
          labeledBoxes={labeledBoxes}
          targetSchema={targetSchema}
          hideLabels={hideLabels}
          overlays={overlays}
          is_required={false}
          currentPageNumber={currentPageNumber}
          onDeleteBox={onDeleteBoxLocal}
          onLabelBox={onLabelBoxLocal}
          extractTextFromBoundingBox={extractTextFromBoundingBox}
        />
      )}
      <button
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
        onClick={() => setCreatingNew(true)}
      >
        Add new item
      </button>
    </div>
  );
};

export default TargetSchemaArray;
