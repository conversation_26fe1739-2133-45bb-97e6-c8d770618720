from functools import wraps

from django.shortcuts import redirect


def unauthenticated_required(redirect_url="dashboard"):  # noqa: ANN001, ANN201
    """
    Decorator to restrict access to views for unauthenticated users.
    Redirects authenticated users to the specified URL.
    """

    def decorator(view_func):  # noqa: ANN001, ANN202
        @wraps(view_func)  # Preserves the original function's metadata
        def wrapper(request, *args, **kwargs):  # noqa: ANN001, ANN002, ANN003, ANN202
            if request.user.is_authenticated:
                return redirect(redirect_url)  # Redirect if user is logged in
            return view_func(request, *args, **kwargs)  # Proceed if user is not logged in

        return wrapper

    return decorator
