"""
Test settings for bridge_project.

This configuration uses an in-memory SQLite database and disables
features not needed during testing for faster test execution.
"""

import structlog

from . import base
from .base import *  # noqa: F403
from .logging_utils import configure_logging

LOGGING = configure_logging("color_console", "INFO", None)
logger = structlog.get_logger(__name__)

logger.info("OVERRIDING TEST SETTINGS")

# Override environment settings
DEBUG = False

# Use in-memory SQLite database for testing
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": ":memory:",
    }
}


# Disable migrations for testing for now.
# We might need them for some tests in future
class DisableMigrations:
    def __contains__(self, item: str) -> bool:
        return True

    def __getitem__(self, item: str) -> None:
        return None


MIGRATION_MODULES = DisableMigrations()

# Use console email backend for testing
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# Disable celery for testing
CELERY_ALWAYS_EAGER = True
CELERY_EAGER_PROPAGATES_EXCEPTIONS = True
CELERY_BROKER_URL = "memory://"

# Disable password hashers for faster tests
PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.MD5PasswordHasher",
]

STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

AWS_STORAGE_BUCKET_NAME = "test-bucket"
SFTP_BUCKET_NAME = "test-bucket"

DEFAULT_FROM_EMAIL = "<EMAIL>"
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
    }
}

INSTALLED_APPS = [app for app in base.INSTALLED_APPS if app != "debug_toolbar"]
MIDDLEWARE = [m for m in base.MIDDLEWARE if not m.startswith("debug_toolbar.")]

# Disable CSRF checks for testing
CSRF_CHECK_TOKENS = False
