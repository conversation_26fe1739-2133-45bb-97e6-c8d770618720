# Bridge Investment Platform - Codebase Summary

## What is Bridge Investment?

Bridge Investment appears to be a financial technology platform that helps investment advisors and their clients manage and track private equity, hedge fund, and alternative investments. The platform automates document retrieval from various investment portals and provides a unified dashboard for portfolio management.

## Key Features

### 1. **Automated Document Retrieval**
- Scrapes 30+ investment portals (Goldman Sachs, Blackstone, KKR, etc.)
- Handles various authentication methods including MFA
- Uses Playwright/Patchright for web automation
- Supports email-based document collection

### 2. **Document Processing**
- AI-powered document classification and summarization
- Extracts key information from PDFs (capital calls, distributions, valuations)
- Uses AWS Bedrock for LLM capabilities
- Stores documents in S3 with metadata in PostgreSQL

### 3. **Portfolio Management**
- Tracks investments across multiple entities
- Provides dashboards and insights
- Supports bulk upload via Excel templates
- Offers document vault with search capabilities

### 4. **Email Integration**
- Automated email forwarding setup via OAuth
- Captures MFA codes from emails
- Processes email attachments
- Uses AWS SNS for webhook integration

## Technical Architecture

### **Tech Stack**
- **Backend**: Python/Django with Django Ninja for APIs
- **Task Queue**: Celery with Redis
- **Database**: PostgreSQL
- **Infrastructure**: AWS (ECS, S3, RDS, Secrets Manager)
- **Frontend**: Server-side rendering with HTMX, minimal JavaScript
- **ML/AI**: LangChain, AWS Bedrock
- **Web Scraping**: Playwright/Patchright
- **IaC**: AWS CDK with TypeScript

### **Key Design Patterns**
1. **Strategy Pattern**: For portal-specific retrieval managers
2. **State Machine**: For retrieval workflow management
3. **Repository Pattern**: For data access
4. **Multi-tenant Architecture**: Organization-based data isolation

### **Security Features**
- Customer credentials stored in AWS Secrets Manager
- Row-level permissions system
- API key authentication for public API
- OAuth integration for email providers

## Development Workflow

### **Local Development**
- Docker-first development approach
- Separate AWS profiles for demo/prod
- UV package manager for Python dependencies
- Strict linting and type checking

### **Testing**
- Demo account that auto-cleans data
- Manual retrieval testing capabilities
- Email webhook testing with ngrok

### **Deployment**
- Custom deployment script (`deploy.py`)
- ECS-based container deployment
- Separate demo and production environments

## Business Model

The platform appears to serve:
1. **Investment Advisory Firms**: Who need to track client portfolios
2. **High Net Worth Individuals**: With multiple alternative investments
3. **Family Offices**: Managing complex investment structures

## Key Challenges Addressed

1. **Portal Fragmentation**: Investors have accounts across many portals
2. **Manual Document Collection**: Time-consuming and error-prone
3. **Data Standardization**: Different portals provide data in various formats
4. **Access Delegation**: Advisors need to access client portals securely

## Notable Implementation Details

- Uses a "demo" organization that auto-deletes data on logout
- Implements email forwarding rules for MFA capture
- Has a sophisticated retry mechanism for failed retrievals
- Supports both automated and manual document upload workflows
- Includes a public API for integrations

This platform essentially acts as a "Plaid for private investments" - aggregating data from multiple sources into a unified interface for better portfolio management and reporting.