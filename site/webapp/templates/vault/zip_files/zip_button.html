{% load static %}
<button disabled
    id="toggle_export"
    class="{{ button_class|default:'h-[37px] px-3 py-2 bg-[#343d36] text-white rounded-lg flex items-center gap-2 opacity-50' }}"
    hx-post="{% url 'multi_pdf_download_zip' %}?page={{ page|default:"vault_download" }}"
    hx-trigger="click"
    hx-include=".item-checkbox"
    hx-swap="outerHTML"
    {# djlint:off #}
    {% if not request.session.zip_job_id %}
    _="
        on change from document or vaultPageChange from document
            set count to (document.querySelectorAll('.item-checkbox:checked').length)
            if count > 0
                remove .opacity-50 remove @disabled add .cursor-pointer
            else
                add .opacity-50 add @disabled='true' remove .cursor-pointer
            end
        end
        on click
            if #zip-loader then remove .hidden from #zip-loader
        end
    "
    {% endif %}
    {# djlint:on #}
    >
    {% if include_download_icon is None or include_download_icon %}
        <img src="{% static 'assets/images/download_icon.svg' %}"
             width="18"
             height="18"
             alt="Export">
    {% endif %}
    {% if button_class is not None %}
        <div class="text-white text-base font-semibold font-inter text-[16px] leading-normal">
            {{ button_text|default:"Download" }}
        </div>
    {% else %}
        {{ button_text|default:"Download" }}
    {% endif %}
</button>
{% include "vault/zip_files/zip_status.html" %}
