import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON><PERSON>ator
from pathlib import Path

import structlog
from dateutil import parser
from playwright.async_api import Locator, expect
from playwright.async_api import TimeoutError as PlaywrightTimeoutError

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.get_logger(__name__)


class KleinerPerkinsAndCAZPortalManagerAbstract(RetrievalManager):
    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        # # Focus and fill the email
        await self.page.get_by_placeholder("Username").count()
        await self.page.get_by_placeholder("Username").fill(username)
        await asyncio.sleep(2)
        # Focus and fill the password
        await self.page.get_by_placeholder("Password").count()
        await self.page.get_by_placeholder("Password").fill(password)
        await asyncio.sleep(2)
        # playwright docs: locating by text "Log in" matches <input type=button value="Log in">
        await self.page.get_by_text("Log in").click()
        await asyncio.sleep(5)

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(5)
        return await self.page.get_by_text("Send email message to").count() > 0

    async def _send_otp(self) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.locator(".twoFactorOptions-Send").click()
        await asyncio.sleep(1)

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.get_by_text("Remember me on this device").check()
        await asyncio.sleep(1)
        await self.page.locator("input.twoFactorEntry-Code").fill(otp)
        await asyncio.sleep(2)
        await self.page.get_by_text("Verify").click()

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.wait_for_load_state("networkidle")
        return (
            sum(
                [
                    await self.page.get_by_placeholder("Username").count(),
                    await self.page.get_by_placeholder("Password").count(),
                    await self.page.get_by_text("Log in").count(),
                    await self.page.get_by_text("Send email message to").count(),
                    await self.page.locator(".twoFactorOptions-Send").count(),
                    await self.page.get_by_text("Remember me on this device").count(),
                    await self.page.locator("input.twoFactorEntry-Code").count(),
                    await self.page.get_by_text("Verify").count(),
                ]
            )
            == 0
        )

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.locator(".primaryNav a").filter(has_text="Documents").click()
        await asyncio.sleep(5)
        while await self.page.locator("#loading:visible").count() > 0:  # noqa: ASYNC110
            await asyncio.sleep(5)
        for table_section in await self.page.locator("#results #accordion .tableSectionHeader").all():
            fund_name = (await table_section.locator(".tableFundInfoLine1 .tableFundName").inner_text()).strip()
            fund_asset_class_locator = table_section.locator(".tableFundInfoLine2 .tableFundAssetClass")
            fund_asset_class = ""
            if await fund_asset_class_locator.count() > 0:
                fund_asset_class = (await fund_asset_class_locator.inner_text()).strip()
            expand_button_div = table_section.locator(".tableFundArrow")
            await expand_button_div.click()
            try:
                # Wait for the expand button to expand
                await expect(expand_button_div.locator("img.open")).to_have_count(1, timeout=30000)
            except PlaywrightTimeoutError:
                logger.exception("Row did not expand after timeout")
                raise CouldNotFindHTMLElementError from PlaywrightTimeoutError
            await asyncio.sleep(1)
            fund_content_locator = self.page.locator(".fundcontent:visible")
            if await fund_content_locator.count() != 1:
                # should only have one visible
                raise CouldNotFindHTMLElementError
            for table_row in await fund_content_locator.locator(".tableRow").all():
                async for doc in self._retrieve_row(table_row, fund_name, fund_asset_class):
                    yield doc
            # collapse section
            await asyncio.sleep(1)
            await expand_button_div.click()
            try:
                # Wait for the expand button to expand
                await expect(expand_button_div.locator("img.open")).to_have_count(0, timeout=30000)
            except PlaywrightTimeoutError:
                logger.exception("Row did not collapse after timeout")
                raise CouldNotFindHTMLElementError from PlaywrightTimeoutError
            await asyncio.sleep(1)

    async def _retrieve_row(
        self, table_row: Locator, fund_name: str, fund_asset_class: str
    ) -> AsyncGenerator[RawDocumentTuple, None]:
        await table_row.scroll_into_view_if_needed()
        raw_date = (await table_row.locator(".tableDate").inner_text()).strip()
        parsed_date = parser.parse(
            raw_date,
        )
        title = (await table_row.locator(".tableDocTitleLink").inner_text()).strip()
        investor = (await table_row.locator(".tableInvestor").inner_text()).strip()
        category = (await table_row.locator(".tableCategory").inner_text()).strip()
        doc_type = self._document_mapper(category)
        metadata = json.dumps(
            {
                "Document Name": title,
                "Fund Name": fund_name,
                "Fund Asset Class": fund_asset_class,
                "Investor": investor,
                "Category": category,
                "Document Date": raw_date,
            }
        )
        if await self.should_download_document(metadata):
            doc = await self._download_doc(table_row, metadata, parsed_date, doc_type)
            await asyncio.sleep(2)
            if doc is not None:
                yield doc
        await asyncio.sleep(2)

    async def _download_doc(
        self, row_locator: Locator, metadata: str, parsed_date: datetime.datetime, doc_type: str
    ) -> RawDocumentTuple:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        await row_locator.locator(".tableActionDropdown .tableActionDropdownInner.dropdown_button").click()
        await asyncio.sleep(1)
        download_button = row_locator.locator(".actionDropdown.dropdown .action.actionDownloadCheckDisclaimer")
        try:
            async with self.page.expect_download(timeout=30000) as download_info:
                await download_button.click()
            download = await download_info.value
            await asyncio.sleep(1)
            file_name = self.local_directory / download.suggested_filename
            await download.save_as(file_name)
        except PlaywrightTimeoutError as playwright_error:
            # TODO: handle consent to electronic documents.
            # should just ask the user to do it themselves since it only needs to happen once
            # For tax documents, download doesn't start until user consent to electronic tax documents
            # NOTE: this part of the code is untested. Once the user consented once the modal no longer appears
            logger.warning("Download didn't start for %s. Checking for consent dialogs...", metadata)
            # this is the element we want to click:
            # <input type="submit" name="AcceptButton" value="I Accept" id="AcceptButton" class="button">
            consent_button = self.page.locator("#AcceptButton")  # TODO: check this locator
            if await consent_button.count() > 0:
                # Found consent button, click it and expect download to start
                async with self.page.expect_download(timeout=30000) as download_info:
                    await consent_button.click()
                download = await download_info.value
                await asyncio.sleep(1)
                file_name = Path(f"/tmp/{download.suggested_filename}")  # noqa: S108 # nosec
                await download.save_as(file_name)
            else:
                # No consent button found, raise
                logger.exception("Download failed and no consent button was found")
                raise ValueError from playwright_error

        content = None
        if file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.warning("Unsupported file format: %s", file_name.suffix)
            raise ValueError
        file_name.unlink()
        await row_locator.locator(
            ".actionDropdown.dropdown.downloadprocessing .close-button_holder .close-button"
        ).click()
        await asyncio.sleep(1)

        return RawDocumentTuple(
            name=download.suggested_filename,
            date=parsed_date,
            doc_type=doc_type,
            content=content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    def _document_mapper(self, category: str) -> str:
        """
        Placeholder method that should be overridden by child classes.
        """
        raise NotImplementedError
