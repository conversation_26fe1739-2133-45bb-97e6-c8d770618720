import { cn } from "../../lib/utils";
import { jsonSchemaType, type SchemaType } from "../../types/jsonSchema";
import React, { useState } from "react";
import type { JSONSchema } from "../../types/jsonSchema";
import SchemaCustomObjectSelector from "./SchemaCustomObjectSelector";
interface SchemaTypeSelectorProps {
  id?: string;
  value: SchemaType;
  schemas?: JSONSchema[]; // List of all schemas for custom object selection
  onChange: (value: JSONSchema) => void;
}

interface TypeOption {
  id: SchemaType;
  label: string;
  description: string;
}

const typeOptions: { id: SchemaType; label: string; description: string }[] = [
  {
    id: "string",
    label: "Text",
    description: "For text values like names, descriptions, etc.",
  },
  {
    id: "number",
    label: "Number",
    description: "For decimal or whole numbers",
  },
  {
    id: "boolean",
    label: "Yes/No",
    description: "For true/false values",
  },
  {
    id: "object",
    label: "Group",
    description: "For grouping related fields together",
  },
  {
    id: "array",
    label: "List",
    description: "For collections of items",
  },
];

const SchemaTypeSelector: React.FC<SchemaTypeSelectorProps> = ({
  id,
  value,
  schemas,
  onChange,
}) => {
  const [type, setType] = useState<SchemaType>("string");
  const [arrayItemType, setArrayItemType] = useState<JSONSchema | null>(null);
  const handleSelectionChange = (newType: SchemaType) => {
    if (newType === "array") {
      // If array, we need to select a type for the items
      setType("array");
      onChange({ type: newType } as JSONSchema);
      return;
    }
    onChange({ type: newType } as JSONSchema);
    setArrayItemType(null);
    setType(newType);
  };
  const handleFieldTypeChange = (newType: JSONSchema | null | undefined) => {
    if (!newType) return;
    if (typeof newType === "boolean") return;
    setArrayItemType(newType);
    onChange({ type: "array", items: newType } as JSONSchema);
  };
  return (
    <>
      <div
        id={id}
        className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 gap-2"
      >
        {typeOptions.map((type) => (
          <button
            type="button"
            key={type.id}
            title={type.description}
            className={cn(
              "p-2.5 rounded-lg border-2 text-left transition-all duration-200",
              value === type.id
                ? "border-green-500 bg-gray-800/5 shadow-sm" // Green border when selected
                : "border-border hover:border-primary/30 hover:bg-gray-900",
            )}
            onClick={() => handleSelectionChange(type.id)}
          >
            <div className="font-medium text-sm">{type.label}</div>
            <div className="text-xs text-white line-clamp-1">
              {type.description}
            </div>
          </button>
        ))}
      </div>
      {type === "array" && (
        <div>
          <div className="flex flex-wrap items-center gap-2 mb-1.5">
            <label htmlFor="fieldType" className="text-sm font-medium">
              Array Item Type
            </label>
          </div>
          <SchemaCustomObjectSelector
            schemas={schemas}
            onChange={handleFieldTypeChange}
          />
        </div>
      )}
    </>
  );
};

export default SchemaTypeSelector;
