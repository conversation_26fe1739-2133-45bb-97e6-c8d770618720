{% load customtags %}
<div id="retrieval_status" class="text-[#616863] font-normal text-center">
    {% if retrieval_status == RetrievalStatus.BLOCKED_LOGIN_OTP %}
        <div>{% label_lookup label=retrieval_status enum=RetrievalStatus %}</div>
        <div class="hidden"
             hx-get="{% url 'multi_factor_authentication_enter_otp' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
             hx-target="#modal-container-{{ merged_portal_credential.pk }}"
             hx-trigger="load delay:1s"
             hx-swap="outerHTML"></div>
    {% else %}
        {% label_lookup label=retrieval_status enum=RetrievalStatus %}...
    {% endif %}
</div>
