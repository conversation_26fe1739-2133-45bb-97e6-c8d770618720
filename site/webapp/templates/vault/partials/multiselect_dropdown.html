<!-- Multi-level multiselect dropdown component -->
<!-- Hidden input for JSON data -->
<input type="hidden"
       id="{{ column_name }}JsonData"
       name="{{ column_name }}_data"
       class="vaultinputs"
       value=""
       hx-post="{% url 'vault' %}"
       hx-target="#docvault_body"
       hx-swap="innerHTML"
       hx-trigger="change"
       hx-include="#docvault_header, .vaultinputs" />
<!-- Dropdown Panel using same pattern as existing dropdowns -->
<div id="dropdownSearch-{{ column_name }}"
     class="absolute z-10 hidden bg-white border border-gray-200 rounded shadow-md w-80">
    <div class="p-4 space-y-2">
        <h3 class="font-medium text-gray-700 text-xs">Filter by Category:</h3>
        <!-- Dynamic Items -->
        <div class="space-y-2 max-h-60 overflow-y-auto"
             id="{{ column_name }}-filter-list">
            {% for item in filter_items %}
                <div class="relative">
                    <div class="flex items-center justify-between px-3 py-2 rounded-lg hover:bg-gray-100 border border-gray-200">
                        <label class="flex items-center gap-2">
                            <input type="checkbox"
                                   class="{{ column_name }}-checkbox w-4 h-4 accent-[#4B554D] focus:ring-0 focus:outline-none cursor-pointer"
                                   data-parent="{{ item.value }}"
                                   {% if item.submenu %}data-submenu="submenu-{{ item.value }}"{% endif %} />
                            <span class="text-xs font-medium">{{ item.label }}</span>
                        </label>
                        {% if item.submenu %}
                            <button class="text-gray-500 submenu-toggle"
                                    data-target="submenu-{{ item.value }}">▶</button>
                        {% endif %}
                    </div>
                    {% if item.submenu %}
                        <div id="submenu-{{ item.value }}"
                             class="submenu mt-2 ml-6 border-l border-gray-300 pl-4 space-y-2 hidden">
                            {% for subitem in item.submenu %}
                                <label class="flex items-center gap-2">
                                    <input type="checkbox"
                                           class="sub-checkbox submenu-{{ item.value }} w-4 h-4 accent-[#4B554D] focus:ring-0 focus:outline-none cursor-pointer"
                                           data-parent="{{ item.value }}"
                                           data-child="{{ subitem.value }}" />
                                    <span class="text-xs font-medium">{{ subitem.label }}</span>
                                </label>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
        <!-- Actions -->
        <div class="pt-2 flex gap-2">
            <button id="{{ column_name }}ResetBtn"
                    class="w-1/2 text-[#953300] border border-[#953300] px-4 py-2 rounded-xl font-semibold hover:bg-[#953300] hover:text-white">
                Reset
            </button>
            <button id="{{ column_name }}ApplyBtn"
                    class="w-1/2 bg-[#953300] text-white px-4 py-2 rounded-xl font-semibold hover:bg-[#7a2600]">
                Apply
            </button>
        </div>
    </div>
</div>
<script>
    // Initialize multiselect for {{ column_name }}
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownMenu = document.getElementById("dropdownSearch-{{ column_name }}");
        const submenuToggles = dropdownMenu?.querySelectorAll(".submenu-toggle");
        const resetBtn = document.getElementById("{{ column_name }}ResetBtn");
        const applyBtn = document.getElementById("{{ column_name }}ApplyBtn");
        const jsonInput = document.getElementById("{{ column_name }}JsonData");

        // Function to collect hierarchical data
        function collectHierarchicalData() {
            const data = {};
            const parentCheckboxes = dropdownMenu.querySelectorAll(".{{ column_name }}-checkbox");

            parentCheckboxes.forEach(parentCb => {
                const parentValue = parentCb.dataset.parent;
                const hasSubmenu = parentCb.dataset.submenu;

                if (parentCb.checked) {
                    if (hasSubmenu) {
                        const childCheckboxes = dropdownMenu.querySelectorAll(`input.submenu-${parentValue}:checked`);
                        const selectedChildren = Array.from(childCheckboxes).map(cb => cb.dataset.child);
                        if (selectedChildren.length > 0) {
                            data[parentValue] = selectedChildren;
                        }
                    } else {
                        data[parentValue] = [];
                    }
                }
            });

            return data;
        }

        // Function to update JSON input
        function updateJsonInput() {
            const hierarchicalData = collectHierarchicalData();
            jsonInput.value = JSON.stringify(hierarchicalData);
        }

        if (dropdownMenu) {
            // Submenu toggle logic
            submenuToggles?.forEach(btn => {
                btn.addEventListener("click", (e) => {
                    e.preventDefault();
                    const targetId = btn.getAttribute("data-target");
                    const submenu = document.getElementById(targetId);
                    if (submenu) {
                        submenu.classList.toggle("hidden");
                        btn.innerHTML = submenu.classList.contains("hidden") ? "▶" : "▼";
                    }
                });
            });

            // Parent checkbox logic
            document.querySelectorAll(".{{ column_name }}-checkbox").forEach(parentCb => {
                parentCb.addEventListener("change", () => {
                    const parentValue = parentCb.dataset.parent;
                    const submenuId = parentCb.getAttribute("data-submenu");

                    if (submenuId) {
                        const childCheckboxes = dropdownMenu.querySelectorAll(`input.submenu-${parentValue}`);
                        childCheckboxes.forEach(childCb => {
                            childCb.checked = parentCb.checked;
                        });
                    }
                    updateJsonInput();
                });
            });

            // Child checkbox logic
            document.querySelectorAll(`.sub-checkbox`).forEach(childCb => {
                childCb.addEventListener("change", () => {
                    const parentValue = childCb.dataset.parent;
                    const parentCheckbox = dropdownMenu.querySelector(`.{{ column_name }}-checkbox[data-parent="${parentValue}"]`);

                    if (parentCheckbox) {
                        const checkedChildren = dropdownMenu.querySelectorAll(`input.submenu-${parentValue}:checked`);
                        parentCheckbox.checked = checkedChildren.length > 0;
                    }
                    updateJsonInput();
                });
            });

            // Reset button
            resetBtn?.addEventListener("click", (e) => {
                e.preventDefault();
                document.querySelectorAll(`#dropdownSearch-{{ column_name }} input[type=checkbox]`).forEach(cb => {
                    cb.checked = false;
                });
                updateJsonInput();
                jsonInput.dispatchEvent(new Event('change', {
                    bubbles: true
                }));
            });

            // Apply button
            applyBtn?.addEventListener("click", (e) => {
                e.preventDefault();
                updateJsonInput();
                jsonInput.dispatchEvent(new Event('change', {
                    bubbles: true
                }));
            });

            // Initialize
            updateJsonInput();
        }
    });
</script>
