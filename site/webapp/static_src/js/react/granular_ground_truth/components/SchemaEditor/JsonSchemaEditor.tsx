import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "../../components/ui/tabs";
import { cn } from "../../lib/utils";
import type { JSONSchema } from "../../types/jsonSchema";
import { Maximize2 } from "lucide-react";
import { ChevronDown, ChevronRight, X } from "lucide-react";
import React from "react";
import { useRef, useState } from "react";
import JsonSchemaVisualizer from "./JsonSchemaVisualizer";
import SchemaVisualEditor from "../SchemaEditor/SchemaVisualEditor";

interface JsonSchemaEditorProps {
  schema?: JSONSchema;
  schemas?: JSONSchema[]; // List of all schemas for custom object selection
  setSchema?: (schema: JSONSchema) => void;
  className?: string;
  collapse?: boolean; // Whether to start expanded
}

const JsonSchemaEditor: React.FC<JsonSchemaEditorProps> = ({
  schema = { type: "object" },
  schemas = [],
  collapse = true,
  setSchema,
  className,
}) => {
  // Handle schema changes and propagate to parent if needed
  const handleSchemaChange = (newSchema: JSONSchema) => {
    if (setSchema) {
      setSchema(newSchema);
    }
  };
  const [expanded, setExpanded] = useState(!collapse);

  return (
    <div className={cn("json-editor-container w-full", className)}>
      {/* For mobile screens - show as tabs */}
      <div className="block w-full">
        <Tabs defaultValue="visual" className="w-full">
          <div className="flex items-center gap-2">
            <button
              type="button"
              className="text-white hover:text-foreground transition-colors"
              onClick={() => setExpanded(!expanded)}
              aria-label={expanded ? "Collapse" : "Expand"}
            >
              {expanded ? (
                <ChevronDown size={18} />
              ) : (
                <ChevronRight size={18} />
              )}
            </button>
            <h3 className="font-medium">
              {typeof schema === "object" && schema.title
                ? schema.title
                : "Untitled Schema"}
            </h3>
          </div>
          {expanded && (
            <div>
              <div className="flex items-center gap-2">
                <TabsList className="grid grid-cols-2 w-[200px]">
                  <TabsTrigger value="visual">Visual</TabsTrigger>
                  <TabsTrigger value="json">JSON</TabsTrigger>
                </TabsList>
              </div>

              <TabsContent
                value="visual"
                className={cn("focus:outline-none w-full", "h-[500px]")}
              >
                <SchemaVisualEditor
                  schema={schema}
                  schemas={schemas}
                  onChange={handleSchemaChange}
                />
              </TabsContent>

              <TabsContent
                value="json"
                className={cn("focus:outline-none w-full", "h-[500px]")}
              >
                <JsonSchemaVisualizer
                  schema={schema}
                  onChange={handleSchemaChange}
                />
              </TabsContent>
            </div>
          )}
        </Tabs>
      </div>
    </div>
  );
};

export default JsonSchemaEditor;
