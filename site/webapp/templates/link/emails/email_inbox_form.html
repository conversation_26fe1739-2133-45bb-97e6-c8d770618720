{% load customtags widget_tweaks static %}
<form class="flex flex-col gap-4"
      {% if merged_portal_credential_pk %} hx-post="{% url 'emails_edit_setup' %}{% urlparams merged_portal_credential_pk=merged_portal_credential_pk %}"{% else %} hx-post="{% url 'emails_onboard_new_email_inbox' %}" {% endif %}
      hx-swap="outerHTML"
      hx-target="#modal">
    {% csrf_token %}
    {% if form.non_field_errors %}
        <div class="w-full text-red-500 text-sm space-y-1 mb-4">
            {% for error in form.non_field_errors %}<div>{{ error }}</div>{% endfor %}
        </div>
    {% endif %}
    <div>
        <div class="flex items-center gap-2">
            <label for="{{ form.email.id_for_label }}"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">{{ form.email.label|title }}</label>
            {% render_field form.email class="w-full px-4 py-2.5 border border-[#eceee8] rounded-lg text-sm text-[#4b554d]" %}
        </div>
        <div class="flex items-center gap-2">
            <div class="w-32"></div>
            <div class="w-full">
                {% if form.email.errors %}
                    <div class="px-2 text-red-500 text-xs space-y-1 mt-1">
                        {% for error in form.email.errors %}<span>{{ error }}</span>{% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="flex items-center gap-2">
        <label for="{{ form.email_provider.id_for_label }}"
               class="w-32 text-left text-sm text-[#4b554d] font-medium">{{ form.email_provider.label|title }}</label>
        {% render_field form.email_provider class="w-full px-4 py-2.5 border border-[#eceee8] rounded-lg text-sm text-[#4b554d]" %}
    </div>
    <button type="submit"
            name="submit"
            class="w-full px-4 py-2.5 bg-[#924f34] text-md font-semibold text-white rounded-lg hover:bg-[#8a5f47] transition-colors">
        Next
    </button>
</form>
