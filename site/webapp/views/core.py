import structlog
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from django.urls import reverse
from django.utils import timezone

logger = structlog.get_logger(__name__)


def home(request: HttpRequest) -> HttpResponse:
    if request.user.is_authenticated and request.user.is_staff and request.user.is_superuser:
        return HttpResponseRedirect(reverse("admin:index"))
    return HttpResponseRedirect("dashboard")


@login_required
def tos_agreement(request: HttpRequest) -> HttpResponse:
    template = "dashboard/tos_agreement.html"
    if request.method == "POST":
        user = request.user
        user.tos_accepted_at = timezone.now().date()  # Update TOS acceptance date
        user.save()
        messages.success(request, "Thank you for accepting the Terms of Service.")
        return HttpResponseRedirect("dashboard")  # Redirect to dashboard after accepting

    return TemplateResponse(request, template, context={"hide_sidebar": True})
