{% load static %}
<!-- Upload Modal -->
<div id="upload-modal" class="relative">
    <div class="flex flex-row font-['Inter']">
        <button type="button"
                _="on click trigger closeModal"
                class="absolute top-3 right-3 text-gray-400 hover:text-gray-800 transition">
            <img src="{% static 'assets/images/close.svg' %}"
                 width="16"
                 height="16"
                 alt="Close">
        </button>
        {# djlint:off #} <div style="min-width:600px;">{# djlint:on #}
        <!-- PDF viewer -->
        {% if doc_file_type == ".pdf" %}
            <embed src="{{ pdf_link }}"
                   title="{{ document_name }}"
                   width="100%"
                   height="100%"
                   type="application/pdf"
                   frameBorder="0"
                   scrolling="auto" />
        {% else %}
            <div class="bg-white w-full h-full flex items-center justify-center p-4">
                <a class="underline text-[#924f34]" href="{{ pdf_link }}">Non-PDF document, click here to download</a>
            </div>
        {% endif %}
    </div>
    {# djlint:off #} <div class="bg-[#eceee8] p-8 shadow-xl" style="min-width:600px;">{# djlint:on #}
    <form id="upload-form"
          class="space-y-4 m-6 p-4 bg-white rounded-xl transform transition-all"
          hx-encoding="multipart/form-data"
          hx-post="{% url 'label_document' %}"
          hx-target="#modal"
          hx-vals='{"raw_document_ids": [{% for id in raw_document_ids %} "{{ id }}" {% if not forloop.last %},{% endif %}{% endfor %}], "total_docs": {{ total_docs }}, "current_doc_index": {{ current_doc_index }}, "client": "{{ selected_client.pk }}", "entity": "{{ selected_entity.pk }}", "investment": "{{ selected_investment.pk }}" }'
          hx-trigger="submit">
        <!-- Header + delete button -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex flex-col">
                <h2 class="text-lg font-medium">Label your documents</h2>
                <span class="text-sm text-gray-600">{{ current_doc_index|add:"1" }}/{{ total_docs }}</span>
            </div>
            <div class="flex items-center">
                <button class="flex flex-row gap-1 flex-1 px-4 py-2.5 border text-xs text-[#343d36] rounded-md shadow-xs hover:bg-[#f7f4f2] transition-colors"
                        type="button"
                        hx-post="{% url 'delete_label_document' %}"
                        hx-target="#modal"
                        hx-vals='{"raw_document_ids": [{% for id in raw_document_ids %} "{{ id }}" {% if not forloop.last %},{% endif %}{% endfor %}], "current_doc_index": {{ current_doc_index }} }'>
                    <img src="{% static 'assets/images/trash.svg' %}"
                         width="14"
                         height="14"
                         alt="Delete">
                    <span class="flex items-center">Delete this document</span>
                </button>
            </div>
        </div>
        <!-- Labelling fields -->
        <div class="flex items-center gap-2">
            <label for="document-name"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Document:</label>
            <input type="text"
                   id="document-name"
                   name="document_name"
                   {% if document_name %}value="{{ document_name }}"{% endif %}
                   class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d] cursor-not-allowed"
                   disabled>
        </div>
        <div class="flex items-center gap-2">
            <label for="document-rename"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Rename:</label>
            <input type="text"
                   id="document-rename"
                   name="document_rename"
                   placeholder="Rename your document"
                   pattern=".*\.(pdf|PDF|xlsx|XLSX|xls|XLS|doc|DOC|docx|DOCX|zip|ZIP|csv|CSV)$"
                   class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                   title="File name must end with: .pdf, .xlsx, .xls, .doc, .docx, .zip, or .csv">
        </div>
        <!-- Disabled line item fields -->
        <div id="dropdowns-container" class="flex flex-col gap-4">
            <div class="flex items-center gap-2">
                <label for="client_name"
                       class="w-32 text-left text-sm text-[#4b554d] font-medium">Client:</label>
                <input type="text"
                       name="client_name"
                       id="client_name"
                       {% if selected_client.legal_name %}value="{{ selected_client.legal_name }}"{% endif %}
                       class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d] cursor-not-allowed"
                       disabled />
            </div>
            <div class="flex items-center gap-2">
                <label for="entity_name"
                       class="w-32 text-left text-sm text-[#4b554d] font-medium">
                    Investing
                    Entity:
                </label>
                <input type="text"
                       name="entity_name"
                       id="entity_name"
                       {% if selected_entity.legal_name %}value="{{ selected_entity.legal_name }}"{% endif %}
                       class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d] cursor-not-allowed"
                       disabled />
            </div>
            <div class="flex items-center gap-2">
                <label for="investment_name"
                       class="w-32 text-left text-sm text-[#4b554d] font-medium">Investment:</label>
                <input type="text"
                       name="investment_name"
                       id="investment_name"
                       {% if selected_investment.legal_name %}value="{{ selected_investment.legal_name }}"{% endif %}
                       class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d] cursor-not-allowed"
                       disabled />
            </div>
        </div>
        <!-- Date fields + Doc type -->
        <div class="flex items-center gap-2">
            <label for="effective-date"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">
                Effective
                Date:
            </label>
            <input type="date"
                   id="effective-date"
                   name="effective_date"
                   {% if selected_effective_date %}value="{{ selected_effective_date }}"{% endif %}
                   class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                   required>
        </div>
        <div class="flex items-center gap-2">
            <label for="posted-date"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Posted Date:</label>
            <input type="date"
                   id="posted-date"
                   name="posted_date"
                   {% if selected_posted_date %} value="{{ selected_posted_date }}" {% else %} value='{% now "Y-m-d" %}' {% endif %}
                   class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                   required>
        </div>
        <div class="flex items-center gap-2">
            <label for="document-type"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Category:</label>
            <select name="document_type"
                    id="document-type"
                    class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                    required
                    hx-get="{% url 'get_valid_sub_document_types' %}"
                    hx-target="#sub-document-type-container"
                    hx-trigger="change"
                    hx-swap="outerHTML"
                    hx-include="[name='document_type']">
                <option value="">Select Document Type</option>
                {% for choice in DocumentType.choices %}
                    <option value="{{ choice.0 }}"
                            {% if choice.0 == selected_document_type %}selected{% endif %}>{{ choice.1 }}</option>
                {% endfor %}
            </select>
        </div>
        <div id="sub-document-type-container">
            {% if valid_sub_document_types %}
                <div class="flex items-center gap-2">
                    <label for="sub-document-type"
                           class="w-32 text-left text-sm text-[#4b554d] font-medium">Sub Category:</label>
                    <select name="sub_document_type"
                            id="sub-document-type"
                            class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]">
                        <option value="">Select Sub Document Type</option>
                        {% for choice in valid_sub_document_types %}
                            <option value="{{ choice.value }}"
                                    {% if choice.value == selected_sub_document_type %}selected{% endif %}>
                                {{ choice.label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            {% endif %}
        </div>
        <!-- Nav buttons -->
        <div class="flex justify-between items-center gap-4 mt-6">
            <button type="button"
                    hx-get="{% url 'label_document' %}"
                    hx-target="#modal"
                    hx-vals='{"raw_document_ids": [{% for id in raw_document_ids %} "{{ id }}" {% if not forloop.last %},{% endif %}{% endfor %}], "total_docs": {{ total_docs }}, "current_doc_index": {{ current_doc_index }} }'
                    {% if current_doc_index == 0 %}disabled{% endif %}
                    class="flex-1 px-4 py-2.5 border text-md font-semibold border-[#924f34] text-[#924f34] rounded-lg hover:bg-[#f7f4f2] transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                Previous Document
            </button>
            <button type="submit"
                    class="flex-1 px-4 py-2.5 bg-[#924f34] text-md font-semibold text-white rounded-lg hover:bg-[#8a5f47] transition-colors">
                {% if current_doc_index|add:"1" < total_docs %}
                    Next Document
                {% else %}
                    Confirm
                {% endif %}
            </button>
        </div>
    </form>
</div>
</div>
</div>
