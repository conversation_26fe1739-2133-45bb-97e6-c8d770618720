#!/usr/bin/env -S uv run
import os
import sys
from pathlib import Path
from typing import Any

import boto3
import click
import django
import numpy as np
import pandas as pd
from dateutil import parser

sys.path.append("site")
os.environ["USE_LOCALHOST"] = "1"
os.environ["DJANGO_SETTINGS_MODULE"] = "bridge_project.settings"
os.environ["ENVIRONMENT"] = "dev"
django.setup()
from webapp.models.documents import DocumentType

# Then use the session to get the resource
s3 = boto3.client("s3")


@click.group()
def cli() -> None:
    pass


@cli.command()
@click.option("--local-folder", default=None, help="Local Folder where demo is located")
@click.option("--s3-folder", default=None, help="S3 folder path to upload to")
@click.option("--upload", default=False, is_flag=True, help="Flag to actually upload")
def update_demo(local_folder: str | None, s3_folder: str | None, *, upload: bool = False) -> None:  # noqa: C901, PLR0912, PLR0915
    if local_folder is None:
        raise ValueError
    path = Path(local_folder)
    bucket = None
    if os.environ.get("AWS_PROFILE", "demo") == "demo":
        bucket = "bridge-demo-userdata-bucket"
    elif os.environ.get("AWS_PROFILE", "demo") == "prod":
        bucket = "bridge-prod-userdata-bucket"
    if bucket is None:
        raise ValueError
    df = pd.read_csv("./site/retrieval/core/managers/demo/demo_data.csv").replace({np.nan: None})
    df["Posted Date"] = df["Posted Date"].apply(parser.parse).apply(lambda x: x.strftime("%Y-%m-%d"))
    df["Effective Date"] = df["Effective Date"].apply(parser.parse).apply(lambda x: x.strftime("%Y-%m-%d"))
    doctype_lookup = {x.label: str(x) for x in DocumentType}
    df["Document Type"] = df["Document Type"].apply(doctype_lookup.get)
    required_columns = ["Client Name", "Investment Entity Legal Name", "Managing Firm Name",
       "Investment / Fund Legal Name", "shorthand", "Doc File Name",
       "Document Type", "Effective Date", "Posted Date", "Capital Call Amount",
       "Distribution Amount", "Committed", "Invested", "Total Value",
       "Realized Value", "Unrealized Value"]

    key2fn: dict[str, list[Path]] = {}
    to_upload :list[tuple[Path, str]]= []
    for local_doc_path in path.resolve().glob("**/*"):
        if not str(local_doc_path).lower().endswith(".pdf") and not str(local_doc_path).lower().endswith("xlsx"):
            continue
        idx = str(local_doc_path).index("Doc Vault")
        demo_object_path = f"{s3_folder}/" + str(local_doc_path)[idx:]
        to_upload.append((local_doc_path, demo_object_path))
        key = local_doc_path.name.removesuffix(local_doc_path.suffix)
        if key not in key2fn:
            key2fn[key] = []
        key2fn[key].append(local_doc_path)
        click.echo(f"Would have uploaded {local_doc_path} to {demo_object_path}")

    click.echo(f"Done uploading to {bucket}/{s3_folder}")
    for column in df.columns:
        if column not in required_columns:
            raise ValueError

    for key, fn in key2fn.items():
        if len(fn) > 1:
            click.echo(f"Duplicate key {key} found with files: {fn}")

    lookup: dict[str, list[dict[str, Any]]] = {}
    for _, row in df.iterrows():
        zip_path = row["Doc File Name"]
        if zip_path not in lookup:
            lookup[zip_path] = []
        lookup[zip_path].append(row.to_dict())

    not_in_file_path = set(lookup.keys()) - set(key2fn.keys())
    if len(not_in_file_path) > 0:
        click.echo("NOT IN FILEPATH")
        for key in sorted(not_in_file_path):
            click.echo(key)

    not_in_csv = set(key2fn.keys()) -  set(lookup.keys())
    if len(not_in_csv) > 0:
        click.echo("NOT IN CSV")
        for key in sorted(not_in_csv):
            click.echo(key)

    if len(not_in_csv) > 0:
        # Every file should be in CSV.
        raise ValueError

    if len(not_in_file_path) == 0:
        # We should have a few hold out documents for upload / email.
        raise ValueError

    if upload:
        for to_upload_path, s3_key in to_upload:
            click.echo(f"Uploading {to_upload_path} to {s3_key}")
            with to_upload_path.open("rb") as f:
                _bytes = f.read()
                s3.put_object(Bucket=bucket, Key=s3_key, Body=_bytes)

if __name__ == "__main__":
    update_demo()
