import datetime
import uuid
from collections import defaultdict
from dataclasses import dataclass
from itertools import groupby
from uuid import uuid4

import structlog
from django.contrib import messages
from django.contrib.auth import update_session_auth_hash
from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.db.models import Q
from django.http import HttpRequest, HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.template.response import TemplateResponse
from django.urls import reverse
from django.views.decorators.http import require_GET, require_http_methods, require_POST
from retrieval.tasks.send_mail import send_invitation_email

from webapp.forms import (
    DeletePermissionBatchForm,
    InvestingEntityForm,
    PasswordUpdateForm,
    ProfileForm,
    UserInviteForm,
    UserPermissionsForm,
)
from webapp.models.investment_details import ADDITIONAL_ENTITY_TYPE_CHOICES, InvestingEntity, Investment
from webapp.models.line_item import LineItem, LineItemPermission
from webapp.models.user import BridgeUser, Role

logger = structlog.get_logger(__name__)


@login_required
def profile_view(request: HttpRequest) -> HttpResponse:
    template = "settings/profile.html"
    if request.method == "GET":
        context = {"form": ProfileForm(instance=request.user)}
        return TemplateResponse(request, template, context)

    if request.method == "POST":
        profile_form = ProfileForm(request.POST, instance=request.user)
        if profile_form.is_valid():
            profile_form.save()
            messages.success(request, "Your settings have updated successfully")
            return redirect("settings_profile")

        context = {"form": profile_form}
        return TemplateResponse(request, template, context)

    return HttpResponse("Invalid request method", status=405)


@login_required
def password_view(request: HttpRequest) -> HttpResponse:
    template = "settings/password.html"
    context = {
        "form": PasswordUpdateForm(user=request.user),
    }

    if request.method == "POST":
        form = PasswordUpdateForm(user=request.user, data=request.POST)
        if form.is_valid():
            new_password = form.cleaned_data["password1"]
            request.user.set_password(new_password)
            request.user.save()
            update_session_auth_hash(request, request.user)  # Prevents logout after password change
            messages.success(request, "Your password was updated successfully.", extra_tags="settings_password success")
            return redirect(reverse("settings_password"))
        context["form"] = form

    return TemplateResponse(request, template, context)


@login_required
def entities_view(request: HttpRequest) -> HttpResponse:
    template = "settings/entities.html"
    entities = InvestingEntity.objects.for_user(request.user).select_related("client").order_by("legal_name")
    context = {
        "entities": entities,
        "additional_entity_type_choices": ADDITIONAL_ENTITY_TYPE_CHOICES,
    }

    return TemplateResponse(request, template, context)


@dataclass
class PermissionBatch:
    batch_id: uuid.UUID
    date: datetime.datetime
    entities: list[InvestingEntity]
    investments: list[Investment]


@login_required
@require_http_methods(["GET", "POST"])
def organization_view(request: HttpRequest) -> HttpResponse:
    organization = request.user.organization
    users_in_organization = BridgeUser.objects.filter(organization=organization).prefetch_related("roles")
    entities = InvestingEntity.objects.for_user(request.user)
    investments = Investment.objects.for_user(request.user)
    template = "settings/organization.html"

    if request.method == "GET":
        user_forms = {}
        user_permission_history = defaultdict(list)

        # Move the current user to the top of the list
        users_in_organization = list(users_in_organization)
        users_in_organization.sort(key=lambda u: u != request.user)

        for user_in_org in users_in_organization:
            user_forms[user_in_org.id] = UserPermissionsForm(
                logged_in_user=request.user,
                user_form=user_in_org,
                entities=entities,
                investments=investments,
            )

            current_user_permissions = (
                LineItemPermission.objects.for_user(request.user)
                .filter(permittee=user_in_org)
                .select_related(
                    "line_item__investing_entity",
                    "line_item__investment",
                    "grantee",
                )
                .order_by("-created_at", "batch_id")
            )
            user_permission_grouped_by_batch = groupby(current_user_permissions, key=lambda p: p.batch_id)

            for batch_id, permissions_iterator in user_permission_grouped_by_batch:
                permissions_in_batch = list(permissions_iterator)

                if not permissions_in_batch:
                    continue

                permissions_entities = sorted(
                    {p.line_item.investing_entity for p in permissions_in_batch}, key=lambda e: e.legal_name
                )
                permissions_investments = sorted(
                    {p.line_item.investment for p in permissions_in_batch}, key=lambda i: i.legal_name
                )
                batch_creation_date = permissions_in_batch[0].created_at

                new_permission_batch = PermissionBatch(
                    batch_id=batch_id,
                    date=batch_creation_date,
                    entities=permissions_entities,
                    investments=permissions_investments,
                )

                user_permission_history[user_in_org.id].append(new_permission_batch)

            user_permission_history[user_in_org.id].sort(key=lambda x: x.date, reverse=True)

        context = {
            "organization": organization,
            "users_in_organization": users_in_organization,
            "user_forms": user_forms,
            "user_permission_history": user_permission_history,
        }
        return TemplateResponse(request, template, context)

    if request.method == "POST":
        form = UserPermissionsForm(
            request.POST, entities=entities, investments=investments, logged_in_user=request.user
        )

        if form.is_valid():
            user_id = form.cleaned_data.get("user_id")
            selected_entity_ids = form.cleaned_data.get("entities")
            selected_investment_ids = form.cleaned_data.get("investments")
            selected_role_name = form.cleaned_data.get("roles")

            target_user = get_object_or_404(BridgeUser, id=user_id)

            logger.info(
                "Processing permissions update for user",
                user_id=target_user.id,
                entities=selected_entity_ids,
                investments=selected_investment_ids,
                role=selected_role_name,
            )

            current_batch_id = uuid.uuid4()
            with transaction.atomic():
                current_role = target_user.roles.first()
                if selected_role_name and current_role and selected_role_name != current_role.name:
                    target_user.roles.clear()
                    role_to_add = Role.objects.get(name=selected_role_name)
                    target_user.roles.add(role_to_add)

                target_line_items = (
                    LineItem.objects.for_user(request.user)
                    .filter(
                        Q(investing_entity_id__in=selected_entity_ids) & Q(investment_id__in=selected_investment_ids)
                    )
                    .distinct()
                )
                for line_item in target_line_items:
                    line_item.grant_access(permittee=target_user, grantee=request.user, batch_id=current_batch_id)

            return HttpResponse(status=200, headers={"HX-Refresh": "true"})

        for error_message in form.errors.values():
            messages.error(request, error_message)
        return HttpResponse(status=405)

    return HttpResponse("Method not allowed.", status=405)


@require_GET
def refresh_investment_dropdown(request: HttpRequest) -> HttpResponse:
    entity_ids = request.GET.getlist("entities")
    user_id = request.GET.get("user_id")
    user_form = get_object_or_404(BridgeUser, id=user_id)

    entities = InvestingEntity.objects.for_user(request.user).filter(id__in=entity_ids)
    investments = Investment.objects.for_user(request.user).filter(line_items__investing_entity__id__in=entity_ids)

    form = UserPermissionsForm(
        logged_in_user=request.user,
        user_form=user_form,
        entities=entities,
        investments=investments,
    )

    return render(request, "settings/partials/investment_checkboxes.html", {"form": form})


@login_required
@require_POST
def delete_permission_batch(request: HttpRequest) -> HttpResponse:
    form = DeletePermissionBatchForm(request.POST, request_user=request.user)
    if not form.is_valid():
        for error_message in form.errors.values():
            messages.error(request, error_message)
        return HttpResponse(status=400)

    batch_id = form.cleaned_data.get("batch_id")
    user_id = form.cleaned_data.get("user_id")

    permittee_user = get_object_or_404(BridgeUser, id=user_id)

    with transaction.atomic():
        permissions_to_delete = LineItemPermission.objects.filter(
            permittee=permittee_user, batch_id=batch_id, organization=request.user.organization
        )

        count = permissions_to_delete.count()
        if count > 0:
            for permission in permissions_to_delete:
                permission.soft_delete()
            messages.success(request, f"Successfully revoked {count} permissions from the batch.")
        else:
            messages.info(request, "No permissions found for the given batch and user, or they were already revoked.")

    return HttpResponse(status=200, headers={"HX-Refresh": "true"})


@login_required
def entity_view(request: HttpRequest, entity_id: int) -> HttpResponse:
    template = "settings/entity.html"
    queryset = InvestingEntity.objects.for_user(request.user).prefetch_related("line_items")
    entity = get_object_or_404(queryset, pk=entity_id)

    has_a_linked_line_item = False
    # TODO: If entity has MPC (non-orphaned entity), then it cant be deleted until future CRUD behavior implemented
    for line_item in entity.line_items.all():
        if line_item.merged_portal_credential:
            has_a_linked_line_item = True
            break

    context = {
        "entity": entity,
        "additional_entity_type_choices": ADDITIONAL_ENTITY_TYPE_CHOICES,
        "has_linked_line_items": has_a_linked_line_item,
    }

    if request.method == "GET":
        context["form"] = InvestingEntityForm(instance=entity)
        return TemplateResponse(request, template, context)

    if request.method == "POST":
        form = InvestingEntityForm(request.POST, instance=entity)
        if form.is_valid():
            form.save()
            messages.success(request, "Entity updated successfully")
            return redirect(reverse("settings_entities"))

        context["form"] = form
        return TemplateResponse(request, template, context)
    if request.method == "DELETE":
        if context["has_linked_line_items"]:
            messages.error(request, "Entity has linked line items")
            return TemplateResponse(request, template, context)

        entity.delete()
        messages.success(request, "Entity deleted successfully")
        return redirect(reverse("settings_entities"))

    return HttpResponse("Invalid request method", status=405)


@login_required
def user_invite_modal(request: HttpRequest) -> HttpResponse:
    if request.method == "GET":
        template = "modal.html"
        form = UserInviteForm()
        return TemplateResponse(
            request, template, {"modal_include_template": "settings/invite_user_content.html", "form": form}
        )

    if request.method == "POST":
        form = UserInviteForm(request.POST, user=request.user)
        if form.is_valid():
            first_name = form.cleaned_data["first_name"]
            last_name = form.cleaned_data["last_name"]
            email = form.cleaned_data["email"]
            role = form.cleaned_data["roles"]

            new_user = BridgeUser(
                username=email,
                email=email,
                first_name=first_name,
                last_name=last_name,
                organization=request.user.organization,
                invitation_token=uuid4(),
                is_active=False,
            )
            new_user.set_unusable_password()
            new_user.save()
            new_user.roles.add(role)

            send_invitation_email.apply_async(
                kwargs={
                    "user_id": str(new_user.pk),
                    "current_domain": request.get_host(),
                }
            )

            messages.success(request, "Invitation sent successfully")
            return HttpResponse(headers={"HX-Trigger": "closeModal", "HX-Refresh": "true"})

        template = "settings/invite_user_content.html"
        return TemplateResponse(request, template, {"form": form})

    return HttpResponse("Invalid request method", status=405)
