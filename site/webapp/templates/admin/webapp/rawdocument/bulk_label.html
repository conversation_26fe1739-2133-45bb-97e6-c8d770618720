{% extends "admin/base_site.html" %}
{% load customtags %}
{% block title %}
    Ground Truth Bulk Labeler
{% endblock title %}
{% block content %}
    <style>
        .header{
            position: sticky;
            top: 0;
            z-index: 1030; /* to prevent the navbar getting covered up by other content */
         }
         .prevent-select {
            -webkit-user-select: none; /* Safari */
            -ms-user-select: none; /* IE 10 and IE 11 */
            user-select: none; /* Standard syntax */
          }
    </style>
    <h1>Raw Docs</h1>
    <form method="post">
        {% csrf_token %}
        <button type="submit">Submit</button>
        {% if counter %}<p>Prediction Status {{ counter }}</p>{% endif %}
        <table>
            <thead class="header">
                <td>Name</td>
                <td>Est. Posted Date</td>
                <td>Est. Document Type</td>
                <td>
                    Document Type
                    <br />
                    <button type="button" onclick="copyValues('select.document_type')">Copy First to All</button>
                </td>
                <td>
                    Sub Document Type
                    <br />
                    <button type="button" onclick="copyValues('select.sub_document_type')">Copy First to All</button>
                </td>
                <td>
                    Line Items
                    <br />
                    <button type="button" onclick="copyLineItems()">Copy First to All</button>
                </td>
                <td>
                    Posted Date
                    <br />
                    <button type="button" onclick="copyValues('input.posted_date')">Copy First to All</button>
                </td>
                <td>
                    Effective Date
                    <br />
                    <button type="button" onclick="copyValues('input.effective_date')">Copy First to All</button>
                </td>
                <td>
                    Mark as read
                    <br />
                    <button type="button" onclick="copyCheckboxValues('input.has_been_viewed')">Copy First to All</button>
                </td>
                <td>PDF Link</td>
                <td>Metdata</td>
            </thead>
            <tbody>
                {% for raw_doc in raw_docs %}
                    <tr>
                        <td>{{ raw_doc.name }}</td>
                        <td>{{ raw_doc.posted_date|date:'Y-m-d' }}</td>
                        <td>{% label_lookup label=raw_doc.document_type enum=DocumentType %}</td>
                        <td>
                            <select class="gt_input document_type"
                                    name="{{ raw_doc.pk }}|document_type"
                                    id="document_type_{{ raw_doc.pk }}">
                                {% for choice in DocumentType.choices %}
                                    <option value="{{ choice.0 }}"
                                            {% if raw_doc.document_type == choice.0 %}selected{% endif %}>
                                        {{ choice.1 }}
                                    </option>
                                {% endfor %}
                            </select>
                        </td>
                        <td>
                            <select class="gt_input sub_document_type"
                                    name="{{ raw_doc.pk }}|sub_document_type"
                                    id="sub_document_type_{{ raw_doc.pk }}">
                                <option value="">Select Sub Document Type</option>
                                {% for choice in SubDocumentType.choices %}
                                    <option value="{{ choice.0 }}"
                                            {% if raw_doc.sub_document_type == choice.0 %}selected{% endif %}>
                                        {{ choice.1 }}
                                    </option>
                                {% endfor %}
                            </select>
                        </td>
                        <td class="line-items-cell">
                            {% for line_item in raw_doc.line_items %}
                                <input type="checkbox"
                                       class="gt_input line-item-checkbox"
                                       name="{{ raw_doc.pk }}|line_items"
                                       id="{{ raw_doc.pk }}|line_items|{{ line_item.pk }}"
                                       value="{{ line_item.pk }}"
                                       data-line-item-id="{{ line_item.pk }}"
                                       {% if line_item.pk in raw_doc.previous_line_items %}checked{% endif %}>
                                <label for="{{ raw_doc.pk }}|line_items|{{ line_item.pk }}"
                                       class="prevent-select">{{ line_item }}</label>
                                <br />
                                <br />
                            {% endfor %}
                        </td>
                        <td>
                            <input class="gt_input posted_date"
                                   type="text"
                                   name="{{ raw_doc.pk }}|posted_date"
                                   value="{{ raw_doc.posted_date|date:'Y-m-d' }}"
                                   required=""
                                   id="id_posted_date_{{ raw_doc.pk }}">
                        </td>
                        <td>
                            <input class="gt_input effective_date"
                                   type="text"
                                   name="{{ raw_doc.pk }}|effective_date"
                                   value="{{ raw_doc.previous_effective_date|date:'Y-m-d' }}"
                                   required=""
                                   id="id_effective_date_{{ raw_doc.pk }}">
                        </td>
                        <td>
                            <input class="gt_input has_been_viewed"
                                   type="checkbox"
                                   name="{{ raw_doc.pk }}|has_been_viewed"
                                   {% if raw_doc.is_backfill %}checked{% endif %}
                                   id="id_has_been_viewed_{{ raw_doc.pk }}">
                        </td>
                        <td>
                            <a href="{% url 'admin:webapp_rawdocument_label' pk=raw_doc.pk %}">📝</a>
                        </td>
                        <td>
                            <table>
                                <thead>
                                    <td>Key</td>
                                    <td>Value</td>
                                </thead>
                                {% for key, value in raw_doc.metadata.items %}
                                    <tr>
                                        <td>{{ key }}</td>
                                        <td>{{ value }}</td>
                                    </tr>
                                {% endfor %}
                            </table>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </form>
    <script>
        function copyCheckboxValues(selector) {
            // Get all checkboxes matching the selector
            const checkboxes = document.querySelectorAll(selector);

            // Exit if no checkboxes found
            if (checkboxes.length < 2) return;

            // Get the checked state of the first checkbox
            const firstChecked = checkboxes[0].checked;

            // Set this checked state for all other checkboxes
            for (let i = 1; i < checkboxes.length; i++) {
                checkboxes[i].checked = firstChecked;
            }
        }

        function copyValues(selector) {
            // Get all posted date inputs
            const selectedInputs = document.querySelectorAll(selector);

            // Exit if no elements found
            if (selectedInputs.length < 2) return;

            // Get the value from the first input
            const firstValue = selectedInputs[0].value;

            // Set this value for all other inputs
            for (let i = 1; i < selectedInputs.length; i++) {
                selectedInputs[i].value = firstValue;
            }
        }

        function copyLineItems() {
            // Get all table rows
            const rows = document.querySelectorAll('tbody tr');

            // Exit if less than 2 rows
            if (rows.length < 2) return;

            // Get checkboxes from first row
            const firstRowCheckboxes = rows[0].querySelectorAll('.line-item-checkbox');

            // For each subsequent row
            for (let i = 1; i < rows.length; i++) {
                // Get checkboxes in this row
                const currentRowCheckboxes = rows[i].querySelectorAll('.line-item-checkbox');

                // For each checkbox in the first row
                firstRowCheckboxes.forEach(firstCheckbox => {
                    // Find the matching checkbox in the current row (by line item ID)
                    const lineItemId = firstCheckbox.getAttribute('data-line-item-id');

                    // Find matching checkbox in current row
                    for (const checkbox of currentRowCheckboxes) {
                        if (checkbox.getAttribute('data-line-item-id') === lineItemId) {
                            checkbox.checked = firstCheckbox.checked;
                            break;
                        }
                    }
                });
            }
        }
    </script>
{% endblock content %}
