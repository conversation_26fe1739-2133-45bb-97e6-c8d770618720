# Generated by Django 5.2.4 on 2025-07-24 19:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0060_extractionschema_rawextraction'),
    ]

    operations = [
        migrations.AlterField(
            model_name='rawextraction',
            name='current_target_schema',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='current_target_raw_extractions', to='webapp.extractionschema'),
        ),
        migrations.AlterField(
            model_name='rawextraction',
            name='last_valid_target_schema',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='last_valid_target_raw_extractions', to='webapp.extractionschema'),
        ),
    ]
