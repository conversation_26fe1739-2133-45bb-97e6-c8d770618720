import asyncio
import hashlib
import json
from collections.abc import Async<PERSON>enerator
from datetime import date
from pathlib import Path
from urllib.parse import unquote, urlparse
from uuid import UUID

import structlog
from dateutil import parser
from django.conf import settings
from patchright.async_api import APIResponse
from pydantic import BaseModel
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.get_logger(__name__)


# Use pydantic here for data validation, bc I don't trust Carta's API
class CartaDocument(BaseModel):
    id: int
    uuid: UUID
    document_name: str
    document_date: date | None
    document_date_raw: str
    document_type: str
    fund_id: int
    fund_name: str
    firm_id: int
    firm_name: str
    fundadmin_partner_uuid: UUID
    capital_account_name: str
    stakeholder_name: str
    file_type: str
    document_url: str


@register_strategy
class CartaManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "carta.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        if settings.DEBUG:
            return [
                ("<EMAIL>", "Carta"),
                ("<EMAIL>", "Carta"),
                ("<EMAIL>", "Carta"),
            ]
        return []

    @classmethod
    def needs_persistent_playwright(cls) -> bool:
        return True

    @classmethod
    def needs_patchright(cls) -> bool:
        return True

    @classmethod
    def needs_playwright(cls) -> bool:
        return False

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        username_locator = self.page.locator("input[id='username']")
        if await username_locator.count() == 1 and await username_locator.is_visible():
            await username_locator.click()
            await username_locator.fill(username)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError

        password_locator = self.page.locator("input[id='password']")
        if await password_locator.count() == 1 and await password_locator.is_visible():
            await password_locator.click()
            await password_locator.fill(password)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError

        button_locator = self.page.locator("button[id='login-btn']")
        if await button_locator.count() == 1 and await button_locator.is_visible():
            await asyncio.sleep(1)
            await button_locator.click()
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        app_ct = await self.page.get_by_text("Authenticator App Carta").count()
        btn_ct = await self.page.locator("button[id='select-device-btn']").count()
        bypass_ct = await self.page.locator("label[for='enable-bypass']").count()
        twofa_inp = await self.page.locator("input[id='two-factor-code-input']").count()
        verify_btn = await self.page.locator("button[id='verify-challenge-btn']").count()
        return (app_ct + btn_ct + bypass_ct + twofa_inp + verify_btn) > 0

    async def _send_otp(self) -> None:
        # Not used here as it is automatically sent, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(1)
        auth_app_button = self.page.get_by_text("Authenticator App Carta").first
        select_device_button = self.page.locator("button[id='select-device-btn']")
        if (
            await auth_app_button.count() == 1
            and await auth_app_button.is_visible()
            and await select_device_button.count() == 1
            and await select_device_button.is_visible()
        ):
            await auth_app_button.click()
            await asyncio.sleep(2)
            await select_device_button.click()
            await asyncio.sleep(10)

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.locator("label[for='enable-bypass']").first.click()
        await asyncio.sleep(2)
        await self.page.locator("input[id='two-factor-code-input']").click()
        await asyncio.sleep(1)
        await self.page.locator("input[id='two-factor-code-input']").fill(otp)
        await asyncio.sleep(1)
        await self.page.locator("button[id='verify-challenge-btn']").click()
        await asyncio.sleep(1)

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        username_ct = await self.page.locator("input[id='username']").count()
        password_ct = await self.page.locator("input[id='password']").count()
        button_ct = await self.page.locator("button[id='login-btn']").count()
        app_ct = await self.page.get_by_text("Authenticator App Carta").count()
        btn_ct = await self.page.locator("button[id='select-device-btn']").count()
        bypass_ct = await self.page.locator("label[for='enable-bypass']").count()
        twofa_inp = await self.page.locator("input[id='two-factor-code-input']").count()
        verify_btn = await self.page.locator("button[id='verify-challenge-btn']").count()
        return (app_ct + btn_ct + bypass_ct + twofa_inp + username_ct + password_ct + button_ct + verify_btn) == 0

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        all_docs = await self._get_all_documents()
        logger.info("Found documents", num_documents=len(all_docs))
        for doc in all_docs:
            if await self.should_download_document(doc.model_dump_json()):
                yield await self._download_doc(doc)

    async def _download_doc(self, doc: CartaDocument) -> RawDocumentTuple:
        metadata = doc.model_dump_json()
        _, resp = await self._carta_get(doc.document_url, is_json=False)
        pdf_url = resp.headers["location"]
        pdf_bytes, _ = await self._carta_get(pdf_url, is_json=False)
        pdf_url = resp.headers["location"]
        doc_name = unquote(Path(urlparse(pdf_url).path).parts[-1])
        return RawDocumentTuple(
            name=doc_name,
            date=doc.document_date,
            doc_type=self._doc_type_mapper(doc.document_type),
            content=pdf_bytes,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    def _doc_type_mapper(self, doc_type: str) -> DocumentType:
        dt = doc_type.lower().strip()
        return (
            {
                "tax - schedule k-1": DocumentType.TAX,
                "general": DocumentType.OTHER,
                "annual meeting": DocumentType.INVESTMENT_UPDATE,
                "capital account statements": DocumentType.ACCOUNT_STATEMENT,
                "tax": DocumentType.TAX,
                "capital calls": DocumentType.CAPITAL_CALL,
                "annual and quarterly report": DocumentType.INVESTMENT_UPDATE,
                "distributions": DocumentType.DISTRIBUTION_NOTICE,
                "tax - estimate": DocumentType.TAX,
                "legal": DocumentType.LEGAL,
                "legal - subscription agreement": DocumentType.LEGAL,
            }
        ).get(dt, DocumentType.UNKNOWN)

    async def _carta_get(
        self, url: str, base_url: str = "https://app.carta.com", *, is_json: bool = True
    ) -> tuple[dict | bytes | None, APIResponse]:
        if self.page is None:
            raise ManagerNotInitializedError
        if url.startswith("/"):
            url = base_url + url
        response = await self.page.request.get(url, max_redirects=0)
        if response.status == 302:  # noqa: PLR2004
            # We don't follow redirects so, we leave to the caller to follow.
            # Some redirects return 401/403 so we don't want to trigger that.
            return None, response
        if response.status != 200:  # noqa: PLR2004
            logger.error("Failed to fetch document", status=response.status, url=url, x_body=await response.body())
            # Doing soft failures to try to continue if there's any weird errors.
            return None, response
        # Enforcing a bit of delay to respect the server
        # Should be ~1 RPS including response time.
        await asyncio.sleep(0.5)
        try:
            if is_json:
                return await response.json(), response
            return await response.body(), response
        except Exception:
            logger.exception("Failed to parse", url=url)
            return None, response

    async def _get_carta_accounts(self) -> list[tuple[str, str, str]]:
        accounts_json, _ = await self._carta_get("/api/fe-platform/account-switcher/")
        if accounts_json is None:
            raise ValueError
        accounts = []
        if "accounts" not in accounts_json:
            raise ValueError
        for account_obj in accounts_json["accounts"]:
            if "name" not in account_obj or "url" not in account_obj:
                raise ValueError
            title = account_obj["name"]
            redirect_href = account_obj["url"]
            carta_firm_id = Path(redirect_href).parts[-1]
            accounts.append((title, redirect_href, carta_firm_id))
        return accounts

    async def _get_investments(self, carta_firm_id: str, carta_individual_id: str | None = None) -> list[str]:
        if carta_individual_id is None:
            investments_json, _ = await self._carta_get(
                f"/api/investors/portfolio/firm/{carta_firm_id}/list_firm_investments/"
            )
        else:
            investments_json, _ = await self._carta_get(
                f"/api/investors/portfolio/firm/{carta_firm_id}/list_individual_portfolio_investments/{carta_individual_id}/list/"
            )
        if investments_json is None:
            return []
        investments = []
        for investment_item in investments_json:
            is_llc = "is_llc_company" in investment_item and investment_item["is_llc_company"]
            carta_investment_id = Path(investment_item["url"]).parts[-1]
            if is_llc:
                # TODO: support LLC investments
                logger.warning(
                    "Found an LLC investment, skipping",
                    carta_firm_id=carta_firm_id,
                    carta_individual_id=carta_investment_id,
                    investment_item=investment_item,
                )
                continue
            investments.append(carta_investment_id)
        return list(set(investments))

    async def _get_investments_follow_redirect(self, redirect_href: str, carta_firm_id: str) -> list[str]:
        _, redirect_response = await self._carta_get(redirect_href, is_json=False)
        redirect_path = redirect_response.headers.get("location")
        carta_id = Path(redirect_path).parts[-2]
        if "investors/individual" in redirect_path:
            if carta_id == carta_firm_id:
                logger.error(
                    "Carta ID and firm ID are the same which is unexpected for individual accounts",
                    carta_id=carta_id,
                    carta_firm_id=carta_firm_id,
                    redirect_path=redirect_path,
                )
                return []
            logger.info("Getting investments for individual account", carta_firm_id=carta_firm_id, carta_id=carta_id)
            investments = await self._get_investments(carta_firm_id, carta_id)
        elif "investors/firm" in redirect_path:
            if carta_id != carta_firm_id:
                logger.error(
                    "Carta ID and firm ID are different which is unexpected for firm accounts",
                    carta_id=carta_id,
                    carta_firm_id=carta_firm_id,
                    redirect_path=redirect_path,
                )
                return []
            logger.info("Getting investments for firm account", carta_firm_id=carta_firm_id)
            investments = await self._get_investments(carta_firm_id)
        else:
            logger.error(
                "Unexpected redirect path, should be for individual or firm accounts",
                redirect_path=redirect_path,
                carta_id=carta_id,
                carta_firm_id=carta_firm_id,
            )
            return []
        return investments

    def _make_carta_document(self, doc: dict) -> CartaDocument | None:
        doc_date = None
        doc_date_raw = doc["document_date"]
        if "document_date" in doc:
            try:
                doc_date = parser.parse(doc_date_raw)
            except (TypeError, ValueError):
                doc_date = None
            doc["document_date"] = doc_date
            doc["document_date_raw"] = doc_date_raw
        try:
            return CartaDocument(**doc)
        except Exception:
            logger.exception("Failed to parse document", doc=doc)
            return None

    async def _get_investment_documents(self, carta_investment_id: str, carta_firm_id: str) -> list[CartaDocument]:
        cur_page = 1
        all_documents = []
        while True:
            url = (
                f"/api/investors/firm/{carta_firm_id}/get_received_documents/sent_from/{carta_investment_id}/"
                f"?page={cur_page}&page_size=50&search=&ordering=-document_date%2Cstakeholder_name"
            )
            documents_json, response = await self._carta_get(url)
            if "results" not in documents_json and "num_pages" not in documents_json:
                logger.error(
                    "Unexpected response from carta",
                    url=url,
                    response=documents_json,
                    carta_investment_id=carta_investment_id,
                    carta_firm_id=carta_firm_id,
                )
                break
            num_pages = documents_json["num_pages"]
            if "results" in documents_json:
                for result in documents_json["results"]:
                    carta_doc = self._make_carta_document(result)
                    if carta_doc is not None:
                        all_documents.append(carta_doc)
            cur_page += 1
            logger.info(
                "Found documents",
                url=url,
                page=cur_page,
                num_pages=num_pages,
                carta_investment_id=carta_investment_id,
                carta_firm_id=carta_firm_id,
                len_all_documents=len(all_documents),
            )
            if cur_page > num_pages:
                break
        return all_documents

    async def _get_all_documents(self) -> list[CartaDocument]:
        all_docs: list[CartaDocument] = []
        for title, redirect_href, carta_firm_id in await self._get_carta_accounts():
            logger.info("Getting documents", title=title, href=redirect_href, carta_firm_id=carta_firm_id)
            investments = await self._get_investments_follow_redirect(redirect_href, carta_firm_id)
            for investment_id in investments:
                investment_documents = await self._get_investment_documents(investment_id, carta_firm_id)
                all_docs.extend(investment_documents)
        return all_docs
