{% load static customtags %}
<div class="w-full relative"
    {# djlint:off #}
       _="
       on click from elsewhere trigger hideMenu(input_name:'{{ input_name }}') end
       on keydown[code=='Escape'] trigger hideMenu(input_name:'{{ input_name }}') end
       "
    {# djlint:on #}
    >
    <!-- search -->
    <input type="text"
        name="{{ input_name }}"
        id="{{ input_name }}"
        class="w-full px-4 py-2.5 border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
        autocomplete="off"
        hx-post="{% url 'line_item_filtered_options' %}{% urlparams line_item_field=input_name %}"
        hx-trigger="keyup changed delay:200ms, load"
        hx-sync="this:replace"
        hx-swap="outerHTML"
        hx-target="#{{ input_name }}_drop_down_results"
        hx-indicator="#{{ input_name }}_drop_down_results, #{{ input_name }}_drop_down_results_indicator, #{{ input_name }}_drop_down_results_menu"
        {# djlint:off #}
              _="
              on focus from me trigger focusInput(input_name:'{{ input_name }}') end
              on setInput(drop_down_option) set my.value to drop_down_option end
              on hideMenu[input_name=='{{ input_name }}'] from document me.blur() end
              "
        {# djlint:on #}/>
        <!-- menu -->
        <div class="hidden absolute overflow-y-auto w-full p-2 bg-white shadow max-h-40 rounded z-10"
            id="{{ input_name }}-menu"
            {# djlint:off #}
            _="
            on hideMenu[input_name=='{{ input_name }}'] from document add .hidden end
            on focusInput[input_name=='{{ input_name }}'] from document remove .hidden end
            "
            {# djlint:on #}
            >
            <!-- options -->
            {% include "line_item_search_option.html" %}
        </div>
    </div>
