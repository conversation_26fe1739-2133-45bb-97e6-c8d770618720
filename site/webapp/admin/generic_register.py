import json

import structlog
from django.contrib import admin, messages
from django.db.models import QuerySet
from django.http import HttpRequest
from django.urls import reverse
from django.utils.html import format_html
from retrieval.tasks.send_mail import send_mfa_notification_email

from webapp.models.documents import (
    CapitalCallDocumentFact,
    DistributionNoticeDocumentFact,
    InvestmentUpdateDocumentFact,
    ProcessedDocument,
)
from webapp.models.emails import (
    BridgeEmail,
    CustomerEmailCredential,
    EmailIntake,
    EmailRetrieval,
    UserForwardingRule,
)
from webapp.models.extraction import DocChunk, ExtractionSchema, RawExtraction
from webapp.models.investment_details import Client, InvestingEntity, Investment
from webapp.models.portal import MultiFactorAuthentication, Portal, PortalCredential, SMSMessage
from webapp.models.retrieval import Retrieval
from webapp.models.user import Organization, Role
from webapp.models.zip_object import ZipJob

logger = structlog.get_logger(__name__)


@admin.register(MultiFactorAuthentication)
class MultiFactorAuthenticationAdmin(admin.ModelAdmin):
    list_display = ("organization", "merged_portal_credential", "multi_factor_authentication_type")
    list_filter = ("multi_factor_authentication_type", "organization")
    ordering = ("-organization", "merged_portal_credential")


@admin.register(Retrieval)
class RetrievalAdmin(admin.ModelAdmin):
    list_display = (
        "merged_portal_credential",
        "manager",
        "organization",
        "updated_at",
        "retrieval_status",
        "user_login_status",
        "number_of_retries",
        "number_documents_retrieved",
    )
    list_filter = ("organization", "manager", "retrieval_status", "merged_portal_credential")
    ordering = ("-updated_at",)
    readonly_fields = ("cloud_watch_url", "video_log_url", "get_task_url", "link_to_raw_docs_retrieved")

    def cloud_watch_url(self, obj: Retrieval) -> str:
        return format_html(
            "<a href='{}'>View CloudWatch URL</a>",
            obj.get_cloud_watch_url(),
        )

    def video_log_url(self, obj: Retrieval) -> str:
        return format_html(
            "<a href='{}'>View Video Log URL</a>",
            obj.get_presigned_video_url(),
        )

    def get_task_url(self, obj: Retrieval) -> str:
        return format_html(
            "<a href='{}'>View Task</a>", reverse("admin:django_celery_results_taskresult_changelist") + f"?q={obj.pk}"
        )

    def link_to_raw_docs_retrieved(self, obj: Retrieval) -> str:
        """
        Returns a link to the raw documents retrieved for this retrieval.
        """
        url = reverse("admin:webapp_rawdocument_changelist") + f"?retrieval__id__exact={obj.pk}"
        return format_html('<a href="{}">View Raw Documents</a>', url)


@admin.action(description="Mark as Viewed")
def mark_viewed(
    modeladmin: "ProcessedDocumentAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: QuerySet,
) -> None:
    pks = {str(pk) for pk in queryset.values_list("pk", flat=True)}
    for rd in ProcessedDocument.objects.filter(pk__in=pks):
        rd.has_been_viewed = True
        rd.save()
        messages.success(request, f"Document Marked as Viewed ({rd.name})")


@admin.action(description="Mark as Unread")
def mark_unread(
    modeladmin: "ProcessedDocumentAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: QuerySet,
) -> None:
    pks = {str(pk) for pk in queryset.values_list("pk", flat=True)}
    for rd in ProcessedDocument.objects.filter(pk__in=pks):
        rd.has_been_viewed = False
        rd.save()
        messages.success(request, f"Document Marked as Unread ({rd.name})")


@admin.register(ProcessedDocument)
class ProcessedDocumentAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "organization",
        "document_type",
        "created_at",
        "has_been_viewed",
        "is_ground_truth",
        "is_visible",
        "is_correct",
        "is_wrong",
        "has_extracted_numbers",
        "label",
    )
    list_filter = (
        "document_type",
        "has_been_viewed",
        "organization",
        "is_ground_truth",
        "is_visible",
        "is_correct",
        "is_wrong",
        "has_extracted_numbers",
    )
    ordering = ("-created_at",)
    search_fields = ("name",)
    raw_id_fields = (
        "raw_retreival_document",
        "line_item",
        "capital_call_document",
        "investment_update_document",
        "distribution_notice_document",
    )
    actions = (mark_viewed, mark_unread)

    def label(self, obj: ProcessedDocument) -> str:
        if obj.raw_retreival_document is None:
            return ""
        url = reverse("admin:webapp_rawdocument_label", args=[obj.raw_retreival_document.pk])
        return format_html('<a href="{}">📝</a>', url)


@admin.register(SMSMessage)
class SMSMessageAdmin(admin.ModelAdmin):
    list_display = ("user_phone_number", "body", "created_at", "parsed_code")
    list_filter = ("user_phone_number", "body", "created_at")
    ordering = ("-created_at",)
    readonly_fields = ("curl",)

    def parsed_code(self, obj: SMSMessage) -> str:
        return obj.extract_code_from_message()

    def curl(self, obj: SMSMessage) -> str:
        raw_payload = json.dumps(obj.raw_payload).replace("\\n", " ")
        return f"""
        curl -X POST http://localhost:8000/sms-code/ \\
        -H "Content-Type: application/json" \\
        -d '{raw_payload}'
        """.strip()


@admin.action(description="Email user for MFA.")
def send_mfa_notification_email_action(
    modeladmin: "ProcessedDocumentAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: QuerySet,
) -> None:
    pks = {str(pk) for pk in queryset.values_list("pk", flat=True)}
    for rd in Portal.objects.filter(pk__in=pks):
        if rd.created_by is not None:
            send_mfa_notification_email.apply_async(
                kwargs={
                    "portal_id": rd.pk,
                    "user_id": rd.created_by.pk,
                    "to_email": rd.created_by.email,
                    "current_domain": "app.bridgeinvest.io",
                }
            )
            messages.success(
                request,
                f"Email sent to {rd.created_by.email if rd.created_by else 'unknown'} for MFA on portal {rd.name}",
            )


@admin.register(Portal)
class PortalAdmin(admin.ModelAdmin):
    list_display = ("name", "organization", "portal_login_url", "portal_type")
    list_filter = ("organization", "portal_type")
    ordering = ("-organization", "name")
    search_fields = ("name", "organization__name")
    actions = (send_mfa_notification_email_action,)


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ("legal_name", "organization")
    list_filter = ("organization",)
    search_fields = ("legal_name",)
    ordering = ("-organization", "legal_name")


@admin.register(InvestingEntity)
class InvestingEntityAdmin(admin.ModelAdmin):
    list_display = ("legal_name", "client", "organization")
    list_filter = ("organization", "client")
    search_fields = ("legal_name", "client__legal_name")
    ordering = ("-organization", "legal_name")


@admin.register(Investment)
class InvestmentAdmin(admin.ModelAdmin):
    list_display = (
        "legal_name",
        "managing_firm_name",
        "organization",
    )
    list_filter = ("organization", "managing_firm_name")
    search_fields = ("legal_name", "managing_firm_name")


@admin.register(DocChunk)
class DocChunkAdmin(admin.ModelAdmin):
    list_display = (
        "pk",
        "content_format",
        "chunker_name",
        "parser_name",
        "chunk_idx",
        "page_number",
        "chunk_length",
        "ingestion_id",
        "created_at",
    )
    raw_id_fields = ("raw_document",)
    ordering = ("-created_at", "ingestion_id", "chunker_name", "parser_name", "page_number", "chunk_idx")
    search_fields = ("ingestion_id",)


@admin.register(ExtractionSchema)
class ExtractionSchemaAdmin(admin.ModelAdmin):
    list_display = ("uri", "column_name", "major_version", "minor_version", "deleted_at", "created_at")
    search_fields = ("uri", "json_schema")
    ordering = ("uri",)


@admin.register(RawExtraction)
class RawExtractionAdmin(admin.ModelAdmin):
    list_display = (
        "pk",
        "raw_document",
        "current_extraction_schema",
        "is_current_extraction_schema_validated",
        "target_json_path",
        "data",
        "current_target_schema",
        "is_current_target_schema_validated",
        "is_ground_truth",
        "created_at",
    )
    raw_id_fields = ("raw_document", "processed_document")
    ordering = ("-created_at",)
    search_fields = ("raw_document__name", "data")


# TODO: make this AWESOME
admin.site.register(Role)
admin.site.register(Organization)
admin.site.register(PortalCredential)
admin.site.register(BridgeEmail)
admin.site.register(CustomerEmailCredential)
admin.site.register(EmailIntake)
admin.site.register(EmailRetrieval)
admin.site.register(UserForwardingRule)
admin.site.register(CapitalCallDocumentFact)
admin.site.register(DistributionNoticeDocumentFact)
admin.site.register(InvestmentUpdateDocumentFact)
admin.site.register(ZipJob)
