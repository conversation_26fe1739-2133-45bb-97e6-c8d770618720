import urllib.parse
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Any

import boto3
import pytz
import structlog
from axes.models import AccessLog  # type: ignore[import-untyped]
from celery import Task, shared_task
from celery.exceptions import Ignore
from django.conf import settings
from django.core.mail import EmailMessage, EmailMultiAlternatives
from django.db.models import Count, Max
from django.template.loader import render_to_string
from django.utils import timezone
from django_celery_beat.models import PeriodicTask
from webapp.models import RawDocument
from webapp.models.documents import DocumentType, ProcessedDocument
from webapp.models.portal import Portal
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser, Organization, Role

from retrieval.tasks.parse_retrieval_error import get_retrieval_error_summary
from retrieval.tasks.utils import singleton_task

logger = structlog.get_logger(__name__)


@shared_task(bind=True, track_started=True)
def send_mfa_notification_email(
    self: Task,  # noqa: ARG001
    portal_id: str,
    user_id: str,
    to_email: str,
    current_domain: str | None = None,
) -> None:
    """
    Celery task to send an email using a static template.

    Args:
        self (Task): Celery Task instance.
        portal_id (str): Primary key of Portal model.
        user_id (str): Primary key of BridgeUser model.
        to_email (str): Email address to which the email is sent.
        current_domain (str|None): The domain from which the email is sent.
            e.g. localhost:8000 or bridgeinvest.co or app.bridgeinvest.io

    Returns:
        None

    """
    try:
        if current_domain is None:
            current_domain = settings.DOMAIN_NAME
        portal = Portal.objects.get(id=portal_id)
        context = {
            "current_domain": current_domain,
            "current_datetime": timezone.now()
            .astimezone(pytz.timezone("US/Eastern"))
            .strftime("%b. %d, %Y, %I:%M %p %Z"),
            "portal_name": portal.name,
            "email": to_email,
        }

        # Log parameters used in rendering the email template
        logger.info("Rendering email notification template with parameters", context=context)

        message = render_to_string("account/email/login_attempt.html", context)

        # Log the rendered email content for debugging
        logger.debug("Rendered email content", content=message)

        # Create an EmailMessage instance
        email = EmailMessage(
            subject=f"Bridge Login attempt was made for {portal.name}",
            body=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[to_email],
        )

        # Send the email
        email.content_subtype = "html"  # Indicate that the email content is HTML
        email.send()

    except Exception:
        logger.exception(
            "Failed to mfa notification send email",
            user_id=user_id,
            portal_id=portal_id,
            current_domain=current_domain,
        )
        raise


@shared_task(bind=True, track_started=True)
def send_invitation_email(self: Task, user_id: str, current_domain: str | None = None) -> None:  # noqa: ARG001
    """
    Celery task to send an email using a static template.

    Args:
        self (Task): Celery Task instance.
        user_id (str): Primary key of BridgeUser model.
        current_domain (str|None): The domain from which the email is sent.
            e.g. localhost:8000 or bridgeinvest.co or app.bridgeinvest.io

    Returns:
        None

    """
    try:
        if current_domain is None:
            current_domain = settings.DOMAIN_NAME
        user = BridgeUser.objects.get(id=user_id)

        # Send an invitation email
        invitation_link = f"http://{current_domain}/accept-invitation/{user.invitation_token}"

        email_content = render_to_string(
            "account/email/email_invite.html",
            {
                "invitation_link": invitation_link,
                "email": user.email,
                "current_domain": current_domain,
            },
        )
        to_emails = [user.email]
        if settings.DEBUG:
            to_emails = ["<EMAIL>"]
            # Stop this from running in debug mode, remove this line and use your email to debug
            return
        email = EmailMultiAlternatives(
            "Welcome to Bridge",
            email_content,
            settings.DEFAULT_FROM_EMAIL,
            to_emails,
        )
        email.attach_alternative(email_content, "text/html")
        email.send()
        logger.info("Invitation email sent", email=user.email)
    except Exception:
        logger.exception("Failed to send email", user_id=user_id, current_domain=current_domain)
        raise


def get_presigned_video_url(bucket: str, folder: str) -> str | None:
    if not bucket or not folder:
        return None

    folder_path = str(Path(folder) / "video")
    s3 = boto3.client("s3")
    try:
        for obj in s3.list_objects_v2(Bucket=bucket, Prefix=folder_path).get("Contents", []):
            key = Path(obj.get("Key", ""))
            if key.suffix == ".webm":
                return s3.generate_presigned_url(
                    ClientMethod="get_object",
                    Params={
                        "Bucket": bucket,
                        "Key": str(key),
                        "ResponseContentType": "application/octet-stream",
                        "ResponseContentDisposition": f'filename="{urllib.parse.quote(key.name)}"',
                    },
                    ExpiresIn=3600 * 24,  # 24 hour in seconds, increase if needed
                )
    except Exception:
        logger.exception("Could not make presigned url", bucket=bucket, folder=folder)
    return None


def get_extra_retrieval_statistics(retrieval: dict[str, Any], current_time: datetime) -> dict[str, Any]:
    base_q = Retrieval.objects.filter(
        merged_portal_credential__portal__name=retrieval["portal"],
        organization=retrieval["organization"],
    )
    last_7_q = base_q.filter(
        created_at__gte=current_time - timedelta(days=7),
    )

    num_failed_last_7 = last_7_q.exclude(
        retrieval_status=Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
    ).count()

    last_successful_run = base_q.filter(
        retrieval_status=Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
    ).aggregate(Max("created_at"))

    num_success_last_7 = last_7_q.filter(
        retrieval_status=Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
    ).count()

    retrieval["num_failed_last_7"] = num_failed_last_7
    retrieval["num_success_last_7"] = num_success_last_7
    if last_successful_run["created_at__max"] is not None:
        retrieval["last_successful_run"] = last_successful_run["created_at__max"].strftime("%b. %d, %Y")
    else:
        retrieval["last_successful_run"] = "Never"
    return retrieval


def format_last_status(res_retrievals: list[dict[str, Any]]) -> str:
    counter = {}
    for retrieval in res_retrievals:
        if retrieval["retrieval_status"] not in counter:
            counter[retrieval["retrieval_status"]] = 0
        counter[retrieval["retrieval_status"]] += 1
    return ", ".join([f"{k}: {v}" for k, v in counter.items()])


@shared_task(bind=True, track_started=True)
@singleton_task()
def send_internal_daily_digest(self: Task, current_domain: str | None = None) -> None:  # noqa: ARG001
    try:
        if current_domain is None:
            current_domain = settings.DOMAIN_NAME
        current_time = timezone.now().astimezone(pytz.timezone("US/Eastern"))
        time_threshold = current_time - timedelta(days=1)
        res_retrievals = [
            get_extra_retrieval_statistics(
                {
                    "retrieval_pk": retrieval.pk,
                    "organization": retrieval.organization,
                    "portal": retrieval.merged_portal_credential.portal.name,
                    "is_successful": retrieval.retrieval_status == Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
                    "retrieval_status": Retrieval.RetrievalStatus(retrieval.retrieval_status).label,
                    "number_documents_retrieved": retrieval.number_documents_retrieved,
                    "number_documents_skipped": retrieval.number_documents_skipped,
                    "number_of_retries": retrieval.number_of_retries,
                    "time_taken": (
                        retrieval.updated_at
                        - (retrieval.start_time if retrieval.start_time is not None else retrieval.created_at)
                    ).seconds,
                    "video_url": retrieval.get_presigned_video_url(),
                },
                current_time,
            )
            for retrieval in (
                Retrieval.objects.filter(created_at__gte=time_threshold)
                .exclude(organization__name="Demo")
                .exclude(is_backfill=True)
                .order_by(
                    "organization__name",
                    "merged_portal_credential__portal__name",
                )
                .all()
            )
        ]
        res_retrievals.append(
            {
                "retrieval_pk": None,
                "organization": "All",
                "portal": "All",
                "retrieval_status": format_last_status(res_retrievals),
                "is_successful": True,
                "number_documents_retrieved": sum(
                    [retrieval["number_documents_retrieved"] for retrieval in res_retrievals]
                ),
                "num_failed_last_7": sum([retrieval["num_failed_last_7"] for retrieval in res_retrievals]),
                "num_success_last_7": sum([retrieval["num_success_last_7"] for retrieval in res_retrievals]),
            }
        )

        # Raw docs
        rd_ct = RawDocument.objects.filter(created_at__gte=time_threshold).count()
        raw_documents_cts = [
            {
                "document_type": rd["document_type"],
                "organization": rd["organization__name"],
                "organization_id": str(rd["organization"]),
                "portal": rd["retrieval__merged_portal_credential__portal__name"],
                "count": rd["dcount"],
            }
            for rd in (
                RawDocument.objects.filter(created_at__gte=time_threshold)
                .exclude(organization__name="Demo")
                .values(
                    "organization",
                    "organization__name",
                    "retrieval__merged_portal_credential__portal__name",
                    "document_type",
                )
                .annotate(dcount=Count("document_type"))
                .order_by("organization__name", "retrieval__merged_portal_credential__portal__name", "document_type")
            )
        ]
        raw_documents_cts.append({"document_type": "Total", "organization": "All", "portal": "All", "count": rd_ct})
        to_emails = [user.email for user in BridgeUser.objects.filter(is_admin=True).all()]
        users_logged_in = [
            {
                "username": alog.username,
                "attempt_time": alog.attempt_time.astimezone(pytz.timezone("US/Eastern")).strftime(
                    "%b. %d, %Y, %I:%M %p %Z"
                ),
            }
            for alog in AccessLog.objects.filter(attempt_time__gte=time_threshold)
            .exclude(path_info__contains="hijack")
            .exclude(username__in=to_emails)
            .exclude(username="<EMAIL>")
            .all()
        ]
        failed_retrievals = {
            retrieval.pk: {
                "portal": retrieval.organization.name + " - " + retrieval.merged_portal_credential.portal.name,
                "error_summary": get_retrieval_error_summary(
                    retrieval,
                    current_time,
                    time_threshold,
                ),
            }
            for retrieval in (
                Retrieval.objects.filter(created_at__gte=time_threshold)
                .exclude(organization__name="Demo")
                .exclude(is_backfill=True)
                .exclude(
                    retrieval_status__in=[
                        Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
                        Retrieval.RetrievalStatus.NOT_STARTED,
                        Retrieval.RetrievalStatus.SUBMITTED,
                        Retrieval.RetrievalStatus.PENDING_SCHEDULE,
                        Retrieval.RetrievalStatus.PENDING_CANCEL,
                        Retrieval.RetrievalStatus.PENDING_LOGIN,
                        Retrieval.RetrievalStatus.PENDING_LOGIN_OTP,
                        Retrieval.RetrievalStatus.BLOCKED_LOGIN_OTP,
                        Retrieval.RetrievalStatus.SUCCESS_LOGGED_IN,
                        Retrieval.RetrievalStatus.PENDING_DOCUMENT_RETRIEVAL,
                    ]
                )
                .order_by(
                    "organization__name",
                    "merged_portal_credential__portal__name",
                )
                .all()
            )
        }
        for retrieval in res_retrievals:
            if retrieval["retrieval_pk"] in failed_retrievals:
                retrieval["error_type"] = str(
                    failed_retrievals.get(retrieval["retrieval_pk"]).get("error_summary").readable_error_type
                )
        email_content = render_to_string(
            "daily_digest_email/internal.html",
            {
                "retrievals": res_retrievals,
                "raw_documents_cts": raw_documents_cts,
                "end_time": current_time.strftime("%b. %d, %Y, %I:%M %p %Z"),
                "start_time": time_threshold.strftime("%b. %d, %Y, %I:%M %p %Z"),
                "users_logged_in": users_logged_in,
                "current_domain": current_domain,
                "DocumentType": DocumentType,
                "failed_retrievals": [
                    {
                        "portal": failed_retrieval["portal"],
                        "error_summary": failed_retrieval["error_summary"].summary,
                        "error_type": failed_retrieval["error_summary"].readable_error_type,
                    }
                    for failed_retrieval in failed_retrievals.values()
                ],
            },
        )

        if settings.DEBUG:
            to_emails = ["<EMAIL>"]
            # Stop this from running in debug mode, remove this line and use your email to debug
            return
        email = EmailMultiAlternatives(
            f"Bridge - Internal Daily Digest - {current_time.strftime('%b. %d, %Y, %I:%M %p %Z')}",
            email_content,
            settings.DEFAULT_FROM_EMAIL,
            to_emails,
        )
        email.attach_alternative(email_content, "text/html")
        email.send()
        logger.info("Internal Daily Digest email sent")

    except Exception:
        logger.exception("Failed to send internal daily digest email", current_domain=current_domain)
        raise


@shared_task(bind=True, track_started=True)
def notify_admins_email_generic(
    self: Task,  # noqa: ARG001
    email_subject: str,
    email_body: str,
    current_domain: str | None = None,
) -> None:
    if current_domain is None:
        current_domain = settings.DOMAIN_NAME
    current_time = timezone.now().astimezone(pytz.timezone("US/Eastern"))
    to_emails = [user.email for user in BridgeUser.objects.filter(is_admin=True).all()]
    if settings.DEBUG:
        to_emails = ["<EMAIL>"]
        # Stop this from running in debug mode, remove this line and use your email to debug
        return
    email = EmailMessage(
        subject=f"Bridge - {email_subject} - {current_time.strftime('%b. %d, %Y, %I:%M %p %Z')}",
        body=email_body,
        from_email=settings.DEFAULT_FROM_EMAIL,
        to=to_emails,
    )

    # Send the email
    email.content_subtype = "html"  # Indicate that the email content is HTML
    logger.info(
        "Sending admin notification email",
        email_subject=email_subject,
        to_emails=to_emails,
        email_body=email_body,
        email=email,
    )
    email.send()


EXPECTED_RUN_TIME_HOUR = 10
EXPECTED_RUN_TIME_MINUTE_START = 0
EXPECTED_RUN_TIME_MINUTE_END = 10


@shared_task(bind=True, track_started=True)
@singleton_task()
def notify_new_processed_documents_v2(  # noqa: C901, PLR0912, PLR0915
    self: Task, current_domain: str | None = None, *, override_suppression: bool = False
) -> None:
    # Guard: Only run between 10:00 and 10:10 AM America/New_York and not already run today
    if not override_suppression:
        ny_tz = pytz.timezone("America/New_York")
        now_ny = timezone.now().astimezone(ny_tz)
        today_str = now_ny.strftime("%Y-%m-%d")
        if not (
            now_ny.hour == EXPECTED_RUN_TIME_HOUR
            and EXPECTED_RUN_TIME_MINUTE_START <= now_ny.minute < EXPECTED_RUN_TIME_MINUTE_END
        ):
            msg = "Skipping Task: notify_new_processed_documents_v2 not in allowed to run outside of 10:00-10:10 AM"
            logger.info(
                msg,
                now=str(now_ny),
            )
            self.update_state(
                state="REVOKED",
                meta={
                    "reason": msg,
                },
            )
            raise Ignore

        # Guard: Only run if the task hasnt been run in the last 24 hours
        try:
            pt = PeriodicTask.objects.get(name="External Daily Digest V2")
        except PeriodicTask.DoesNotExist:
            msg = "Skipping Task: notify_new_processed_documents_v2 task does not exist"
            self.update_state(
                state="REVOKED",
                meta={
                    "reason": msg,
                },
            )
            logger.info(msg, today=today_str)
            raise Ignore  # noqa: B904
        # TODO: Look for only successful tasks, not just any run. Revoked tasks shouldn't block a new run.
        if pt.last_run_at and pt.last_run_at > timezone.now() - timedelta(hours=23):
            msg = "Skipping Task: notify_new_processed_documents_v2 task already ran today"
            logger.info(msg, today=today_str)
            raise Ignore

    try:
        current_domain = current_domain or settings.DOMAIN_NAME
        current_time = timezone.now().astimezone(pytz.timezone("US/Eastern"))
        posted_date_time_threshold = current_time - timedelta(hours=24)

        orgs_to_notify = Organization.objects.exclude(name__in=["Demo", "Bridge", "Test", "Demo - Design"]).distinct()

        ordered_document_types = [
            DocumentType.CAPITAL_CALL,
            DocumentType.DISTRIBUTION_NOTICE,
            DocumentType.ACCOUNT_STATEMENT,
            DocumentType.TAX,
            DocumentType.INVESTMENT_UPDATE,
            DocumentType.FINANCIAL_STATEMENTS,
        ]

        other_doc_types = {
            DocumentType.LEGAL,
            DocumentType.OTHER,
            DocumentType.UNKNOWN,
        }

        document_type_mapping = {
            DocumentType.CAPITAL_CALL: "Capital Calls",
            DocumentType.DISTRIBUTION_NOTICE: "Distribution Notices",
            DocumentType.ACCOUNT_STATEMENT: "Account Statements",
            DocumentType.TAX: "Tax Documents",
            DocumentType.INVESTMENT_UPDATE: "Investment Updates",
            DocumentType.FINANCIAL_STATEMENTS: "Financial Statements",
        }

        for org in orgs_to_notify:
            new_documents = (
                ProcessedDocument.objects.filter(
                    organization=org,
                    posted_date__gte=posted_date_time_threshold,
                    is_visible=True,
                )
                .order_by(
                    "line_item__investment__legal_name",
                    "line_item__investing_entity__legal_name",
                    "document_type",
                    "name",
                )
                .select_related(
                    "line_item",
                    "line_item__investing_entity",
                    "line_item__investment",
                    "line_item__investing_entity__client",
                    "capital_call_document",
                    "distribution_notice_document",
                )
                .only(
                    "name",
                    "document_type",
                    "posted_date",
                    "effective_date",
                    "document_summary",
                    "line_item__investing_entity__legal_name",
                    "line_item__investment__legal_name",
                    "line_item__investing_entity__client__legal_name",
                    "capital_call_document__amount",
                    "distribution_notice_document__amount",
                )[:50]
            )

            time_period = (
                f"{posted_date_time_threshold.strftime('%b %d, %Y, %I:%M %p %Z')} to "
                f"{current_time.strftime('%b %d, %Y, %I:%M %p %Z')}"
            )

            # Build sections
            ordered_document_sections = []
            docs_by_type = {doc_type: [] for doc_type in ordered_document_types}
            other_docs = []

            for doc in new_documents:
                if doc.document_type in docs_by_type:
                    docs_by_type[doc.document_type].append(doc)
                elif doc.document_type in other_doc_types:
                    other_docs.append(doc)

            for doc_type in ordered_document_types:
                docs = docs_by_type[doc_type]
                if docs:
                    ordered_document_sections.append((document_type_mapping[doc_type], docs))

            if other_docs:
                ordered_document_sections.append(("Other Documents", other_docs))

            base_url = "http://localhost:8000" if settings.DEBUG else f"https://{current_domain}"

            context = {
                "DocumentType": DocumentType,
                "current_domain": current_domain,
                "time_period": time_period,
                "organization": org,
                "ordered_document_sections": ordered_document_sections,
                "base_url": base_url,
                "todays_date": current_time.strftime("%B %d, %Y"),
                "total_documents": len(new_documents),
            }

            email_content = render_to_string(
                "daily_digest_email/daily_digest_v2.html",
                context,
            )

            users = (
                BridgeUser.objects.filter(organization=org, is_active=True)
                .exclude(is_superuser=True)
                # TODO: Send one email per user and apply permissions for that email.
                # Filtering out VIEWER roles for now.
                .exclude(roles__name=Role.RoleName.VIEWER)
                .distinct()
            )

            if not users.exists():
                logger.info("No users found for organization", org_name=org.name)
                continue

            possible_emails = [user.email for user in users if user.email]
            bcc_emails = [user.email for user in BridgeUser.objects.filter(is_superuser=True).all()]
            # TODO: Add orgs here as needed
            enabled_orgs = {"Aspiriant", "F.L.Putnam", "11.5 Holdings", "Ezralow", "Summit Trail"}
            to_emails = possible_emails if org.name in enabled_orgs else []

            logger.info(
                "Sending email daily digest",
                to_emails=to_emails,
                possible_emails=possible_emails,
                bcc_emails=bcc_emails,
                org_name=org.name,
                num_documents=len(new_documents),
            )
            postpend = ""
            if settings.DEBUG:
                to_emails = []
                bcc_emails = ["<EMAIL>", "<EMAIL>"]
                postpend = f" {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
                logger.error(
                    "Debug mode - email not sent, please input your usename to test",
                    to_emails=to_emails,
                    bcc_emails=bcc_emails,
                )
                # Comment out to test
                return

            prepend = ""
            if len(to_emails) == 0:
                prepend = "[INTERNAL] "
            # Send email
            email = EmailMultiAlternatives(
                subject=f"{prepend}Bridge - Daily Digest for {org.name}{postpend}",
                body=email_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=to_emails,
                bcc=bcc_emails,
            )
            email.attach_alternative(email_content, "text/html")
            email.send()

    except Exception:
        logger.exception("Error in notify_new_processed_documents")
        raise
