from datetime import date

import pytest
from django.contrib.auth.signals import user_logged_out
from django.http import HttpRequest

from webapp.models import (
    BridgeEmail,
    Client,
    CustomerEmailCredential,
    InvestingEntity,
    Investment,
    LineItem,
    MergedPortalCredential,
    MultiFactorAuthentication,
    Portal,
    PortalCredential,
    ProcessedDocument,
    Retrieval,
)
from webapp.models.documents import DocumentType
from webapp.models.portal import MFAType
from webapp.models.user import BridgeUser, Organization, Role, cleanup_demo


@pytest.fixture
def cleanup_signal() -> None:
    """Connect the cleanup signal during test and disconnect afterward."""
    # Connect the signal
    user_logged_out.connect(cleanup_demo)

    # Run the test
    yield

    # Disconnect the signal
    user_logged_out.disconnect(cleanup_demo)


@pytest.fixture
def demo_test_user(demo_org: Organization, test_role: Role) -> BridgeUser:
    """Create a demo test user."""
    return BridgeUser.objects.create_user(
        username="tomjonesadvisor",
        email="<EMAIL>",
        first_name="<PERSON>",
        last_name="<PERSON>",
        organization=str(demo_org.id),
        roles=[str(test_role.id)],
        password="testpass123",  # noqa: S106
    )


@pytest.fixture
def mock_request() -> HttpRequest:
    """Create a mock HttpRequest."""
    return HttpRequest()


@pytest.mark.django_db(transaction=True)
def test_cleanup_demo_signal(
    demo_test_user: BridgeUser,
    mock_request: HttpRequest,
    cleanup_signal: None,
) -> None:
    """Test that the cleanup_demo signal handler deletes demo user data."""
    # Create test data for all models that should be cleaned up
    client = Client.objects_unsafe.create(
        legal_name="Test Client",
        organization=demo_test_user.organization,
        created_by=demo_test_user,
        updated_by=demo_test_user,
    )

    _ = BridgeEmail.objects_unsafe.create(
        organization=demo_test_user.organization, created_by=demo_test_user, updated_by=demo_test_user
    )

    credential = CustomerEmailCredential.objects_unsafe.create(
        organization=demo_test_user.organization, created_by=demo_test_user, updated_by=demo_test_user
    )

    entity = InvestingEntity.objects_unsafe.create(
        legal_name="Test Entity",
        client=client,
        organization=demo_test_user.organization,
        created_by=demo_test_user,
        updated_by=demo_test_user,
    )

    investment = Investment.objects_unsafe.create(
        organization=demo_test_user.organization, created_by=demo_test_user, updated_by=demo_test_user
    )

    portal = Portal.objects_unsafe.create(
        name="Test Portal",
        organization=demo_test_user.organization,
        created_by=demo_test_user,
        updated_by=demo_test_user,
    )
    portal_credential = PortalCredential.objects_unsafe.create(
        organization=demo_test_user.organization,
        updated_by=demo_test_user,
        created_by=demo_test_user,
        portal=portal,
        username="",
    )
    multi_factor_authentication = MultiFactorAuthentication.objects_unsafe.create(
        created_by=demo_test_user,
        updated_by=demo_test_user,
        organization=demo_test_user.organization,
        multi_factor_authentication_type=MFAType.EMAIL,
        receiving_email=credential,
    )

    mpc = MergedPortalCredential.objects_unsafe.create(
        organization=demo_test_user.organization,
        created_by=demo_test_user,
        updated_by=demo_test_user,
        portal=portal,
        portal_credential=portal_credential,
        multi_factor_authentication=multi_factor_authentication,
    )

    line_item = LineItem.objects_unsafe.create(
        investing_entity=entity,
        investment=investment,
        organization=demo_test_user.organization,
        merged_portal_credential=mpc,
        created_by=demo_test_user,
        updated_by=demo_test_user,
    )

    _ = Retrieval.objects_unsafe.create(
        organization=demo_test_user.organization, created_by=demo_test_user, updated_by=demo_test_user
    )

    _ = ProcessedDocument.objects_unsafe.create(
        organization=demo_test_user.organization,
        document_type=DocumentType.UNKNOWN,
        md5="testmd5",
        name="testname",
        process_document_source="testsource",
        process_document_version=1,
        posted_date=date(2024, 1, 1),
        effective_date=date(2024, 1, 1),
        content_type="testcontenttype",
        line_item=line_item,
        created_by=demo_test_user,
        updated_by=demo_test_user,
        is_visible=True,
    )
    _ = ProcessedDocument.objects_unsafe.create(
        organization=demo_test_user.organization,
        document_type=DocumentType.UNKNOWN,
        md5="testmd52",
        name="testname2",
        process_document_source="testsource2",
        process_document_version=1,
        posted_date=date(2024, 1, 1),
        effective_date=date(2024, 1, 1),
        content_type="testcontenttype2",
        line_item=line_item,
        created_by=demo_test_user,
        updated_by=demo_test_user,
        is_visible=False,
    )

    # Verify data exists before logout
    assert Client.objects.for_user(user=demo_test_user).exists()
    assert BridgeEmail.objects.for_user(user=demo_test_user).exists()
    assert CustomerEmailCredential.objects.for_user(user=demo_test_user).exists()
    assert InvestingEntity.objects.for_user(user=demo_test_user).exists()
    assert Investment.objects.for_user(user=demo_test_user).exists()
    assert LineItem.objects.for_user(user=demo_test_user).exists()
    assert Portal.objects.for_user(user=demo_test_user).exists()
    assert Retrieval.objects.for_user(user=demo_test_user).exists()
    assert MergedPortalCredential.objects.for_user(user=demo_test_user).exists()
    assert ProcessedDocument.objects.for_user(user=demo_test_user).count() == 1

    # Trigger the logout signal
    user_logged_out.send(sender=BridgeUser, request=mock_request, user=demo_test_user)

    # Verify all data is deleted
    assert not Client.objects.for_user(user=demo_test_user).exists()
    assert not BridgeEmail.objects.for_user(user=demo_test_user).exists()
    assert not CustomerEmailCredential.objects.for_user(user=demo_test_user).exists()
    assert not InvestingEntity.objects.for_user(user=demo_test_user).exists()
    assert not Investment.objects.for_user(user=demo_test_user).exists()
    assert not LineItem.objects.for_user(user=demo_test_user).exists()
    assert not Portal.objects.for_user(user=demo_test_user).exists()
    assert not Retrieval.objects.for_user(user=demo_test_user).exists()
    assert not MergedPortalCredential.objects.for_user(user=demo_test_user).exists()
    assert not ProcessedDocument.objects.for_user(user=demo_test_user).exists()


@pytest.mark.django_db(transaction=True)
def test_cleanup_demo_signal_non_demo_user(
    test_user: BridgeUser,
    mock_request: HttpRequest,
    test_role: Role,
    cleanup_signal: None,
) -> None:
    """Test that the cleanup_demo signal handler doesn't delete non-demo user data."""
    test_user.roles.add(test_role)

    _ = Client.objects_unsafe.create(
        legal_name="Test Client", organization=test_user.organization, created_by=test_user, updated_by=test_user
    )

    # Verify data exists before logout
    assert Client.objects.for_user(user=test_user).exists()

    # logout signal
    user_logged_out.send(sender=BridgeUser, request=mock_request, user=test_user)

    # Verify data still exists
    assert Client.objects.for_user(user=test_user).exists()


@pytest.mark.django_db(transaction=True)
def test_cleanup_demo_wrong_demo_user(
    demo_org: Organization,
    test_role: Role,
    mock_request: HttpRequest,
    cleanup_signal: None,
) -> None:
    """Test that the cleanup_demo signal handler doesn't delete data for wrong demo user."""
    # Create a demo user with different credentials
    wrong_demo_user = BridgeUser.objects.create_user(
        username="wrongdemo",
        email="<EMAIL>",  # Different email
        first_name="Wrong",  # Different name
        last_name="Demo",
        organization=str(demo_org.id),
        roles=[str(test_role.id)],
        password="testpass123",  # noqa: S106
    )

    # Create test data
    _ = Client.objects_unsafe.create(
        legal_name="Test Client",
        organization=wrong_demo_user.organization,
        created_by=wrong_demo_user,
        updated_by=wrong_demo_user,
    )

    # Verify data exists before logout
    assert Client.objects.for_user(user=wrong_demo_user).exists()

    # Trigger the logout signal
    user_logged_out.send(sender=BridgeUser, request=mock_request, user=wrong_demo_user)

    # Verify data still exists (shouldn't be deleted because user details don't match)
    assert Client.objects.for_user(user=wrong_demo_user).exists()
