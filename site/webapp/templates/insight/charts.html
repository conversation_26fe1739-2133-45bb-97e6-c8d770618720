<div class="flex w-full pt-5">
    <!-- First Container -->
    <div class="px-8 w-1/2">
        <!-- Centering the title using flex and justify-center -->
        <div class="flex justify-center">
            <div class="BodyLgSemibold text-[#343d36] text-2xl font-semibold font-['Inter'] leading-9">Asset Class</div>
        </div>
        <div class="p-4 bg-white min-h-[84px] rounded-xl border border-[#eceee8] justify-center items-center">
            <div id="asset_class_pie_chart"
                 class="p-8 Text text-black text-sm font-['Inter'] leading-tight">
                <!-- Pie chart content goes here -->
            </div>
        </div>
    </div>
    <div class="px-8 w-1/2 ">
        <div class="flex justify-center">
            <div class="BodyLgSemibold text-[#343d36] text-2xl font-semibold font-['Inter'] leading-9">Geography</div>
        </div>
        <div class="p-4 bg-white min-h-[84px] rounded-xl border border-[#eceee8] justify-center items-center">
            <div id="geography_pie_chart" class="h-full p-8  rounded-lg"></div>
        </div>
    </div>
</div>
<div class="flex w-full pt-3">
    <!-- First Container -->
    <div class="px-8 w-1/2">
        <!-- Centering the title using flex and justify-center -->
        <div class="flex justify-center">
            <div class="BodyLgSemibold text-[#343d36] text-2xl font-semibold font-['Inter'] leading-9">Sector</div>
        </div>
        <div class="p-4 bg-white min-h-[84px] rounded-xl border border-[#eceee8] justify-center items-center">
            <div id="sector_pie_chart" class="h-full p-8 rounded-lg">
                <!-- Pie chart content goes here -->
            </div>
        </div>
    </div>
    <div class="px-8 w-1/2 ">
        <div class="flex justify-center">
            <div class="BodyLgSemibold text-[#343d36] text-2xl font-semibold font-['Inter'] leading-9">Sub-Sector</div>
        </div>
        <div class="p-4 bg-white min-h-[84px] rounded-xl border border-[#eceee8] justify-center items-center">
            <div id="sub_Sector_pie_chart" class="h-full p-8  rounded-lg"></div>
        </div>
    </div>
</div>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
{# djlint:off #}
<script>
    google.charts.load('current', {
        'packages': ['corechart']
    });

    google.charts.setOnLoadCallback(drawChart);

    function drawChart() {
        // Define your data here, mapping categories and percentages dynamically.
        var data = {{chart_data | safe }};
        var categories = ['asset_class', 'geography', 'sector', 'sub_Sector'];

        // Loop through each category and create a PieChart for it dynamically
        for (var category of categories) {
            // Get the corresponding container for the chart
            var chartContainer = document.getElementById(category + '_pie_chart');

            // Check if data for the current category exists and is valid
            if (!data[category] || data[category].length <= 1) {
                // Manually set "No data available" message
                chartContainer.innerHTML = "<div class='Text text-center text-black text-sm font-semibold font-['Inter'] leading-tight'>No data available</div>";
                continue; // Skip drawing the chart
            }

            // Convert the data into Google Chart's format
            var chartData = google.visualization.arrayToDataTable(data[category]);

            // Set chart options
            var options = {
                is3D: false,
                chartArea: {
                    width: '100%',
                    height: '100%'
                },
                backgroundColor: '#fff'
            };

            // Instantiate and draw the chart
            var chart = new google.visualization.PieChart(chartContainer);
            chart.draw(chartData, options);
        }
    }
    
</script>
{# djlint:on #}
