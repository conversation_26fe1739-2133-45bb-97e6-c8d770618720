{% load customtags static %}
{% if mpc.user_forwarding_rule %}
    <button type="button"
            hx-get="{% url 'emails_add_line_item' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
            hx-swap="outerHTML"
            hx-target="#modal"
            hx-trigger="click"
            class="w-full px-4 py-2.5 bg-[#924f34] text-md font-semibold text-white rounded-lg hover:bg-[#8a5f47] transition-colors">
        Next
    </button>
{% else %}
    <button type="button"
            hx-get="{% url 'multi_factor_authentication_end_screen' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
            hx-swap="outerHTML"
            hx-target="#modal-container"
            hx-trigger="click"
            class="w-full px-4 py-2.5 bg-[#924f34] text-md font-semibold text-white rounded-lg hover:bg-[#8a5f47] transition-colors">
        Next
    </button>
{% endif %}
