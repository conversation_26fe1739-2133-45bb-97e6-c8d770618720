import asyncio
import json
from collections import Counter

import boto3
import structlog
from botocore.exceptions import ClientError

logger = structlog.get_logger(__name__)


# TODO: success rate is really low, try other models / libraries
async def parse_captcha(
    base64_image_content: str,
    prompt: str = "Identify captcha: alphanumeric only, case-sensitive. Reply ONLY with the captcha",
) -> str:
    try:
        bedrock_runtime = boto3.client(service_name="bedrock-runtime")
        model_id = "anthropic.claude-3-haiku-20240307-v1:0"
        max_tokens = 1000
        message = {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "source": {"type": "base64", "media_type": "image/png", "data": base64_image_content},
                },
                {"type": "text", "text": prompt},
            ],
        }

        messages = [message]

        body = json.dumps({"anthropic_version": "bedrock-2023-05-31", "max_tokens": max_tokens, "messages": messages})
        response = bedrock_runtime.invoke_model(body=body, modelId=model_id)
        response_body = json.loads(response.get("body").read())
        # TODO: save down the image and the result so we can check success rate?
        logger.info("parsed captcha", response_body=response_body)
        try:
            return response_body["content"][0]["text"]
        except (KeyError, IndexError):
            logger.exception("Unexpected response format: %s", response_body)
    except ClientError as err:
        message = err.response["Error"]["Message"]
        logger.exception("A client error occurred: %s", message)


async def get_average_captcha_result(
    base64_image_content: str,
    num_of_calls: int = 3,
    prompt: str = "Identify captcha: alphanumeric only, case-sensitive. Reply ONLY with the captcha",
) -> str | None:
    if num_of_calls == 1:
        return parse_captcha(base64_image_content, prompt)
    captchas = []
    for _ in range(num_of_calls):
        result = await parse_captcha(base64_image_content, prompt)
        captchas.append(result)
        await asyncio.sleep(5)  # sleep to avoid too many requests
    logger.info("listing %s captcha results: %s", num_of_calls, ", ".join(captchas))
    # return if there is a consensus
    counter = Counter(captchas)
    most_common = counter.most_common(1)
    if most_common[0][1] > 1:
        return most_common[0][0]
    return None
