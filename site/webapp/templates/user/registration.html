{% extends "account/base_entrance.html" %}
{% load widget_tweaks static allauth account %}
{% block head_title %}
    User Registration
{% endblock head_title %}
{% block extra_head %}
    <header class="min-w-full h-[100px] bg-primary-dark-olive flext flex-col justify-center items-center gap-2.5 inline-flex">
        <div class="justify-start items-center gap-10 inline-flex">
            <div class="h-9 relative">
                <a href="{% url 'account_login' %}">
                    <img class="items-center w-50"
                         alt="Bridge"
                         height="37px"
                         width="130px"
                         src="{% static 'assets/images/logo.svg' %}">
                </a>
            </div>
        </div>
    </header>
{% endblock extra_head %}
{% block content %}
    <main class="mx-auto">
        {% if not is_valid_token %}
            <div class="max-w-md mx-auto mt-6 p-4 bg-red-100 border-l-4 border-red-500 text-red-700">
                <h2 class="text-lg font-semibold">Invalid Invitation Link</h2>
                <p class="mt-2">
                    This invitation link is invalid or has already been
                    used. Please contact support to request a new link.
                </p>
            </div>
        {% else %}
            <div class="mx-auto mt-4">
                <div class="text-[#343d36] text-4xl font-bold font-['Nanum Myeongjo'] leading-[44px] text-left">
                    Finish setting up your account
                </div>
                <form id="password-validation-form"
                      method="post"
                      hx-post="{% url 'password_validators' %}"
                      hx-target="#password_reset_validators"
                      hx-trigger="keyup, load, change"
                      hx-swap="innerHTML"
                      class="self-stretch rounded-xl flex-col justify-start items-start gap-6 flex mt-6">
                    {% csrf_token %}
                    <div class="Form w-4/5 flex flex-col gap-4">
                        <!-- First Name -->
                        <div class="flex flex-col gap-2">
                            <div class="inline-flex">
                                <label for="first_name"
                                       class="text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                                    First Name
                                </label>
                            </div>
                            <div class="bg-white rounded-lg shadow border border-[#eceee8] flex items-center gap-2">
                                <input id="first_name"
                                       name="first_name"
                                       type="text"
                                       value="{{ form.first_name.value|default:'' }}"
                                       placeholder="Enter your first name"
                                       class="grow h-11 flex border-none rounded-xl text-[#343d36] text-sm font-normal font-inter focus:outline-none focus:ring-0" />
                            </div>
                            {% if form.first_name.errors %}
                                <div class="text-red-500 text-sm">{{ form.first_name.errors|join:", " }}</div>
                            {% endif %}
                        </div>
                        <!-- Last Name -->
                        <div class="flex flex-col gap-1.5">
                            <div class="inline-flex">
                                <label for="last_name"
                                       class="text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                                    Last Name
                                </label>
                            </div>
                            <div class="bg-white rounded-lg shadow border border-[#eceee8] flex items-center gap-2">
                                <input id="last_name"
                                       name="last_name"
                                       type="text"
                                       value="{{ form.last_name.value|default:'' }}"
                                       placeholder="Enter your last name"
                                       class="grow h-11 flex border-none rounded-xl text-[#343d36] text-sm font-normal font-inter focus:outline-none focus:ring-0" />
                            </div>
                            {% if form.last_name.errors %}<div class="text-red-500 text-sm">{{ form.last_name.errors|join:", " }}</div>{% endif %}
                        </div>
                        <!-- Last Name -->
                        <div class="flex flex-col gap-1.5">
                            <div class="inline-flex">
                                <label for="contact_number"
                                       class="text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                                    Phone Number
                                </label>
                            </div>
                            <div class="Input bg-white rounded-lg shadow border border-[#eceee8] flex items-center gap-2">
                                <input id="contact_number"
                                       name="contact_number"
                                       type="text"
                                       value="{{ form.contact_number.value|default:'' }}"
                                       oninput="formatPhoneNumber(this)"
                                       onload="formatPhoneNumber(this)"
                                       placeholder="Enter your phone number"
                                       class="grow h-11 flex border-none rounded-xl text-[#343d36] text-sm font-normal font-inter focus:outline-none focus:ring-0" />
                            </div>
                            {% if form.contact_number.errors %}
                                <div class="text-red-500 text-sm">{{ form.contact_number.errors|join:", " }}</div>
                            {% endif %}
                        </div>
                        <!-- Email -->
                        <div class="flex flex-col gap-1.5">
                            <div class="inline-flex">
                                <label for="email"
                                       class="text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                                    Email
                                </label>
                            </div>
                            <div class="bg-white rounded-lg shadow border border-[#eceee8] flex items-center gap-2">
                                <input id="email"
                                       name="email"
                                       readonly
                                       type="email"
                                       value="{{ form.email.value|default:'' }}"
                                       placeholder="Enter your email"
                                       required
                                       class="w-full px-4 py-2 border border-gray-300 bg-gray-200 text-gray-500  rounded-md shadow-sm focus:outline-none " />
                            </div>
                            {% for error in form.email.errors %}<div class="text-red-500">{{ error }}</div>{% endfor %}
                        </div>
                        <!-- Password -->
                        <div class="flex flex-col gap-1.5 ">
                            <div class="inline-flex">
                                <label for="password1"
                                       class="text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                                    Password
                                </label>
                            </div>
                            <div class="bg-white rounded-lg shadow border border-[#eceee8] flex items-center gap-2">
                                <input id="password1"
                                       name="password1"
                                       type="password"
                                       placeholder="Create a password"
                                       class="grow h-11 flex border-none rounded-xl text-[#343d36] text-sm font-normal font-inter focus:outline-none focus:ring-0" />
                                <div class="pr-3">
                                    <img id="toggleIcon1"
                                         src="{% static 'assets/images/hide_icon.svg' %}"
                                         alt="Toggle visibility"
                                         width="20"
                                         tabindex="-1"
                                         onclick="togglePasswordVisibility('password1', 'toggleIcon1')"
                                         height="20">
                                </div>
                            </div>
                        </div>
                        <!-- Confirm Password -->
                        <div class="flex flex-col gap-1.5">
                            <div class="inline-flex">
                                <label for="password2"
                                       class="Label text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                                    Confirm Password
                                </label>
                            </div>
                            <div class="bg-white rounded-lg shadow border border-[#eceee8] flex items-center gap-2">
                                <input id="password2"
                                       name="password2"
                                       type="password"
                                       placeholder="Confirm your password"
                                       class="grow h-11 flex border-none rounded-xl text-[#343d36] text-sm font-normal font-inter focus:outline-none focus:ring-0" />
                                <div class="pr-3">
                                    <img id="toggleIcon2"
                                         src="{% static 'assets/images/hide_icon.svg' %}"
                                         alt="Toggle visibility"
                                         width="20"
                                         tabindex="-1"
                                         onclick="togglePasswordVisibility('password2', 'toggleIcon2')"
                                         height="20">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="password_reset_validators"></div>
                    <!-- Terms of Use Checkbox -->
                    {% if form.tos_agreement.errors %}<div class="text-red-500 text-sm">{{ form.tos_agreement.errors.0 }}</div>{% endif %}
                    <input hidden type="text" name="button_text" value="Register" />
                    <input type="hidden" name="show_tos_checkbox" value="true">
                    <br>
                    <br>
                </form>
            </div>
        {% endif %}
    </main>
{% endblock content %}
