# Bridge Investment - Technical Areas for Deep Dive

## Core Technical Components to Explore

### 1. **Web Scraping Architecture**
```
Key Files:
- site/retrieval/core/strategy.py (base RetrievalManager)
- site/retrieval/core/managers/*.py (portal-specific implementations)
- site/retrieval/core/registry.py (manager selection logic)

Questions to Ask:
- How do you handle dynamic content and JavaScript-heavy sites?
- What's the approach for handling CAPTCHAs?
- How do you detect and adapt to portal UI changes?
```

### 2. **State Machine Implementation**
```
Key Files:
- site/webapp/models/retrieval.py (Retrieval states)
- site/webapp/models/base.py (base state machine)

Questions to Ask:
- How are state transitions triggered?
- What happens during state rollbacks?
- How do you prevent race conditions in state changes?
```

### 3. **Document Processing Pipeline**
```
Key Files:
- site/ml_app/core/doc_vault_lib.py
- site/ml_app/core/ingestion/*.py
- site/ml_app/tasks/ingestion.py

Questions to Ask:
- What's the document parsing strategy for different formats?
- How do you handle OCR for scanned documents?
- What's the chunking strategy for large documents?
```

### 4. **Multi-tenant Permissions System**
```
Key Files:
- site/webapp/models/core.py (PermissionManager)
- site/webapp/models/user.py (Role, Organization)
- site/webapp/models/line_item.py (LineItemPermission)

Questions to Ask:
- How are cross-organization data leaks prevented?
- What's the permission inheritance hierarchy?
- How do you handle permission changes for existing data?
```

### 5. **Email Integration Architecture**
```
Key Files:
- site/email_webhook/api.py
- site/email_webhook/tasks/email_parse.py
- site/webapp/services/emails.py

Questions to Ask:
- How reliable is the email forwarding setup?
- What's the email parsing accuracy for MFA codes?
- How do you handle email provider API changes?
```

## Architecture Decisions to Understand

### 1. **Why Django + HTMX over SPA?**
- Server-side rendering benefits
- Reduced JavaScript complexity
- SEO considerations?

### 2. **Celery Task Architecture**
- Task routing strategy
- Failure handling and retries
- Task result storage

### 3. **AWS Service Selection**
- Why ECS over Lambda/Fargate?
- S3 vs database for document storage
- Secrets Manager vs Parameter Store

### 4. **Database Design**
- Soft delete pattern usage
- JSON fields vs normalized tables
- Index strategy for performance

## Code Patterns to Study

### 1. **Async/Await Usage**
```python
# Pattern seen in managers
@sync_to_async
def database_operation():
    pass
```

### 2. **Error Handling Strategy**
```python
# Custom exceptions for different failure modes
class TimeoutForOTPTokenError(Exception):
    pass
```

### 3. **Logging and Monitoring**
```python
# Structured logging with context
logger = structlog.get_logger(__name__)
```

### 4. **Configuration Management**
```python
# Environment-specific settings
site/bridge_project/settings/*.py
```

## Performance Considerations

### 1. **Concurrent Retrieval Handling**
- How many parallel browser instances?
- Resource pooling strategy?
- Rate limiting per portal?

### 2. **Database Query Optimization**
- Use of select_related/prefetch_related
- Query performance monitoring
- Database connection pooling

### 3. **Document Storage Strategy**
- S3 storage classes used?
- CDN for document delivery?
- Compression strategies?

## Security Deep Dive

### 1. **Credential Management**
- Encryption at rest and in transit
- Key rotation strategy
- Access audit logging

### 2. **Portal Authentication**
- Session management
- Cookie handling
- OAuth token storage

### 3. **API Security**
- Rate limiting implementation
- API key generation and rotation
- Request validation

## Integration Points

### 1. **External Services**
- AWS services integration
- LLM API usage (Bedrock)
- Email provider APIs

### 2. **Webhook Handling**
- SNS message validation
- Idempotency guarantees
- Failure recovery

### 3. **Portal APIs**
- API vs web scraping decisions
- API rate limit handling
- Authentication methods

## Testing Strategy

### 1. **Portal Integration Testing**
- Mock portal responses
- Screenshot-based testing?
- Regression test suite

### 2. **Document Processing Testing**
- Test document corpus
- Expected extraction validation
- ML model testing

### 3. **End-to-End Testing**
- Demo account automation
- Retrieval pipeline testing
- Performance benchmarks

## Deployment and Operations

### 1. **Zero-Downtime Deployments**
- Blue-green deployment?
- Database migration strategy
- Rollback procedures

### 2. **Monitoring and Alerting**
- Key metrics tracked
- Alert thresholds
- On-call procedures

### 3. **Debugging Production Issues**
- Log aggregation strategy
- Distributed tracing?
- Production access controls

## Areas for Potential Improvement

### 1. **Scalability Bottlenecks**
- Single database limitations?
- Celery queue scaling
- Browser instance management

### 2. **Code Organization**
- Monolith splitting opportunities
- Shared library extraction
- API versioning strategy

### 3. **Developer Experience**
- Local development speed
- Testing feedback loops
- Documentation gaps

## Innovation Opportunities

### 1. **AI/ML Enhancements**
- Better document understanding
- Predictive analytics
- Anomaly detection

### 2. **Real-time Features**
- WebSocket integration?
- Push notifications
- Live dashboard updates

### 3. **Platform Expansion**
- Mobile applications
- Third-party integrations
- White-label offerings