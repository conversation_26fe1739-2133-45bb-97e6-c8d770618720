import hashlib
import json
import os
from argparse import Argument<PERSON><PERSON><PERSON>
from pathlib import Path
from zipfile import ZipFile

import boto3
import pandas as pd
import structlog
from django.conf import settings
from django.core.management.base import BaseCommand
from django.utils import timezone
from django_extensions.management.utils import signalcommand
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, save_document

from webapp.models.documents import DocumentType
from webapp.models.portal import MergedPortalCredential, Portal
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser

logger = structlog.get_logger(__name__)


def download_from_s3(s3: boto3.client, excel_file: Path, zip_file: Path) -> tuple[Path, Path]:
    logger.info("Processing files from S3")
    excel_bucket = excel_file.parts[1]
    zip_bucket = zip_file.parts[1]
    if excel_bucket != zip_bucket:
        logger.error("Both files must be in the same S3 bucket")
        raise ValueError
    bucket_name = excel_bucket
    excel_file_key = str(Path(*excel_file.parts[2:]))
    zip_file_key = str(Path(*zip_file.parts[2:]))
    try:
        s3.head_object(Bucket=bucket_name, Key=excel_file_key)
        s3.head_object(Bucket=bucket_name, Key=zip_file_key)
    except BaseException:
        logger.exception(
            "One or both files do not exist in the S3 bucket",
            bucket_name=bucket_name,
            excel_file_key=excel_file_key,
            zip_file_key=zip_file_key,
        )
        raise

    dump_local_folder = Path("./site")
    excel_file_name = str(dump_local_folder / excel_file.parts[-1])
    zip_file_name = str(dump_local_folder / zip_file.parts[-1])
    s3.download_file(bucket_name, excel_file_key, excel_file_name)
    s3.download_file(bucket_name, zip_file_key, zip_file_name)
    local_zip_file_path = Path(zip_file_name)
    local_excel_file_path = Path(excel_file_name)
    logger.info(
        "Downloaded files", local_zip_file_path=local_zip_file_path, local_excel_file_path=local_excel_file_path
    )
    return local_excel_file_path, local_zip_file_path


def upload_to_s3(s3: boto3.client, excel_file: Path, zip_file: Path) -> tuple[Path, Path]:
    logger.info("Uploading to S3! First checking files' validity")
    _ = process_files(str(excel_file), str(zip_file))  # raises if invalid
    manual_retrieval_folder = Path("manual_retrieval") / str(timezone.now().strftime("%Y-%m-%d_%H-%M-%S"))
    excel_s3_file = manual_retrieval_folder / excel_file.name.replace(" ", "_")
    zip_s3_file = manual_retrieval_folder / zip_file.name.replace(" ", "_")
    s3.upload_file(excel_file, settings.AWS_STORAGE_BUCKET_NAME, str(excel_s3_file))
    s3.upload_file(zip_file, settings.AWS_STORAGE_BUCKET_NAME, str(zip_s3_file))
    logger.info(
        "We have successfully uploaded these files to S3. Processing is only allowed if the files are valid and in s3"
    )
    return excel_s3_file, zip_s3_file


class Command(BaseCommand):
    help = "Processes Excel and ZIP files to check if files from the Document Path exist in the ZIP file."

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument(
            "-z",
            "--zip",
            type=str,
            required=True,
            help="Path to the ZIP file",
        )
        parser.add_argument(
            "-x",
            "--xlsx",
            type=str,
            required=True,
            help="Path to the CSV file",
        )
        parser.add_argument(
            "-u",
            "--user",
            type=str,
            help="User Email",
        )
        parser.add_argument(
            "-p",
            "--portal",
            type=str,
            help="Portal Name",
        )

    @signalcommand
    def handle(self, *args: list[str], **kwargs: dict) -> None:  # noqa: ARG002, C901, PLR0912
        s3 = boto3.client("s3")
        excel_file = Path(kwargs.get("xlsx"))
        zip_file = Path(kwargs.get("zip"))
        local_zip_file_path = None
        local_excel_file_path = None
        try:
            if Path(zip_file).suffix != ".zip":
                logger.error("ZIP file must have a .zip extension")
                raise ValueError
            if Path(excel_file).suffix != ".csv":
                logger.error("Excel file must have a .csv extension. Please export and delete extra rows/data")
                raise ValueError
            if (excel_file.parts[0] == "s3:") ^ (zip_file.parts[0] == "s3:"):
                logger.error("Both files must either be local or in S3")
                raise ValueError

            if excel_file.parts[0] == "s3:" and zip_file.parts[0] == "s3:":
                local_excel_file_path, local_zip_file_path = download_from_s3(s3, excel_file, zip_file)

            elif zip_file.exists() ^ excel_file.exists():
                logger.error("Both files must exist locally or in S3")
                raise ValueError

            elif zip_file.exists() and excel_file.exists():
                excel_s3_file, zip_s3_file = upload_to_s3(s3, excel_file, zip_file)
                logger.info("Please run the following command:")
                logger.info(
                    './site/manage.py process_manual_retrieval -z "s3://%s/%s" -x "s3://%s/%s"',
                    settings.AWS_STORAGE_BUCKET_NAME,
                    zip_s3_file,
                    settings.AWS_STORAGE_BUCKET_NAME,
                    excel_s3_file,
                )
                return

            if not (
                local_zip_file_path is not None
                and local_excel_file_path is not None
                and local_zip_file_path.exists()
                and local_excel_file_path.exists()
            ):
                logger.error(
                    "Both files must be downloaded locally from s3 to be processed",
                    excel=local_excel_file_path,
                    zip=local_zip_file_path,
                    zip_exists=local_zip_file_path.exists(),
                    excel_exists=local_excel_file_path.exists(),
                )
                raise ValueError

            # In summary, at this point in the code, we have validated that both files exist in S3,
            # Can be processed, and are downloaded locally.
            logger.info("Processing files", excel=local_excel_file_path, zip=local_zip_file_path)

            user = kwargs.get("user")
            portal_name = kwargs.get("portal")
            if user is None or portal_name is None:
                logger.error("User and Portal name are required to proceed")
                return
            user = BridgeUser.objects.get(email=user)
            portal = Portal.objects.get(name=portal_name, organization=user.organization)
            merged_portal_credential = MergedPortalCredential.objects.for_user(user).filter(portal=portal).all()
            if len(merged_portal_credential) == 0:
                logger.error("Could not find a MergedPortalCredential for the user and portal")
                return
            if len(merged_portal_credential) > 1:
                logger.error("Found multiple MergedPortalCredentials for the user and portal")
                return

            merged_portal_credential = merged_portal_credential[0]

            retrieval = Retrieval.create(
                user=user,
                merged_portal_credential=merged_portal_credential,
                s3_format_version=-1,
                s3_key="/".join(zip_file.parts[2:-1]),
                s3_bucket=zip_file.parts[1],
                manager="ManualRetrievalV1",
                exists_in_s3=True,
            )
            _ = process_files(
                excel_file=str(local_excel_file_path), zip_file=str(local_zip_file_path), retrieval=retrieval, user=user
            )
            retrieval.update_login_status(Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL)

        finally:
            logger.info("Cleaning up")
            if local_zip_file_path:
                Path.unlink(local_zip_file_path)
            if local_excel_file_path:
                Path.unlink(local_excel_file_path)

        # make retrieval
        # run save document everywhere.


def process_files(  # noqa: C901, PLR0912, PLR0915 too complex
    excel_file: str, zip_file: str, retrieval: Retrieval | None = None, user: BridgeUser | None = None
) -> list[RawDocumentTuple]:
    logger.info("Starting the processing of files")

    rows = pd.read_csv(excel_file).to_dict(orient="records")

    paths = [row["Document path"] for row in rows]
    common_folder = os.path.commonprefix(paths)
    windows = common_folder.startswith("C:\\")
    split_char = "\\" if windows else "/"
    if not common_folder.endswith(split_char):
        common_folder = split_char.join(common_folder.split(split_char)[:-1]) + split_char

    zip_name = zip_file.split("/")[-1].replace(".zip", "")
    split = common_folder.split(split_char)
    logger.info("Attempting to find the common folder", original_split=split, zip_name=zip_name)
    logger.info("IMPORTANT: The common folder name must be the same as the zip file name")
    try:
        while split[-1].replace(" ", "_") != zip_name.replace(" ", "_"):
            split.pop()
            logger.info("Attempting to find the common folders", split_level=split[-1])
        split.pop()
    except:
        logger.exception("Could not find the common folder")
        raise
    common_folder = split_char.join(split) + split_char
    logger.info("Common folder", common_folder=common_folder)

    docs_with_content = []
    docs_without_content = []
    zip_file_names = []

    with ZipFile(zip_file, "r") as zf:
        zip_file_names.extend(zf.namelist())
        zip_file_names = [zfn for zfn in zip_file_names if not (zfn.endswith("/") or zfn.startswith("__MACOSX/"))]
        for row in rows:
            document_path = row["Document path"]
            document_path = document_path.replace(common_folder, "").replace(split_char, "/")
            name = document_path.split("/")[-1]
            posted_date = row["BridgePostedDate"]
            posted_date = (
                timezone.now() if posted_date is None else pd.to_datetime(posted_date).to_pydatetime(warn=False)
            )
            doc_type = map_to_bridge_type(row)
            doc_hash = generate_document_hash(row)
            raw_metadata = json.dumps(
                {
                    k: v
                    for k, v in row.items()
                    if k not in ["Document path", "BridgePostedDate", "BridgeDocumentType", None]
                    or k.startswith("Unnamed")
                },
                default=str,
            )
            if document_path in zip_file_names:
                zip_file_names.remove(document_path)
                if retrieval is not None and user is not None:
                    with zf.open(document_path) as pdf_file:
                        doc = RawDocumentTuple(
                            name=name,
                            date=posted_date,
                            doc_type=doc_type,
                            content=pdf_file.read(),
                            raw_metadata=raw_metadata,
                            doc_hash=doc_hash,
                        )
                        save_document(user, doc, retrieval=retrieval)
                        retrieval.increment_documents_retrieved()
                else:
                    docs_with_content.append(
                        RawDocumentTuple(
                            name=name,
                            date=posted_date,
                            doc_type=doc_type,
                            content=None,
                            raw_metadata=raw_metadata,
                            doc_hash=doc_hash,
                        )
                    )
            else:
                docs_without_content.append(row)
    if user is not None and retrieval is not None:
        return []

    logger.info(
        "Found documents",
        length_docs_with_context=len(docs_with_content),
        length_docs_without_context=len(docs_without_content),
    )

    for unmatched in zip_file_names:
        logger.error("Unmatched file in ZIP", unmatched=unmatched)

    if len(docs_without_content) > 0:
        logger.error("Either documents without content or unmatched files in ZIP")
        logger.error("Please edit these files to have no errors")
        raise ValueError

    dupes = {}
    for doc in docs_with_content:
        if doc.doc_hash not in dupes:
            dupes[doc.doc_hash] = []
        dupes[doc.doc_hash].append(doc)

    has_duplicates = False
    for doc_hash, docs in dupes.items():
        if len(docs) > 1:
            logger.error("Duplicate document hash found", doc_hash=doc_hash, length=len(docs))
            for doc in docs:
                logger.error("Dupe Document", doc_name=doc.name)
            logger.info("------------------------")
            has_duplicates = True
    if has_duplicates:
        logger.error("Please fix the duplicates in the Excel file")
        raise ValueError
    logger.info("No duplicates found")
    logger.info("All checks passed", length_processed_files=len(docs_with_content))
    return docs_with_content


def map_to_bridge_type(document: dict[str, str]) -> DocumentType:  # noqa: PLR0911
    doc_type = document["BridgeDocumentType"].strip()
    if doc_type == "Distribution Notice":
        return DocumentType.DISTRIBUTION_NOTICE
    if doc_type == "Investment Update":
        return DocumentType.INVESTMENT_UPDATE
    if doc_type == "Capital Call":
        return DocumentType.CAPITAL_CALL
    if doc_type == "Tax":
        return DocumentType.TAX
    if doc_type == "Legal":
        return DocumentType.LEGAL
    if doc_type == "Other":
        return DocumentType.OTHER
    return DocumentType.UNKNOWN


def generate_document_hash(document: dict[str, str]) -> DocumentHash:
    keys = sorted([k for k in document if k is not None and k.lower().startswith("portal")])
    string_value = "".join(str([document[k] for k in keys]))
    return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, "ManualRetrieval")  # noqa: S324 # nosec
