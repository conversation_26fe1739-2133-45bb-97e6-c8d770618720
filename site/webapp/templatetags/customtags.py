import decimal
import json
from datetime import date, datetime
from typing import Any
from urllib.parse import urlencode

from babel.numbers import format_currency
from django import template
from django.conf import settings
from django.db import models
from django.utils import timezone

register = template.Library()


# https://stackoverflow.com/questions/51375759/django-url-template-with-query-parameters
# Why are URL params so hard in Django?
@register.simple_tag
def urlparams(*_: list[str], **kwargs: str | None) -> str:
    safe_args = {k: v for k, v in kwargs.items() if v is not None}
    if safe_args:
        return f"?{urlencode(safe_args)}"
    return ""


@register.simple_tag
def label_lookup(label: str, enum: models.TextChoices) -> str:
    try:
        return enum(label).label
    except ValueError:
        return f"Unknown (bad label {label})"


@register.simple_tag
def alias(obj: str) -> str:
    return obj


@register.filter
def tojson(obj: Any) -> str:  # noqa: ANN401
    return json.dumps(obj, indent=2)


@register.simple_tag
def settings_value(name: str) -> str:
    return getattr(settings, name, "")


@register.filter
def lookup(value: str | dict[str, Any], arg: str, default: str | None = None) -> Any:  # noqa: ANN401
    if isinstance(value, dict):
        return value.get(arg, default)
    return None


@register.filter
def format_as_currency(value: float, currency: str = "USD") -> str:
    try:
        return format_currency(value, currency, locale="en_US")
    except (ValueError, TypeError, decimal.InvalidOperation):
        return "N/A"


@register.filter
def format_to_mdy(d: str | datetime | date) -> str:
    if d is None:
        return "N/A"
    if isinstance(d, str):
        if d.strip() == "":
            return "N/A"
        try:
            d = datetime.strptime(d.strip(), "%B %d, %Y").date()  # noqa: DTZ007
        except ValueError:
            msg = "String format must be 'Month DD, YYYY'"
            raise ValueError(msg)  # noqa: B904

    if isinstance(d, (date, datetime)):
        return d.strftime("%m/%d/%y")

    msg = "Expected str, date, or datetime"
    raise TypeError(msg)


@register.filter
def endswith(text: str, suffix: str) -> str:
    return text.endswith(suffix)


@register.filter
def is_future(datetime_value: datetime) -> bool:
    """Check if a datetime is in the future compared to current time."""
    if datetime_value is None:
        return False
    return datetime_value > timezone.now()
