{% load static customtags %}
<!DOCTYPE html>
<html lang="en">
    <head>
        <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .email-container {
            max-width: 1440px;
            margin: 20px auto;
            background-color: #ffffff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: right;
        }
        .content {
            padding: 20px;
        }
        .content h1 {
            font-size: 20px;
            color: #333333;
        }
        .content p {
            font-size: 16px;
            color: #555555;
        }
        .content a{
            color: #555555;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            margin: 20px 0;
            background-color: #924f34;
            color: #ffffff;
            text-decoration: none;
            font-size: 16px;
            border-radius: 5px;
        }
        .footer {
            font-size: 12px;
            color: #777777;
            text-align: center;
            padding: 10px;
        }
        .footer a {
            color: #555555;
            text-decoration: none;
        }
        .footer span a {
            margin: 0 5px;
        }
        table, th, td {
            border: 1px solid black;
            border-collapse: collapse;
        }
        th, td {
            padding: 15px;
        }
        th {
            font-weight: bolder;
        }
        .bold-red{
            font-weight: bold;
            color: red;
        }
        </style>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="format-detection" content="telephone=no, address=no, email=no">
        <meta name="description" content="Bridge Internal Daily Digest">
        <meta name="keywords" content="Bridge, Daily, Digest, Internal, Metrics">
        <meta name="color-scheme" content="light dark">
        <title>Bridge Internal Daily Digest</title>
    </head>
    <body>
        <div class="email-container">
            <div class="header">
                <img src="https://{{ current_domain }}{% static 'assets/images/email_logo.png' %}"
                     alt="Bridge Logo"
                     width="129"
                     height="36"
                     class="logo">
            </div>
            <div class="content">
                <h2>Documents Retrieved Last 24h</h2>
                <table>
                    <thead>
                        <th>Organization</th>
                        <th>Portal</th>
                        <th>Document Type</th>
                        <th>Count</th>
                        <th>Link</th>
                    </thead>
                    <tbody>
                        {% for raw_documents in raw_documents_cts %}
                            <tr>
                                <td>{{ raw_documents.organization }}</td>
                                <td>{{ raw_documents.portal }}</td>
                                <td>
                                    {% if raw_documents.document_type != 'Total' %}
                                        {% label_lookup label=raw_documents.document_type enum=DocumentType %}
                                    {% else %}
                                        {{ raw_documents.document_type }}
                                    {% endif %}
                                </td>
                                <td>{{ raw_documents.count }}</td>
                                <td>
                                    <a href="https://{{ current_domain }}{% url 'admin:webapp_rawdocument_changelist' %}{% urlparams document_type__exact=raw_documents.document_type organization__id__exact=raw_documents.organization_id retrieval__merged_portal_credential__portal__name=raw_documents.portal %}"
                                       target="_blank">View</a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <h2>User Logins Last 24h</h2>
                <table>
                    <thead>
                        <th>Username</th>
                        <th>Time</th>
                    </thead>
                    <tbody>
                        {% for user_login in users_logged_in %}
                            <tr>
                                <td>{{ user_login.username }}</td>
                                <td>{{ user_login.attempt_time }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <h2>
                    <a href="https://180294215839.us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:logs-insights$3FqueryDetail$3D~(end~0~start~-172800~timeType~'RELATIVE~tz~'UTC~unit~'seconds~editorString~'fields*20*40timestamp*2c*20logger*2c*20level*2c*20event*2c*20action*2c*20code*2c*20request*2c*20request_id*2c*20user_id*0a*7c*20filter*20level*3d*22error*22*0a*7c*20sort*20*40timestamp*20asc*0a*7c*20limit*2010000~queryId~'90c3c4ce-d76f-41a1-8cc4-76434bf9f5cd~source~(~'SiteServiceStack-SiteLogGroupCAF00FD1-089b3gbkQJBW~'CeleryServiceStack-CeleryLogGroup0C29692E-9UPbQUAwrr0Y)~lang~'CWLI)"
                       class="bold-red"
                       target="_blank">View All Production Errors</a>
                </h2>
                <h2>Failed Retrievals Summary</h2>
                <ul>
                    {% for failed_retrieval in failed_retrievals %}
                        <li>
                            <span><strong>{{ failed_retrieval.portal }}</strong> - {{ failed_retrieval.error_type }}</span>
                            <ul>
                                <li>
                                    <span>{{ failed_retrieval.error_summary }}</span>
                                </li>
                            </ul>
                        </li>
                    {% endfor %}
                </ul>
                <h2>Retrieval Runs Last 24h</h2>
                <table>
                    <thead>
                        <th>Organization</th>
                        <th>Portal</th>
                        <th>Retrieval Status</th>
                        <th>Number of New Docs</th>
                        <th>Number of Skipped Docs</th>
                        <th>Last Successful Run</th>
                        <th>Time taken (seconds)</th>
                        <th>Number of retries</th>
                        <th>Success last 7 days</th>
                        <th>Failed last 7 days</th>
                        <th>DB Obj</th>
                    </thead>
                    <tbody>
                        {% for retrieval in retrievals %}
                            {% if retrieval.is_successful %}
                                {% alias "" as retrieval_class %}
                            {% else %}
                                {% alias "bold-red" as retrieval_class %}
                            {% endif %}
                            <tr>
                                <td class="{{ retrieval_class }}">{{ retrieval.organization }}</td>
                                <td class="{{ retrieval_class }}">{{ retrieval.portal }}</td>
                                <td class="{{ retrieval_class }}">
                                    {% if retrieval.error_type %}
                                        {{ retrieval.error_type }}
                                    {% else %}
                                        {{ retrieval.retrieval_status }}
                                    {% endif %}
                                </td>
                                <td class="{{ retrieval_class }}">{{ retrieval.number_documents_retrieved }}</td>
                                <td class="{{ retrieval_class }}">{{ retrieval.number_documents_skipped }}</td>
                                <td class="{{ retrieval_class }}">{{ retrieval.last_successful_run }}</td>
                                <td class="{{ retrieval_class }}">{{ retrieval.time_taken }}s</td>
                                <td class="{{ retrieval_class }}">{{ retrieval.number_of_retries }}</td>
                                <td class="{{ retrieval_class }}">{{ retrieval.num_success_last_7 }}</td>
                                <td class="{{ retrieval_class }}">{{ retrieval.num_failed_last_7 }}</td>
                                <td class="{{ retrieval_class }}">
                                    {% if retrieval.retrieval_pk %}
                                        <a href="https://{{ current_domain }}{% url 'admin:webapp_retrieval_change' object_id=retrieval.retrieval_pk %}"
                                           target="_blank">Retrival Object</a>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <!-- Footer Section -->
            <div class="footer">
                <p>
                    If you'd rather not receive this kind of email, you can unsubscribe or manage your email preferences
                    by contacting <a href="mailto:<EMAIL>"><EMAIL></a>.
                </p>
                <p>
                    <span>
                        <a href="https://bridgeinvest.io">Bridge Investment Technologies, Inc.</a>
                        | <a href="mailto:<EMAIL>">Support</a>
                        | <a href="https://www.bridgeinvest.io/privacy-policy">Privacy Policy</a>
                    </span>
                </p>
            </div>
        </div>
    </body>
</html>
