from typing import Any, override

from django.core.exceptions import ValidationError
from django.db import models


class StateMachineChoices(models.TextChoices):
    """
    Base class for implementing state machines using Django's TextChoices.

    This class provides a framework for defining states and valid transitions between them.
    Subclasses should define their states as class attributes and override the valid_transitions
    property to specify which transitions are allowed.
    """

    @classmethod
    def valid_transitions(cls) -> dict[str, list[str]]:
        """
        Define valid transitions between states.

        This should be overridden in subclasses to define the state machine transitions.
        Returns a dictionary mapping source states to lists of valid target states.

        Returns:
            Dict[str, List[str]]: Dictionary of valid transitions

        """
        return {}

    @classmethod
    def validate_transition(cls, from_state: str, to_state: str) -> bool:
        """
        Validate if a transition from one state to another is allowed.

        Args:
            from_state: The current state
            to_state: The target state

        Returns:
            bool: True if the transition is valid, False otherwise

        """
        # Allow transition to the same state
        if from_state == to_state:
            return True

        # Check if the transition is defined in valid_transitions
        valid_targets = cls.valid_transitions().get(from_state, [])
        return to_state in valid_targets

    @classmethod
    def get_valid_next_states(cls, current_state: str) -> list[str]:
        """
        Get all valid states that can be transitioned to from the current state.

        Args:
            current_state: The current state

        Returns:
            List[str]: List of valid next states

        """
        valid_states = [current_state]
        valid_states.extend(cls.valid_transitions().get(current_state, []))
        return valid_states


class StateMachineField(models.CharField):
    """
    A custom field for state machines that enforces valid state transitions.
    """

    def __init__(
        self, state_choices: type[StateMachineChoices], *args: tuple[Any, ...], **kwargs: dict[str, Any]
    ) -> None:
        self.state_choices = state_choices
        kwargs["choices"] = state_choices.choices
        kwargs["max_length"] = 30  # Reasonable default for state names
        super().__init__(*args, **kwargs)

    @override
    def deconstruct(self) -> tuple[str, str, tuple[Any, ...], dict[str, Any]]:
        name, path, args, kwargs = super().deconstruct()
        kwargs["state_choices"] = self.state_choices
        del kwargs["choices"]  # Remove choices as it's derived from state_choices
        return name, path, args, kwargs

    @override
    def pre_save(self, model_instance: models.Model, add: bool) -> str:
        current_value = getattr(model_instance, self.attname)

        if add:
            return current_value
        try:
            original_instance = model_instance.__class__.objects.get(pk=model_instance.pk)
            original_value = getattr(original_instance, self.attname)
            if not self.state_choices.validate_transition(original_value, current_value):
                msg = (
                    f"Invalid state transition from '{original_value}' to '{current_value}'. "
                    f"Valid next states are: {self.state_choices.get_valid_next_states(original_value)}"
                )
                raise ValidationError(msg)

        except model_instance.__class__.DoesNotExist:
            pass

        return current_value


# Example usage:
class OrderStatus(StateMachineChoices):
    """
    Example state machine for order processing.
    """

    CREATED = "created", "Created"
    PROCESSING = "processing", "Processing"
    SHIPPED = "shipped", "Shipped"
    DELIVERED = "delivered", "Delivered"
    CANCELLED = "cancelled", "Cancelled"

    @classmethod
    def valid_transitions(cls) -> dict[str, list[str]]:
        """Define valid transitions for the order process."""
        return {
            cls.CREATED: [cls.PROCESSING, cls.CANCELLED],
            cls.PROCESSING: [cls.SHIPPED, cls.CANCELLED],
            cls.SHIPPED: [cls.DELIVERED, cls.CANCELLED],
            cls.DELIVERED: [],  # Terminal state, no further transitions
            cls.CANCELLED: [],  # Terminal state, no further transitions
        }
