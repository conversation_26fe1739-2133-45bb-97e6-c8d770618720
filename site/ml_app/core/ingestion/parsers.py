import base64
import json
import re
import tempfile
from abc import ABC, abstractmethod

import boto3
import camelot
import pymupdf
import structlog
from bs4 import BeautifulSoup
from camelot.core import Table
from webapp.models.documents import RawDocument

from ml_app.core.ingestion.core import BoundingBox, ParsedDocument

logger = structlog.get_logger(__name__)


class Parser(ABC):
    @abstractmethod
    def name(self) -> str:
        pass

    @abstractmethod
    def parse(self, raw_document_pk: str) -> list[ParsedDocument]:
        # html, page_number, bounding_box
        pass


class CamelotHybridHTMLParser(Parser):
    # https://arxiv.org/html/2305.13062v4
    # https://arxiv.org/html/2410.09871v1#abstract

    def name(self) -> str:
        return "camelot_hybrid_html"

    def parse(self, raw_document_pk: str) -> list[ParsedDocument]:
        raw_doc = RawDocument.objects.get(pk=raw_document_pk)
        parsed_documents: list[ParsedDocument] = []
        i = 0
        with raw_doc.get_doc_file() as file_path:
            tables = []
            while True:
                i += 1
                try:
                    for flavor in ["lattice", "stream", "network", "hybrid"]:
                        ctables = camelot.io.read_pdf(file_path, pages=str(i), flavor=flavor)
                        tables.extend(ctables)
                except IndexError:
                    break
            deduped_tables = nms(tables, iou_threshold=0.9)
            for table in deduped_tables:
                with tempfile.NamedTemporaryFile(delete=True) as tmp_file:
                    table.to_html(path=tmp_file.name)
                    tmp_file.seek(0)
                    html = tmp_file.read().decode("utf-8")
                bounding_box = BoundingBox.from_camelot_table(table)
                page_number = -1 if table.page is None else table.page
                parsed_documents.append(ParsedDocument(html, page_number, "html", self.name(), bounding_box))

        return parsed_documents


# Scale-aware NMS: normalize IoU by the area of the smaller box
def scale_iou(b1: tuple[float, float, float, float], b2: tuple[float, float, float, float]) -> float:
    x_left = max(b1[0], b2[0])
    y_top = max(b1[1], b2[1])
    x_right = min(b1[2], b2[2])
    y_bottom = min(b1[3], b2[3])
    if x_right < x_left or y_bottom < y_top:
        return 0.0
    intersection = (x_right - x_left) * (y_bottom - y_top)
    area1 = (b1[2] - b1[0]) * (b1[3] - b1[1])
    area2 = (b2[2] - b2[0]) * (b2[3] - b2[1])
    min_area = min(area1, area2)
    return intersection / min_area if min_area > 0 else 0.0


def nms(tables: list[Table], iou_threshold: float = 0.95) -> list[Table]:
    deduped = {}

    for t in tables:
        if t.page not in deduped:
            deduped[t.page] = []

        keep = True
        to_remove = []
        for d in deduped[t.page]:
            # should we also bias for the amount of data in the table? shape inner product
            # or length of characters in the resultant.
            if scale_iou(t._bbox, d._bbox) > iou_threshold:  # noqa: SLF001
                if t.accuracy > d.accuracy:
                    to_remove.append(d)
                else:
                    keep = False
                    break

        for d in to_remove:
            deduped[t.page].remove(d)

        if keep:
            deduped[t.page].append(t)
    res = []
    for ts in deduped.values():
        res.extend(ts)
    return res


class MuPyPDFHTMLParser(Parser):
    # https://arxiv.org/html/2410.09871v1#abstract

    def name(self) -> str:
        return "mupdf_html"

    def parse(self, raw_document_pk: str) -> list[ParsedDocument]:
        raw_doc = RawDocument.objects.get(pk=raw_document_pk)
        content = raw_doc.get_doc_bytes()
        doc = pymupdf.open(stream=content, filetype="pdf")
        res = []
        for page in doc.pages():
            image_list = page.get_images(full=True)
            all_image_boxes = []
            for _, img in enumerate(image_list):
                xref = img[0]  # image object reference
                rects = page.get_image_rects(xref)
                all_image_boxes.extend(
                    [
                        {
                            "xref": xref,
                            "bbox": [rect.x0, rect.y0, rect.x1, rect.y1],
                            "width": rect.width,
                            "height": rect.height,
                        }
                        for rect in rects
                    ]
                )

            html = page.get_text("html")
            soup = BeautifulSoup(html, "html.parser")
            compress_span_tags(soup, "b")
            compress_span_tags(soup, "p")
            compress_b_in_p_to_heading(soup)
            add_render_positioning(soup)
            describe_images_and_structure_html(
                soup, all_image_boxes, model_id="us.anthropic.claude-3-7-sonnet-20250219-v1:0"
            )
            out_html = soup.prettify()
            res.append(
                ParsedDocument(
                    out_html,
                    page_number=page.number + 1,  # pymupdf is 0-indexed, we want 1-indexed
                    format="html",
                    parser_name=self.name(),
                    bounding_box=BoundingBox.from_html_element(out_html),
                )
            )
        return res


def merge_styles(*style_strs: str) -> str:
    styles = {}
    for style in style_strs:
        if not style:
            continue
        for part in style.split(";"):
            if ":" in part:
                key, val = part.strip().split(":", 1)
                styles[key.strip()] = val.strip()
    return "; ".join(f"{k}: {v}" for k, v in styles.items())


def compress_span_tags(soup: BeautifulSoup, tag: str) -> None:
    for p in soup.find_all(tag):
        children = list(p.children)
        if len(children) == 1 and children[0].name == "span":
            span = children[0]
            p["style"] = merge_styles(p.get("style", ""), span.get("style", ""))
            p.string = span.get_text(strip=False)
            span.decompose()


def compress_b_in_p_to_heading(soup: BeautifulSoup, max_n: int = 4) -> None:  # noqa: C901
    all_font_sizes = []
    for p in soup.find_all("p"):
        if len(p.contents) == 1 and p.b:
            b = p.b
            font_size = None
            if b.has_attr("style"):
                styles = b["style"].split(";")
                for s in styles:
                    if "font-size" in s:
                        font_size = float(s.split(":")[1].replace("pt", "").strip())
                        all_font_sizes.append(font_size)
                        break

    top_4 = sorted(set(all_font_sizes), reverse=True)[:max_n]

    def get_heading_tag(font_size: float) -> str:
        for i in range(max_n):
            if len(top_4) > i and font_size >= top_4[i]:
                return f"h{i + 1}"
        return "b"

    for p in soup.find_all("p"):
        if len(p.contents) == 1 and p.b:
            b = p.b
            font_size = None
            if b.has_attr("style"):
                styles = b["style"].split(";")
                for s in styles:
                    if "font-size" in s:
                        font_size = float(s.split(":")[1].replace("pt", "").strip())
                        break

            tag_name = get_heading_tag(font_size) if font_size is not None else "h4"

            new_tag = soup.new_tag(tag_name)
            new_tag.string = b.get_text()
            new_tag["style"] = b.get("style", "")

            new_tag["style"] = merge_styles(p.get("style", ""), b.get("style", ""))

            p.replace_with(new_tag)


def convert_pt_to_px(style_str: str) -> str:
    def replace(match: re.Match) -> str:
        prop = match.group(1)
        value_pt = float(match.group(2))
        value_px = value_pt
        return f"{prop}:{value_px}px"

    return re.sub(r"(\b[a-zA-Z\-]+):\s*([\d.]+)pt", replace, style_str)


def add_render_positioning(soup: BeautifulSoup) -> None:
    for tag in soup.find_all(style=True):
        style_str = tag["style"]

        styles = {k.strip(): v.strip() for k, v in [s.split(":", 1) for s in style_str.split(";") if ":" in s]}

        if "top" in styles and "left" in styles:
            styles["position"] = "absolute"

        styles.pop("color", None)
        styles.pop("font-family", None)

        tag["style"] = "; ".join(f"{k}: {v}" for k, v in styles.items())
        tag["style"] = convert_pt_to_px(tag["style"])


def flatten_div_global_xy_coords(soup: BeautifulSoup, buffer: float = 50.0) -> BeautifulSoup:
    # Parse the original HTML
    soup = BeautifulSoup(str(soup), "html.parser")
    output_soup = BeautifulSoup("<html><body></body></html>", "html.parser")
    if output_soup.html is None:
        return soup
    root = output_soup.html.body
    if root is None:
        return soup

    offset = 0.0

    for div in soup.find_all("div"):
        # Extract height from the div's style
        height_match = re.search(r"height:([\d.]+)px", div.get("style", ""))
        page_height = float(height_match.group(1)) if height_match else 0.0

        for child in div.find_all(recursive=False):
            if "style" in child.attrs:
                # Adjust top position
                top_match = re.search(r"top:([\d.]+)px", child["style"])
                if top_match:
                    original_top = float(top_match.group(1))
                    new_top = original_top + offset
                    child["style"] = re.sub(r"top:[\d.]+px", f"top:{new_top:.2f}px", child["style"])

            root.append(child)

        offset += page_height + buffer
    return output_soup


def describe_images_and_structure_html(
    soup: BeautifulSoup, bbox_lookup: list, model_id: str = "anthropic.claude-3-sonnet-20240229-v1:0"
) -> None:
    bedrock = boto3.client("bedrock-runtime", region_name="us-east-1")

    for idx, img in enumerate(soup.find_all("img")):
        src = img.get("src")
        if not src:
            continue

        # Identify image type
        if src.startswith("data:image/"):
            header, encoded = src.split(",", 1)
            image_data = base64.b64decode(encoded)
            mime_type = header.split(";")[0].split(":")[1]
        else:
            continue  # Skip unsupported src

        # Build Claude 3 payload for vision
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": mime_type,
                                "data": base64.b64encode(image_data).decode(),
                            },
                        },
                        {"type": "text", "text": "Describe this image in detail."},
                    ],
                }
            ],
            "anthropic_version": "bedrock-2023-05-31",
            "max_tokens": 1024,
        }

        # Send to Bedrock
        response = bedrock.invoke_model(
            modelId=model_id, body=json.dumps(payload), contentType="application/json", accept="application/json"
        )
        result = json.loads(response["body"].read())
        description = result["content"][0]["text"].strip()

        if "alt" not in img.attrs:
            img["alt"] = ""
        img["alt"] += description
        img["data-image-index"] = idx
        img["src"] = f"image_{idx}.png"
        if idx < len(bbox_lookup):
            top = round(min(bbox_lookup[idx]["bbox"][1], bbox_lookup[idx]["bbox"][3]), 2)
            left = round(min(bbox_lookup[idx]["bbox"][0], bbox_lookup[idx]["bbox"][2]), 2)
            height = round(bbox_lookup[idx]["height"], 2)
            width = round(bbox_lookup[idx]["width"], 2)
            img["style"] = (
                img.get("style", "")
                + f" position: relative;top:{top}px;left:{left}px;height:{height}px;width:{width}px;"
            )
        else:
            logger.error("Image bounding box not found", idx=idx, bbox_lookup_length=len(bbox_lookup))
