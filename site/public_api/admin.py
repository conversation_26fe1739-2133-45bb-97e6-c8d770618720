from django import forms
from django.contrib import admin, messages
from django.db import models
from django.http import HttpRequest

from .auth import generate_key
from .models import APIKey


@admin.action(description="Revoke selected API keys")
def revoke_key(modeladmin: "APIKeyAdmin", request: HttpRequest, queryset: models.QuerySet) -> None:  # noqa: ARG001
    queryset.update(revoked=True)


@admin.register(APIKey)
class APIKeyAdmin(admin.ModelAdmin):
    list_display = (
        "prefix",
        "user",
        "label",
        "created_at",
        "expires_at",
        "revoked",
        "is_active",
    )
    readonly_fields = ("prefix", "hashed_key", "created_at")
    actions = (revoke_key,)
    list_filter = ("revoked",)

    @admin.display
    def is_active(self, obj: APIKey) -> bool:
        return obj.is_valid

    is_active.boolean = True

    def save_model(self, request: HttpRequest, obj: APIKey, form: forms.ModelForm, change: bool) -> None:  # noqa: FBT001
        if not obj.prefix:  # New API key
            key = generate_key()
            obj.prefix = key.prefix
            obj.hashed_key = key.hashed_key

            messages.warning(
                request,
                f"The API key for {obj} is '{key.prefix}.{key.key}'."
                " You should store it somewhere safe:"
                " you will not be able to see the key again.",
            )
        super().save_model(request, obj, form, change)
