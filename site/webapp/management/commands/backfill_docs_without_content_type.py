import boto3
import filetype
import structlog
from django.core.management.base import BaseCommand
from django_extensions.management.utils import signalcommand

from webapp.models.documents import ProcessedDocument, RawDocument

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Back filling raw and processed documents without proper file type extension or content type"

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002
        s3 = boto3.client("s3")
        raw_documents = RawDocument.objects.all().only("name", "s3_bucket", "s3_key", "exists_in_s3")
        file_extensions = [".pdf", ".xlsx", ".xls", ".doc", ".docx", ".zip", ".csv"]

        raw_doc_count = 0
        processed_doc_count = 0
        for raw_doc in raw_documents:
            raw_doc_name = raw_doc.name
            if all(ext not in raw_doc_name.lower() for ext in file_extensions):
                if not raw_doc.exists_in_s3:
                    continue

                response = s3.get_object(Bucket=raw_doc.s3_bucket, Key=raw_doc.s3_key)
                content = response["Body"].read()
                kind = filetype.guess(content)
                if kind:
                    raw_doc.name = raw_doc_name + "." + kind.extension
                    raw_doc.save()
                    raw_doc_count += 1
                else:
                    logger.error("Cannot update raw document", doc_id=raw_doc.id, doc_name=raw_doc.name)

            processed_docs = ProcessedDocument.objects.filter(raw_retreival_document=raw_doc).only(
                "name", "s3_bucket", "s3_key", "exists_in_s3", "content_type"
            )

            for processed_doc in processed_docs:
                processed_doc_name = processed_doc.name
                if all(ext not in processed_doc_name.lower() for ext in file_extensions):
                    if not processed_doc.exists_in_s3:
                        continue

                    if (raw_doc.s3_bucket, raw_doc.s3_key) != (processed_doc.s3_bucket, processed_doc.s3_key):
                        pd_response = s3.get_object(Bucket=processed_doc.s3_bucket, Key=processed_doc.s3_key)
                        pd_content = pd_response["Body"].read()
                        pd_kind = filetype.guess(pd_content)
                    else:
                        pd_kind = kind

                    if pd_kind:
                        processed_doc.name = processed_doc_name + "." + pd_kind.extension
                        processed_doc.content_type = pd_kind.mime
                        processed_doc_count += 1
                    else:
                        processed_doc.is_visible = False
                        logger.error(
                            "Cannot update processed document", doc_id=processed_doc.id, doc_name=processed_doc.name
                        )
                    processed_doc.save()

        logger.info("Number of raw docs changed", raw_doc_count=raw_doc_count)
        logger.info("Number of processed docs changed", processed_doc_count=processed_doc_count)
