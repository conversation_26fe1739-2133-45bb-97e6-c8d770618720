import structlog
from webapp.models.documents import DocumentType

from retrieval.core.managers.kleiner_perkins_caz import KleinerPerkinsAndCAZPortalManagerAbstract
from retrieval.core.registry import register_strategy

logger = structlog.get_logger(__name__)


@register_strategy
class KleinerPerkinsInvestorPortalManager(KleinerPerkinsAndCAZPortalManagerAbstract):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "portal.kleinerperkins.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "Kleiner Perkins Investor Portal"),
        ]

    @classmethod
    def email_otp_rules(cls) -> dict:
        return {
            "senderContains": ["kleinerperkins.com"],
        }

    def _document_mapper(self, category: str) -> str:
        return {
            "Tax Information": DocumentType.TAX,
            "Financials": DocumentType.FINANCIAL_STATEMENTS,
            "Capital Account Statements": DocumentType.ACCOUNT_STATEMENT,
            "Capital Call": DocumentType.CAPITAL_CALL,
        }.get(category, DocumentType.UNKNOWN)
