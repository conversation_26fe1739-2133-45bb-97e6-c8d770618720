import asyncio
import hashlib
import json
from collections.abc import As<PERSON><PERSON><PERSON><PERSON>, <PERSON>wai<PERSON>, Callable
from pathlib import Path

import structlog
from dateutil import parser
from playwright.async_api import Locator, expect
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)
from retrieval.core.utils import get_average_captcha_result

logger = structlog.getLogger(__name__)


@register_strategy
class NavFundServicesPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "navfundservices.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return []

    async def _enter_captcha(
        self,
        fill_input_action: Callable[[], Awaitable[None]],
        captcha_input_locator: Locator,
        button_locator: Locator,
        retry_count: int = 3,
    ) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.wait_for_load_state("networkidle", timeout=6000)
        captcha_length = 5
        get_new_captcha_count = 10
        while retry_count > 0 and get_new_captcha_count > 0:
            await fill_input_action()
            image_src = await self.page.get_by_alt_text("Captcha").get_attribute("src")
            if image_src is None or not image_src.startswith("data:"):
                raise CouldNotFindHTMLElementError
            # Extract base64 content from data URL
            base64_part = image_src.split("base64,")[1]
            content_image = base64_part.strip()
            raw_captcha = await get_average_captcha_result(
                content_image,
                prompt=f"Identify captcha: alphanumeric string of {captcha_length}, CASE-SENSITIVE. Reply ONLY with the captcha",  # noqa: E501
            )
            captcha = None
            if raw_captcha is not None:
                captcha = raw_captcha.replace(" ", "")  # handle LLM sometimes returning spaces
            logger.info("captcha from raw_captcha", captcha=captcha, raw_captcha=raw_captcha)
            if captcha is not None and len(captcha) == captcha_length:
                await captcha_input_locator.fill(captcha)
                await asyncio.sleep(2)
                invalid_captcha_locator = self.page.locator("p").filter(has_text="Please enter a valid CAPTCHA code")
                await button_locator.click()
                await asyncio.sleep(2)
                if await invalid_captcha_locator.count() == 0:
                    return
                retry_count -= 1
            else:  # if no consensus from get_average_captcha_result, get a new captcha
                await self.page.locator("[data-icon='arrows-rotate']").click()
                get_new_captcha_count -= 1
        logger.error("Reached captcha retry limit")
        raise CouldNotFindHTMLElementError

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(2)
        captcha_input = self.page.get_by_placeholder("Enter captcha text")

        async def fill_user_name_lambda() -> None:
            if self.page is None:
                raise ManagerNotInitializedError
            await self.page.get_by_placeholder("Username").fill(username)

        await self._enter_captcha(
            fill_user_name_lambda,
            captcha_input,
            self.page.locator("button").filter(has_text="Next"),
            3,
        )
        await asyncio.sleep(2)
        await self.page.wait_for_load_state("networkidle")

        async def fill_password_lambda() -> None:
            if self.page is None:
                raise ManagerNotInitializedError
            await self.page.get_by_placeholder("Password").fill(password)

        await self._enter_captcha(
            fill_password_lambda,
            captcha_input,
            self.page.locator("button").filter(has_text="Login"),
            3,
        )
        await asyncio.sleep(2)

    async def _check_has_otp(self) -> bool:
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:
        pass

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        try:
            await self.page.wait_for_load_state("networkidle", timeout=6000)
            await expect(self.page.get_by_placeholder("Username")).to_have_count(0)
            await expect(self.page.get_by_placeholder("Password")).to_have_count(0)
        except AssertionError:
            return False
        except PlaywrightTimeoutError:
            logger.exception("_check_login_successful timed out")
            return False
        return True

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        # "All documents" submenu nav should be open by default, click it open otherwise
        try:
            await expect(self.page.locator("a:visible").filter(has_text="Fund Documents")).to_have_count(
                1, timeout=5000
            )
        except Exception:  # noqa: BLE001
            # get by aria label
            await self.page.get_by_label("All Documents").click()
            await asyncio.sleep(2)
        # TODO: check if these submenus are the same when we add a new line item to this portal
        sub_menus = ["Fund Documents", "Fund Financials", "Investor Documents"]
        for sub_menu in sub_menus:
            has_next_page = True
            await self.page.locator("a:visible").filter(has_text=sub_menu).click()
            await asyncio.sleep(2)
            while has_next_page:
                for rows in await self.page.get_by_role("row").all():
                    async for doc in self._retrieve_row(rows, sub_menu):
                        yield doc

                next_page_locator = self.page.get_by_label("Next Page")
                if await next_page_locator.is_disabled():
                    has_next_page = False
                else:
                    await next_page_locator.click()
                    await asyncio.sleep(5)

    async def _retrieve_row(self, table_row: Locator, document_category: str) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        fund_name = (await table_row.locator("[tabulator-field='fundName']").inner_text()).strip()
        report_name = (await table_row.locator("[tabulator-field='reportName']").inner_text()).strip()
        uploaded_on = (await table_row.locator("[tabulator-field='lastUploadedOn']").inner_text()).strip()
        file_locator = table_row.locator("[tabulator-field='staticFiles'] .file-name")
        document_name = (await file_locator.inner_text()).strip()

        # only in Investor Documents
        investor_name = ""
        investor_name_locator = table_row.locator("[tabulator-field='investorName']")
        if await investor_name_locator.count() > 0:
            investor_name = (await investor_name_locator.inner_text()).strip()
        # not in Fund Documents
        report_date = ""
        report_date_locator = table_row.locator("[tabulator-field='reportDate']")
        if await report_date_locator.count() > 0:
            report_date = (await report_date_locator.inner_text()).strip()

        doc_type = self.__document_mapper(report_name)
        metadata = json.dumps(
            {
                "Document Name": document_name,
                "Document Category": document_category,
                "Document Type": doc_type,
                "Fund Name": fund_name,
                "Investor Name": investor_name,
                "Report Name": report_name,
                "Report Date": report_date,
                "Uploaded On": uploaded_on,
            }
        )
        if await self.should_download_document(metadata):
            async with self.page.expect_download() as download_info:
                await file_locator.click()
            download = await download_info.value
            await asyncio.sleep(1)
            file_name = Path(f"/tmp/{download.suggested_filename}")  # noqa: S108 # nosec
            await download.save_as(file_name)
            content = None
            if file_name.suffix.lower() in [".pdf", ".xlsx"]:
                with Path.open(file_name, "rb") as pdf_file:
                    content = pdf_file.read()
            else:
                logger.warning("Unsupported file format: %s", file_name.suffix)
                raise ValueError
            file_name.unlink()
            doc = RawDocumentTuple(
                name=download.suggested_filename,
                date=parser.parse(uploaded_on),
                doc_type=doc_type,
                content=content,
                raw_metadata=metadata,
                doc_hash=self._doc_hash(metadata),
            )
            if doc is not None:
                yield doc

    def __document_mapper(self, category: str) -> str:
        return {
            "Fund Documents": DocumentType.DISTRIBUTION_NOTICE,  # a lot of Investment letters / notices
            "Audited Financial Statements": DocumentType.FINANCIAL_STATEMENTS,
            "Call Notices": DocumentType.CAPITAL_CALL,
            "Investor Statements": DocumentType.ACCOUNT_STATEMENT,
            "Schedule Tax Statements": DocumentType.TAX,
            "Tax Statements": DocumentType.TAX,
        }.get(category, DocumentType.UNKNOWN)
