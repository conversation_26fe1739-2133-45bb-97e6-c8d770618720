#!/usr/bin/env -S uv run
import os

import boto3
import structlog
from pyngrok import ngrok

logger = structlog.get_logger(__name__)


class NGrokError(Exception):
    pass


# TODO: maybe oneday clickify this
def main() -> None:
    tunnel_port = "8000"
    api_endpoint = "/api/email-webhook/sns-webhook"
    target = os.environ.get("AWS_PROFILE", "demo")
    if target == "demo":
        sns_topic_arn = "arn:aws:sns:us-east-1:654654313761:Inbound-Email-Topic-S3"
    elif target == "prod":
        sns_topic_arn = "arn:aws:sns:us-east-1:180294215839:Inbound-Email-Topic-S3"

    # Step 1: Start an Ngrok tunnel
    tunnel = ngrok.connect(tunnel_port, "http")
    if tunnel is None or tunnel.public_url is None:
        raise NGrokError
    ngrok_url = tunnel.public_url

    import urllib.parse as urlparse

    logger.info("Ngrok tunnel established", ngrok_url=ngrok_url)
    parsed_url = urlparse.urlparse(ngrok_url)
    url = urlparse.urlunparse(
        urlparse.ParseResult(
            scheme=parsed_url.scheme,
            netloc=f"bridge:snswebhook@{parsed_url.netloc}",
            path=api_endpoint,
            params=parsed_url.params,
            query=parsed_url.query,
            fragment=parsed_url.fragment,
        ),
    )

    logger.info("AWS SNS subscription URL", sns_subscription_url=url)

    # # Step 2: Subscribe to the SNS topic
    sns_client = boto3.client("sns")

    # # The protocol for the endpoint is "https" because we're using Ngrok over HTTPS
    sns_response = sns_client.subscribe(
        TopicArn=sns_topic_arn,
        Protocol="https",
        Endpoint=url,  # Ngrok URL as the endpoint
    )

    logger.info("SNS subscription response", sns_response=sns_response)

    # Do your work here (e.g., wait for some messages or set up your server to handle them)
    # ...
    try:
        input("Press Enter to disconnect the Ngrok tunnel...")
    except KeyboardInterrupt:
        logger.exception("Dont interupt me!")
    # Step 3: Disconnect the Ngrok tunnel when done

    try:
        ngrok.disconnect(tunnel.public_url)
    except Exception:
        logger.exception("Error disconnecting Ngrok tunnel")
    finally:
        logger.info("Ngrok tunnel disconnected")
        response = sns_client.list_subscriptions_by_topic(
            TopicArn=sns_topic_arn,
        )
        for sub in response["Subscriptions"]:
            if parsed_url.netloc in sub["Endpoint"]:
                sns_client.unsubscribe(SubscriptionArn=sub["SubscriptionArn"])
                logger.info("SNS subscription deleted", subscription_arn=sub["SubscriptionArn"])


if __name__ == "__main__":
    main()
