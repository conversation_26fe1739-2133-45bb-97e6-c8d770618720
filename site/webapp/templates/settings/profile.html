{% extends "settings/base_settings.html" %}
{% load static %}
{% load widget_tweaks %}
{% block settings_content %}
    <div class="py-2 px-12">
        <h2 class="text-xl font-semibold mb-4">Profile Information</h2>
        <form method="post"
              hx-post="{% url 'settings_profile' %}"
              hx-target="body"
              class="w-full">
            {% csrf_token %}
            {% if form.non_field_errors %}
                <div class="w-full text-red-500 text-sm space-y-1 mb-4">
                    {% for error in form.non_field_errors %}<div>{{ error }}</div>{% endfor %}
                </div>
            {% endif %}
            <!-- Name Section -->
            <div class="flex items-start space-x-2 pt-6">
                <!-- Label -->
                <label for="name" class="w-1/5 text-sm font-medium text-gray-800">
                    Name <span class="text-red-500">*</span>
                </label>
                <!-- Input container -->
                <div class="flex-col w-2/5 space-y-1">
                    <div class="flex space-x-2">
                        <!-- First Name Input -->
                        {% with error_class=form.first_name.errors|yesno:"border-red-500,border-gray-300" %}
                            {{ form.first_name|attr:"id:first_name"|attr:"placeholder:First Name"|add_class:"w-1/2 px-4 py-2 border rounded-md shadow-sm focus:outline-none outline-none"|add_class:error_class }}
                        {% endwith %}
                        <!-- Last Name Input -->
                        {% with error_class=form.last_name.errors|yesno:"border-red-500,border-gray-300" %}
                            {{ form.last_name|attr:"id:last_name"|attr:"placeholder:Last Name"|add_class:"w-1/2 px-4 py-2 border rounded-md shadow-sm focus:outline-none outline-none"|add_class:error_class }}
                        {% endwith %}
                    </div>
                    <!-- Display Field Errors Below Inputs -->
                    {% if form.first_name.errors or form.last_name.errors %}
                        <div class="text-red-500 text-xs space-y-1">
                            {% for error in form.first_name.errors %}<div>{{ error }}</div>{% endfor %}
                            {% for error in form.last_name.errors %}<div>{{ error }}</div>{% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            <!-- Email Section -->
            <div class="flex items-start space-x-2 pt-6">
                <!-- Label -->
                <label for="{{ form.email.id_for_label }}"
                       class="w-1/5 text-sm font-medium text-gray-800">{{ form.email.label|title }}</label>
                <!-- Input container -->
                <div class="w-2/5">
                    <!-- Email Input -->
                    {% render_field form.email id="email" placeholder="Email" class="w-full px-4 py-2 border border-gray-300 bg-gray-200 text-gray-500  rounded-md shadow-sm focus:outline-none " %}
                </div>
            </div>
            <!-- Phone Number Section -->
            <div class="flex items-start space-x-2 pt-6">
                <!-- Label -->
                <label for="{{ form.contact_number.id_for_label }}"
                       class="w-1/5 text-sm font-medium text-gray-800">
                    {{ form.contact_number.label|title }} <span class="text-red-500">*</span>
                </label>
                <!-- Input container -->
                <div class="w-2/5">
                    <!-- Phone Number Input -->
                    {% with error_class=form.contact_number.errors|yesno:"border-red-500,border-gray-300" %}
                        {{ form.contact_number|attr:"id:contact_number"|attr:"placeholder:(XXX)-XXX-XXXX"|attr:"inputmode:numeric"|attr:"title:Phone number must be in the format (XXX)-XXX-XXXX"|attr:"oninput:formatPhoneNumber(this) "|add_class:"w-full px-4 py-2 border rounded-md shadow-sm focus:outline-none"|add_class:error_class }}
                    {% endwith %}
                    {% if form.contact_number.errors %}
                        <div class="text-red-500 text-xs space-y-1 mt-1">
                            {% for error in form.contact_number.errors %}<div>{{ error }}</div>{% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            <!-- Organization Section -->
            <div class="flex items-start space-x-2 pt-6">
                <!-- Label -->
                <label for="{{ form.organization.id_for_label }}"
                       class="w-1/5 text-sm font-medium text-gray-800">{{ form.organization.label }}</label>
                <!-- Input container -->
                <div class="w-2/5">
                    <!-- Organization Input -->
                    {% render_field form.organization class="w-full px-4 py-2 border border-gray-300 bg-gray-200 text-gray-500  rounded-md shadow-sm focus:outline-none" %}
                </div>
            </div>
            <!-- Role Section -->
            <div class="flex items-start space-x-2 pt-6">
                <!-- Label -->
                <label for="{{ form.roles.id_for_label }}"
                       class="w-1/5 text-sm font-medium text-gray-800">{{ form.roles.label }}</label>
                <!-- Input container -->
                <div class="w-2/5">
                    <!-- Role Display -->
                    <div class="w-full px-4 py-2 border border-gray-300 bg-gray-200 text-gray-500 rounded-md shadow-sm focus:outline-none">
                        {% if form.instance and form.instance.pk %}
                            {% for role_obj in form.instance.roles.all %}
                                {{ role_obj }}
                                {% if not forloop.last %},{% endif %}
                            {% endfor %}
                        {% else %}
                            N/A
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="flex justify-center items-center mt-5">
                <div class="flex-col gap-4 flex w-1/4 pt-2">
                    <div class="self-stretch px-4 py-2.5 bg-[#924f34] rounded-lg shadow-inner justify-center items-center gap-1.5 inline-flex">
                        <div class="px-0.5 justify-center items-center flex">
                            <button type="submit"
                                    class="text-white text-base font-semibold font-inter leading-normal">
                                Save Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <script>
        const contactInput = document.getElementById('contact_number');
        if (contactInput) {
            formatPhoneNumber(contactInput);
        }
    </script>
{% endblock settings_content %}
