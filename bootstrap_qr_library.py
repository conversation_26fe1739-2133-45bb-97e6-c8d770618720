#!/usr/bin/env -S uv run
import io

import cv2
import numpy as np
import pyotp
import pyqrcode
import structlog
from qreader import QReader

logger = structlog.get_logger(__name__)

if __name__ == "__main__":
    qreader = QReader()
    pyotp.random_base32()

    qr_secret = "SSECCCRREEETTCCCCODDDEEE"  # noqa: S105 # nosec
    qr_user = "<EMAIL>"
    qr_issuer = "Bridge Invest"

    otp1 = pyotp.totp.TOTP(qr_secret)
    url_qr = otp1.provisioning_uri(qr_user, issuer_name=qr_issuer)

    url = pyqrcode.create(url_qr)
    file_in_memory = io.BytesIO()
    url.png(file_in_memory, scale=8)
    file_in_memory.seek(0)

    reader = QReader()
    np_image = np.asarray(bytearray(file_in_memory.read()), dtype="uint8")
    image = cv2.imdecode(np_image, cv2.COLOR_BGR2RGB)
    decoded_text = qreader.detect_and_decode(image=image)

    otp2 = pyotp.parse_uri(decoded_text[0])
    logger.info(decoded_text)
    logger.info(otp1.now() == otp2.now())
