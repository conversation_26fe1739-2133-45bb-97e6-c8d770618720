<div id="modal"
     class="fixed z-10 inset-0 overflow-y-auto hidden"
     aria-labelledby="pdf-modal-title"
     role="dialog"
     aria-modal="true"
     tabindex="-1"
     _="on closeModal add .closing then wait for animationend then remove me">
    <div class="flex items-center justify-center min-h-screen px-4 text-center">
        <div class="modal-underlay fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
             aria-hidden="true"
             _="on click trigger closeModal"></div>
        <div class="inline-block align-bottom text-left overflow-hidden sm:my-8 sm:align-middle">
            <div class="modal-content">
                {% comment %} Bit of a workaround for dynamic include statements  {% endcomment %}
                {% with template_name=modal_include_template %}
                    {% include ""|add:template_name %}
                {% endwith %}
            </div>
        </div>
    </div>
</div>
