import requests
import structlog
from django.conf import settings

logger = structlog.get_logger(__name__)


def send_message_to_slack(message: str, channel: str, blocks: list | None = None, thread_ts: str | None = None) -> dict:
    """Send message to slack. If blocks is provided, message will be the fallback text"""
    headers = {"Authorization": f"Bearer {settings.SLACK_OAUTH_KEY}", "Content-Type": "application/json"}

    payload = {"channel": channel, "text": message}

    if blocks:
        payload["blocks"] = blocks

    # Add thread_ts to payload if provided to reply to a thread
    if thread_ts:
        payload["thread_ts"] = thread_ts

    if settings.DEBUG:
        logger.info("slack message sent in debug mode", json=payload, headers=headers)
        payload["channel"] = "U08DWQ83160"  # Marcos dm with bridge bot
        # Stop this from running in debug mode, remove this line and use your slack id to debug
        return {}

    response = requests.post("https://slack.com/api/chat.postMessage", json=payload, headers=headers, timeout=10)
    response.raise_for_status()  # Raise exception for 4XX/5XX responses

    return response.json()


def send_slack_thread_messages(channel: str, messages: list[str]) -> None:
    initial_response = send_message_to_slack(message=messages[0], channel=channel)
    if initial_response.get("ok"):
        thread_ts = initial_response.get("ts")
        # Reply to the thread
        for message in messages[1:]:
            send_message_to_slack(message=message, channel=channel, thread_ts=thread_ts)
