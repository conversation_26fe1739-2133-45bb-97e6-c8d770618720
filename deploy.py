#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.12"
# dependencies = [
#       "boto3>=1.35.59",
#       "click>=8.1.7",
# ]
# ///
# ruff: noqa: S602
import os
import subprocess  # nosec
import sys
import time
from typing import NamedTuple

import boto3
import click

TEN_MINUTES = 600

# Initialize boto3 clients
ecr_client = boto3.client("ecr")
ecs_client = boto3.client("ecs")


class ECSService(NamedTuple):
    cluster_name: str
    service_short_name: str
    service_name: str
    docker_target: str
    docker_image: str
    docker_tag: str
    service_task: str | None
    container_name: str | None


def get_account_and_region() -> tuple[str, str]:
    account_id = boto3.client("sts").get_caller_identity().get("Account")
    region = boto3.session.Session().region_name
    return account_id, region


def get_environment() -> str:
    account_id, region = get_account_and_region()
    if region != "us-east-1":
        click.echo(f"Unsupported region: {region}. Only us-east-1 is supported.")
        raise ValueError
    if account_id == "************":
        return "prod"
    if account_id == "************":
        return "demo"
    click.echo(f"Unsupported account: {account_id}")
    raise ValueError


# Service short name to (docker target, docker image name)
def get_service_to_docker_mapping() -> dict[str, tuple[str, str]]:
    account_id, region = get_account_and_region()
    if account_id == "************" and region == "us-east-1":
        return {
            "SiteService": ("bridge-prod", "bridge-prod-python"),
            "CeleryService": ("bridge-prod", "bridge-prod-python"),
        }
    if account_id == "************" and region == "us-east-1":
        return {
            "SiteService": ("bridge-prod", "bridge-demo-python"),
            "CeleryService": ("bridge-prod", "bridge-demo-python"),
        }
    raise ValueError


def get_ecr_repository() -> tuple[str, str]:
    """
    Function to get ECR repository name
    """
    account_id, region = get_account_and_region()
    return f"{account_id}.dkr.ecr.{region}.amazonaws.com", region


def ecr_login(ecr_repository: str, region: str, cmd: str = "sudo nerdctl") -> None:
    """
    Function to get ECR login password and login to Docker
    """
    click.echo("Logging into ECR")
    subprocess.run(
        f"aws ecr get-login-password --region {region} | {cmd} login -u AWS --password-stdin {ecr_repository}",
        shell=True,
        check=True,
    )  # nosec
    click.echo("Logged into ECR")


def get_docker_images(tag: str | None = None) -> list[tuple[str, str, str]]:
    ecr_repository, region = get_ecr_repository()
    cluster_services = find_cluster_and_service_names(tag)
    docker_images = set()
    for service in cluster_services.values():
        full_docker_uri = f"{ecr_repository}/{service.docker_image}:{service.docker_tag}"
        docker_uri_no_tag = f"{ecr_repository}/{service.docker_image}"
        docker_images.add((service.docker_target, full_docker_uri, docker_uri_no_tag))
    return list(docker_images)


def build_docker_image(target: str, full_docker_uri: str, docker_uri_no_tag: str, *, push: bool = True) -> str:
    """
    Function to build and push Docker images
    """
    click.echo(f"Building and pushing Docker image {full_docker_uri}")
    cmd = (
        "sudo nerdctl build . "
        "-f ./Dockerfile "
        "--platform=linux/amd64 "
        f"--target={target} "
        f"--cache-to=mode=max,image-manifest=true,oci-mediatypes=true,type=registry,ref={docker_uri_no_tag}:buildcache "
        f"--cache-from={docker_uri_no_tag}:buildcache "
        f"-t {full_docker_uri}"
    )
    subprocess.run(cmd, shell=True, check=True)  # nosec
    if push:
        push_docker_image_soci(full_docker_uri)
    click.echo(f"Built Docker image {full_docker_uri}")
    return full_docker_uri


def push_docker_image_soci(full_docker_uri: str) -> None:
    click.echo(f"Pushing Docker image {full_docker_uri}")
    subprocess.run(f"sudo nerdctl push --snapshotter soci {full_docker_uri}", shell=True, check=True)  # nosec
    click.echo(f"Pushed Docker image {full_docker_uri}")


# Function to update ECS service
def update_ecs_service(cluster_name: str, service_name: str) -> None:
    click.echo(f"Updating ECS service {service_name} in cluster {cluster_name}")
    ecs_client.update_service(
        cluster=cluster_name,
        service=service_name,
        forceNewDeployment=True,
    )
    click.echo(f"Updated ECS service {service_name} in cluster {cluster_name}")


# Function to check the status of an ECS deployment
def check_deployment_status(cluster_name: str, service_name: str) -> str:
    response = ecs_client.describe_services(cluster=cluster_name, services=[service_name])
    deployments = response["services"][0]["deployments"]
    for deployment in deployments:
        if deployment["status"] == "PRIMARY":
            return deployment["rolloutState"]
    click.echo(f"Unknown status for {service_name} in cluster {cluster_name}")
    return "UNKNOWN"


def wait_for_completed_deployment(cluster_name: str, service_name: str) -> None:
    start = time.time()
    while True:
        status = check_deployment_status(cluster_name, service_name)
        click.echo(f"Deployment of {service_name} in cluster {cluster_name} status: {status}")
        if status == "COMPLETED":
            break
        if status == "FAILED":
            msg = f"Deployment of {service_name} in cluster {cluster_name} failed"
            click.echo(msg)
            sys.exit(1)
        time.sleep(5)
        if time.time() - start > TEN_MINUTES:
            msg = f"Deployment of {service_name} in cluster {cluster_name} took too long"
            click.echo(msg)
            sys.exit(1)
    click.echo(f"Deployment of {service_name} in cluster {cluster_name} completed in {time.time() - start} seconds")


def find_cluster_and_service_names(tag: str | None = None) -> dict[str, ECSService]:
    """
    Function to find ECS cluster and service names
    """
    click.echo("Finding cluster and service names")
    # This is a weird place to do this, but its where we build our task list
    if tag is None:
        tag = subprocess.run(
            "git rev-parse --short HEAD",  # nosec # noqa: S607
            capture_output=True,
            text=True,
            shell=True,
            check=True,
        ).stdout.strip("\n")
    clusters = ecs_client.list_clusters()["clusterArns"]
    service_metadata = {}
    for cluster in clusters:
        cluster_name = cluster.split("/")[-1]
        list_services = ecs_client.list_services(cluster=cluster_name)

        services = list_services["serviceArns"]
        service_names = [service.split("/")[-1] for service in services]
        for service_short_name, (docker_target, docker_image) in get_service_to_docker_mapping().items():
            for service_name in service_names:
                # HACK: This is a hack to match the service name with the short name
                if service_short_name in service_name:
                    service_tasks = ecs_client.list_tasks(cluster=cluster_name, serviceName=service_name)["taskArns"]
                    service_task_first = None
                    container_name = None
                    if service_tasks:
                        service_task_first = service_tasks[0]
                        container_name = ecs_client.describe_tasks(cluster=cluster_name, tasks=service_tasks)["tasks"][
                            0
                        ]["containers"][0]["name"]
                    service_metadata[service_short_name] = ECSService(
                        cluster_name,
                        service_short_name,
                        service_name,
                        docker_target,
                        docker_image,
                        tag,
                        service_task_first,
                        container_name,
                    )
    click.echo("Found cluster and service names")
    return service_metadata


def get_cluster_and_service_names_filtered(tag: str, service_target: list[str]) -> dict[str, ECSService] | None:
    """
    Function to find ECS cluster and service names filtered by tag
    """
    account, region = get_account_and_region()
    if os.environ.get("DEPLOY_ARGS") is not None:
        if len(service_target) > 0:
            click.echo(f"DEPLOY_ARGS envvar is set, do not pass service_target argument: {service_target}")
            return None
        deploy_args = os.environ["DEPLOY_ARGS"].split(" ")
        environment = get_environment()
        account, region = get_account_and_region()
        if deploy_args[0] != "/deploy":
            click.echo("DEPLOY_ARGS must start with '/deploy'. Exiting.")
            return None
        if deploy_args[1] != environment:
            click.echo(f"Environment {deploy_args[1]} does not match {environment}. Exiting.")
            return None
        service_target = deploy_args[2:]
    cluster_services_all = find_cluster_and_service_names(tag)
    if len(service_target) == 1 and service_target[0] == "ALL":
        cluster_services = cluster_services_all
    else:
        # Filter cluster_services to only include specified service_targets
        cluster_services = {k: v for k, v in cluster_services_all.items() if k in service_target}
        does_not_exist = [
            service_short_name
            for service_short_name in service_target
            if service_short_name not in cluster_services_all
        ]
        if does_not_exist:
            click.echo(f"Some specified services not found: {', '.join(does_not_exist)}")
            return None
        if not cluster_services:
            click.echo(f"No matching services found for: {', '.join(service_target)}")
            return None

    for service_short_name, service in cluster_services.items():
        click.echo(f"Service: {service_short_name}")
        for k, v in service._asdict().items():
            click.echo(f"  {k}: {v}")

    return cluster_services


def build_docker_images(
    cluster_services: dict[str, ECSService], tag: str | None = None, *, dry_run: bool = True
) -> None:
    """
    Function to build Docker images for all services
    """
    if not dry_run:
        ecr_login(*get_ecr_repository())
    cluster_service_docker_tags = {service: service.docker_target for service in cluster_services.values()}
    for docker_target, full_docker_uri, docker_uri_no_tag in get_docker_images(tag):
        # Only build images for selected services if service_target is specified
        if docker_target in cluster_service_docker_tags.values():
            services = [
                service.service_short_name
                for service, target in cluster_service_docker_tags.items()
                if target == docker_target
            ]
            click.echo(
                f"Building Docker image for {', '.join(services)}"
                f" with target {docker_target}"
                f" going to {full_docker_uri}"
            )
            if not dry_run:
                build_docker_image(docker_target, full_docker_uri, docker_uri_no_tag, push=True)
    click.echo(f"{', '.join(cluster_services.keys())} docker images built")


def bounce(cluster_services: dict[str, ECSService], *, wait: bool = True, dry_run: bool = True) -> None:
    for service_short_name, service in cluster_services.items():
        click.echo(f"Bouncing {service_short_name} in cluster {service.cluster_name}")
        if not dry_run:
            update_ecs_service(service.cluster_name, service.service_name)
    if wait:
        for service_short_name, service in cluster_services.items():
            click.echo(f"Waiting for deployment of {service_short_name} in cluster {service.cluster_name}")
            if not dry_run:
                wait_for_completed_deployment(service.cluster_name, service.service_name)
    click.echo(f"{', '.join(cluster_services.keys())} services bounced")


@click.group()
def cli() -> None:
    pass


@cli.command()
def ls() -> None:
    """List all ECS clusters and their services."""
    cluster_services = find_cluster_and_service_names()
    click.echo(cluster_services)
    for service_short_name, service in cluster_services.items():
        click.echo(f"Service: {service_short_name}")
        for k, v in service._asdict().items():
            click.echo(f"  {k}: {v}")


@cli.command()
@click.option("--tag", default=None, help="Docker image tag")
@click.option("--dry-run", is_flag=True, default=False, help="Push Docker images to ECR")
@click.argument("service_target", nargs=-1)
def build_push_bounce(tag: str | None = None, *, dry_run: bool, service_target: tuple[str, ...]) -> None:
    """Build and optionally push Docker images, then bounce/restart ECS services."""
    if dry_run:
        click.echo("Dry run mode enabled. No Docker images will be pushed or services bounced.")
    cluster_services = get_cluster_and_service_names_filtered(tag, service_target)
    if cluster_services is None:
        click.echo("No matching services found. Exiting.")
        sys.exit(1)

    build_docker_images(cluster_services, tag, dry_run=dry_run)
    bounce(cluster_services, dry_run=dry_run)


@cli.command()
@click.argument("service_name")
def interactive(service_name: str) -> None:
    """Interactive mode for deploying a service."""
    if service_name == "local":
        subprocess.run("docker compose exec site /bin/bash", shell=True, check=True)  # nosec # noqa: S607
    else:
        cluster_services = find_cluster_and_service_names()
        service = cluster_services[service_name]
        click.echo(
            f"Connecting to {service.service_name} running container {service.container_name}"
            f" in cluster {service.cluster_name} ..."
        )
        subprocess.run(
            f"aws ecs execute-command "
            f"--cluster {service.cluster_name} "
            f"--task {service.service_task} "
            f"--container {service.container_name} "
            f"--interactive "
            f"--command '/bin/sh'",
            shell=True,
            check=True,
        )  # nosec


if __name__ == "__main__":
    cli()
