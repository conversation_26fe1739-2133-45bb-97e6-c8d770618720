from django.http import HttpResponse
from django.urls import path

from webapp import views

urlpatterns = [
    # Top level renders
    path("", views.core.home, name="home"),
    path("account/password_validators", views.account.validate_password, name="password_validators"),
    path("link", views.line_items.dashboard, name="link_dashboard"),
    path(
        "insights",
        views.insights.insight_home,
        name="insight_home",
    ),
    path("line-items/search", views.line_items.search_line_items, name="line_item_search"),
    path(
        "insights/dynamic",
        views.insights.insight_dynamic,
        name="insight_dynamic",
    ),
    # Line Items
    path("line-items/aggregate", views.line_items.aggregate_cards, name="line_item_aggregate"),
    path("line-items/confirm_edit", views.line_items.confirm_edit, name="line_item_confirm_edit"),
    path("line-items/delete_line_item", views.line_items.delete_line_item, name="line_item_delete"),
    path(
        "line-items/line_item_filtered_options",
        views.line_items.line_item_filtered_options,
        name="line_item_filtered_options",
    ),
    path(
        "merged-portal-credential",
        views.line_items.manage_merged_portal_credential,
        name="merged_portal_credential_manage",
    ),
    path(
        "credentials/modal",
        views.credentials_flow.modal,
        name="credentials_modal",
    ),
    path(
        "credentials/manage",
        views.credentials_flow.manage,
        name="credentials_manage",
    ),
    path(
        "real_time_encryption",
        views.real_time_encryption.encrypt,
        name="real_time_encryption",
    ),
    path(
        "emails/add_sender_email_field",
        views.emails_flow.add_sender_email_field,
        name="add_sender_email_field",
    ),
    path(
        "emails/confirm_email_modal",
        views.emails_flow.confirm_email_modal,
        name="confirm_email_modal",
    ),
    path(
        "emails/integration_end",
        views.emails_flow.email_integration_end,
        name="email_integration_end",
    ),
    path(
        "emails/email_integration_failed",
        views.emails_flow.email_integration_failed,
        name="email_integration_failed",
    ),
    path(
        "emails/emails_msft_redirect_uri",
        views.emails_flow.msft_redirect_uri,
        name="emails_msft_redirect_uri",
    ),
    # This is required in production to be able to verify the domain ownership
    path(
        ".well-known/microsoft-identity-association.json",
        views.emails_flow.msft_identity_association,
        name="emails_msft_identity_association",
    ),
    path(
        "emails/add_line_item",
        views.emails_flow.add_line_item,
        name="emails_add_line_item",
    ),
    path(
        "emails/edit_line_item",
        views.emails_flow.edit_line_item,
        name="emails_edit_line_item",
    ),
    path(
        "emails/onboard_new_email_inbox",
        views.emails_flow.onboard_new_email_inbox,
        name="emails_onboard_new_email_inbox",
    ),
    path(
        "emails/setup_integration",
        views.emails_flow.setup_integration,
        name="emails_setup_integration",
    ),
    path(
        "emails/edit_setup",
        views.emails_flow.edit_setup,
        name="emails_edit_setup",
    ),
    path(
        "emails/bulk_upload_historic",
        views.emails_flow.bulk_upload_historic,
        name="bulk_upload_historic",
    ),
    path(
        "emails/bulk_forward",
        views.emails_flow.bulk_forward,
        name="bulk_forward",
    ),
    path("sms-code/", views.sms.forward_sms, name="forward_sms"),
    path(
        "multi_factor_authentication/modal",
        views.multi_factor_authentication.modal,
        name="multi_factor_authentication_modal",
    ),
    path(
        "multi_factor_authentication/end_screen",
        views.multi_factor_authentication.end_screen,
        name="multi_factor_authentication_end_screen",
    ),
    path(
        "multi_factor_authentication/enter_otp",
        views.multi_factor_authentication.enter_otp,
        name="multi_factor_authentication_enter_otp",
    ),
    path(
        "multi_factor_authentication/check_retrieval_status/",
        views.multi_factor_authentication.check_retrieval_status,
        name="check_retrieval_status",
    ),
    path(
        "multi_factor_authentication/establish_connection",
        views.multi_factor_authentication.establish_connection,
        name="multi_factor_authentication_establish_connection",
    ),
    path(
        "link/errors/modal",
        views.link_errors.modal,
        name="link_errors_modal",
    ),
    path(
        "retrieval/run",
        views.retrieval.run,
        name="run_retrieval",
    ),
    path(
        "retrieval/status",
        views.retrieval.status,
        name="retrieval_status",
    ),
    path("vault", views.vault.vault, name="vault"),
    path("vault/header", views.vault.vault_header, name="vault_header"),
    path("vault/mark-viewed/", views.vault.mark_documents_viewed, name="mark_documents_viewed"),
    path("vault/upload", views.vault.vault_upload, name="vault_upload"),
    path("vault/confirm", views.vault.vault_confirm, name="vault_confirm"),
    path("vault/filtered-options", views.vault.vault_filtered_options, name="vault_filtered_options"),
    path("vault/label-document", views.vault.label_document, name="label_document"),
    path("vault/delete-label-document", views.vault.delete_label_document, name="delete_label_document"),
    path("vault/get_upload_signed_url", views.vault.get_upload_signed_url, name="get_upload_signed_url"),
    path(
        "vault/get_valid_sub_document_types",
        views.vault.get_valid_sub_document_types,
        name="get_valid_sub_document_types",
    ),
    path(
        "pdf_modal",
        views.client_dashboard.pdf_modal,
        name="pdf_modal",
    ),
    path(
        "multi-pdf-download-zip",
        views.zip_job_api.multi_pdf_download_zip,
        name="multi_pdf_download_zip",
    ),
    path(
        "zip-status-checker",
        views.zip_job_api.zip_status,
        name="zip_status_checker",
    ),
    path("accept-invitation/<str:token>", views.user_registration.registration, name="accept_invitation"),
    # Settings App
    path("settings", views.settings_views.profile_view, name="settings"),
    path("settings/profile", views.settings_views.profile_view, name="settings_profile"),
    path("settings/password", views.settings_views.password_view, name="settings_password"),
    path("settings/entities", views.settings_views.entities_view, name="settings_entities"),
    path("settings/entity/<uuid:entity_id>", views.settings_views.entity_view, name="settings_entity"),
    path("settings/organization", views.settings_views.organization_view, name="settings_organization"),
    path(
        "settings/organization/refresh-investments/",
        views.settings_views.refresh_investment_dropdown,
        name="refresh_investment_dropdown",
    ),
    path(
        "settings/organization/delete-permission-batch/",
        views.settings_views.delete_permission_batch,
        name="delete_permission_batch",
    ),
    path("settings/user-invite", views.settings_views.user_invite_modal, name="user_invite_modal"),
    path("slack_event", views.slack.handle_slack_event, name="slack_event"),
    path("dashboard", views.client_dashboard.dashboard, name="dashboard"),
    path(
        "dashboard/notifications/card",
        views.client_dashboard.notifications_card,
        name="dashboard_new_notifications_card",
    ),
    path("dashboard/export-modal", views.client_dashboard.export_modal, name="dashboard_export_modal"),
    path("dashboard/table", views.client_dashboard.table, name="client_dashboard_table"),
    path("dashboard/add_note", views.client_dashboard.add_note, name="add_note"),
    path("agree-to-terms-and-service", views.core.tos_agreement, name="tos_agreement_page"),
    path("save-bulk-investments/", views.client_dashboard.save_bulk_investments, name="save_bulk_investments"),
    path(
        "update-capital-call-status/",
        views.client_dashboard.update_capital_call_status,
        name="update_capital_call_status",
    ),
    path("vault/edit/", views.vault.edit_processed_documents, name="vault_edit_processed_documents"),
    path(
        "add-portal",
        views.portal_flow.add_portal,
        name="add_portal",
    ),
    path(
        "a9b281fa4376886fc7eae94310ec332c.html",
        lambda _: HttpResponse("twilio-domain-verification=a9b281fa4376886fc7eae94310ec332c"),
        name="twilio_domain_verification",
    ),
]
