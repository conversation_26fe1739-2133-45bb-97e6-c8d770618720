{% load static %}
<div id="{{ input_name }}_drop_down_results"
     role="menu"
     class="flex flex-col gap-1 group ">
    <div id="{{ input_name }}_drop_down_results_indicator"
         class="w-full px-4 py-2 rounded-sm text-sm text-[#4b554d] opacity-50 [&:not(.htmx-request)]:hidden">
        Searching...
    </div>
    <div id="{{ input_name }}_drop_down_results_menu"
         class="[.menu-indicator&.htmx-request]:hidden menu-indicator">
        {% if drop_down_options %}
            {% for drop_down_option in drop_down_options %}
                <div class="w-full px-4 py-2 hover:bg-[#f7f7f5] rounded-sm text-sm text-[#4b554d] cursor-pointer"
                     _="on click send setInput(drop_down_option:'{{ drop_down_option.legal_name }}') to #{{ input_name }} then trigger hideMenu(input_name:'{{ input_name }}') end on mouseenter add .hover end on mouseleave remove .hover end ">
                    {{ drop_down_option.legal_name }}
                </div>
            {% endfor %}
        {% else %}
            <div class="w-full px-4 py-2 rounded-sm text-sm text-[#4b554d] opacity-50">
                No exisiting {{ input_display_name }} found ...
            </div>
        {% endif %}
    </div>
</div>
