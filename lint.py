#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.12"
# dependencies = [
#       "click>=8.1.7",
#       "ruff>=0.8.3",
#       "bandit>=1.8.0",
#       "djlint>=1.36.3",
# ]
# ///
import os
import pty
import shutil
import subprocess  # nosec
import sys
from pathlib import Path

import click

LOG_FILE = Path("lint.log")


def run_command(command: str, description: str, *, suppress_all: bool = False) -> bool:
    prep = " Running: "
    c = shutil.get_terminal_size((80, 20)).columns
    m = len(prep) + 1 + len(description)
    n = (c - m) // 2
    click.secho(f"{'-' * n}{prep}{description} {'-' * (n)}", fg="blue")
    # Create a pseudo-terminal pair
    master_fd, slave_fd = pty.openpty()
    # Start the subprocess with the slave end of the pty
    process = subprocess.Popen(  # noqa: S602 # nosec
        command,
        shell=True,
        stdin=slave_fd,
        stdout=slave_fd,
        stderr=slave_fd,
        close_fds=True,
    )
    # Close the slave_fd in the parent process
    os.close(slave_fd)

    output_bytes = b""

    # Read the output from the master_fd
    try:
        while True:
            data = os.read(master_fd, 1024)
            if not data:
                break
            output_bytes += data
    except OSError:
        pass
    finally:
        os.close(master_fd)

    # Wait for the process to finish and get the exit code
    exit_code = process.wait()

    if exit_code != 0:
        # Command failed, output the captured output
        if not suppress_all:
            to_write = output_bytes.decode(errors="replace")
            with LOG_FILE.open("a") as f:
                f.write(to_write)
                f.write("\n\n\n")
            sys.stdout.write(to_write)
            sys.stdout.flush()
        click.secho(f"Command failed: {description}", fg="red")
        return False
    return True
    # Do not raise exceptions; continue even if the command failed


@click.command()
@click.option(
    "--fix",
    is_flag=True,
    default=False,
    help="Automatically fix linting issues where possible.",
)
@click.option(
    "--no-check",
    is_flag=True,
    default=True,
    help="Only check for linting issues without making changes.",
)
@click.option(
    "--no-python",
    is_flag=True,
    default=True,
    help="Only check for linting issues without making changes.",
)
@click.option(
    "--no-js",
    is_flag=True,
    default=True,
    help="Only check for linting issues without making changes.",
)
@click.option(
    "--python-types",
    is_flag=True,
    default=False,
    help="Check for python types",
)
def lint(*, fix: bool, no_check: bool, no_python: bool, no_js: bool, python_types: bool) -> None:  # noqa: C901
    """
    Run linting commands for Python, Django, and TypeScript projects.
    """
    # Python linting
    if LOG_FILE.exists():
        LOG_FILE.unlink()
    succeeded = True
    js_dirs = ["infrastructure", "site/webapp/static_src/js"]
    if fix:
        click.secho("Fixing linting issues...", fg="blue")
        if no_python:
            succeeded &= run_command("uv run ruff check --fix .", "Python: Ruff with fix", suppress_all=True)
            succeeded &= run_command("uv run ruff format *.py site", "Python: Ruff with format fix", suppress_all=True)
            succeeded &= run_command("uv run djlint . --reformat", "Django: djlint reformat", suppress_all=True)
        if no_js:
            for d in js_dirs:
                succeeded &= run_command(
                    f"npx prettier {d} --write",
                    f"TypeScript: Prettier write for {d}",
                    suppress_all=True,
                )

    if no_check:
        click.secho("Checking for issues...", fg="blue")
        if no_python:
            succeeded &= run_command("uv run ruff check .", "Python: Ruff Lint")
            succeeded &= run_command("uv run ruff format --check *.py site", "Python: Ruff Format")
            succeeded &= run_command("uv run bandit -c pyproject.toml -r .", "Python: Bandit")

            succeeded &= run_command("uv run djlint . --check", "Django: djlint check")
            succeeded &= run_command("uv run djlint . --lint", "Django: djlint lint")

        if python_types:
            succeeded &= run_command("uv run pyright .", "Python: pyright")
            succeeded &= run_command("ENVIRONMENT=test uv run mypy .", "Python: mypy")

        if no_js:
            for d in js_dirs:
                succeeded &= run_command(
                    f"npx prettier {d} --check",
                    f"TypeScript: Prettier check for {d}",
                )

    if succeeded:
        click.secho("Linting completed successfully! :)", fg="green")
        sys.exit(0)
    else:
        click.secho("Linting completed unsuccessfully :(", fg="red")
        sys.exit(1)


if __name__ == "__main__":
    lint()
