import json
import uuid
from typing import Any

import boto3
import pymupdf  # type: ignore[import-untyped]
import structlog
from asgiref.sync import sync_to_async
from django.db import transaction
from django.db.models import <PERSON>oleanField, Case, Prefetch, Subquery, When
from langchain.chat_models import init_chat_model
from langchain.tools import BaseTool
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
    ToolMessage,
)
from langchain_core.tools import StructuredTool, tool
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph
from pydantic import BaseModel
from webapp.models import DocumentType, LineItem, MergedPortalCredential, ProcessedDocument, RawDocument

from ml_app.core.data_classes.doc_vault import (
    ConfidenceBracketsEnum,
    CountingLineItemModels,
    DocumentTypeEnum,
    DocVaultInput,
    DocVaultInputKnownLineItems,
    DocVaultOutput,
    DocVaultTaskIO,
    DocVaultTaskWithKnownLineItemsIO,
    LineItemClassification,
    LineItemModel,
    LineItemSummary,
)
from ml_app.core.data_classes.state import BootstrapState, State

logger = structlog.get_logger(__name__)


###################### UTILS ######################

ERROR_PREPEND = "Error Guidance:"
ERROR_PROMPT = f"""{ERROR_PREPEND}
The last tool call raised an exception.
Try calling the tool again with corrected arguments. Do not repeat mistakes.
"""


class LLMToolCallError(Exception):
    def __init__(self, message: str, human_message: str | None = None) -> None:
        super().__init__(message)
        if human_message is None:
            self.human_message = ERROR_PROMPT
        else:
            self.human_message = f"{ERROR_PREPEND}{human_message}"


def get_first_page(raw_doc: RawDocument) -> str:
    s3 = boto3.client("s3")
    obj = s3.get_object(Bucket=raw_doc.s3_bucket, Key=raw_doc.s3_key)
    file_bytes: bytes = obj["Body"].read()
    try:
        doc = pymupdf.open(stream=file_bytes, filetype="pdf")
        for p in doc.pages():
            return p.get_text("text")
    except Exception:
        logger.exception("Error reading PDF file")
    return ""


async def make_message(
    tool: StructuredTool,
    inp: dict[str, Any],
    ai_content: str = "",
    inp_kwargs: dict[str, Any] | None = None,
) -> list[BaseMessage]:
    messages = []
    tool_call_id = str(uuid.uuid4())
    messages.append(
        AIMessage(
            content=ai_content,
            tool_calls=[
                {
                    "id": tool_call_id,
                    "args": inp,
                    "name": tool.name,
                }
            ],
        )
    )
    if inp_kwargs is None:
        inp_kwargs = {}
    new_inp = {
        **inp,
    }
    new_inp.update(inp_kwargs)
    res, _ = await tool.ainvoke(new_inp)
    try:
        outp = res.model_dump_json() if isinstance(res, BaseModel) else json.dumps(res)
    except (json.JSONDecodeError, TypeError):
        outp = str(res)
    messages.append(ToolMessage(outp, tool_call_id=tool_call_id))
    return messages


def format_line_item(li: LineItem) -> LineItemModel:
    return LineItemModel(
        investment_name=li.investment.legal_name,
        client_investor_name=li.investing_entity.client.legal_name,
        investing_entity_name=li.investing_entity.legal_name,
        line_item_confidence=ConfidenceBracketsEnum.MEDIUM,
    )


@sync_to_async
def aformat_raw_doc(raw_document_id: str, state: dict | None = None) -> DocVaultTaskIO:
    return load_orm_data(raw_document_id, k_nearest=0, state=state)[0]


def format_raw_doc(raw_doc: RawDocument) -> DocVaultTaskIO:
    if hasattr(raw_doc, "examples"):
        gt_pds = raw_doc.examples
        gt_pd = gt_pds[0] if gt_pds else None
    else:
        gt_pds = []
        gt_pd = None
    output = None
    if gt_pd is not None:
        output = DocVaultOutput(
            pk=str(raw_doc.pk),
            effective_date=gt_pd.effective_date,
            document_type=DocumentType(gt_pd.document_type).label,
            line_items=[format_line_item(pd.line_item) for pd in gt_pds],
            effective_date_confidence=ConfidenceBracketsEnum.MEDIUM,
            document_type_confidence=ConfidenceBracketsEnum.MEDIUM,
            line_items_confidence=ConfidenceBracketsEnum.MEDIUM,
        )
    return DocVaultTaskIO(
        pk=str(raw_doc.pk),
        input=DocVaultInput(
            pk=str(raw_doc.pk),
            scraped_posted_date=raw_doc.original_posted_date,
            scraped_document_type=DocumentType(raw_doc.original_document_type).label,
            metadata=raw_doc.metadata if isinstance(raw_doc.metadata, str) else json.dumps(raw_doc.metadata),
            first_page=get_first_page(raw_doc),
        ),
        output=output,
    )


def format_raw_doc_w_line_items(
    raw_doc: RawDocument,
    line_items: LineItemClassification | None = None,
    *,
    use_known_line_items: bool = False,
) -> DocVaultTaskIO:
    if line_items is None and not use_known_line_items:
        raise ValueError
    gt_pds = raw_doc.examples
    gt_pd = gt_pds[0] if gt_pds else None
    output = None
    if gt_pd is not None:
        output = DocVaultOutput(
            pk=str(raw_doc.pk),
            effective_date=gt_pd.effective_date,
            document_type=DocumentType(gt_pd.document_type).label,
            line_items=[format_line_item(pd.line_item) for pd in gt_pds],
            effective_date_confidence=ConfidenceBracketsEnum.MEDIUM,
            document_type_confidence=ConfidenceBracketsEnum.MEDIUM,
            line_items_confidence=ConfidenceBracketsEnum.MEDIUM,
        )
    if use_known_line_items:
        local_line_items = [format_line_item(li) for li in raw_doc.retrieval.merged_portal_credential.line_items.all()]
    elif line_items is not None:
        local_line_items = line_items["accurate_line_items"]
    return DocVaultTaskWithKnownLineItemsIO(
        pk=str(raw_doc.pk),
        input=DocVaultInputKnownLineItems(
            pk=str(raw_doc.pk),
            scraped_posted_date=raw_doc.original_posted_date,
            scraped_document_type=DocumentType(raw_doc.original_document_type).label,
            metadata=raw_doc.metadata if isinstance(raw_doc.metadata, str) else json.dumps(raw_doc.metadata),
            possible_line_items=local_line_items,
            first_page=get_first_page(raw_doc),
        ),
        output=output,
    )


@sync_to_async
def aload_orm_data(
    raw_document_id: str,
    k_nearest: int = 5,
    *,
    state: dict | None = None,
) -> tuple[DocVaultTaskIO | DocVaultTaskWithKnownLineItemsIO, list[DocVaultTaskIO | DocVaultTaskWithKnownLineItemsIO]]:
    return load_orm_data(
        raw_document_id,
        k_nearest=k_nearest,
        state=state,
    )


def load_orm_data(
    raw_document_id: str,
    k_nearest: int = 5,
    *,
    state: dict | None = None,
) -> tuple[DocVaultTaskIO | DocVaultTaskWithKnownLineItemsIO, list[DocVaultTaskIO | DocVaultTaskWithKnownLineItemsIO]]:
    if state is None:
        state = {}
    use_known_line_items = False
    if "use_known_line_items" in state:
        use_known_line_items = state["use_known_line_items"]
    line_items = None
    if "line_items" in state:
        line_items = state["line_items"]
    logger.info(
        "Loading raw document",
        raw_document_id=raw_document_id,
        k_nearest=k_nearest,
        use_known_line_items=use_known_line_items,
        line_items=line_items,
    )
    raw_doc = RawDocument.objects.get(pk=raw_document_id)
    if raw_doc.retrieval is None:
        return RawDocument.objects.none()
    raw_docs = (
        RawDocument.objects.filter(
            retrieval__merged_portal_credential__pk=raw_doc.retrieval.merged_portal_credential.pk,
            original_posted_date__lte=raw_doc.original_posted_date,
        )
        .annotate(
            is_doc_type_same=Case(
                When(original_document_type=raw_doc.original_document_type, then=True),
                default=False,
                output_field=BooleanField(),
            ),
            is_original_doc=Case(
                When(pk=raw_doc.pk, then=True),
                default=False,
                output_field=BooleanField(),
            ),
        )
        .prefetch_related("retrieval__merged_portal_credential__line_items")
        .order_by("-is_original_doc", "-is_doc_type_same", "-original_posted_date")[: k_nearest + 1]
    )

    latest_doc_subquery = ProcessedDocument.objects.filter(
        is_visible=True,
        raw_retreival_document__in=raw_docs,
    ).values("id")

    raw_docs = raw_docs.prefetch_related(
        Prefetch(
            "processed_documents",
            queryset=ProcessedDocument.objects.filter(id__in=Subquery(latest_doc_subquery)),
            to_attr="examples",
        ),
    )
    if line_items is not None or use_known_line_items:
        return format_raw_doc_w_line_items(raw_docs[0], line_items, use_known_line_items=use_known_line_items), [
            format_raw_doc_w_line_items(other_raw_doc, line_items, use_known_line_items=use_known_line_items)
            for other_raw_doc in raw_docs[1:]
        ]
    return format_raw_doc(raw_docs[0]), [format_raw_doc(other_raw_doc) for other_raw_doc in raw_docs[1:]]


PROCESS_DOCUMENT_VERSION = 1
PROCESS_DOCUMENT_MODEL_NAME = "20250505-claude-3-5-haiku-fast-doc-vault-v2"


@sync_to_async
def ainsert_doc_vault_util(parsed_document: DocVaultOutput) -> list[str]:
    """Returns successfully if data was correctly inserted into the database."""
    raw_document = RawDocument.objects.get(pk=parsed_document.pk)
    if raw_document is None:
        msg = f"Raw document not found: {parsed_document.pk}"
        raise ValueError(msg)

    viable_line_items = [
        (
            LineItem.objects.get(
                investment__legal_name=li.investment_name,
                investing_entity__client__legal_name=li.client_investor_name,
                investing_entity__legal_name=li.investing_entity_name,
                deleted_at__isnull=True,
                merged_portal_credential__pk=raw_document.retrieval.merged_portal_credential.pk,
            ),
            li,
        )
        for li in parsed_document.line_items
    ]
    for li_db, li_llm in viable_line_items:
        if li_db is None:
            msg = f"Line item not found: {li_llm}"
            raise ValueError(msg)

    is_visible = False
    if raw_document.retrieval is not None:
        merged_portal_credential = raw_document.retrieval.merged_portal_credential
        if merged_portal_credential is not None and merged_portal_credential.is_backfilled:
            is_visible = True
    processed_docs = ProcessedDocument.create(
        raw_document=raw_document,
        line_items=[li_db for li_db, li_llm in viable_line_items],
        document_type=parsed_document.document_type.name,
        sub_document_type=parsed_document.sub_document_type.name if parsed_document.sub_document_type else "",
        posted_date=raw_document.posted_date,
        effective_date=parsed_document.effective_date,
        process_document_version=PROCESS_DOCUMENT_VERSION,
        process_document_source=PROCESS_DOCUMENT_MODEL_NAME,
        labeled_by=None,
        is_visible=is_visible,
        is_ground_truth=False,
    )
    logger.info(
        "Created processed document",
        raw_document_id=raw_document.pk,
        processed_docs_length=len(processed_docs),
        processed_documents=[str(pd.pk) for pd in processed_docs if pd is not None],
        line_items=[str(li) for li in viable_line_items],
        document_type=parsed_document.document_type,
    )
    return [str(pd.pk) for pd in processed_docs]


@sync_to_async
def ainsert_line_items_util(line_items: list[LineItemModel], mpc_pk: str) -> None:
    """Inserts line items into the database."""
    try:
        with transaction.atomic():
            for li in line_items:
                exists = LineItem.objects.filter(
                    merged_portal_credential__pk=mpc_pk,
                    investment__legal_name=li.investment_name,
                    investing_entity__client__legal_name=li.client_investor_name,
                    investing_entity__legal_name=li.investing_entity_name,
                )
                if exists.count() > 0:
                    logger.info("Line item already exists", line_item=li)
                    continue
                LineItem.create_from_merged_portal_credential(
                    merged_portal_credential=MergedPortalCredential.objects.get(pk=mpc_pk),
                    client_legal_name=li.client_investor_name,
                    entity_legal_name=li.investing_entity_name,
                    investment_fund_legal_name=li.investment_name,
                    investment_managing_firm_name="Unknown",
                )
    except Exception as e:
        logger.exception("Error inserting line item", error=str(e), line_item=li)
        raise
    logger.info("Inserted line items", line_items=[str(li) for li in line_items])


###################### TOOLS ######################


# We never have the LLM call this, but we register it so the LLM knows about the structure of the output.
@tool
async def get_document_input(
    raw_document_id: str,
    default: DocVaultInput | None = None,
    state: State | None = None,
) -> tuple[DocVaultInput, dict]:
    """Get all document metadata"""
    if default is None:
        default = (await aformat_raw_doc(raw_document_id, state=state)).input
    return default, {}


@tool
async def aggregate_line_items(
    # TODO: use real types and rationalize/fix all the state types.
    state: dict | None = None,
) -> tuple[LineItemSummary, dict]:
    """
    Aggregates the line items to report on how many line items are in the document.
    After calling this tool, immediately call `insert_and_filter_line_items` to check if the line items are correct.
    """
    results = {}
    for o in state["id2obj"].values():
        if o["document_type"] not in results:
            results[o["document_type"]] = {}
        for li in o["line_items"]:
            li_json = li.model_dump() if isinstance(li, BaseModel) else li
            li_json["line_item_confidence"] = ConfidenceBracketsEnum.MEDIUM.value
            li_json = json.dumps(li_json)
            if li_json not in results[o["document_type"]]:
                results[o["document_type"]][li_json] = 0
            results[o["document_type"]][li_json] += 1
    return LineItemSummary(
        document_type_to_line_items_count={
            (doc_type.value if isinstance(doc_type, DocumentTypeEnum) else doc_type): [
                CountingLineItemModels(line_item=LineItemModel.model_validate(json.loads(li_json)), count=count)
                for li_json, count in line_items.items()
            ]
            for doc_type, line_items in results.items()
        }
    ), {}


@tool
async def insert_and_filter_line_items(  # noqa: C901
    line_items_classification: LineItemClassification,
    *,
    persist: bool = True,
    state: dict | None = None,
) -> tuple[bool, dict]:
    """
    Call this tool to report which line items are accurate and which are wrong.
    Every time you call this tool, it will check if the line items are consistent with the original counter.
    All line items reported in aggregate_line_items must be present in either accurate or wrong.
    Think carefully about which line items are accurate and which are wrong.
    You must use the exact line item returned in aggregate_line_items.
    Do not call this with no arguments.
    """
    line_items = set()
    for o in state["id2obj"].values():
        for li in o["line_items"]:
            li_tup = (li["investment_name"], li["investing_entity_name"], li["client_investor_name"])
            if li_tup not in line_items:
                line_items.add(li_tup)
    accurate = {
        (li.investment_name, li.investing_entity_name, li.client_investor_name)
        for li in line_items_classification.accurate_line_items
    }
    wrong = {
        (li.investment_name, li.investing_entity_name, li.client_investor_name)
        for li in line_items_classification.wrong_line_items
    }
    same = accurate.intersection(wrong)
    msg = ""
    target_li = []
    if same:
        msg += f"\nLine items are the same in both accurate and wrong, must be in one or the other: {same}"
        target_li = list(same)
    all_li = accurate.union(wrong)
    for li in accurate:
        if li not in line_items:
            msg += f"\nLine item not found in original counter: {li}"
            target_li.append(li)
    for li in line_items:
        if li not in all_li:
            if has_too_many_errors(state["messages"], max_retries=1):
                line_items_classification.wrong_line_items.append(
                    LineItemModel(
                        investment_name=li[0],
                        investing_entity_name=li[1],
                        client_investor_name=li[2],
                        line_item_confidence=ConfidenceBracketsEnum.NO,
                    )
                )
            else:
                msg += f"\nLine item not found in accurate or wrong, but in original counter: {li}"
                target_li.append(li)
    if msg:
        raise LLMToolCallError(
            msg,
            human_message="There was an exception, recall the insert_and_filter_line_items "
            f"appending to it the following missing line items: {target_li}",
        )
    if persist and state.get("do_database_insertion_line_item", False):
        await ainsert_line_items_util(
            line_items_classification.accurate_line_items, mpc_pk=state["merged_portal_credential_pk"]
        )
    state["line_items"] = line_items_classification
    return True, {**state}


@tool
async def insert_doc_vault(  # noqa: C901, PLR0912
    parsed_document: DocVaultOutput, *, persist: bool = True, state: State | None = None
) -> tuple[dict[str, list[str]], dict]:
    """Returns successfully if data was correctly inserted into the database."""
    logger.info("Inserting", parsed_document=parsed_document)
    tool_call_content = None
    new_state = {}
    if state:
        id2messages = state["id2messages"]
        id2obj = state["id2obj"]
        id2obj[parsed_document.pk] = parsed_document
        if parsed_document.pk not in id2messages:
            id2messages[parsed_document.pk] = []
        start_index = 0
        for idx, m in enumerate(state["messages"]):
            if isinstance(m, ToolMessage):
                try:
                    tool_call_content_cur = json.loads(m.content)
                    if "pk" in tool_call_content_cur and tool_call_content_cur["pk"] == parsed_document.pk:
                        tool_call_content = tool_call_content_cur
                except json.JSONDecodeError:
                    continue
            if isinstance(m, AIMessage) and m.tool_calls:
                for tool_call in m.tool_calls:
                    if (
                        tool_call["name"] == "get_document_input"
                        and tool_call["args"]["raw_document_id"] == parsed_document.pk
                    ):
                        start_index = idx
                        break

        id2messages[parsed_document.pk] = state["messages"][start_index:]
        new_state = {
            "id2messages": id2messages,
            "id2obj": id2obj,
        }

    if tool_call_content is None:
        logger.info("messages", messages=state["messages"] if state else [], state=state)
        msg = f"Tool call not found for {parsed_document.pk}, must call `get_document_input` first."
        raise LLMToolCallError(msg)
    bad_lis = []

    if "possible_line_items" in tool_call_content:
        typed_line_items = [LineItemModel(**li) for li in tool_call_content["possible_line_items"]]
        tup_line_items = [
            (li.investment_name, li.investing_entity_name, li.client_investor_name) for li in typed_line_items
        ]
        parsed_doc_tup = [
            (li.investment_name, li.investing_entity_name, li.client_investor_name) for li in parsed_document.line_items
        ]
        bad_lis = [li for li in parsed_doc_tup if li not in tup_line_items]

    if bad_lis:
        msg = f"Invalid values for Line Items: {bad_lis}, must be one of {tool_call_content['possible_line_items']}"
        raise LLMToolCallError(msg)

    logger.debug("Tool call found", parsed_document=parsed_document.pk, tool_call_content=tool_call_content)
    if not state["use_ground_truth"]:
        for li in parsed_document.line_items:
            if li.investing_entity_name == li.investment_name:
                msg = (
                    f"Line item is not a valid investment: {li},"
                    " investing_entity_name and investment_name are the same."
                    " This is a sign of overconfidence in the model. This can never happen in real life."
                )
                raise LLMToolCallError(msg)

    if not persist or not state["do_database_insertion_processed_doc"]:
        return {"pks": [str(uuid.uuid4()) for _ in parsed_document.line_items]}, new_state

    # TODO: Sanity prechecks with good errors.
    # pass the state in and use "ruberic" to check the values
    pks = await ainsert_doc_vault_util(parsed_document)
    return {"pks": pks}, new_state


###################### NODES ######################


async def ground_truth_k_shot_examples(state: State | BootstrapState) -> State | BootstrapState:
    """Get k examples"""
    raw_doc_pk = state["raw_doc_pk"]
    use_ground_truth = state["use_ground_truth"]
    # https://python.langchain.com/v0.1/docs/modules/model_io/chat/function_calling/
    state["messages"] = [
        SystemMessage(
            content="""You are a helpful assistant that helps to extract information from private equity documents.
            Use the output of the get_document_input tool to call insert_doc_vault
            Think very very carefully about the values you are extracting
                and make sure they are valid against the output
            from get_document_input and the examples you have seen.
               Explain your reasoning before calling the tool.
            If you are not sure about the value of an attribute asked to extract, return the value with low confidence.
            If you are not sure about the category of the document, return the value with low confidence.
            If you are not sure about the effective date, return the value with low confidence.
            If you are not sure about the line items, return the value with low confidence.
            Make your best effort to extract the information, but if you are not sure,
                return the value with low confidence.
            For each previous invocation of insert_doc_vault, inspect it and
                see if it holds consistent with your current understanding of this new document.
            Call the tool multiple times if needed to update old documents with the appropriate extracted values
                if somethings looks off.
            Make sure to call it at least once for each document.
            """
        )
    ]

    logger.info("ground truth k shot", state_keys=state.keys())
    raw_doc, other_docs = await aload_orm_data(
        raw_doc_pk,
        state=state,
    )

    for other_raw_doc in other_docs:
        if state["use_past_predictions"] and str(other_raw_doc.pk) in state["id2messages"]:
            state["messages"].extend(state["id2messages"][str(other_raw_doc.pk)])
            continue
        if not use_ground_truth:
            continue
        inp = {"raw_document_id": str(other_raw_doc.pk)}
        if other_raw_doc.output is None:
            continue
        state["messages"].extend(
            await make_message(get_document_input, inp, inp_kwargs={"default": other_raw_doc.input})
        )
        state["messages"].extend(
            await make_message(
                insert_doc_vault,
                {"parsed_document": json.loads(other_raw_doc.output.model_dump_json())},
                inp_kwargs={"persist": False, "state": state},
            )
        )

    inp = {"raw_document_id": raw_doc_pk}
    state["messages"].extend(await make_message(get_document_input, inp, inp_kwargs={"default": raw_doc.input}))
    return {
        **state,
    }


class LLMNode:
    def __init__(self, llm: BaseChatModel) -> None:
        self.llm = llm

    async def __call__(self, state: State) -> State:
        return {"messages": [await self.llm.ainvoke(state["messages"])]}


def has_too_many_errors(messages: list[BaseMessage], max_retries: int = 3) -> bool:
    cur_tool = None
    cur_doc_pk = None
    err_dict = {}
    for m in messages:
        if isinstance(m, SystemMessage):
            continue
        if isinstance(m, AIMessage):
            err_tool_calls = m.tool_calls[0]
            if "name" in err_tool_calls:
                cur_tool = err_tool_calls["name"]
            if (
                "args" in err_tool_calls
                and "parsed_document" in err_tool_calls["args"]
                and "pk" in err_tool_calls["args"]["parsed_document"]
            ):
                cur_doc_pk = err_tool_calls["args"]["parsed_document"]["pk"]
        if isinstance(m, HumanMessage) and m.content and m.content.startswith(ERROR_PREPEND):
            if (cur_tool, cur_doc_pk) not in err_dict:
                err_dict[(cur_tool, cur_doc_pk)] = 0
            err_dict[(cur_tool, cur_doc_pk)] += 1
            cur_tool = None
            cur_doc_pk = None
    too_many = False
    for ct in err_dict.values():
        if ct >= max_retries:
            too_many = True
    return too_many


class BasicToolNode:
    """A node that runs the tools requested in the last AIMessage."""

    def __init__(self, tools_by_name: dict, retries: int = 3) -> None:
        self.tools_by_name = tools_by_name
        self.retries = retries

    async def __call__(self, state: State | BootstrapState) -> State | BootstrapState:
        if messages := state.get("messages", []):
            message = messages[-1]
        else:
            msg = "No message found in input"
            raise ValueError(msg)
        outputs = []
        for tool_call in message.tool_calls:
            new_state = {}
            try:
                tool_args = {
                    **tool_call["args"],
                    "state": state,
                }
                logger.info("Calling tool", tool_call=tool_call)
                tool_result, new_state = await self.tools_by_name[tool_call["name"]].ainvoke(tool_args)
                try:
                    tool_outp = (
                        tool_result.model_dump_json() if isinstance(tool_result, BaseModel) else json.dumps(tool_result)
                    )
                except (json.JSONDecodeError, TypeError):
                    tool_outp = str(tool_result)
                msg = ToolMessage(
                    content=tool_outp,
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                    status="success",
                )
                if (
                    "id2messages" in new_state
                    and "args" in tool_call
                    and "parsed_document" in tool_call["args"]
                    and "pk" in tool_call["args"]["parsed_document"]
                    and tool_call["args"]["parsed_document"]["pk"] in new_state["id2messages"]
                ):
                    new_state["id2messages"][tool_call["args"]["parsed_document"]["pk"]].append(msg)
                outputs.append(msg)
            except Exception as exception:
                logger.exception(
                    "Error calling tool",
                    tool_name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                    error=str(exception),
                )
                outputs.append(
                    ToolMessage(
                        name=tool_call["name"],
                        tool_call_id=tool_call["id"],
                        content=json.dumps({"exception": str(exception)}),
                        status="error",
                    )
                )
                if not has_too_many_errors(messages, self.retries):
                    outputs.append(
                        HumanMessage(
                            content=(
                                exception.human_message if isinstance(exception, LLMToolCallError) else ERROR_PROMPT
                            ),
                        )
                    )
        return {**state, "messages": outputs, **new_state}


def fetch_portal_docs(state: BootstrapState) -> BootstrapState:
    mpc_pk = state["merged_portal_credential_pk"]
    all_docs = (
        RawDocument.objects.filter(
            retrieval__merged_portal_credential__pk=mpc_pk,
            is_duplicate=False,
        )
        .order_by("original_posted_date")
        .values_list("pk", flat=True)[:2]
    )
    return {
        "line_items_predict_work_todo": [str(doc) for doc in all_docs],
        "id2messages": {},
        "id2obj": {},
        "line_items_predict_work_done": [],
        "line_items_predict_work_errors": [],
    }


class DocVaultGraphNode:
    def __init__(self, graph: CompiledStateGraph, current_pk_key: str, options: dict) -> None:
        self.graph = graph
        self.options = options
        self.current_pk_key = current_pk_key

    async def __call__(self, state: BootstrapState) -> BootstrapState:
        res = await self.graph.ainvoke(
            {
                "raw_doc_pk": state[self.current_pk_key],
                "id2obj": state["id2obj"],
                "id2messages": state["id2messages"],
                "line_items": state.get("line_items"),
                **self.options,
            }
        )
        return {
            **state,
            "id2messages": res["id2messages"],
            "id2obj": res["id2obj"],
        }


class LineItemSummaryNode:
    def __init__(self, llm: BaseChatModel, tools: dict[str, BaseTool]) -> None:
        llm = get_llm()
        tools = get_tools()
        self.llm_tools = llm.bind_tools(tools.values())

    async def __call__(self, state: BootstrapState) -> BootstrapState:
        messages = await make_message(aggregate_line_items, inp={}, inp_kwargs={"state": state})
        messages.append(
            HumanMessage(
                content="""
        Use the output of the aggregate_line_items tool to call insert_and_filter_line_items.
        Only insert the line items that are in the output of aggregate_line_items.
        Think very carefully about which line items should be inserted vs which should not.
        """.strip()
            )
        )
        llm_r = await self.llm_tools.ainvoke(messages)
        return {
            **state,
            "final_predict_work_todo": state.get("line_items_predict_work_done", []),
            "final_predict_work_done": [],
            "final_predict_work_errors": [],
            # Guard
            "line_items": (
                llm_r.tool_calls[0]["args"]["line_items_classification"]
                if len(llm_r.tool_calls) > 0
                and "args" in llm_r.tool_calls[0]
                and "line_items_classification" in llm_r.tool_calls[0]["args"]
                else None
            ),
            "messages": [*messages, llm_r],
        }


###################### ROUTES ######################


class CallToolConditionalEdge:
    def __init__(self, tool_node: str, next_state: str) -> None:
        self.tool_node = tool_node
        self.next_state = next_state

    def __call__(self, state: State) -> str:
        if messages := state.get("messages", []):
            ai_message = messages[-1]
        else:
            msg = f"No messages found in input state to tool_edge: {state}"
            raise ValueError(msg)
        if hasattr(ai_message, "tool_calls") and len(ai_message.tool_calls) > 0:
            return self.tool_node
        return self.next_state


class RouteToolRetryConditionalEdge:
    def __init__(self, tool_node: str, next_state: str) -> None:
        self.tool_node = tool_node
        self.next_state = next_state

    def __call__(self, state: State) -> str:
        if messages := state.get("messages", []):
            tool_message = messages[-1]
        else:
            msg = f"No messages found in input state to tool_edge: {state}"
            raise ValueError(msg)
        if isinstance(tool_message, HumanMessage):
            return self.tool_node
        return self.next_state


###################### EXEC ######################


def summarize_line_items(llm: BaseChatModel, tools: dict[str, BaseTool]) -> CompiledStateGraph:
    graph_builder = StateGraph(State)
    llm_tools = llm.bind_tools(tools.values())
    graph_builder.add_node("line_item_summary", LineItemSummaryNode(llm_tools, tools))
    graph_builder.add_node("tools", BasicToolNode(tools, retries=2))

    graph_builder.add_edge(START, "line_item_summary")
    graph_builder.add_conditional_edges(
        "line_item_summary",
        CallToolConditionalEdge("tools", END),
        {"tools": "tools", END: END},
    )
    graph_builder.add_conditional_edges(
        "tools",
        RouteToolRetryConditionalEdge("line_item_summary", END),
        {"line_item_summary": "line_item_summary", END: END},
    )

    return graph_builder.compile()


def build_doc_vault_graph(llm: BaseChatModel, tools: dict[str, BaseTool]) -> CompiledStateGraph:
    graph_builder = StateGraph(State)
    llm_tools = llm.bind_tools(tools.values())
    graph_builder.add_node("doc_vault_predictor", LLMNode(llm_tools))
    graph_builder.add_node("tools", BasicToolNode(tools))
    graph_builder.add_node("k_shot_examples", ground_truth_k_shot_examples)

    graph_builder.add_edge(START, "k_shot_examples")
    graph_builder.add_edge("k_shot_examples", "doc_vault_predictor")
    graph_builder.add_conditional_edges(
        "doc_vault_predictor",
        CallToolConditionalEdge("tools", END),
        {"tools": "tools", END: END},
    )
    graph_builder.add_conditional_edges(
        "tools",
        RouteToolRetryConditionalEdge("doc_vault_predictor", END),
        {"doc_vault_predictor": "doc_vault_predictor", END: END},
    )

    return graph_builder.compile()


def get_llm() -> BaseChatModel:
    return init_chat_model(
        "us.anthropic.claude-3-5-haiku-20241022-v1:0", model_provider="bedrock", region_name="us-east-1"
    )


def get_tools() -> dict[str, BaseTool]:
    return {
        t.name: t for t in [insert_doc_vault, get_document_input, aggregate_line_items, insert_and_filter_line_items]
    }
