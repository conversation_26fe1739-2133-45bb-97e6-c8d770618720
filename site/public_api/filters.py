from datetime import date
from uuid import UUID

import structlog
from django.db.models import Q
from ninja import Field, FilterSchema
from webapp.models import DocumentType

from public_api.schemas import DocumentTypeEnum

logger = structlog.get_logger(__name__)


class EntityFilterSchema(FilterSchema):
    """Filter schema for Investing Entities"""

    client_id: UUID | None = Field(None, description="Filter by client ID")


class LineItemFilterSchema(FilterSchema):
    """Filter schema for Line Items"""

    client_id: UUID | None = Field(None, description="Filter by client ID")
    entity_id: UUID | None = Field(None, description="Filter by investing entity ID")
    investment_id: UUID | None = Field(None, description="Filter by investment ID")


class DocumentFilterSchema(FilterSchema):
    """Filter schema for Documents"""

    client_id: UUID | None = Field(None, description="Filter by client ID")
    entity_id: UUID | None = Field(None, description="Filter by investing entity ID")
    investment_id: UUID | None = Field(None, description="Filter by investment ID")
    line_item_id: UUID | None = Field(None, description="Filter by line item ID")

    document_type: DocumentTypeEnum | None = Field(None, description="Filter by document type")
    from_date: date | None = Field(None, description="Filter by documents created after this date")
    to_date: date | None = Field(None, description="Filter by documents created before this date")

    def filter_client_id(self, value: UUID | None) -> Q:
        if value:
            return Q(line_item__investing_entity__client_id=value)
        return Q()

    def filter_entity_id(self, value: UUID | None) -> Q:
        if value:
            return Q(line_item__investing_entity_id=value)
        return Q()

    def filter_investment_id(self, value: UUID | None) -> Q:
        if value:
            return Q(line_item__investment_id=value)
        return Q()

    def filter_line_item_id(self, value: UUID | None) -> Q:
        if value:
            return Q(line_item_id=value)
        return Q()

    def filter_document_type(self, value: str | None) -> Q:
        if value:
            return Q(document_type=({k.label: k.value for k in DocumentType}.get(value, DocumentType.UNKNOWN)))
        return Q()

    def filter_from_date(self, value: date | None) -> Q:
        if value:
            return Q(posted_date__gte=value)
        return Q()

    def filter_to_date(self, value: date | None) -> Q:
        if value:
            return Q(posted_date__lte=value)
        return Q()


class CashFlowFilterSchema(FilterSchema):
    """Filter schema for Cash Flows"""

    client_id: UUID | None = Field(None, description="Filter by client ID")
    entity_id: UUID | None = Field(None, description="Filter by investing entity ID")
    investment_id: UUID | None = Field(None, description="Filter by investment ID")
    line_item_id: UUID | None = Field(None, description="Filter by line item ID")
    from_date: date | None = Field(None, description="Filter by cash flows after this date")
    to_date: date | None = Field(None, description="Filter by cash flows before this date")

    def filter_client_id(self, value: str | None) -> Q:
        if value:
            return Q(processed_document__line_item__investing_entity__client_id=value)
        return Q()

    def filter_entity_id(self, value: str | None) -> Q:
        if value:
            return Q(processed_document__line_item__investing_entity_id=value)
        return Q()

    def filter_investment_id(self, value: str | None) -> Q:
        if value:
            return Q(processed_document__line_item__investment_id=value)
        return Q()

    def filter_line_item_id(self, value: str | None) -> Q:
        if value:
            return Q(processed_document__line_item_id=value)
        return Q()

    def filter_from_date(self, value: date | None) -> Q:
        if value:
            return Q(processed_document__effective_date__gte=value)
        return Q()

    def filter_to_date(self, value: date | None) -> Q:
        if value:
            return Q(processed_document__effective_date__lte=value)
        return Q()


class ValuationFilterSchema(CashFlowFilterSchema):
    """Filter schema for Valuations"""
