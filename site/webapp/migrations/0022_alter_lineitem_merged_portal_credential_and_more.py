# Generated by Django 5.1.7 on 2025-03-14 19:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0021_backfill_portal_credentials'),
    ]

    operations = [
        migrations.AlterField(
            model_name='lineitem',
            name='merged_portal_credential',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='webapp.mergedportalcredential'),
        ),
        migrations.AlterField(
            model_name='mergedportalcredential',
            name='multi_factor_authentication',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='merged_portal_credential', to='webapp.multifactorauthentication'),
        ),
        migrations.AlterField(
            model_name='mergedportalcredential',
            name='portal',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='merged_portal_credentials', to='webapp.portal'),
        ),
        migrations.AlterField(
            model_name='mergedportalcredential',
            name='portal_credential',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='merged_portal_credential', to='webapp.portalcredential'),
        ),
    ]
