import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON><PERSON>ator
from pathlib import Path

import structlog
from playwright.async_api import Locator
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class JuniperSquarePortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "junipersquare.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [("<EMAIL>", "Juniper Square")]

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError

        email_input_locator = self.page.locator("input[type='email']")
        password_input_locator = self.page.locator("input[type='password']")
        login_button_locator = self.page.locator("button[type='submit']")
        if await email_input_locator.count() == 1 and await email_input_locator.is_visible():
            await email_input_locator.click()
            await email_input_locator.fill(username)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        if await password_input_locator.count() == 1 and await password_input_locator.is_visible():
            await password_input_locator.click()
            await password_input_locator.fill(password)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        if await login_button_locator.count() == 1 and await login_button_locator.is_visible():
            await login_button_locator.click()
            await asyncio.sleep(5)
        else:
            raise CouldNotFindHTMLElementError

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError

        new_code_text_ct = await self.page.get_by_text("Enter authentication code").count()
        otp_input_ct = await self.page.locator("div.react-code-input").count()
        return (new_code_text_ct + otp_input_ct) > 0

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError

        remember_me_locator = self.page.locator("input[type='checkbox'][name='rememberTwoFa']")
        if await remember_me_locator.count() == 1 and await remember_me_locator.is_visible():
            await remember_me_locator.check()
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError

        otp_input_div_locator = self.page.locator("div.react-code-input")
        if (
            otp_input_div_locator
            and await otp_input_div_locator.count() == 1
            and await otp_input_div_locator.is_visible()
        ):
            otp_input_div_locator = self.page.locator("div.react-code-input")
            otp_input_locators = otp_input_div_locator.locator("input")
            otp_input_locators_count = await otp_input_locators.count()
            if otp_input_locators_count == 0:
                logger.error("Cannot find otp input on page")
                raise CouldNotFindHTMLElementError
            for i in range(otp_input_locators_count):
                otp_input = otp_input_locators.nth(i)
                if await otp_input.count() != 1:
                    logger.error("Cannot find otp input on page")
                    raise CouldNotFindHTMLElementError
                await otp_input.fill(otp[i])
                await asyncio.sleep(1)
        await asyncio.sleep(10)

    async def _check_login_successful(self) -> bool:
        await asyncio.sleep(10)
        await self.page.wait_for_load_state(state="load")
        if self.page is None:
            raise ManagerNotInitializedError
        portfolio_text_ct = await self.page.get_by_text("Portfolio").count()
        documents_text_ct = await self.page.get_by_text("Documents").count()
        investments_text_ct = await self.page.get_by_text("Investments").count()
        return (portfolio_text_ct + documents_text_ct + investments_text_ct) > 0

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: C901, PLR0912, PLR0915
        docs_locator = self.page.get_by_text("Documents")
        if await docs_locator.count() == 1:
            await docs_locator.click()
            await asyncio.sleep(5)
        else:
            logger.error("This portal does not have a documents page")
            raise CouldNotFindHTMLElementError

        columns_container_locator = self.page.locator("div[data-cy='layouts-column-container']")
        tabs_list = columns_container_locator.locator("div.styles_Tabs__list__BWcrP")
        tabs = tabs_list.locator("li")
        tabs_count = await tabs.count()
        if tabs_count == 0:
            logger.error("The documents page has no tabs with documents to download")
            raise CouldNotFindHTMLElementError

        for i in range(tabs_count):
            tab = tabs.nth(i)
            tab_title = await tab.inner_text()
            await tab.click()
            await asyncio.sleep(5)

            table_locator = self.page.locator("table")
            table_body = table_locator.locator("tbody")
            table_rows = table_body.locator("tr[tabindex]")
            table_rows_count = await table_rows.count()
            if table_rows_count == 0:
                logger.error("The page has no documents to download")
                raise CouldNotFindHTMLElementError

            for j in range(table_rows_count):
                row = table_rows.nth(j)
                row_metadata = await self._get_row_metadata(row)
                metadata = json.dumps(row_metadata)
                row_checkbox_locator = row.locator("td").first
                row_checkbox_input = row_checkbox_locator.locator("input")
                if await row_checkbox_input.count() == 1:
                    await row_checkbox_input.check()
                else:
                    logger.info("Could not check row", row=row, row_metadata=row_metadata)
                    continue
                await asyncio.sleep(1)
                if await self.should_download_document(metadata):
                    download_button = self.page.locator("button[data-testid='download-all-docs-button']")
                    await download_button.wait_for(state="visible")
                    if (not await download_button.is_visible()) or (await download_button.count() != 1):
                        logger.info("Could not download document", row=row, row_metadata=row_metadata)
                        await row_checkbox_input.uncheck()
                        await asyncio.sleep(1)
                        continue
                    doc_name, doc_content = await self._download_document(download_button)
                    if doc_content is None:
                        logger.info("Could not download documnet", row=row, row_metadata=row_metadata)
                        await row_checkbox_input.uncheck()
                        await asyncio.sleep(1)
                        continue
                    if "Category" in row_metadata:
                        doc_type = self._document_mapper(row_metadata["Category"])
                    else:
                        doc_type = self._document_mapper(tab_title)
                    if "Date" in row_metadata:
                        datetime_date = datetime.datetime.strptime(row_metadata["Date"], "%b %d, %Y")  # noqa: DTZ007
                    else:
                        logger.info("Document row does not have a date", row=row, row_metadata=row_metadata)
                        await row_checkbox_input.uncheck()
                        await asyncio.sleep(1)
                        continue
                    doc = RawDocumentTuple(
                        name=doc_name,
                        date=datetime_date,
                        doc_type=doc_type,
                        content=doc_content,
                        raw_metadata=metadata,
                        doc_hash=self._doc_hash(metadata),
                    )
                    if doc is not None:
                        yield doc
                await row_checkbox_input.uncheck()
                await asyncio.sleep(1)

    async def _get_row_metadata(self, row: Locator) -> dict[str, str]:
        row_metadata = {}
        row_cells = row.locator("td")
        table_locator = self.page.locator("table")
        table_header = table_locator.locator("thead")
        table_header_row = table_header.locator("tr.styles_TableRow__g0b3Z")
        table_header_cells = table_header_row.locator("th")
        table_header_cells_count = await table_header_cells.count()
        if table_header_cells_count == 0:
            logger.error("Table has no header cells to determine row metadata", row=row)
            raise CouldNotFindHTMLElementError

        for i in range(table_header_cells_count):
            table_header_cell = table_header_cells.nth(i)
            header_cell_inner_text = await table_header_cell.inner_text()
            if header_cell_inner_text.strip():
                if header_cell_inner_text.strip() == "Last viewed":
                    continue
                row_cell = row_cells.nth(i)
                row_cell_inner_text = await row_cell.inner_text()
                if row_cell_inner_text.strip():
                    row_metadata[header_cell_inner_text.strip()] = row_cell_inner_text.strip()
        return row_metadata

    async def _download_document(self, download_button: Locator) -> tuple[str, bytes]:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        async with self.page.expect_download() as download_info:
            await download_button.click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.error("Unsupported file format", file_name_suffix=file_name.suffix)
        file_name.unlink()
        return download.suggested_filename, content

    def _document_mapper(self, document_type: str | None) -> DocumentType | str:
        if document_type is None:
            return DocumentType.UNKNOWN

        mapping = {
            "Subscription Documents": DocumentType.INVESTMENT_UPDATE,
            "Tax": DocumentType.TAX,
            "Investment documents": DocumentType.INVESTMENT_UPDATE,
            "Tax documents": DocumentType.TAX,
        }

        return mapping.get(document_type, DocumentType.UNKNOWN)
