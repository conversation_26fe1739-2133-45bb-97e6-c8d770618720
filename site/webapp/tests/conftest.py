import pytest
from django.core.cache import cache
from django.test import Client

from webapp.models.user import BridgeUser, Organization, Role

# Mark all tests in this directory as requiring database access
pytestmark = pytest.mark.django_db


@pytest.fixture(autouse=True)
def clear_cache() -> None:
    """Clear cache before each test."""
    cache.clear()


@pytest.fixture
def client() -> Client:
    """A Django test client instance."""
    return Client()


@pytest.fixture
def test_org() -> Organization:
    """Create a test organization."""
    return Organization.objects.create(name="Test Org", description="Test Organization")


@pytest.fixture
def demo_org() -> Organization:
    """Create a demo organization."""
    return Organization.objects.create(name="Demo", description="Demo Organization")


@pytest.fixture
def test_role() -> Role:
    """Create a test role."""
    return Role.objects.create(name=Role.RoleName.MANAGER)


@pytest.fixture
def test_user(test_org: Organization, test_role: Role) -> BridgeUser:
    """Create a test user with standard permissions."""
    return BridgeUser.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=str(test_org.id),
        roles=[str(test_role.id)],
        password="testpass123",  # noqa: S106
    )


@pytest.fixture
def admin_user(test_org: Organization, test_role: Role) -> BridgeUser:
    """Create an admin user."""
    return BridgeUser.objects.create_superuser(
        username="admin",
        email="<EMAIL>",
        first_name="Admin",
        last_name="User",
        organization=str(test_org.id),
        roles=[str(test_role.id)],
        password="adminpass123",  # noqa: S106
    )


@pytest.fixture
def authenticated_client(client: Client, test_user: BridgeUser) -> Client:
    """A Django test client instance with a logged-in user."""
    client.force_login(test_user)
    return client


@pytest.fixture
def admin_client(client: Client, admin_user: BridgeUser) -> Client:
    """A Django test client instance with a logged-in admin user."""
    client.force_login(admin_user)
    return client
