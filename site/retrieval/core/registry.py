from typing import ClassVar

import structlog
from asgiref.sync import sync_to_async
from webapp.models.portal import Portal
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser

from retrieval.core.strategy import RetrievalManager

logger = structlog.get_logger(__name__)


class RetrievalRegistry:
    manager_interfaces: ClassVar = []
    _manager_interfaces_lookup: ClassVar = {}

    @classmethod
    def register(cls, strategy: type[RetrievalManager]) -> None:
        cls.manager_interfaces.append(strategy)
        logger.debug("Registered", strategy_name=strategy.__name__, manager_interfaces=cls.manager_interfaces)

    @classmethod
    def get_retrieval_manager_for_portal(cls, user: BridgeUser, portal: Portal) -> type[RetrievalManager] | None:
        name, portal_login_url = portal.name, portal.portal_login_url
        manager = cls.get_retrieval_manager(user=user, name=name, portal_login_url=portal_login_url)
        if manager is None:
            logger.error("No manager found and setting retrieval as failed")
        return manager

    @classmethod
    def get_retrieval_manager_for_retrieval(
        cls, user: BridgeUser, retrieval: Retrieval
    ) -> type[RetrievalManager] | None:
        portal = retrieval.merged_portal_credential.portal
        manager = cls.get_retrieval_manager_for_portal(user, portal)
        if manager is None:
            logger.error("No manager found and setting retrieval as failed")
            retrieval.update_login_status(Retrieval.RetrievalStatus.FAILED_LOGIN_CREDENTIALS)
        return manager

    @classmethod
    @sync_to_async
    def aget_retrieval_manager_for_retrieval(
        cls, user: BridgeUser, retrieval: Retrieval
    ) -> type[RetrievalManager] | None:
        return cls.get_retrieval_manager_for_retrieval(user, retrieval)

    @classmethod
    def get_retrieval_manager(
        cls, *, user: BridgeUser, name: str | None = None, portal_login_url: str | None = None
    ) -> type[RetrievalManager] | None:
        if name is None and portal_login_url is None:
            raise ValueError
        logger.debug("Getting retrieval manager", name=name, manager_interfaces=cls.manager_interfaces)
        res = None
        if name in cls._manager_interfaces_lookup:
            res = cls._manager_interfaces_lookup[name]
        else:
            if user.is_demo:
                managers = [
                    manager
                    for manager in cls.manager_interfaces
                    if manager.supports_portal(portal_login_url) and user.is_demo == manager.is_demo()
                ]
            else:
                managers = [
                    manager
                    for manager in cls.manager_interfaces
                    if manager.supports_portal(portal_login_url)
                    and (str(user), name) in manager.known_user_portals()
                    and user.is_demo == manager.is_demo()
                ]
            if len(managers) > 1:
                msg = f"Multiple retrieval managers found for portal {name}"
                raise ValueError(msg)
            res = managers[0] if len(managers) == 1 else None
        cls._manager_interfaces_lookup[name] = res
        if res is None:
            logger.error(
                "No retrieval manager found",
                name=name,
                portal_login_url=portal_login_url,
                manager_interfaces=cls.manager_interfaces,
                manager_interfaces_lookup=cls._manager_interfaces_lookup,
            )
        return res


def register_strategy(cls: type[RetrievalManager]) -> type[RetrievalManager]:
    """
    Class decorator to register scraper strategies.
    """
    logger.debug("Registering Strategy", strategy_name=cls.__name__)
    RetrievalRegistry.register(cls)
    return cls
