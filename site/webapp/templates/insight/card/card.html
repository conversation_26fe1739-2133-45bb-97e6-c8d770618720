{% load customtags %}
<div class=" h-52 py-8 border-t border-b border-secondary-orange-beige  flex-col justify-start items-start gap-6 inline-flex w-full">
    <div class="self-stretch h-[116px] px-8 flex-col justify-start items-start gap-6 flex">
        <div class=" self-stretch justify-start items-start gap-4 inline-flex">
            <div class=" grow shrink basis-0 p-6 bg-white rounded-xl border border-secondary-light-olive flex-col justify-start items-start inline-flex">
                <div class=" self-stretch bg-white justify-start items-center gap-6 inline-flex">
                    <div class=" grow shrink basis-0 text-[#343d36] text-base font-semibold font-['Inter'] leading-normal">
                        Total Invested Capital
                    </div>
                </div>
                <div class=" self-stretch bg-white justify-start items-end gap-4 inline-flex">
                    <div class=" grow shrink basis-0 flex-col justify-start items-start gap-2 inline-flex">
                        <div class=" self-stretch text-[#343d36] text-[28px] font-bold font-['Nanum Myeongjo'] leading-[44px] ">
                            {{ invested_capital|format_as_currency }}
                        </div>
                    </div>
                </div>
            </div>
            <div class=" grow shrink basis-0 p-6 bg-white rounded-xl border border-secondary-light-olive flex-col justify-start items-start inline-flex">
                <div class=" self-stretch bg-white justify-start items-center gap-6 inline-flex">
                    <div class=" grow shrink basis-0 text-[#343d36] text-base font-semibold font-['Inter'] leading-normal">
                        Total Realized
                    </div>
                </div>
                <div class=" self-stretch bg-white justify-start items-end gap-4 inline-flex">
                    <div class=" grow shrink basis-0 flex-col justify-start items-start gap-2 inline-flex">
                        <div class=" self-stretch text-[#343d36] text-[28px] font-bold font-['Nanum Myeongjo'] leading-[44px] ">
                            {{ realized|format_as_currency }}
                        </div>
                    </div>
                </div>
            </div>
            <div class=" grow shrink basis-0 p-6 bg-white rounded-xl border border-secondary-light-olive flex-col justify-start items-start inline-flex">
                <div class=" self-stretch bg-white justify-start items-center gap-6 inline-flex">
                    <div class=" grow shrink basis-0 text-[#343d36] text-base font-semibold font-['Inter'] leading-normal">
                        Total Unrealized
                    </div>
                </div>
                <div class=" self-stretch bg-white justify-start items-end gap-4 inline-flex">
                    <div class=" grow shrink basis-0 flex-col justify-start items-start gap-2 inline-flex">
                        <div class=" self-stretch text-[#343d36] text-[28px] font-bold font-['Nanum Myeongjo'] leading-[44px] ">
                            {{ unrealized|format_as_currency }}
                        </div>
                    </div>
                </div>
            </div>
            <div class=" grow shrink basis-0 p-6 bg-white rounded-xl border border-secondary-light-olive flex-col justify-start items-start inline-flex">
                <div class=" self-stretch bg-white justify-start items-center gap-6 inline-flex">
                    <div class=" grow shrink basis-0 text-[#343d36] text-base font-semibold font-['Inter'] leading-normal">
                        Total Value
                    </div>
                </div>
                <div class=" self-stretch bg-white justify-start items-end gap-4 inline-flex">
                    <div class=" grow shrink basis-0 flex-col justify-start items-start gap-2 inline-flex">
                        <div class=" self-stretch text-[#343d36] text-[28px] font-bold font-['Nanum Myeongjo'] leading-[44px] ">
                            {{ total_value|format_as_currency }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
