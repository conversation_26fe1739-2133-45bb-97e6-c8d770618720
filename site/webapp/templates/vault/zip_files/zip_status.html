{% load static customtags %}
{% if zip_job and zip_job.status == ZipJobStatus.COMPLETED and download_url %}
    <script>
        window.open("{{ download_url|safe }}", '_blank');
        var modal = document.getElementById('modal-container-export-line-items')
        if (modal) {
            const outerModal = document.getElementById('modal')
            outerModal.dispatchEvent(new Event('closeModal'));
        }
        document.dispatchEvent(new Event('reloadVault'));
        document.dispatchEvent(new Event('reloadClientDashboard'));
    </script>
{% elif zip_job and zip_job.status == ZipJobStatus.FAILED %}
    <script>
        document.dispatchEvent(new Event('reloadVault'));
        document.dispatchEvent(new Event('reloadClientDashboard'));
    </script>
{% elif request.session.zip_job_id %}
    <div class="hidden"
         hx-get="{% url 'zip_status_checker' %}{% urlparams job_id=request.session.zip_job_id %}"
         hx-swap="outerHTML"
         hx-trigger="every 2s"></div>
{% endif %}
