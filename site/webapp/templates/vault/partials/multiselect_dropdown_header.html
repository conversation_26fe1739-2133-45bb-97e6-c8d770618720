{% load static %}
<div>
    <button class="dropdown-btn flex text-left justify-between py-1 text-xs font-semibold text-base-darker-olive-muted font-inter rounded focus:ring-2 focus:ring-blue-300 focus:outline-none"
            type="button"
            id="dropdownSearchButton-{{ column_name }}">
        <span>{{ title }}</span>
        <span id="filterIndicator-{{ column_name }}"
              class="hidden ml-1 text-red-500 font-bold">*</span>
        <img class="ml-1"
             width="16"
             height="16"
             alt="sort_arrows"
             src="{% static 'assets/images/table_header_arrow.svg' %}">
    </button>
    <!-- Hidden input for JSON data -->
    <input type="hidden"
           id="{{ column_name }}JsonData"
           name="{{ column_name }}_data"
           class="vaultinputs"
           value=""
           hx-post="{% url 'vault' %}"
           hx-target="#docvault_body"
           hx-swap="innerHTML"
           hx-trigger="change"
           hx-include="#docvault_header, .vaultinputs" />
    <!-- Dropdown Panel -->
    <div id="dropdownSearch-{{ column_name }}"
         class="absolute z-10 hidden bg-white border border-gray-200 rounded shadow-md w-80">
        <div class="p-4 space-y-2">
            <h3 class="font-medium text-gray-700 text-xs">Filter by Category:</h3>
            <!-- Dynamic Items -->
            <div class="space-y-2 max-h-60 overflow-y-auto"
                 id="{{ column_name }}-filter-list">
                {% for item in filter_items %}
                    <div class="relative">
                        <div class="flex items-center justify-between px-3 py-2 rounded-lg hover:bg-gray-100 border border-gray-200">
                            <label class="flex items-center gap-2">
                                <input type="checkbox"
                                       class="{{ column_name }}-checkbox w-4 h-4 accent-[#4B554D] focus:ring-0 focus:outline-none cursor-pointer"
                                       data-parent="{{ item.value }}"
                                       {% if item.submenu %}data-submenu="submenu-{{ item.value }}"{% endif %} />
                                <span class="text-xs font-medium">{{ item.label }}</span>
                            </label>
                            {% if item.submenu %}
                                <button class="text-gray-500 submenu-toggle"
                                        data-target="submenu-{{ item.value }}">▶</button>
                            {% endif %}
                        </div>
                        {% if item.submenu %}
                            <div id="submenu-{{ item.value }}"
                                 class="submenu mt-2 ml-6 border-l border-gray-300 pl-4 space-y-2 hidden">
                                {% for subitem in item.submenu %}
                                    <label class="flex items-center gap-2">
                                        <input type="checkbox"
                                               class="sub-checkbox submenu-{{ item.value }} w-4 h-4 accent-[#4B554D] focus:ring-0 focus:outline-none cursor-pointer"
                                               data-parent="{{ item.value }}"
                                               data-child="{{ subitem.value }}" />
                                        <span class="text-xs font-medium">{{ subitem.label }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
            <!-- Actions -->
            <div class="pt-2 flex gap-2">
                <button id="{{ column_name }}ResetBtn"
                        class="w-1/2 text-[#953300] border border-[#953300] px-4 py-2 rounded-xl font-semibold hover:bg-[#953300] hover:text-white">
                    Reset
                </button>
                <button id="{{ column_name }}ApplyBtn"
                        class="w-1/2 bg-[#953300] text-white px-4 py-2 rounded-xl font-semibold hover:bg-[#7a2600]">
                    Apply
                </button>
            </div>
        </div>
    </div>
</div>
<script>
    // Initialize multiselect for {{ column_name }}
    (function() {
        const COLUMN_NAME = "{{ column_name }}";
        let isInitialized = false;

        function initializeMultiselect() {

            // Prevent multiple initializations
            if (isInitialized) {
                return;
            }

            const dropdownButton = document.getElementById(`dropdownSearchButton-${COLUMN_NAME}`);
            const dropdownMenu = document.getElementById(`dropdownSearch-${COLUMN_NAME}`);

            if (!dropdownMenu || !dropdownButton) {
                return;
            }

            // Manual dropdown toggle (replace Flowbite functionality)
            dropdownButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Close other dropdowns first
                document.querySelectorAll('[id^="dropdownSearch-"]:not([id="dropdownSearch-' + COLUMN_NAME + '"])').forEach(dropdown => {
                    dropdown.classList.add('hidden');
                });

                // Toggle this dropdown
                dropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!dropdownButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
                    dropdownMenu.classList.add('hidden');
                }
            });

            const submenuToggles = dropdownMenu.querySelectorAll(".submenu-toggle");
            const resetBtn = document.getElementById(`${COLUMN_NAME}ResetBtn`);
            const applyBtn = document.getElementById(`${COLUMN_NAME}ApplyBtn`);
            const jsonInput = document.getElementById(`${COLUMN_NAME}JsonData`);

            // Function to collect hierarchical data
            function collectHierarchicalData() {
                const data = {};
                const parentCheckboxes = dropdownMenu.querySelectorAll(`.${COLUMN_NAME}-checkbox`);

                parentCheckboxes.forEach(parentCb => {
                    const parentValue = parentCb.dataset.parent;
                    const hasSubmenu = parentCb.dataset.submenu;

                    if (parentCb.checked) {
                        if (hasSubmenu) {
                            const childCheckboxes = dropdownMenu.querySelectorAll(`input.submenu-${parentValue}:checked`);
                            const selectedChildren = Array.from(childCheckboxes).map(cb => cb.dataset.child);
                            if (selectedChildren.length > 0) {
                                data[parentValue] = selectedChildren;
                            }
                        } else {
                            data[parentValue] = [];
                        }
                    }
                });

                return data;
            }

            // Function to update JSON input
            function updateJsonInput() {
                const hierarchicalData = collectHierarchicalData();
                if (jsonInput) {
                    jsonInput.value = JSON.stringify(hierarchicalData);
                }
            }

            // Submenu toggle logic - FIXED VERSION
            submenuToggles.forEach((btn) => {
                btn.addEventListener("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const targetId = btn.getAttribute("data-target");
                    const submenu = document.getElementById(targetId);

                    if (submenu) {
                        const isHidden = submenu.classList.contains("hidden");
                        submenu.classList.toggle("hidden");
                        btn.innerHTML = isHidden ? "▼" : "▶";
                    }
                });
            });

            // Parent checkbox logic
            dropdownMenu.querySelectorAll(`.${COLUMN_NAME}-checkbox`).forEach(parentCb => {
                parentCb.addEventListener("change", function() {
                    const parentValue = parentCb.dataset.parent;
                    const submenuId = parentCb.getAttribute("data-submenu");

                    if (submenuId) {
                        const childCheckboxes = dropdownMenu.querySelectorAll(`input.submenu-${parentValue}`);
                        childCheckboxes.forEach(childCb => {
                            childCb.checked = parentCb.checked;
                        });
                    }
                    updateJsonInput();
                });
            });

            // Child checkbox logic
            dropdownMenu.querySelectorAll(`.sub-checkbox`).forEach(childCb => {
                childCb.addEventListener("change", function() {
                    const parentValue = childCb.dataset.parent;
                    const parentCheckbox = dropdownMenu.querySelector(`.${COLUMN_NAME}-checkbox[data-parent="${parentValue}"]`);

                    if (parentCheckbox) {
                        const checkedChildren = dropdownMenu.querySelectorAll(`input.submenu-${parentValue}:checked`);
                        parentCheckbox.checked = checkedChildren.length > 0;
                    }
                    updateJsonInput();
                });
            });

            // Reset button
            if (resetBtn) {
                resetBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    dropdownMenu.querySelectorAll('input[type=checkbox]').forEach(cb => {
                        cb.checked = false;
                    });
                    updateJsonInput();
                    if (jsonInput) {
                        jsonInput.dispatchEvent(new Event('change', {
                            bubbles: true
                        }));
                    }
                });
            }

            // Apply button
            if (applyBtn) {
                applyBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    updateJsonInput();
                    if (jsonInput) {
                        jsonInput.dispatchEvent(new Event('change', {
                            bubbles: true
                        }));
                    }
                    dropdownMenu.classList.add('hidden'); // Close dropdown after apply
                });
            }

            // Initialize
            updateJsonInput();
            isInitialized = true;
        }

        // Reset initialization flag when navigating away
        function resetInitialization() {
            isInitialized = false;
        }

        // Initialize on DOM ready and after HTMX swaps
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeMultiselect);
        } else {
            setTimeout(initializeMultiselect, 100); // Small delay to ensure DOM is ready
        }

        document.addEventListener('htmx:afterSwap', function(e) {
            // Only reinitialize if this specific dropdown was swapped
            if (e.detail.target && e.detail.target.querySelector(`#dropdownSearch-${COLUMN_NAME}`)) {
                resetInitialization();
                setTimeout(initializeMultiselect, 100);
            }
        });

        document.addEventListener('htmx:beforeRequest', resetInitialization);
    })();
</script>
