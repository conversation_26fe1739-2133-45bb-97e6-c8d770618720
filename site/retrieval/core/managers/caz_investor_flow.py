import structlog
from webapp.models.documents import DocumentType

from retrieval.core.managers.kleiner_perkins_caz import Klein<PERSON><PERSON>erkinsAndCAZPortalManagerAbstract
from retrieval.core.registry import register_strategy

logger = structlog.get_logger(__name__)


@register_strategy
class CAZInvestorFlowPortalManager(KleinerPerkinsAndCAZPortalManagerAbstract):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "investorflow.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "CAZ Investor Flow"),
        ]

    @classmethod
    def email_otp_rules(cls) -> dict:
        return {
            "senderContains": ["cazinvestments.com"],
        }

    def _document_mapper(self, category: str) -> str:
        return {
            "Quarterly Statements": DocumentType.ACCOUNT_STATEMENT,
            "Capital Calls & Distributions": DocumentType.CAPITAL_CALL,
            "Tax Related": DocumentType.TAX,
            "Underlying Investment Reports": DocumentType.INVESTMENT_UPDATE,
            "Fund Updates": DocumentType.INVESTMENT_UPDATE,
            "Audited Financial Statements": DocumentType.FINANCIAL_STATEMENTS,
        }.get(category, DocumentType.UNKNOWN)
