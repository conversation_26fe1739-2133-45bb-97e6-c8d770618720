# Generated by Django 5.1.6 on 2025-02-14 17:03

from django.db import migrations, models


class Migration(migrations.Migration):
    def set_my_defaults(apps, schema_editor):
        ProcessedDocuments = apps.get_model('webapp', 'processeddocument')
        for pd in ProcessedDocuments.objects.all().iterator():
            pd.effective_date = pd.posted_date
            pd.save()

    def reverse_func(apps, schema_editor):
        pass  # code for reverting migration, if any

    dependencies = [
        ('webapp', '0014_client_note_investingentity_note_investment_note'),
    ]

    operations = [
        migrations.AddField(
            model_name='processeddocument',
            name='effective_date',
            field=models.DateField(null=True),
        ),
        migrations.RunPython(set_my_defaults, reverse_func),
        migrations.AlterField(
            model_name='processeddocument',
            name='effective_date',
            field=models.DateField(),
        ),
        migrations.AddField(
            model_name='investmentupdatedocumentfact',
            name='unrealized_value',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True),
        ),
    ]
