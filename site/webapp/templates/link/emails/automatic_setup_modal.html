{% load customtags static %}
<div id="modal-container">
    <div class="w-full max-w-lg max-h-full">
        <!-- Modal content -->
        <div class="relative">
            <!-- Modal header -->
            <div class="flex items-center justify-between px-4 pt-4 md:p-5 rounded-t dark:border-gray-600">
                <div class="FeaturedIcon  p-2 rounded-[10px] justify-center items-center inline-flex">
                    <div class="Frame  relative flex-col justify-start items-start flex">
                        <div class="Group  relative">
                            <img src="{% static 'assets/images/micro_soft.svg' %}"
                                 width="146"
                                 height="33"
                                 alt="closeButton">
                        </div>
                    </div>
                </div>
                <button _="on click trigger closeModal"
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                    <img src="{% static 'assets/images/close.svg' %}"
                         width="16"
                         height="19"
                         alt="closeButton">
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div class="mt-1 px-5">
                We only support a few providers right now. Please message support if you would like to see more.
                <br />
                <br />
                <a href="{{ microsoft_auth_uri }}">
                    <div class="ButtonsButton w-full h-11 px-4 py-2.5 rounded-lg border border-[#924f34] justify-center items-center gap-1.5 inline-flex">
                        <div class="TextPadding px-0.5 justify-center items-center flex">
                            <div class="Text text-[#924f34] text-base font-semibold font-inter leading-normal">Microsoft Outlook</div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
