import asyncio
import datetime
import hashlib
import json
from collections.abc import Async<PERSON>enerator

import structlog
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import Document<PERSON><PERSON>, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class DSTPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "ssnc.cloud" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [("<EMAIL>", "DST Vision (Ares, Fortress)")]

    @classmethod
    def email_otp_rules(cls) -> dict:
        return {
            "senderContains": ["notice.dstsystems.com"],
        }

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError

        username_input_locator = self.page.locator("input[id='username']")
        password_input_locator = self.page.locator("input[id='password']")
        login_button_locator = self.page.locator("button[alt='Sign In']")
        if await username_input_locator.count() == 1 and await username_input_locator.is_visible():
            await username_input_locator.click()
            await username_input_locator.fill(username)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        if await password_input_locator.count() == 1 and await password_input_locator.is_visible():
            await password_input_locator.click()
            await password_input_locator.fill(password)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        if await login_button_locator.count() == 1 and await login_button_locator.is_visible():
            await login_button_locator.click()
            await asyncio.sleep(5)
        else:
            raise CouldNotFindHTMLElementError

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError

        security_code_text_ct = await self.page.get_by_text("security code").count()
        send_code_btn_ct = await self.page.locator("button[id='sendCodeBtn']").count()
        return (security_code_text_ct + send_code_btn_ct) > 0

    async def _send_otp(self) -> None:
        if self.page is None:
            raise ManagerNotInitializedError

        send_code_btn = self.page.locator("button[id='sendCodeBtn']")
        if await send_code_btn.count() == 1 and await send_code_btn.is_visible():
            await send_code_btn.click()
            await asyncio.sleep(10)
        else:
            raise CouldNotFindHTMLElementError

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError

        otp_input_locator = self.page.locator("input[id='token']")
        verify_code_locator = self.page.locator("button[id='verifyCodeBtn']")
        if await otp_input_locator.count() == 1 and await otp_input_locator.is_visible():
            await otp_input_locator.click()
            for digit in otp:
                await otp_input_locator.type(digit)
            await asyncio.sleep(2)
        else:
            raise CouldNotFindHTMLElementError
        if await verify_code_locator.count() == 1 and await verify_code_locator.is_visible():
            await verify_code_locator.click()
            await asyncio.sleep(10)
        else:
            raise CouldNotFindHTMLElementError

    async def _check_login_successful(self) -> bool:
        await asyncio.sleep(10)
        await self.page.wait_for_load_state(state="load")
        if self.page is None:
            raise ManagerNotInitializedError

        agree_btn = self.page.locator("button[type='submit'][value='I Agree']")
        if await agree_btn.count() == 1 and await agree_btn.is_visible():
            await agree_btn.click()
            await asyncio.sleep(10)
            await self.page.wait_for_load_state(state="load")

        sign_out_text_ct = await self.page.get_by_text("Sign Out").count()
        profile_text_ct = await self.page.get_by_text("Profile").count()
        operations_text_ct = await self.page.get_by_text("Operations").count()
        return (sign_out_text_ct + profile_text_ct + operations_text_ct) > 0

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError

        starting_point = await self.get_starting_point()
        operations_tab = self.page.locator("a[id='operationstab']")
        if await operations_tab.count() == 1 and await operations_tab.is_visible():
            await operations_tab.click()
            await asyncio.sleep(3)
        else:
            logger.error("Could not find Operations tab")
            raise CouldNotFindHTMLElementError

        asset_summary_locator = self.page.locator("div[id='divAssetSummary']")
        asset_iframe_locator = asset_summary_locator.locator("iframe[id='dppGbobFrame']")
        if await asset_iframe_locator.count() == 0:
            logger.error("Could not find assets frame")
            raise CouldNotFindHTMLElementError
        iframe_handle = await asset_iframe_locator.element_handle()
        frame = await iframe_handle.content_frame()
        table_body = frame.locator("tbody")
        table_rows = table_body.locator("tr")
        table_rows_count = await table_rows.count()
        table_row_starting_idx = 0
        if starting_point and "asset_mgmt_company_idx" in starting_point:
            logger.info("skipping to asset mgmt company starting point", starting_point=starting_point)
            table_row_starting_idx = starting_point["asset_mgmt_company_idx"]
        for i in range(table_row_starting_idx, table_rows_count):
            table_row = table_rows.nth(i)
            inner_i_tag = table_row.locator("i[onmouseover]")
            await inner_i_tag.hover()
            await asyncio.sleep(1)
            dropdown_menu = self.page.locator("div[id='menuCon']")
            if await dropdown_menu.count() == 1 and await dropdown_menu.is_visible():
                assets_reps_locator = dropdown_menu.locator("a[data-val='ASSETS']")
                await assets_reps_locator.click()
                await asyncio.sleep(5)
                logger.info(
                    "update_check_point",
                    checkpoint="asset_mgmt_company_idx",
                    asset_mgmt_company_idx=i,
                )
                await self.update_check_point({"asset_mgmt_company_idx": i})
                use_starting_point = i == table_row_starting_idx
                async for doc in self._retrieve_assets(starting_point, use_starting_point=use_starting_point):
                    yield doc

    async def _retrieve_assets(
        self, starting_point: dict, *, use_starting_point: bool
    ) -> AsyncGenerator[RawDocumentTuple, None]:
        table_locator = self.page.locator("table.c-table")
        table_body = table_locator.locator("> tbody")
        table_rows = table_body.locator("> tr")
        table_rows_count = await table_rows.count()
        fund_row_starting_idx = 2
        if starting_point and "fund_row_idx" in starting_point and use_starting_point:
            logger.info("skipping to fund row starting point", starting_point=starting_point)
            fund_row_starting_idx = starting_point["fund_row_idx"]
        for i in range(fund_row_starting_idx, table_rows_count):
            table_row = table_rows.nth(i)
            accounts_a_tag = table_row.locator("a")
            await accounts_a_tag.click()
            await asyncio.sleep(5)
            logger.info(
                "update_check_point",
                checkpoint="fund_row_idx",
                fund_row_idx=i,
            )
            await self.update_check_point({"fund_row_idx": i})
            use_starting_point = i == fund_row_starting_idx
            async for doc in self._retrieve_accounts(starting_point, use_starting_point=use_starting_point):
                yield doc

            breadcrumb_trail_div = self.page.locator("div[id='breadCrumbTrailDiv']")
            assets_button = breadcrumb_trail_div.locator("a", has_text="Assets & Reps")
            await assets_button.click()
            await asyncio.sleep(3)

    async def _retrieve_accounts(
        self, starting_point: dict, *, use_starting_point: bool
    ) -> AsyncGenerator[RawDocumentTuple, None]:
        next_page_tag = self.page.locator("a", has_text="next page").first
        accounts_page_idx = 0
        if starting_point and "accounts_page_idx" in starting_point and use_starting_point:
            logger.info("skipping to accounts page check point", starting_point=starting_point)
            while accounts_page_idx < starting_point["accounts_page_idx"]:
                await next_page_tag.click()
                await asyncio.sleep(3)
                accounts_page_idx += 1
        while await next_page_tag.count() > 0:
            logger.info(
                "update_check_point",
                checkpoint="accounts_page_idx",
                accounts_page_idx=accounts_page_idx,
            )
            await self.update_check_point({"accounts_page_idx": accounts_page_idx})
            async for doc in self._retrieve_account_details(starting_point, use_starting_point=use_starting_point):
                yield doc
            await next_page_tag.click()
            await asyncio.sleep(3)
            accounts_page_idx += 1
            use_starting_point = False

        async for doc in self._retrieve_account_details(starting_point, use_starting_point=use_starting_point):
            yield doc

    async def _retrieve_account_details(
        self, starting_point: dict, *, use_starting_point: bool
    ) -> AsyncGenerator[RawDocumentTuple, None]:
        table_locator = self.page.locator("table.c-table")
        inner_table_locator = table_locator.locator("table.DataBkgrndColor")
        table_body = inner_table_locator.locator("> tbody")
        table_rows = table_body.locator("> tr.c-table__row")
        table_rows_count = await table_rows.count()
        account_detail_starting_idx = 0
        if starting_point and "account_detail_idx" in starting_point and use_starting_point:
            logger.info("skipping to account detail idx starting point", starting_point=starting_point)
            account_detail_starting_idx = starting_point["account_detail_idx"]
        for i in range(account_detail_starting_idx, table_rows_count):
            table_row = table_rows.nth(i)
            details_a_tag = table_row.locator("a")
            await details_a_tag.click()
            await asyncio.sleep(5)
            logger.info(
                "update_check_point",
                checkpoint="account_detail_idx",
                account_detail_idx=i,
            )
            await self.update_check_point({"account_detail_idx": i})
            use_starting_point = i == account_detail_starting_idx
            async for doc in self._retrieve_single_account(starting_point, use_starting_point=use_starting_point):
                yield doc
            breadcrumb_trail_div = self.page.locator("div[id='breadCrumbTrailDiv']")
            accounts_button = breadcrumb_trail_div.locator("a", has_text="Accounts")
            await accounts_button.click()
            await asyncio.sleep(3)

    async def _retrieve_single_account(  # noqa: PLR0915
        self, starting_point: dict, *, use_starting_point: bool
    ) -> AsyncGenerator[RawDocumentTuple, None]:
        account_metadata = await self._get_account_metadata()
        statement_tax_form_button = self.page.locator("a.SubSectionLinkSmB", has_text="Stmnts / Tax Forms")
        await statement_tax_form_button.click()
        await asyncio.sleep(10)
        if await self.page.get_by_text("There are no statements to display for this account.").count() == 0:
            iframe_locator = self.page.locator("iframe[id='viewstmt']")
            iframe_handle = await iframe_locator.element_handle()
            iframe = await iframe_handle.content_frame()
            nav_frame_locator = iframe.locator("frame[name='navframe']")
            nav_frame_handle = await nav_frame_locator.element_handle()
            nav_frame = await nav_frame_handle.content_frame()
            statement_form = nav_frame.locator("form[id='stmt']")
            statement_selection = statement_form.locator("select[id='idx']")
            statement_selection_options = statement_selection.locator("option")
            statement_selection_options_count = await statement_selection_options.count()
            if statement_selection_options_count == 1:
                option_title = await statement_selection_options.nth(0).inner_text()
                option_title_cleaned = option_title.strip()
                account_metadata["Title"] = option_title_cleaned
                metadata = json.dumps(account_metadata)
                if await self.should_download_document(metadata):
                    doc = await self._download_single_statement_tax_form(metadata, option_title_cleaned)
                    if doc is not None:
                        yield doc
            else:
                go_button = statement_form.locator("a[id='but_go']")
                statement_option_starting_idx = 0
                if starting_point and "statement_option_idx" in starting_point and use_starting_point:
                    logger.info("skipping to statement option starting point", starting_point=starting_point)
                    statement_option_starting_idx = starting_point["statement_option_idx"]
                for i in range(statement_option_starting_idx, statement_selection_options_count):
                    await statement_selection.select_option(index=i)
                    await asyncio.sleep(3)
                    await go_button.click()
                    await asyncio.sleep(7)
                    logger.info(
                        "update_check_point",
                        checkpoint="statement_option_idx",
                        statement_option_idx=i,
                    )
                    await self.update_check_point({"statement_option_idx": i})
                    option_title = await statement_selection_options.nth(i).inner_text()
                    option_title_cleaned = option_title.strip()
                    account_metadata["Title"] = option_title_cleaned
                    metadata = json.dumps(account_metadata)
                    if await self.should_download_document(metadata):
                        doc = await self._download_single_statement_tax_form(metadata, option_title_cleaned)
                        if doc is not None:
                            yield doc
        else:
            logger.info("No statements or tax forms found for this account", account_metadata=account_metadata)

        breadcrumb_trail_div = self.page.locator("div[id='breadCrumbTrailDiv']")
        accounts_details_button = breadcrumb_trail_div.locator("a", has_text="Account Details")
        await accounts_details_button.click()
        await asyncio.sleep(3)
        transaction_history_button = self.page.locator("a.SubSectionLinkSmB", has_text="Transaction History")
        await transaction_history_button.click()
        await asyncio.sleep(3)
        async for doc in self._retrieve_transactions(
            account_metadata, starting_point, use_starting_point=use_starting_point
        ):
            yield doc

    async def _download_single_statement_tax_form_bytes(self) -> bytes | None:
        iframe_locator = self.page.locator("iframe[id='viewstmt']")
        iframe_handle = await iframe_locator.element_handle()
        iframe = await iframe_handle.content_frame()
        statement_frame_locator = iframe.locator("frame[name='stmtframe']")
        statement_frame_handle = await statement_frame_locator.element_handle()
        statement_frame = await statement_frame_handle.content_frame()
        response = await self.page.request.get(statement_frame.url)
        if response.ok:
            return await response.body()
        return None

    async def _download_single_statement_tax_form(self, metadata: json, doc_title: str) -> RawDocumentTuple:
        row_metadata = json.loads(metadata)
        doc_type, date = doc_title.rsplit(" ", 1)
        doc_content = await self._download_single_statement_tax_form_bytes()
        if doc_content is None:
            logger.info("Could not download documnet", row_metadata=row_metadata)
            return None
        doc_type = self._document_mapper(doc_type)
        try:
            datetime_date = datetime.datetime.strptime(date, "%Y-%m-%d")  # noqa: DTZ007
        except ValueError:
            try:
                datetime_date = datetime.datetime.strptime(date, "%Y")  # noqa: DTZ007
            except ValueError:
                logger.info("Statement/Tax Form does a recognizeable date format", row_metadata=row_metadata)
                return None

        doc_name = doc_title + ".pdf"  # making official document name have .pdf extension
        return RawDocumentTuple(
            name=doc_name,
            date=datetime_date,
            doc_type=doc_type,
            content=doc_content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    async def _get_account_metadata(self) -> dict[str, str]:
        account_metadata = {}
        outer_div_locator = self.page.locator("div.dstTableMarTop")
        inner_div_locator = outer_div_locator.locator("div.c-card__item")
        table_locator = inner_div_locator.locator("> table").first
        tbody_locator = table_locator.locator("> tbody")
        metadata_tr_locator = tbody_locator.locator("> tr").nth(1)
        metadata_inner_td_locator = metadata_tr_locator.locator("> td")
        metadata_inner_table_locator = metadata_inner_td_locator.locator("> table")
        metadata_inner_tbody_locator = metadata_inner_table_locator.locator("> tbody")
        metadata_inner_tr_locator = metadata_inner_tbody_locator.locator("> tr")
        metadata_tds = metadata_inner_tr_locator.locator("> td[valign='top']")
        metadata_tds_count = await metadata_tds.count()
        for i in range(metadata_tds_count):
            td = metadata_tds.nth(i)
            table_rows = td.locator("tr")
            table_rows_count = await table_rows.count()
            for j in range(table_rows_count):
                table_row = table_rows.nth(j)
                table_row_cells = table_row.locator("td")
                if await table_row_cells.count() < 2:  # noqa: PLR2004
                    continue
                first_cell_inner_text = await table_row_cells.nth(0).inner_text()
                first_cell_inner_text_cleaned = first_cell_inner_text.replace("\xa0", "")
                second_cell_inner_text = await table_row_cells.nth(1).inner_text()
                second_cell_inner_text_cleaned = second_cell_inner_text.replace("\xa0", "")
                second_cell_inner_text_cleaned = second_cell_inner_text_cleaned.replace("\n", " ")
                if "Tax ID/SSN" in first_cell_inner_text_cleaned:
                    continue
                if first_cell_inner_text_cleaned and second_cell_inner_text_cleaned:
                    account_metadata[first_cell_inner_text_cleaned] = second_cell_inner_text_cleaned

        return account_metadata

    async def _retrieve_transactions(
        self, account_metadata: dict, starting_point: dict, *, use_starting_point: bool
    ) -> AsyncGenerator[RawDocumentTuple, None]:
        all_transactions_text = self.page.get_by_text("All Account Transactions")
        if await all_transactions_text.count() == 1 and await all_transactions_text.is_visible():
            await all_transactions_text.click()
            await asyncio.sleep(3)
        else:
            logger.error("Could not find 'All Account Transactions' text")
            raise CouldNotFindHTMLElementError

        if await self.page.get_by_text("No transactions found for the specified date range.").count() > 0:
            logger.info("No transactions found for this account", account_metadata=account_metadata)
            return

        table_locator = self.page.locator("table[id='tblNonRepTransHistory']")
        table_body = table_locator.locator("> tbody")
        table_rows = table_body.locator("> tr")
        table_rows_count = await table_rows.count()

        account_transaction_starting_idx = 0
        if starting_point and "account_transaction_idx" in starting_point and use_starting_point:
            logger.info("skipping to account transaction starting point", starting_point=starting_point)
            account_transaction_starting_idx = starting_point["account_transaction_idx"]
        # 3 table rows for each visual row
        for i in range(account_transaction_starting_idx, table_rows_count, 3):
            second_row = table_rows.nth(i + 1)
            details_a_tag = second_row.locator("a", has_text="Details")
            if await details_a_tag.count() == 1 and await details_a_tag.is_visible():
                await details_a_tag.click()
                await asyncio.sleep(3)
                logger.info(
                    "update_check_point",
                    checkpoint="account_transaction_idx",
                    account_transaction_idx=i,
                )
                await self.update_check_point({"account_transaction_idx": i})
                transaction_raw_metadata = await self._get_transaction_metadata()
                transaction_metadata = account_metadata | transaction_raw_metadata
                metadata = json.dumps(transaction_metadata)
                if await self.should_download_document(metadata):
                    doc = await self._download_single_trasaction(metadata)
                    if doc is not None:
                        yield doc
                return_to_transaction_history_button = self.page.locator("a", has_text="Return to Transaction History")
                await return_to_transaction_history_button.click()
                await asyncio.sleep(3)

    async def _get_transaction_metadata(self) -> dict[str, str]:
        transaction_metadata = {}
        main_content_div = self.page.locator("div.mainContent")
        card_content_div = main_content_div.locator("div.c-card__content").first
        section_header_content = card_content_div.locator("div[id='sectionHeaderContentDiv']")
        section_header_title = section_header_content.locator("div.transTitle")
        section_header_title_text = await section_header_title.inner_text()
        section_header_title_cleaned = section_header_title_text.split(":")[-1].strip().replace("\xa0", " ")
        transaction_metadata["Transaction Type"] = section_header_title_cleaned
        section_content_locator = card_content_div.locator("div.TextSm")
        section_content_divs = section_content_locator.locator("> div")
        section_content_divs_count = await section_content_divs.count()
        for i in range(section_content_divs_count):
            section_content_div = section_content_divs.nth(i)
            section_content_div_class = await section_content_div.get_attribute("class")
            if "dataColumn" in section_content_div_class:
                inner_divs = section_content_div.locator("> div")
                inner_divs_count = await inner_divs.count()
                for j in range(inner_divs_count):
                    inner_div = inner_divs.nth(j)
                    if await inner_div.locator("div").count() == 2:  # noqa: PLR2004
                        unit_div = inner_div.locator("div").nth(0)
                        value_div = inner_div.locator("div").nth(1)
                        unit_text = await unit_div.inner_text()
                        value_text = await value_div.inner_text()
                        unit_text_cleaned = unit_text.replace("\xa0", " ").replace(":", "").strip()
                        value_text_cleaned = value_text.replace("\xa0", " ").replace(":", "").strip()
                        if unit_text_cleaned and value_text_cleaned:
                            transaction_metadata[unit_text_cleaned] = value_text_cleaned

        return transaction_metadata

    async def _download_single_trasaction(self, metadata: json) -> RawDocumentTuple:
        download_button = self.page.locator("button[id='printButton']")

        async with self.context.expect_page() as new_page_info:
            await download_button.click()
            await asyncio.sleep(2)

        new_page = await new_page_info.value
        await new_page.wait_for_load_state()

        async def handle_route(route, request) -> None:  # noqa: ANN001
            global request_body  # noqa: PLW0603
            global response  # noqa: PLW0603
            response = await new_page.context.request.fetch(request)
            request_body = await response.body()
            await route.fulfill(
                headers={**(await request.all_headers()), "content-disposition": "attachment"},
                response=response,
            )

        await new_page.route("https://vision.ssnc.cloud/tf/Vision/HistoryDetailsPrePrintEvent", handle_route)
        await asyncio.sleep(2)
        done_button = new_page.locator("button", has_text="Done")
        await done_button.click()
        await asyncio.sleep(3)
        await new_page.close()

        if response.ok:
            doc_content = request_body
        else:
            logger.error("Could not download documnet", row_metadata=metadata)
            return None

        row_metadata = json.loads(metadata)
        doc_type = self._document_mapper(row_metadata["Transaction Type"])
        datetime_date = datetime.datetime.strptime(row_metadata["Confirm Date"], "%m/%d/%Y")  # noqa: DTZ007
        # Making official document name have .pdf extension
        doc_name = f"{row_metadata['Transaction Type']}_{row_metadata['Confirm Date']}.pdf"
        return RawDocumentTuple(
            name=doc_name,
            date=datetime_date,
            doc_type=doc_type,
            content=doc_content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    def _document_mapper(self, document_type: str | None) -> DocumentType | str:
        if document_type is None:
            return DocumentType.UNKNOWN

        mapping = {
            "statement": DocumentType.ACCOUNT_STATEMENT,
            "sub": DocumentType.TAX,
            "confirmation": DocumentType.INVESTMENT_UPDATE,
            "tax form": DocumentType.TAX,
            "tax fees": DocumentType.TAX,
            "tax": DocumentType.TAX,
            "distribution": DocumentType.DISTRIBUTION_NOTICE,
            "shares purchased": DocumentType.INVESTMENT_UPDATE,
            "reinvestment": DocumentType.INVESTMENT_UPDATE,
            "shares redeemed": DocumentType.DISTRIBUTION_NOTICE,
            "transfer in": DocumentType.CAPITAL_CALL,
            "equity investment": DocumentType.INVESTMENT_UPDATE,
            "dist/service/advisory": DocumentType.DISTRIBUTION_NOTICE,
            "revenue share payment": DocumentType.DISTRIBUTION_NOTICE,
        }

        for k, v in mapping.items():
            if k in document_type.lower():
                return v
        return DocumentType.UNKNOWN
