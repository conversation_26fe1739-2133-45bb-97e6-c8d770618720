import uuid

import structlog
from django.contrib.postgres.search import SearchVector
from webapp.models.documents import RawDocument
from webapp.models.extraction import DocChunk

from ml_app.core.ingestion.chunkers_and_parsers import ParserAndChunker
from ml_app.core.ingestion.core import BoundingBox, ParsedDocumentWithChunk
from ml_app.core.ingestion.embedder import Embedder

logger = structlog.get_logger(__name__)


class Ingestor:
    def __init__(self, parser_and_chunker: list[ParserAndChunker], embedders: list[Embedder]) -> None:
        self.parser_and_chunker = parser_and_chunker
        self.embedders = embedders

    def ingest(self, raw_document_pk: str) -> None:
        ingestion_id = uuid.uuid4()
        structlog.contextvars.bind_contextvars(ingestion_id=ingestion_id, raw_document_pk=raw_document_pk)
        raw_doc = RawDocument.objects.get(pk=raw_document_pk)
        prev_docs = list(raw_doc.doc_chunks.values_list("ingestion_id", flat=True))
        success = False
        for config in self.parser_and_chunker:
            try:
                parsed_doc_chunks = config.parse_and_chunk(raw_document_pk)
            except Exception:
                logger.exception("Error in parse and chunk")
                continue
            for parsed_doc_chunk in parsed_doc_chunks:
                embeddings = {}
                for embedder in self.embedders:
                    try:
                        embeddings[embedder.name()] = embedder.embed(parsed_doc_chunk.chunk)
                    except Exception:
                        logger.exception("Error in embedding")
                        continue
                try:
                    self._save(raw_doc, parsed_doc_chunk, embeddings, ingestion_id)
                    success = True
                except Exception:
                    logger.exception("Error in saving")
        if success:
            DocChunk.objects.filter(
                raw_document=raw_doc,
                ingestion_id__in=prev_docs,
            ).exclude(chunker_name="granular_ground_truth").delete()

    def _save(
        self,
        raw_doc: RawDocument,
        parsed_doc_chunk: ParsedDocumentWithChunk,
        embeddings: dict[str, list[float]],
        ingestion_id: uuid.UUID,
    ) -> None:
        logger.info("Saving chunk")
        chunk_kwargs: dict[str, list[float] | float] = embeddings

        bounding_box: BoundingBox | None = None
        if parsed_doc_chunk.chunk_bounding_box is not None:
            bounding_box = parsed_doc_chunk.chunk_bounding_box
        elif parsed_doc_chunk.parsed_document.bounding_box is not None:
            bounding_box = parsed_doc_chunk.parsed_document.bounding_box

        if bounding_box is not None:
            chunk_kwargs["bbox_xmin"] = bounding_box.xmin
            chunk_kwargs["bbox_ymin"] = bounding_box.ymin
            chunk_kwargs["bbox_xmax"] = bounding_box.xmax
            chunk_kwargs["bbox_ymax"] = bounding_box.ymax
            chunk_kwargs["has_bounding_box"] = True

        doc_chunk: DocChunk = DocChunk.objects.create(
            user=raw_doc.created_by,
            raw_document=raw_doc,
            content=parsed_doc_chunk.chunk,
            ingestion_id=ingestion_id,
            content_format=parsed_doc_chunk.parsed_document.format,
            chunker_name=parsed_doc_chunk.chunker_name,
            parser_name=parsed_doc_chunk.parsed_document.parser_name,
            page_number=parsed_doc_chunk.parsed_document.page_number,
            chunk_length=len(parsed_doc_chunk.chunk),
            chunk_idx=parsed_doc_chunk.chunk_idx,
            **chunk_kwargs,
        )
        doc_chunk.search_vector = SearchVector("content")
        doc_chunk.save()
