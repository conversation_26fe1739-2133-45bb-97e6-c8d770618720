import asyncio
import hashlib
import json
import math
from collections.abc import As<PERSON><PERSON><PERSON><PERSON>

import structlog
from bs4 import BeautifulSoup as bs  # noqa: N813
from dateutil import parser
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.get_logger(__name__)


@register_strategy
class RialtoCapitalPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "rialtocapital.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return []

    async def _enter_credentials(self, username: str, password: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError

        # Focus and fill the username
        await self.page.get_by_placeholder("User Name").click()
        await self.page.get_by_placeholder("User Name").fill(username)
        await asyncio.sleep(1)

        await self.page.get_by_placeholder("Password").click()
        await self.page.get_by_placeholder("Password").fill(password)
        await asyncio.sleep(1)
        await self.page.locator("input#user_terms_accepted").click()
        await asyncio.sleep(1)

        await self.page.locator("input[name='commit']").click()

    async def _check_has_otp(self) -> bool:
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _check_login_successful(self) -> bool:
        return await self.page.get_by_text("LOG OUT").count() > 0

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: C901, PLR0912, PLR0915
        await self.page.locator("a[href='/my-documents']").first.click()
        await self.page.wait_for_load_state("networkidle")

        soup = bs(markup=await self.page.content(), features="html.parser")
        tables = soup.select(".documentBlock")
        id2raw_doc_type = {}
        for table in tables:
            names = table.find_previous_sibling().select("h4")
            raw_doc_type = "Unknown" if len(names) != 1 else names[0].text
            if raw_doc_type == "Unknown":
                names = table.find_previous_sibling().select("h3")
                raw_doc_type = "Unknown" if len(names) != 1 else names[0].text
            id2raw_doc_type[table["id"]] = raw_doc_type
        for table_id, raw_doc_type in id2raw_doc_type.items():
            tables = self.page.locator(f"#{table_id}")
            table_length = await tables.count()
            if table_length == 1:
                selector = tables.locator("select[role='listbox']")
                options = await selector.locator("option").all()
                max_option = max([int(await option.get_attribute("value")) for option in options])
                await selector.select_option([str(max_option)])
                await asyncio.sleep(20)
            try:
                doc_num = int((await tables.locator(".ui-paging-info").text_content()).split(" of ")[1])
            except (ValueError, IndexError):
                logger.exception("Skipping table", table_id=table_id, raw_doc_type=raw_doc_type)
                continue
            expected_pages = math.ceil(doc_num / max_option)
            for i in range(1, expected_pages + 1):
                try:
                    doc_num = int((await tables.locator(".ui-paging-info").text_content()).split(" of ")[1])
                    expected_id_len = min(max_option, doc_num - (i - 1) * max_option)
                except (ValueError, IndexError):
                    logger.exception("Skipping table", table_id=table_id, raw_doc_type=raw_doc_type)
                    continue
                inputs = (
                    await self.page.locator(f"#{table_id}")
                    .locator("td")
                    .filter(has=self.page.locator(".ui-pg-input"))
                    .all()
                )
                for input_options in inputs:
                    text_content = await input_options.text_content()
                    try:
                        pages = int(text_content.split(" of ")[1])
                        if pages == expected_pages:
                            break
                    except (ValueError, IndexError):
                        pass
                input_box = input_options.locator("input")
                if i > 1:
                    await input_box.click()
                    await input_box.fill(str(i))
                    await input_box.press("Enter", delay=0.1)
                    await asyncio.sleep(20)
                potential_doc_tables = await tables.locator("table").all()
                for doc_table in potential_doc_tables:
                    ids = []
                    for row in await doc_table.locator("tr").all():
                        d_id = await row.get_attribute("id")
                        if d_id is not None:
                            ids.append(d_id)
                    if len(ids) == expected_id_len:
                        break
                for doc_id in ids:
                    downloaded_doc = await self._download_doc(doc_id, raw_doc_type)
                    if downloaded_doc is not None:
                        yield downloaded_doc

    async def _download_doc(self, doc_id: str, raw_doc_type: str) -> None:
        row = self.page.locator(f"tr[id='{doc_id}']")
        tds = await row.locator("td").all()
        raw_date = await tds[2].text_content()
        display_name = await tds[3].locator("span").first.text_content()
        metadata = json.dumps(
            {
                "Title": display_name,
                "Section": raw_doc_type,
                "Date": raw_date,
                "Document ID": doc_id,
            }
        )
        if not await self.should_download_document(metadata):
            return None
        await row.hover()
        async with self.page.expect_download() as download_info:
            await self.page.locator(f"tr[id='{doc_id}']").locator("a").click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = file_name.read_bytes()
        file_name.unlink()
        return RawDocumentTuple(
            name=download.suggested_filename,
            date=parser.parse(raw_date),
            doc_type=self._document_mapper(raw_doc_type),
            content=content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    def _document_mapper(self, document_type: str) -> str:
        return {
            "Capital Call Notices": DocumentType.CAPITAL_CALL,
            "Distribution Notices": DocumentType.DISTRIBUTION_NOTICE,
            "Tax Documents": DocumentType.TAX,
            "Statements of Partner's Capital": DocumentType.ACCOUNT_STATEMENT,
            "Investor Reports": DocumentType.INVESTMENT_UPDATE,
            "Financial Statements": DocumentType.INVESTMENT_UPDATE,
            "Subscription Documents": DocumentType.OTHER,
            "Fund Governing Documents": DocumentType.LEGAL,
            "Form ADV": DocumentType.OTHER,
            "Other Documents": DocumentType.OTHER,
            "Privacy Notice": DocumentType.OTHER,
        }.get(document_type, DocumentType.UNKNOWN)
