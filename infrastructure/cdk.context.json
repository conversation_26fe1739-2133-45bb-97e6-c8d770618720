{"availability-zones:account=************:region=us-east-1": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "ssm:account=************:parameterName=/bridge/infra/demo/NetworkStackOutputs/VpcId:region=us-east-1": "vpc-03caac4b4366d0277", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/AwsAccount:region=us-east-1": "************", "ssm:account=************:parameterName=/bridge/infra/demo/ClusterStackOutputs/ClusterName:region=us-east-1": "ClusterStack-ClusterEB0386A7-vDSkZi5UkxXN", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/EmailConfigurationSetName:region=us-east-1": "my-first-configuration-set", "ssm:account=************:parameterName=/bridge/infra/demo/SiteServiceStackOutputs/LoadBalancerName:region=us-east-1": "SiteSe-ALBAE-Og9fH0u96Vjn", "ssm:account=************:parameterName=/bridge/infra/demo/CeleryServiceStackOutputs/CeleryTaskRoleArn:region=us-east-1": "arn:aws:iam::************:role/CeleryServiceStack-CeleryTaskRoleFA47BBF2-CufGiQV4MX0w", "ssm:account=************:parameterName=/bridge/infra/demo/NetworkStackOutputs/VpcAvailabilityZones:region=us-east-1": "us-east-1a,us-east-1b", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/AwsRegion:region=us-east-1": "us-east-1", "ssm:account=************:parameterName=/bridge/infra/demo/ClusterStackOutputs/ExecutionRoleArn:region=us-east-1": "arn:aws:iam::************:role/ClusterStack-ExecutionRole605A040B-oFFSLUIk6MpV", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/EmailSendAddress:region=us-east-1": "<EMAIL>", "ssm:account=************:parameterName=/bridge/infra/demo/NetworkStackOutputs/VpcPublicSubnetIds:region=us-east-1": "subnet-093106ad2ef86bbde,subnet-01d5681ecbb7cecc8", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/DeploymentTarget:region=us-east-1": "demo", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/DBCredentialsSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-StorageStack-db-credentials-enc-Kwv21C", "ssm:account=************:parameterName=/bridge/infra/demo/NetworkStackOutputs/VpcPublicRouteTableIds:region=us-east-1": "rtb-05e5a1d890504db9a,rtb-017267a42cbb1b9e6", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/HostedZoneId:region=us-east-1": "Z05671591T7GRTZV7JAEQ", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/DBInstanceIds:region=us-east-1": "storagestack-dbinstancefromsnapshotwriter20b0fa79-0dhbqpi6skrj", "ssm:account=************:parameterName=/bridge/infra/demo/NetworkStackOutputs/VpcCidrBlock:region=us-east-1": "10.0.0.0/16", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/DomainName:region=us-east-1": "bridgeinvest.co", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/DBClusterArn:region=us-east-1": "arn:aws:rds:us-east-1:************:cluster:storagestack-dbinstancefromsnapshotab8e2103-frai5lruvlhf", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/DjangoSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-DjangoSecret-pt4puL", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/S3BucketName:region=us-east-1": "bridge-demo-userdata-bucket", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/BulkUploadsS3Path:region=us-east-1": "bulk_uploads/", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/RedisSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-StorageStack-redis-credentials-yqJOHX", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/EmailDocsS3Path:region=us-east-1": "email_documents/", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/RedisClusterName:region=us-east-1": "str1oiot73x8glb1", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/ProcessedDocsS3Path:region=us-east-1": "email_docs/", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/UserDataKmsArn:region=us-east-1": "arn:aws:kms:us-east-1:************:key/e3d95246-4ba4-44bd-b4c7-fdd6f47a2710", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/RetrievalLogsS3Path:region=us-east-1": "retrieval_logs/", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/RedisClusterPrimaryEndPointAddress:region=us-east-1": "master.str1oiot73x8glb1.0ga9ix.use1.cache.amazonaws.com", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/EmailS3Path:region=us-east-1": "inbound_emails/", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/RedisClusterPrimaryEndPointPort:region=us-east-1": "6379", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/VantaAuditorRoleArn:region=us-east-1": "arn:aws:iam::************:role/vanta-auditor", "ssm:account=************:parameterName=/bridge/infra/demo/StorageStackOutputs/RedisSecurityGroupId:region=us-east-1": "sg-090525312f0b3e20a", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/GitHubActionsRoleArn:region=us-east-1": "arn:aws:iam::************:role/ConfigStack-GitHubActionsRole4F1BBA26-Vf9PCMMYmDTK", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/MsftEmailOauthSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-Custom-MSFT-Oauth-7kVfy1", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/DevTwilioSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-Development-TwilioSecrets-vCr4s0", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/PythonContainerRepositoryName:region=us-east-1": "bridge-demo-python", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/UserBucketName:region=us-east-1": "bridge-demo-userdata-bucket", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/SftpBucketName:region=us-east-1": "bridge-demo-sftpdata-bucket", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/DatabaseName:region=us-east-1": "bridge_demo_db", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/UserDataKmsName:region=us-east-1": "bridge-demo-userdata-kms", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/EmailTopicName:region=us-east-1": "Inbound-Email-Topic-S3", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/EmailWebhookPath:region=us-east-1": "api/email-webhook/sns-webhook", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/EmailWebhookUsername:region=us-east-1": "bridge", "availability-zones:account=************:region=us-east-1": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "ssm:account=************:parameterName=/bridge/infra/prod/NetworkStackOutputs/VpcId:region=us-east-1": "vpc-01fc68732cd08ee8d", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/AwsAccount:region=us-east-1": "************", "ssm:account=************:parameterName=/bridge/infra/prod/ClusterStackOutputs/ClusterName:region=us-east-1": "ClusterStack-ClusterEB0386A7-ePJwWlnxiJda", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/EmailConfigurationSetName:region=us-east-1": "my-first-configuration-set", "ssm:account=************:parameterName=/bridge/infra/prod/SiteServiceStackOutputs/LoadBalancerName:region=us-east-1": "SiteSe-ALBAE-lgT8mt55yuIK", "ssm:account=************:parameterName=/bridge/infra/prod/CeleryServiceStackOutputs/CeleryTaskRoleArn:region=us-east-1": "arn:aws:iam::************:role/CeleryServiceStack-CeleryTaskRoleFA47BBF2-N4kj07nVco8t", "ssm:account=************:parameterName=/bridge/infra/prod/NetworkStackOutputs/VpcAvailabilityZones:region=us-east-1": "us-east-1a,us-east-1b", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/AwsRegion:region=us-east-1": "us-east-1", "ssm:account=************:parameterName=/bridge/infra/prod/ClusterStackOutputs/ExecutionRoleArn:region=us-east-1": "arn:aws:iam::************:role/ClusterStack-ExecutionRole605A040B-fjMQhtCKVpOx", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/EmailSendAddress:region=us-east-1": "<EMAIL>", "ssm:account=************:parameterName=/bridge/infra/prod/NetworkStackOutputs/VpcPublicSubnetIds:region=us-east-1": "subnet-091591ea7de0d2abd,subnet-0938662aeeecf374e", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/DeploymentTarget:region=us-east-1": "prod", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/DBCredentialsSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-StorageStack-db-credentials-enc-gzr16k", "ssm:account=************:parameterName=/bridge/infra/prod/NetworkStackOutputs/VpcPublicRouteTableIds:region=us-east-1": "rtb-0d1c12e514081c7bc,rtb-007645039d8c20e83", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/HostedZoneId:region=us-east-1": "Z05101163RY8ZZFJ9ECPS", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/DBInstanceIds:region=us-east-1": "storagestack-dbinstancefromsnapshotwriter20b0fa79-ge3jppeaw1ki", "ssm:account=************:parameterName=/bridge/infra/prod/NetworkStackOutputs/VpcCidrBlock:region=us-east-1": "10.0.0.0/16", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/DomainName:region=us-east-1": "app.bridgeinvest.io", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/DBClusterArn:region=us-east-1": "arn:aws:rds:us-east-1:************:cluster:storagestack-dbinstancefromsnapshotab8e2103-lqxjquzjm8bk", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/DjangoSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-DjangoSecret-JCT3u3", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/S3BucketName:region=us-east-1": "bridge-prod-userdata-bucket", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/BulkUploadsS3Path:region=us-east-1": "bulk_uploads/", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/RedisSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-StorageStack-redis-credentials-lKsAdD", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/EmailDocsS3Path:region=us-east-1": "email_documents/", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/RedisClusterName:region=us-east-1": "strndffch7fjqt7", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/ProcessedDocsS3Path:region=us-east-1": "email_docs/", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/UserDataKmsArn:region=us-east-1": "arn:aws:kms:us-east-1:************:key/7a8d0419-6a72-4771-9ba7-9ec32ba3c1f2", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/RetrievalLogsS3Path:region=us-east-1": "retrieval_logs/", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/RedisClusterPrimaryEndPointAddress:region=us-east-1": "master.strndffch7fjqt7.vtldh3.use1.cache.amazonaws.com", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/EmailS3Path:region=us-east-1": "inbound_emails/", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/RedisClusterPrimaryEndPointPort:region=us-east-1": "6379", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/VantaAuditorRoleArn:region=us-east-1": "arn:aws:iam::************:role/vanta-auditor", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/GitHubActionsRoleArn:region=us-east-1": "arn:aws:iam::************:role/ConfigStack-GitHubActionsRole4F1BBA26-71zxRqETdMkp", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/MsftEmailOauthSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-MsftEmailOauth-Zzbqd3", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/DevTwilioSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-Development-TwilioSecrets-C9gKNK", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/PythonContainerRepositoryName:region=us-east-1": "bridge-prod-python", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/UserBucketName:region=us-east-1": "bridge-prod-userdata-bucket", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/SftpBucketName:region=us-east-1": "bridge-prod-sftpdata-bucket", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/DatabaseName:region=us-east-1": "bridge_prod_db", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/UserDataKmsName:region=us-east-1": "bridge-prod-userdata-kms", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/EmailTopicName:region=us-east-1": "Inbound-Email-Topic-S3", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/EmailWebhookPath:region=us-east-1": "api/email-webhook/sns-webhook", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/EmailWebhookUsername:region=us-east-1": "bridge", "ssm:account=************:parameterName=/bridge/infra/prod/StorageStackOutputs/RedisSecurityGroupId:region=us-east-1": "sg-018ff5611ab98dc28", "ssm:account=************:parameterName=/bridge/infra/demo/ConfigStackOutputs/SlackSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-Slack-krOJ6v", "ssm:account=************:parameterName=/bridge/infra/prod/ConfigStackOutputs/SlackSecretArn:region=us-east-1": "arn:aws:secretsmanager:us-east-1:************:secret:Application-Slack-i5s1QX"}