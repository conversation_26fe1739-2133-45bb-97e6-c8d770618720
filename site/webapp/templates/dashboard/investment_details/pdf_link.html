{% load customtags static %}
{% comment %} 
args: title, has_been_viewed, processed_doc_pk
{% endcomment %}
{% if title %}
    <a href="{% url 'pdf_modal' %}{% urlparams processed_doc_pk=processed_doc_pk %}"
       target="_blank"
       title="{{ title }}"
       _="on click or auxclick trigger openedProcessedDocPdf(pk:'{{ processed_doc_pk }}')">
        <div class="w-full text-white flex items-center gap-1">
            <div class="w-8 h-8 flex justify-center items-center">
                <img src="{% static 'assets/images/pdf.svg' %}"
                     width="20"
                     height="20"
                     alt="pdf icon" />
            </div>
            {% if has_been_viewed %}
                {% alias "bg-white" as background %}
            {% else %}
                {% alias "bg-secondary-orange-beige font-semibold" as background %}
            {% endif %}
            <div class="pl-1.5 pr-0.5 py-0.5 {{ background }} rounded-md shadow border border-secondary-orange-beige flex justify-center items-center gap-1"
                 _="on openedProcessedDocPdf[pk=='{{ processed_doc_pk }}'] from elsewhere remove .bg-secondary-orange-beige remove .font-semibold add .bg-white">
                <div class="text-left text-[#616863] text-xs font-normal font-['Inter'] leading-[18px] truncate-text"
                     data-max-length="10">{{ title }}</div>
            </div>
        </div>
    </a>
{% else %}
    <div class="w-full text-white flex items-center gap-1">
        <div class="w-8 h-8 flex justify-center items-center">
            <img src="{% static 'assets/images/pdf.svg' %}"
                 width="20"
                 height="20"
                 alt="pdf icon" />
        </div>
        <div class="pl-1.5 pr-0.5 py-0.5 bg-white rounded-md shadow border border-secondary-orange-beige flex justify-center items-center gap-1">
            <div class="text-left text-[#616863] text-xs font-normal font-['Inter'] leading-[18px] select-none"
                 data-max-length="10">N/A</div>
        </div>
    </div>
{% endif %}
