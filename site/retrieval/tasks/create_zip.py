import datetime
import re
import tempfile
import zipfile
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor
from pathlib import Path

import boto3
import structlog
from celery import Task, shared_task
from django.conf import settings
from webapp.models import ProcessedDocument
from webapp.models.documents import DocumentType
from webapp.models.user import BridgeUser
from webapp.models.zip_object import Zip<PERSON>ob, ZipJobStatus

from retrieval.tasks.excel_export import get_excel_export_documents, get_summit_trail_excel_export_documents

logger = structlog.get_logger(__name__)


@shared_task(bind=True, track_started=True, soft_time_limit=100)
def process_zip_job_task(self: Task, user_id: str, docs: list[str], job_id: str, *, is_excel_export: bool) -> None:  # noqa: ARG001
    """Celery task to process the ZIP job."""
    try:
        user = BridgeUser.objects.get(id=user_id)

        zip_job = ZipJob.objects.for_user(user).get(pk=job_id)

        s3 = boto3.client("s3")
        zip_path_obj = create_zip_from_documents(user, docs, zip_job, s3, is_excel_export=is_excel_export)

        if not zip_path_obj:
            zip_job.status = ZipJobStatus.FAILED
            zip_job.save()
            return

        s3_key = f"pdf_zipfiles/{user.id}/{zip_job.pk!s}/{zip_path_obj.name}"
        s3_bucket = settings.AWS_STORAGE_BUCKET_NAME

        with zip_path_obj.open("rb") as zip_file:
            s3.put_object(
                Body=zip_file,
                Bucket=s3_bucket,
                Key=s3_key,
                ContentType="application/zip",
            )

        zip_job.status = ZipJobStatus.COMPLETED
        zip_job.s3_bucket = s3_bucket
        zip_job.s3_key = s3_key
        zip_job.exists_in_s3 = True
        zip_job.save()

        schedule_s3_deletion_task.apply_async(args=[s3_bucket, s3_key], countdown=180)

        zip_path_obj.unlink(missing_ok=True)

    except Exception:
        logger.exception("Error in ZIP job processing")
        try:
            zip_job = ZipJob.objects.get(job_id=job_id)
            zip_job.status = ZipJobStatus.FAILED
            zip_job.save()
        except Exception:
            logger.exception("Error updating ZIP job status")


@shared_task(bind=True, track_started=True)
def schedule_s3_deletion_task(self: Task, s3_bucket: str, s3_key: str) -> None:  # noqa: ARG001
    """Celery task to delete the file from S3 after a delay."""
    if not s3_key:
        logger.warning("Skipping deletion: S3 key is None or empty")
        return
    try:
        s3 = boto3.client("s3")
        s3.delete_object(Bucket=s3_bucket, Key=s3_key)
        logger.info("Deleted S3 file", s3_key=s3_key, s3_bucket=s3_bucket)
    except Exception:
        logger.exception("Failed to delete S3 file", s3_key=s3_key, s3_bucket=s3_bucket)
        raise


def create_unique_filename(base_filename: str, used_filenames: set) -> str:
    """Creates a unique filename by adding a counter suffix if needed."""
    base_path = Path(base_filename)
    stem, ext = base_path.stem, base_path.suffix

    unique_filename = base_filename
    counter = 1

    while unique_filename in used_filenames:
        unique_filename = f"{stem} {counter}{ext}"
        counter += 1

    if counter > 1:
        logger.info("Renaming zip file", base_filename=base_filename, unique_filename=unique_filename)

    return unique_filename


def sanitize_filename_part(s: str) -> str:
    return re.sub(r'[\\/*?:"<>|]', "_", s)


def make_download_list(
    user: BridgeUser, processed_doc_pks: list[str]
) -> list[tuple[int, str | None, str | None, str | None]]:
    used_filenames = set()
    res = []
    processed_docs = (
        ProcessedDocument.objects.for_user(user)
        .filter(pk__in=processed_doc_pks, is_visible=True, exists_in_s3=True)
        .select_related("line_item__investing_entity__client", "line_item__investment")
        .all()
    )
    for i, processed_doc in enumerate(processed_docs):
        if not processed_doc:
            res.append((i, None, None, None))
            continue
        client_name = sanitize_filename_part(processed_doc.line_item.investing_entity.client.legal_name)
        entity_name = sanitize_filename_part(processed_doc.line_item.investing_entity.legal_name)
        investment_name = sanitize_filename_part(processed_doc.line_item.investment.legal_name)
        doc_type = DocumentType(processed_doc.document_type).label
        effective_date = processed_doc.effective_date.strftime("%Y-%m-%d")

        structured_filename = (
            f"{client_name}/{entity_name}/{investment_name}/{doc_type}/{effective_date}/{processed_doc.name}"
        )
        unique_filename = create_unique_filename(structured_filename, used_filenames)
        used_filenames.add(unique_filename)
        res.append((i, unique_filename, processed_doc.s3_bucket, processed_doc.s3_key))
    return res


def create_zip_from_documents(  # noqa: C901
    user: BridgeUser, docs: list[str], zip_job: ZipJob, s3: any, *, is_excel_export: bool = False
) -> Path | None:
    """Fetches documents, validates them, and writes them into a secure temporary ZIP file."""
    successful_count = 0
    failed_count = 0
    total_count = len(docs)

    temp_dir = tempfile.mkdtemp()
    zip_folder = Path(temp_dir) / str(zip_job.pk)
    zip_folder.mkdir(parents=True, exist_ok=True)
    zip_name = "Bridge_export_" if is_excel_export else "Bridge_DocVault_"
    zip_path = zip_folder / f"{zip_name}{datetime.datetime.now(tz=datetime.UTC).strftime('%Y_%m_%d')}.zip"
    if is_excel_export:
        if user.organization.name == "Summit Trail":
            docs_to_download = get_summit_trail_excel_export_documents(user, docs)
        else:
            docs_to_download = get_excel_export_documents(user, docs)
    else:
        docs_to_download = make_download_list(user, docs)

    def download_file(tup: tuple[int, str | None, str | None, str | None]) -> tuple[int, str | None, str | None]:
        idx, unique_name, s3_bucket, s3_key = tup
        logger.info("Downloading", idx=idx, unique_name=unique_name, s3_bucket=s3_bucket, s3_key=s3_key)
        try:
            if not s3_key or not s3_bucket or not unique_name:
                logger.warning("Skipping download: S3 key or bucket is None or empty")
                return idx, unique_name, None
            response = s3.get_object(Bucket=s3_bucket, Key=s3_key)
            logger.info("Downloaded", idx=idx, unique_name=unique_name, s3_bucket=s3_bucket, s3_key=s3_key)
            return idx, unique_name, response["Body"].read()
        except Exception:
            logger.exception("Failed to download", idx=idx, unique_name=unique_name, s3_bucket=s3_bucket, s3_key=s3_key)
            return idx, unique_name, None

    try:
        with (
            zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zip_file,
            ThreadPoolExecutor(max_workers=5) as executor,
        ):
            for i, unique_filename, content in executor.map(download_file, docs_to_download):
                if content:
                    zip_file.writestr(unique_filename, content)
                    successful_count += 1
                else:
                    failed_count += 1
                progress = int((i + 1) / total_count * 100)
                zip_job.progress = progress
                zip_job.successful_document_count = successful_count
                zip_job.failed_document_count = failed_count
                zip_job.save()

        if successful_count == 0:
            zip_path.unlink(missing_ok=True)
            return None
    except Exception:
        logger.exception("Error creating ZIP file:")
        return None
    else:
        return zip_path
