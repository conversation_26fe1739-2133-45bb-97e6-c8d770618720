import React, { lazy, useState } from "react";
import type { BoxData } from "js/react/granular_ground_truth/types/pdfLabeler";
import {
  getColumnName,
  JSONSchema,
} from "js/react/granular_ground_truth/types/jsonSchema";
import LabelSelectorForm from "./LabelSelectorForm";
import LabelBox from "./LabelBox";
import { Target } from "lucide-react";
import {
  getSchemaProperties,
  removeIdFields,
} from "js/react/granular_ground_truth/lib/schemaEditor";
import Ajv, { JSONSchemaType } from "ajv";
import addFormats from "ajv-formats";
import { TargetSchemaProps } from "js/react/granular_ground_truth/components/TargetSchemaFiller/TargetSchemaInterface";
const TargetSchema = lazy(
  () =>
    import(
      "js/react/granular_ground_truth/components/TargetSchemaFiller/TargetSchema"
    ),
);

const ajv = new Ajv({
  strictSchema: "log",
});
addFormats(ajv);

const TargetSchemaObject: React.FC<TargetSchemaProps> = ({
  schema,
  path,
  labeledBoxes,
  targetSchema,
  hideLabels,
  overlays,
  currentPageNumber,
  onDeleteBox,
  onLabelBox,
  extractTextFromBoundingBox,
}) => {
  if (typeof schema !== "object") {
    return <></>;
  }

  const cleanedSchema = removeIdFields(schema);
  const validate = ajv.compile(cleanedSchema);

  const boxData = React.useRef<BoxData | null>(null);
  const [sboxData, setBoxData] = React.useState<BoxData | null>(null);
  const [change, setChange] = React.useState<boolean>(true);

  const onLabelBoxLocal = (boxes: BoxData[]) => {
    const box = boxes[boxes.length - 1];
    if (box.path && box.data) {
      const paths = box.path.split(".");
      const field = paths[paths.length - 1];
      setChange(!change);
      if (boxData.current) {
        const new_data = {
          ...boxData.current.data,
          [field]: box.data,
        };
        const new_box = {
          id: -1,
          pageNumber: -1,
          // TODO: merge bounding boxes.
          boundingBox: box.boundingBox,
          content: "",
          schema: schema,
          data: new_data,
          isValid: validate(new_data),
          isSaved: true,
          path: path,
        };

        boxData.current = new_box;
        boxes.push(new_box);
        onLabelBox(boxes);
        setBoxData(new_box);
      } else {
        const new_data = {
          [field]: box.data,
        };
        const new_box = {
          id: -1,
          pageNumber: -1,
          // TODO: merge bounding boxes.
          boundingBox: box.boundingBox,
          content: "",
          schema: schema,
          data: new_data,
          isValid: validate(new_data),
          // Should isSaved be true here?
          isSaved: true,
          path: path,
        };
        boxData.current = new_box;
        boxes.push(new_box);
        onLabelBox(boxes);
        setBoxData(new_box);
      }
    }
  };
  const onDeleteBoxLocal = (boxes: BoxData[]) => {
    if (onDeleteBox) {
      onDeleteBox(boxes);
    }
  };
  const properties = getSchemaProperties(schema);
  return (
    <div>
      {properties.map((property) => {
        return (
          <TargetSchema
            key={`${path}.${getColumnName(property.schema)}`}
            schema={property.schema}
            is_required={property.required}
            path={`${path}.${getColumnName(property.schema)}`}
            hideLabels={hideLabels}
            labeledBoxes={labeledBoxes}
            overlays={overlays}
            currentPageNumber={currentPageNumber}
            targetSchema={targetSchema}
            onDeleteBox={onDeleteBoxLocal}
            onLabelBox={onLabelBoxLocal}
            extractTextFromBoundingBox={extractTextFromBoundingBox}
          />
        );
      })}
    </div>
  );
};

export default TargetSchemaObject;
