#!/usr/bin/env -S uv run
import os
import sys
from pathlib import Path
from typing import Any

import click
import django
import structlog

sys.path.append("site")
os.environ["USE_LOCALHOST"] = "1"
os.environ["DJANGO_SETTINGS_MODULE"] = "bridge_project.settings"
os.environ["ENVIRONMENT"] = "dev"
django.setup()


from email_webhook.tasks.email_parse import (
    extract_attachments,
    extract_email_addresses,
    extract_security_code,
    get_email_from_s3,
    get_text_from_email_message,
    route_email,
)

logger = structlog.get_logger(__name__)


@click.group()
def cli() -> None:
    pass

def save_attachments(attachments: dict[str, dict[str, Any]], output_dir: str) -> dict[str, Path]:
    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(exist_ok=True, parents=True)

    saved_files:dict[str,Path] = {}

    for filename_raw, attachment_data in attachments.items():
        filename = filename_raw if filename_raw else f"attachment_{len(saved_files)}"

        # Clean filename to ensure it's valid
        safe_filename = "".join(c for c in filename if c.isalnum() or c in "._- ")

        # Ensure we don't overwrite existing files with same name
        file_path = Path(output_dir) / safe_filename
        counter = 1
        while file_path.exists():
            ext = file_path.suffix
            base = file_path.name.replace(file_path.suffix, "")
            file_path = Path(output_dir) / f"{base}_{counter}{ext}"
            counter += 1

        # Write attachment data to file
        with file_path.open("wb") as f:
            f.write(attachment_data["bytes"])

        saved_files[filename] = file_path

    return saved_files

@cli.command()
@click.option("--key", default=None, help="Wait for deployment to finish")
def parse_email(key: str | None) -> None:
    if key is None:
        logger.info("key is None, must be set, exiting")
        return
    full_key = f"inbound_emails/{key}"
    bucket = None
    if os.environ.get("AWS_PROFILE", "demo") == "demo":
        bucket = "bridge-demo-userdata-bucket"
    elif os.environ.get("AWS_PROFILE", "demo") == "prod":
        bucket = "bridge-prod-userdata-bucket"
    email_msg = get_email_from_s3(bucket, full_key)
    attachments = extract_attachments(email_msg)
    text = get_text_from_email_message(email_msg)
    emails = list(set(extract_email_addresses(str(email_msg)) + extract_email_addresses(text)))
    security_code = extract_security_code(text)
    logger.info("--" * 30)
    logger.info("Email", email=text)
    logger.info("--" * 30)
    logger.info("Attachment length", length_attachments=len(attachments))
    logger.info("Emails", emails=emails)
    logger.info("Security Code", security_code=security_code)
    # uncomment to save attachments saved_files = save_attachments(attachments, ".")
    route_email(full_key, bucket)


if __name__ == "__main__":
    parse_email()
