import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON><PERSON>ator
from pathlib import Path

import structlog
from dateutil import parser
from playwright.async_api import Locator
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.get_logger(__name__)


@register_strategy
class RevCapPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "revcap.co.uk" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return []

    async def _enter_credentials(self, username: str, password: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        username_box = (
            self.page.locator("div")
            .filter(has=self.page.get_by_text("Username"))
            .filter(has=self.page.locator("input"))
            .last
        )
        await username_box.locator("input[type='text']").fill(username)
        await asyncio.sleep(1)
        await username_box.locator("input[type='submit']").click()
        await asyncio.sleep(5)
        password_box = (
            self.page.locator("div")
            .filter(has=self.page.get_by_text("Password"))
            .filter(has=self.page.locator("input"))
            .last
        )
        await password_box.locator("input[type='password']").fill(password)
        await asyncio.sleep(1)
        await password_box.locator("input[type='submit']").click()
        await asyncio.sleep(5)

    async def _check_has_otp(self) -> bool:
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _check_login_successful(self) -> bool:
        return await self.page.get_by_text("LOG OUT").count() > 0

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        for dropdown_title in ["Fund information", "Fund archive"]:
            async for doc in self._retrieve_documents_from_drop_down(dropdown_title):
                yield doc

    async def _retrieve_documents_from_drop_down(self, dropdown_title: str) -> AsyncGenerator[RawDocumentTuple, None]:
        drop_down_trigger = self.page.locator("div.dropdown").filter(has=self.page.get_by_text(dropdown_title))
        await drop_down_trigger.click()
        await asyncio.sleep(1)
        dropdown_options = await drop_down_trigger.locator("a").all()
        await drop_down_trigger.click()
        await asyncio.sleep(1)
        for option in dropdown_options:
            await drop_down_trigger.click()
            await asyncio.sleep(1)
            await option.click()
            await asyncio.sleep(1)
            rows = await self._create_rows_enumerator()
            while rows:
                row_locator, metadata, ref_date, doc_name, raw_doc_type = rows.pop(0)
                if await self.should_download_document(metadata):
                    try:
                        doc = await self.__download_doc(
                            row_locator, metadata, ref_date, self.__document_mapper(doc_name, raw_doc_type)
                        )
                    except ValueError:
                        logger.exception("Skipping document", doc_name=doc_name)
                        await self.page.reload()
                        await asyncio.sleep(5)
                        continue
                    if doc is not None:
                        yield doc
                else:
                    logger.info("Skipping document", doc_name=doc_name)

    async def _create_rows_enumerator(self) -> list[tuple[Locator, str, datetime.datetime, str, str]]:
        fund_table = await self.page.locator("table.fundtable tr").all()
        fund_title = (await self.page.locator("h2.fundtitle").inner_text()).strip()
        fund_year = (await self.page.locator("h4.fundyear").inner_text()).strip()
        res = []
        heading = ""
        for row_locator in fund_table:
            if await row_locator.locator("a").count() > 0:
                cells = row_locator.locator("td")
                raw_doc_type = heading
                doc_name = (await cells.nth(0).inner_text()).strip()
                raw_date = (await cells.nth(1).inner_text()).strip()
                # TODO: There is a bug where the date always has the date of the retrieval?
                #       This portal doesn't give us the exact date as posted date, only the
                #       month and year, parser.parse autofills the date to the current date.
                #       This is desireable on a goforward basis, but not for backfills.
                ref_date = parser.parse(raw_date)
                doc_size = (await cells.nth(2).inner_text()).strip()
                file_img_src = (await cells.nth(3).locator("img").get_attribute("src")).strip()
                # file types seen on revcap: docx, pdf, mp3, mp4, m4v
                if file_img_src in [
                    "controls/imageembed.ashx?filename=pdf.gif",
                    "controls/imageembed.ashx?filename=docx.gif",
                ]:
                    metadata = json.dumps(
                        {
                            "Document Name": doc_name,
                            "Fund Name": fund_title,
                            "Fund Year": fund_year,
                            "Ref Date": raw_date,
                            "Document Size": doc_size,
                            "Document Type": raw_doc_type,
                        }
                    )
                    res.append((row_locator, metadata, ref_date, doc_name, raw_doc_type))
            elif await row_locator.locator("td.heading").count() > 0:
                heading = (await row_locator.locator("td.heading").inner_text()).strip()
        return res

    async def __download_doc(
        self, row_locator: Locator, metadata: str, ref_date: datetime.datetime, doc_type: str
    ) -> RawDocumentTuple:
        await asyncio.sleep(1)
        async with self.page.expect_download() as download_info:
            await row_locator.locator("a").nth(0).click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() == ".pdf" or file_name.suffix.lower() == ".docx":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.error("Unsupported file format", file_name_suffix=file_name.suffix)
            raise ValueError
        file_name.unlink()
        return RawDocumentTuple(
            name=download.suggested_filename,
            date=ref_date,
            doc_type=doc_type,
            content=content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    def __document_mapper(self, doc_name: str, doc_type: str) -> str:
        if "Tax Rules Note" in doc_name:
            return DocumentType.TAX
        return {
            "Audited Accounts": DocumentType.INVESTMENT_UPDATE,
            "Draft NAVs": DocumentType.ACCOUNT_STATEMENT,
            "Other": DocumentType.OTHER,
            "Quarterly Reports": DocumentType.INVESTMENT_UPDATE,
            "Summary Sheets – Completed Transactions": DocumentType.INVESTMENT_UPDATE,  # noqa: RUF001
        }.get(doc_type, DocumentType.UNKNOWN)
