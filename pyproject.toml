[project]
name = "bridge-python"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "babel>=2.14.0",
    "beautifulsoup4>=4.12.3",
    "boto3>=1.35.59",
    "celery>=5.4.0",
    "click>=8.1.7",
    "cryptography>=43.0.3",
    "django-allauth>=65.2.0",
    "django-axes>=7.0.0",
    "django-browser-reload>=1.17.0",
    "django-celery-results>=2.5.1",
    "django-debug-toolbar>=4.4.6",
    "django-extensions>=3.2.3",
    "django-health-check>=3.18.3",
    "django-hijack>=3.7.0",
    "django-htmx>=1.21.0",
    "django-ninja>=1.3.0",
    "django-phonenumber-field[phonenumberslite]>=8.0.0",
    "django-tailwind>=3.8.0",
    "django-widget-tweaks>=1.5.0",
    "django==5.2.4",
    "gunicorn>=23.0.0",
    "msal>=1.31.0",
    "opencv-python-headless>=*********",
    "openpyxl>=3.1.5",
    "pandas>=2.2.3",
    "playwright>=1.48.0",
    "psycopg-binary>=3.2.3",
    "psycopg-pool>=3.2.4",
    "psycopg>=3.2.3",
    "pycryptodome>=3.21.0",
    "pyotp>=2.9.0",
    "redis>=5.2.0",
    "requests>=2.32.4",
    "watchdog>=6.0.0",
    "whitenoise>=6.8.2",
    "django-celery-beat>=2.8.1",
    "django-ses>=4.3.1",
    "qreader>=3.14",
    "pyqrcode>=1.2.1",
    "pypng>=0.20220715.0",
    "torch==2.7.1",
    "torchvision==0.22.1",
    "twilio>=8.0.0",
    "django-structlog>=9.0.1",
    "rich>=13.9.4",
    "colorama>=0.4.6",
    "langchain>=0.2.3",
    "aiofiles>=24.1.0",
    "httpx>=0.28.1",
    "django-redis>=5.4.0",
    "python-redis-lock[django]>=4.0.0",
    "langchain-aws>=0.2.18",
    "pymupdf>=1.25.5",
    "aenum>=3.1.16",
    "python-gnupg>=0.5.4",
    "langgraph>=0.4.1",
    "patchright>=1.52.4",
    "pgvector>=0.4.1",
    "camelot-py>=1.0.0",
    "langchain-experimental>=0.3.4",
    "jsonschema>=4.23.0",
    "filetype>=1.2.0",
]

[tool.djlint]
extend_exclude="node_modules,results"
profile="django"
format_js=true

[tool.ruff]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
    # django specific
    "migrations",
    "manage.py",
    "*.ipynb",
]

# match black
line-length = 120

target-version = "py312"

[tool.ruff.lint]
select = [
    "ALL", # include all the rules, including new ones
]
ignore = [
    #### specific rules
    "COM812", # Duplicative, applied already with formatting.
    "D100",   # ignore missing docs
    "D101",
    "D102",
    "D103",
    "D104",
    "D105",
    "D106",
    "D107",
    "D200",    # end docstring section
    "D205", # blank line after summary
    "D212", # multi line summary first line
    "D400", # ends in period
    "D401", # non imperitive mood
    "D415", # ends in punctuation
    "D203",   # no-blank-line-before-class
    "E402",   # false positives for local imports
    "ISC001", # Doesnt work well with black formatting
    "TRY003", # external messages in exceptions are too verbose
    "TD002", # missing TODO author, use git blame
    "TD003", # missing TODO link, just comment stuff
    "FIX002", # too verbose descriptions of to-dos
    "FIX004", # I like the HACK tag
    "EXE003", # We should SHEBANG with uv
    "PGH004", # allow us to diable for whole files lol
    "PLC0415", # allows us to have relative imports
]

[tool.ruff.lint.per-file-ignores]
"site/webapp/tests/**/*test*.py" = [
    "ANN001",  # ignore type complaints
    "S101",  # Ignore assert statements
    "PLR2004", #magic values
    "ARG001", # unused argument, sometimes needs kwargs in functions
    "ARG002", # unused argument, needed for pytest fixtures.
    "DJ008", # model validation not needed in tests
]
"site/webapp/tests/**/conftest.py" = [
    "ANN001",  # ignore type complaints
    "S101",  # Ignore assert statements
]

[tool.black]
line-length = 120
target-version = ["py312"]

[tool.bandit]
exclude_dirs = [
    ".venv",
    "node_modules",
    "*/tests/**/*test*.py",
    "*/tests/**/conftest.py",
    "*/tests/conftest.py",
    "cdk.out",
    "*/_test*.py",
]

[dependency-groups]
dev = [
    "bandit>=1.8.0",
    "boto3>=1.35.59",
    "boto3-stubs[full]>=1.35.83",
    "celery-types>=0.22.0",
    "click>=8.1.7",
    "django-stubs[compatible-mypy]>=5.1.1",
    "djlint>=1.36.3",
    "freezegun>=1.5.1",
    "mypy>=1.13.0",
    "pandas-stubs>=2.2.3.241126",
    "pytest>=8.0.0",
    "pytest-django>=4.8.0",
    "ruff>=0.12.0",
    "types-beautifulsoup4>=4.12.0.20241020",
    "types-openpyxl>=3.1.5.20241126",
    "types-python-dateutil>=2.9.0.20241206",
    "types-requests>=2.32.0.20241016",
    "ipykernel>=6.29.5",
    "notebook>=7.3.1",
    "jupyter>=1.1.1",
    "pyperclip>=1.9.0",
    "pyngrok>=7.2.3",
    "pyright>=1.1.396",
    "mlflow>=2.22.0",
    "datamodel-code-generator>=0.31.2",
]

[tool.uv]
default-groups = []
required-version = ">=0.7.3"
environments = [
    "sys_platform == 'darwin'",
    "sys_platform == 'linux'",
]

[tool.uv.sources]
torch = [
    { index = "pytorch-cpu" },
]
torchvision = [
    { index = "pytorch-cpu" },
]

[[tool.uv.index]]
name = "pytorch-cpu"
url = "https://download.pytorch.org/whl/cpu"
explicit = true

[tool.mypy]
plugins = ["mypy_django_plugin.main"]
mypy_path = "./site"

[tool.django-stubs]
django_settings_module = "bridge_project.settings.test"

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "bridge_project.settings.test"
python_files = ["test_*.py"]
addopts = """
    -v
    --reuse-db
    --disable-warnings
    --no-migrations
    --strict-markers
    --tb=short
"""
testpaths = ["site"]
markers = [
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
    "e2e: marks tests as end-to-end tests",
]
