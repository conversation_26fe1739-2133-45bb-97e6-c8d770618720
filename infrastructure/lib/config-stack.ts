import { Construct } from "constructs";
import * as cdk from "aws-cdk-lib";
import { ConfigStackOutputs, SharedProps, SharedStack } from "./schema";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import * as iam from "aws-cdk-lib/aws-iam";

export class ConfigStack extends SharedStack {
  constructor(scope: Construct, id: string, props: SharedProps) {
    super(scope, id, props);

    const githubOidcProvider = new iam.CfnOIDCProvider(
      this,
      "GithubOidcProvider",
      {
        url: "https://token.actions.githubusercontent.com",
        clientIdList: ["sts.amazonaws.com"],
        thumbprintList: [
          "6938fd4d98bab03faadb97b34396831e3780aea1",
          "1c58a3a8518e8759bf075b76b750d4f2df264fcd",
        ],
      },
    );

    // Define the GitHub Actions role
    const githubActionsRole = new iam.Role(this, "GitHubActionsRole", {
      assumedBy: new iam.WebIdentityPrincipal(
        `arn:aws:iam::${this.account}:oidc-provider/token.actions.githubusercontent.com`,
      ).withConditions({
        StringEquals: {
          "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
        },
        StringLike: {
          "token.actions.githubusercontent.com:sub":
            "repo:BridgeInvestTech/bridge-python:*",
        },
      }),
      description: "Role for GitHub Actions to deploy CDK stacks",
    });

    // Define a Vanta role
    // Managed Policy
    const vantaAdditionalPermissionsPolicy = new iam.ManagedPolicy(
      this,
      "VantaAdditionalPermissionsPolicy",
      {
        managedPolicyName: "VantaAdditionalPermissions",
        statements: [
          new iam.PolicyStatement({
            effect: iam.Effect.DENY,
            actions: [
              "datapipeline:EvaluateExpression",
              "datapipeline:QueryObjects",
              "rds:DownloadDBLogFilePortion",
            ],
            resources: ["*"],
          }),
        ],
      },
    );

    // IAM Role
    const vantaAuditorRole = new iam.Role(this, "VantaAuditorRole", {
      roleName: "vanta-auditor",
      assumedBy: new iam.PrincipalWithConditions(
        new iam.ArnPrincipal("arn:aws:iam::956993596390:role/scanner"),
        {
          StringEquals: {
            "sts:ExternalId": "7E8A0CDC2061C55",
          },
        },
      ),
      path: "/",
      maxSessionDuration: cdk.Duration.hours(1),
      managedPolicies: [
        iam.ManagedPolicy.fromManagedPolicyArn(
          this,
          "SecurityAuditPolicy",
          "arn:aws:iam::aws:policy/SecurityAudit",
        ),
        vantaAdditionalPermissionsPolicy,
      ],
    });

    // // Attach the necessary policies
    // TODO: scope this down minimally
    githubActionsRole.addManagedPolicy(
      iam.ManagedPolicy.fromAwsManagedPolicyName("AdministratorAccess"),
    );

    var secretArn = "";
    if (props.deploymentTarget == "prod") {
      secretArn =
        "arn:aws:secretsmanager:us-east-1:180294215839:secret:Application-MsftEmailOauth-Zzbqd3";
    } else if (props.deploymentTarget == "demo") {
      secretArn =
        "arn:aws:secretsmanager:us-east-1:654654313761:secret:Application-Custom-MSFT-Oauth-7kVfy1";
    } else {
      throw new Error("Invalid deployment target");
    }
    const msftOauthSecret = secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "MsftOauthSecret",
      secretArn,
    );

    var secretSlackArn = "";
    if (props.deploymentTarget == "prod") {
      secretSlackArn =
        "arn:aws:secretsmanager:us-east-1:180294215839:secret:Application-Slack-i5s1QX";
    } else if (props.deploymentTarget == "demo") {
      secretSlackArn =
        "arn:aws:secretsmanager:us-east-1:654654313761:secret:Application-Slack-krOJ6v";
    } else {
      throw new Error("Invalid deployment target");
    }
    const slackWebhookSecret = secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "SlackWebhookSecret",
      secretSlackArn,
    );

    var devTwilioSecretArn = "";
    if (props.deploymentTarget == "prod") {
      devTwilioSecretArn =
        "arn:aws:secretsmanager:us-east-1:180294215839:secret:Application-Development-TwilioSecrets-C9gKNK";
    } else if (props.deploymentTarget == "demo") {
      devTwilioSecretArn =
        "arn:aws:secretsmanager:us-east-1:654654313761:secret:Application-Development-TwilioSecrets-vCr4s0";
    } else {
      throw new Error("Invalid deployment target");
    }
    const devTwilioSecret = secretsmanager.Secret.fromSecretCompleteArn(
      this,
      "DevTwilioSecret",
      devTwilioSecretArn,
    );

    const djangoSecret = new secretsmanager.Secret(this, "DjangoSecret", {
      secretName: "Application-DjangoSecret",
      description: "Django Secret Key",
      generateSecretString: {
        secretStringTemplate: JSON.stringify({}),
        generateStringKey: "password",
        passwordLength: 64,
      },
    });

    new ConfigStackOutputs({
      // Shared Inputs
      AwsAccount: props.awsAccount,
      AwsRegion: props.awsRegion,
      DeploymentTarget: props.deploymentTarget,
      HostedZoneId: props.hostedZoneId,
      DomainName: props.domainName,

      // Application Config
      DjangoSecretArn: djangoSecret.secretArn,
      BulkUploadsS3Path: "bulk_uploads/",
      EmailDocsS3Path: "email_documents/",
      ProcessedDocsS3Path: "email_docs/",
      RetrievalLogsS3Path: "retrieval_logs/",

      // Shared w Email Stack
      EmailS3Path: "inbound_emails/",

      // External Roles and Secrets Stack Inputs
      VantaAuditorRoleArn: vantaAuditorRole.roleArn,
      GitHubActionsRoleArn: githubActionsRole.roleArn,
      MsftEmailOauthSecretArn: msftOauthSecret.secretArn,
      DevTwilioSecretArn: devTwilioSecret.secretArn,
      SlackSecretArn: slackWebhookSecret.secretArn,

      // Cluster Stack Inputs
      PythonContainerRepositoryName: `bridge-${props.deploymentTarget}-python`,

      // Storage Stack Inputs
      SftpBucketName: `bridge-${props.deploymentTarget}-sftpdata-bucket`,
      UserBucketName: `bridge-${props.deploymentTarget}-userdata-bucket`,
      DatabaseName: `bridge_${props.deploymentTarget}_db`,
      UserDataKmsName: `bridge-${props.deploymentTarget}-userdata-kms`,

      // Email Stack Inputs
      EmailTopicName: "Inbound-Email-Topic-S3",
      EmailWebhookPath: "api/email-webhook/sns-webhook",
      EmailWebhookUsername: "bridge",
    }).toCdkOutput(this);
  }
}
