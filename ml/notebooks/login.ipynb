{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"USE_LOCALHOST\"] = \"1\"\n", "os.environ[\"AWS_PROFILE\"] = \"prod\"\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "\n", "import importlib\n", "\n", "import cracker_tk as tk\n", "\n", "importlib.reload(tk)\n", "tk.setup_django()\n", "\n", "import asyncio\n", "import traceback\n", "\n", "from webapp.models.portal import MergedPortalCredential, Portal\n", "from webapp.models.user import BridgeUser\n", "\n", "importlib.reload(tk)\n", "from IPython.display import display  # noqa: A004\n", "from ipywidgets import Button, Text\n", "\n", "if \"shared_values\" in locals() and \"pw\" in shared_values:  # noqa: F821\n", "    await shared_values[\"pw\"].cleanup()  # noqa: F821\n", "    del shared_values[\"pw\"]  # noqa: F821\n", "\n", "shared_values = {}\n", "\n", "\n", "def exception_decorator(func: callable) -> callable:\n", "    async def wrapper(*args, **kwargs):  # noqa: ANN002, ANN202, ANN003\n", "        importlib.reload(tk)\n", "        if \"pw\" not in shared_values:\n", "            display(\"No pw object found, get_line_item first\")\n", "            return\n", "        try:\n", "            await func(*args, **kwargs)\n", "        except Exception:\n", "            display(traceback.format_exc())\n", "            raise\n", "\n", "    return wrapper\n", "\n", "\n", "USERNAME = \"<EMAIL>\"\n", "PORTAL = \"Brookfield (IV and VI)\"\n", "\n", "async def get_line_item(_: <PERSON><PERSON>, *, cold: bool) -> None:\n", "    display(type(_))\n", "    try:\n", "        importlib.reload(tk)\n", "        if \"pw\" in shared_values:\n", "            display(\"cleaning\")\n", "            if shared_values[\"pw\"].has_init:\n", "                await shared_values[\"pw\"].cleanup()\n", "            del shared_values[\"pw\"]\n", "        # TODO: <PERSON>KE THIS GENERIC AND EASY TO INPUT\n", "        # TODO: add OTP button\n", "        # TODO: rename line items.\n", "        user = BridgeUser.objects.get(email=USERNAME)\n", "        portal = Portal.objects.get(name=PORTAL, organization=user.organization)\n", "        merged_portal_credential = MergedPortalCredential.objects.for_user(user).filter(portal=portal).first()\n", "        shared_values[\"pw\"] = tk.<PERSON>rack<PERSON>lay<PERSON>(mpc=merged_portal_credential, cold=cold)\n", "    except Exception:\n", "        display(traceback.format_exc())\n", "        raise\n", "\n", "\n", "get_line_item_cold_button = <PERSON>ton(description=\"Get Line Item (Cold)\")\n", "get_line_item_cold_button.on_click(lambda x: asyncio.create_task(get_line_item(x, cold=True)))\n", "\n", "\n", "get_line_item_hot_button = <PERSON>ton(description=\"Get Line Item (Hot)\")\n", "get_line_item_hot_button.on_click(lambda x: asyncio.create_task(get_line_item(x, cold=False)))\n", "\n", "\n", "@exception_decorator\n", "async def launch_playwright(_: <PERSON><PERSON>) -> None:\n", "    if shared_values[\"pw\"].has_init:\n", "        await shared_values[\"pw\"].cleanup()\n", "    await shared_values[\"pw\"].init()\n", "    await shared_values[\"pw\"].goto_portal()\n", "\n", "\n", "launch_portal_button = Button(description=\"Launch Portal\")\n", "launch_portal_button.on_click(lambda x: asyncio.create_task(launch_playwright(x)))\n", "\n", "\n", "@exception_decorator\n", "async def cleanup_playwright(_: <PERSON><PERSON>) -> None:\n", "    await shared_values[\"pw\"].cleanup()\n", "    del shared_values[\"pw\"]\n", "\n", "\n", "cleanup_portal_button = Button(description=\"Cleanup Portal\")\n", "cleanup_portal_button.on_click(lambda x: asyncio.create_task(cleanup_playwright(x)))\n", "\n", "\n", "@exception_decorator\n", "async def save_storage_state(_: <PERSON><PERSON>) -> None:\n", "    await shared_values[\"pw\"].save_storage_state()\n", "\n", "\n", "save_storage_state_button = Button(description=\"Save Storage State\")\n", "save_storage_state_button.on_click(lambda x: asyncio.create_task(save_storage_state(x)))\n", "\n", "\n", "@exception_decorator\n", "async def click_last_action(_: <PERSON><PERSON>) -> None:\n", "    await shared_values[\"pw\"].click_last_action()\n", "\n", "\n", "click_last_action_button = Button(description=\"Click Last Action\")\n", "click_last_action_button.on_click(lambda x: asyncio.create_task(click_last_action(x)))\n", "\n", "fill_text = Text(\n", "    value=None,\n", "    placeholder=\"Type something\",\n", "    description=\"Fill Text:\",\n", "    disabled=False,\n", ")\n", "\n", "\n", "@exception_decorator\n", "async def fill_last_action(_: <PERSON><PERSON>) -> None:\n", "    await shared_values[\"pw\"].fill_last_action(fill_text.value)\n", "\n", "\n", "fill_last_action_button = Button(description=\"Fill Last Action\")\n", "fill_last_action_button.on_click(lambda x: asyncio.create_task(fill_last_action(x)))\n", "\n", "\n", "@exception_decorator\n", "async def fill_last_action_username(_: <PERSON><PERSON>) -> None:\n", "    await shared_values[\"pw\"].fill_last_action(shared_values[\"pw\"].get_username())\n", "\n", "\n", "fill_last_action_username_button = <PERSON><PERSON>(description=\"Fill Last Action Username\")\n", "fill_last_action_username_button.on_click(lambda x: asyncio.create_task(fill_last_action_username(x)))\n", "\n", "\n", "@exception_decorator\n", "async def fill_last_action_password(_: <PERSON><PERSON>) -> None:\n", "    await shared_values[\"pw\"].fill_last_action(shared_values[\"pw\"].get_password())\n", "\n", "\n", "fill_last_action_password_button = Button(description=\"Fill Last Action Password\")\n", "fill_last_action_password_button.on_click(lambda x: asyncio.create_task(fill_last_action_password(x)))\n", "\n", "@exception_decorator\n", "async def fill_last_action_mfa(_: <PERSON><PERSON>) -> None:\n", "    await shared_values[\"pw\"].fill_last_action(shared_values[\"pw\"].get_mfa())\n", "\n", "\n", "fill_last_action_mfa_button = Button(description=\"Fill Last Action MFA\")\n", "fill_last_action_mfa_button.on_click(lambda x: asyncio.create_task(fill_last_action_mfa(x)))\n", "\n", "\n", "@exception_decorator\n", "async def silence(_: <PERSON><PERSON>) -> None:\n", "    await shared_values[\"pw\"].silence()\n", "\n", "\n", "silence_button = Button(description=\"SILENCE!!!\")\n", "silence_button.on_click(lambda x: asyncio.create_task(silence(x)))\n", "\n", "import pyperclip\n", "\n", "\n", "@exception_decorator\n", "async def copy_username_to_clipboard(_: <PERSON><PERSON>) -> None:\n", "    pyperclip.copy(shared_values[\"pw\"].get_username())\n", "\n", "\n", "copy_username_button = <PERSON>ton(description=\"Copy Username to Clipboard\")\n", "copy_username_button.on_click(lambda x: asyncio.create_task(copy_username_to_clipboard(x)))\n", "\n", "\n", "@exception_decorator\n", "async def copy_password_to_clipboard(_: <PERSON><PERSON>) -> None:\n", "    pyperclip.copy(shared_values[\"pw\"].get_password())\n", "\n", "\n", "copy_password_button = Button(description=\"Copy Password to Clipboard\")\n", "copy_password_button.on_click(lambda x: asyncio.create_task(copy_password_to_clipboard(x)))\n", "\n", "@exception_decorator\n", "async def copy_mfa_to_clipboard(_: <PERSON><PERSON>) -> None:\n", "    pyperclip.copy(shared_values[\"pw\"].get_mfa())\n", "\n", "\n", "copy_mfa_button = Button(description=\"Copy MFA to Clipboard\")\n", "copy_mfa_button.on_click(lambda x: asyncio.create_task(copy_mfa_to_clipboard(x)))\n", "\n", "\n", "@exception_decorator\n", "async def mark_last_action(_: <PERSON><PERSON>) -> None:\n", "    await shared_values[\"pw\"].mark_last_action()\n", "\n", "\n", "mark_last_action_button = Button(description=\"Mark Last Action\")\n", "mark_last_action_button.on_click(lambda x: asyncio.create_task(mark_last_action(x)))\n", "\n", "display(\n", "    fill_text,\n", "    get_line_item_cold_button,\n", "    get_line_item_hot_button,\n", "    launch_portal_button,\n", "    save_storage_state_button,\n", "    mark_last_action_button,\n", "    fill_last_action_button,\n", "    # Send User Email.\n", "    fill_last_action_username_button,\n", "    fill_last_action_password_button,\n", "    silence_button,\n", "    cleanup_portal_button,\n", "    copy_username_button,\n", "    copy_password_button,\n", "    copy_mfa_button,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CHnage this to the user / portal you want to test\n", "\n", "user = BridgeUser.objects.get(email=\"<EMAIL>\")\n", "portal = Portal.objects.get(name=\"State Street Corporations\", organization=user.organization)\n", "# Possible there are multiple merged portal credentials for the same portal and organization.\n", "# Some might not work, so you might need to try a few.\n", "# Please mark those as inactive in the admin panel.\n", "merged_portal_credential = MergedPortalCredential.objects.for_user(user).filter(portal=portal).first()"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_portal_credential.get_otp()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["importlib.reload(tk)\n", "line_items_raw = tk.get_line_items(**tk.get_hardcoded_line_items())\n", "len(line_items_raw)\n", "li = tk.format_line_items(line_items_raw)\n", "len(li)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["line_items_raw = tk.get_line_items(investment__legal_name=\"NB Secondary Opportunities Fund V, LP\")\n", "len(line_items_raw)\n", "li = tk.format_line_items(line_items_raw)\n", "{i.investing_entity for i in li}\n", "len(li)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["portals = [x for x in li if x.portal_url != \"None\"]\n", "\n", "display(len(portals))\n", "sorted(\n", "    {\n", "        (x.portal_url, x.client, x.investing_entity, x.investment, x.portal_username)\n", "        for x in portals\n", "        if x.portal_username is not None\n", "    },\n", "    key=lambda x: x[0],\n", ")\n", "sorted({(x.portal_url, x.portal_username, x.get_password()) for x in portals}, key=lambda x: x[0])\n", "sorted({(x.portal_url) for x in portals if x.portal_username is not None}, key=lambda x: x[0])\n", "{x.portal_url: x.investment for x in portals if x.portal_username is not None}\n", "{x.portal_url: x.investing_entity for x in portals if x.portal_username is not None}\n", "{x.portal_url: x.client for x in portals if x.portal_username is not None}"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}