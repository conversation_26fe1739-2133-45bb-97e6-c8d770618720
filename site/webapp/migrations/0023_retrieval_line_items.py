# Generated by Django 5.1.7 on 2025-03-14 20:42

from django.db import migrations, models
import structlog
logger = structlog.get_logger(__name__)
class Migration(migrations.Migration):
    atomic = True

    dependencies = [
        ('webapp', '0022_alter_lineitem_merged_portal_credential_and_more'),
    ]

    def backfill_line_items_and_retrievals(apps, schema_editor):
        Retrieval = apps.get_model('webapp', 'retrieval')
        retrieval_ct = 0
        li_ct = 0
        for r in Retrieval.objects.all():
            if r.merged_portal_credential is None:
                logger.warning(f"Retrieval {r.id} has no merged portal credential, skipping")
                continue
            lis = r.merged_portal_credential.line_items.all()
            r.line_items.set(lis)
            r.save()
            li_ct += lis.count()
            retrieval_ct += 1
        logger.info("Backfilled", retrievals=retrieval_ct, line_items=li_ct)


    def reverse_func(apps, schema_editor):
        pass

    operations = [
        migrations.AddField(
            model_name='retrieval',
            name='line_items',
            field=models.ManyToManyField(blank=True, related_name='retrievals', to='webapp.lineitem'),
        ),
        migrations.RunPython(backfill_line_items_and_retrievals, reverse_func),
    ]
