{% extends "admin/base_site.html" %}
{% load customtags %}
{% block title %}
    Ground Truth Labeler
{% endblock title %}
{% block content %}
    <style>
    .label_header {
        display:flex;
        flex-direction:row;
        width:100%;
    }
    .label_header a {
        padding: 10px;
    }
    .push_right {
        margin-left:auto;
    }
    td {
        resize: both;
        overflow: auto;
    }
    .pdf_and_form{
        display:flex;
        flex-direction:row;
        width:100%;
    }
    .pdf{
        width:50%;
        height:1080px;
        padding:10px;
        border:0;
        overflow:auto;
    }
    .form {
        width:50%;
        display:flex;
        flex-direction:column;
        overflow:auto;
        padding:10px;
        box-sizing:border-box;
    }
    
    /* Spinner styles */
    .spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 8px;
        vertical-align: middle;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .summary-generating {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .status-loading {
        color: #3498db;
    }
    
    .status-success {
        color: #27ae60;
    }
    
    .status-error {
        color: #e74c3c;
    }
    
    .summary-controls {
        margin: 10px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    #summary_status {
        margin-right: 10px;
        font-style: italic;
        flex-grow: 1;
    }
    </style>
    <div class="label_header">
        <h1>Document Labeling</h1>
        <a class="push_right"
           href="{% url 'admin:webapp_rawdocument_changelist' %}{% urlparams organization__id__exact=raw_doc.organization.id retrieval__merged_portal_credential__portal__name=raw_doc.retrieval.merged_portal_credential.portal.name %}">Back to Raw Documents</a>
        {% if video_link %}<a href="{{ video_link }}">Video of scrape</a>{% endif %}
        {% if next_raw_doc %}
            <a href="{% url 'admin:webapp_rawdocument_label' pk=next_raw_doc.pk %}">Next Raw Document to Label</a>
        {% else %}
            <a href="https://www.youtube.com/watch?v=72PWMeaStY0">All done (with this document type)! Nice!</a>
        {% endif %}
    </div>
    <div class="pdf_and_form">
        {% if raw_doc_file_type == ".pdf" %}
            <embed src="{{ raw_doc.signed_url }}" class="pdf" type="application/pdf" />
        {% else %}
            <a href="{{ raw_doc.signed_url }}" class="pdf">Non-PDF document {{ raw_doc_file_type }}, click here to download</a>
        {% endif %}
        <div class="form">
            <h1>Raw Document Metadata</h1>
            <table>
                <thead>
                    <td>Key</td>
                    <td>Value</td>
                </thead>
                {% for key, value in raw_doc_metadata.items %}
                    <tr>
                        <td>{{ key }}</td>
                        <td>{{ value }}</td>
                    </tr>
                {% endfor %}
            </table>
            <br />
            <br />
            <h1>Ground Truth Form</h1>
            <form method="post">
                {% csrf_token %}
                {{ gt_form.as_p }}
                <div class="summary-controls">
                    <span id="summary_status"></span>
                    <button type="button" id="generate_summary_btn" onclick="generateSummary()">Generate Summary</button>
                </div>
                <button type="submit" id="gt_submit_button">Submit</button>
            </form>
            <br />
            <br />
        </div>
    </div>
    <h1>Previous Ground Truth</h1>
    <table>
        <thead>
            <td>Created At</td>
            <td>Is Visible to User</td>
            <td>Name</td>
            <td>Posted Date</td>
            <td>Document Type</td>
            <td>Sub Document Type</td>
            <td>Line Item</td>
            <td>Currency</td>
            <td>Capital Call Due Date</td>
            <td>Capital Call Amount</td>
            <td>Distribution Amount</td>
            <td>Investment Update Invested</td>
            <td>Investment Update Total Value</td>
            <td>Investment Update Unfunded</td>
            <td>Investment Update Total Value</td>
            <td>Investment Update Committed Capital</td>
            <td>Labeled by</td>
            <td>Source</td>
            <td>URL</td>
        </thead>
        {% for gt in previous_ground_truth %}
            <tr>
                <td>{{ gt.created_at }}</td>
                <td>{{ gt.is_visible }}</td>
                <td>{{ gt.name }}</td>
                <td>{{ gt.posted_date }}</td>
                <td>{% label_lookup label=gt.document_type enum=DocumentType %}</td>
                <td>{% label_lookup label=gt.sub_document_type enum=SubDocumentType %}</td>
                <td>{{ gt.line_item }}</td>
                <td>{{ gt.investment_update_document.currency }}</td>
                <td>{{ gt.capital_call_document.capital_call_due_date }}</td>
                <td>{{ gt.capital_call_document.amount }}</td>
                <td>{{ gt.distribution_notice_document.amount }}</td>
                <td>{{ gt.investment_update_document.invested }}</td>
                <td>{{ gt.investment_update_document.total_value }}</td>
                <td>{{ gt.investment_update_document.unfunded }}</td>
                <td>{{ gt.investment_update_document.total_value }}</td>
                <td>{{ gt.investment_update_document.committed_capital }}</td>
                <td>{{ gt.labeled_by }}</td>
                <td>{{ gt.process_document_source }}</td>
                <td>
                    <a href="{% url 'admin:webapp_processeddocument_change' gt.pk %}">More</a>
                </td>
            </tr>
        {% endfor %}
    </table>
    <script>
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                document.getElementById('gt_submit_button').click();
            }
        });

        function generateSummary() {
            const btn = document.getElementById('generate_summary_btn');
            const status = document.getElementById('summary_status');
            const summaryTextarea = document.getElementById('id_document_summary');

            btn.disabled = true;
            btn.innerHTML = '<span class="spinner"></span>Generating...';
            status.innerHTML = '<div class="summary-generating">Please wait, this may take a few moments...</div>';
            status.className = 'status-loading';

            fetch('{% url "admin:webapp_rawdocument_generate_summary" pk=raw_doc.pk %}', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Populate the summary textarea
                        summaryTextarea.value = data.summary;
                        status.textContent = `Summary generated successfully`;
                        status.className = 'status-success';
                    } else {
                        status.textContent = `Error: ${data.error}`;
                        status.className = 'status-error';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    status.textContent = 'Network error occurred';
                    status.className = 'status-error';
                })
                .finally(() => {
                    // Re-enable button and remove spinner
                    btn.disabled = false;
                    btn.textContent = 'Generate Summary';
                });
        }
    </script>
{% endblock content %}
