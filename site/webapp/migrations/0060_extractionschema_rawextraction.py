# Generated by Django 5.2.4 on 2025-07-23 21:07

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0059_docchunk'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExtractionSchema',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('uri', models.CharField(max_length=4096, unique=True)),
                ('column_name', models.CharField(max_length=2048)),
                ('title', models.CharField(max_length=4096)),
                ('json_type', models.Char<PERSON><PERSON>(default='object', max_length=256)),
                ('major_version', models.IntegerField(default=1)),
                ('minor_version', models.IntegerField(default=0)),
                ('json_schema', models.JSONField(blank=True, default=dict)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s', to='webapp.organization')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
                'default_manager_name': 'objects',
            },
        ),
        migrations.CreateModel(
            name='RawExtraction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('is_ground_truth', models.BooleanField(default=False, help_text='Is this extraction a ground truth for training?')),
                ('extraction_source_name', models.CharField(blank=True, max_length=256, null=True)),
                ('is_current_extraction_schema_validated', models.BooleanField(default=False)),
                ('is_current_target_schema_validated', models.BooleanField(default=False)),
                ('target_json_path', models.TextField(blank=True, null=True)),
                ('data', models.JSONField(blank=True, default=dict)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('current_extraction_schema', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='current_extraction_schemas', to='webapp.extractionschema')),
                ('current_target_schema', models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, related_name='current_target_raw_extractions', to='webapp.extractionschema')),
                ('doc_chunks', models.ManyToManyField(blank=True, related_name='raw_extractions', to='webapp.docchunk')),
                ('last_valid_extraction_schema', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='last_valid_extraction_schemas', to='webapp.extractionschema')),
                ('last_valid_target_schema', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='last_valid_target_raw_extractions', to='webapp.extractionschema')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s', to='webapp.organization')),
                ('original_extraction_schema', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='original_extraction_schemas', to='webapp.extractionschema')),
                ('original_target_schema', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='original_target_raw_extractions', to='webapp.extractionschema')),
                ('processed_document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='raw_extractions', to='webapp.processeddocument')),
                ('raw_document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='raw_extractions', to='webapp.rawdocument')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
                'default_manager_name': 'objects',
            },
        ),
    ]
