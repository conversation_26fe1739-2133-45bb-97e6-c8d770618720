import { Badge } from "../../components/ui/badge";
import { But<PERSON> } from "../../components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import { Input } from "../../components/ui/input";
import type { JSONSchema, NewField, SchemaType } from "../../types/jsonSchema";
import { CirclePlus, HelpCircle, Info } from "lucide-react";
import React from "react";
import { useState } from "react";
import SchemaTypeSelector from "../SchemaEditor/SchemaTypeSelector";

interface AddSchemaButtonProps {
  addNewSchema: (schema: JSONSchema) => void;
  schemas?: JSONSchema[]; // List of all schemas for custom object selection
}

const AddSchemaButton: React.FC<AddSchemaButtonProps> = ({
  addNewSchema,
  schemas,
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [schema, setSchema] = useState<JSONSchema>(true);
  const updateSchema = (newSchema: JSONSchema) => {
    setSchema((prev) =>
      typeof prev === "object" && typeof newSchema === "object"
        ? { ...prev, ...newSchema }
        : newSchema,
    );
  };

  const schemaName = typeof schema === "object" ? schema.title || "/" : "/";
  const schemaDescription =
    typeof schema === "object" ? schema.description || "" : "";
  const schemaType =
    typeof schema === "object" ? schema.type || "object" : "object";

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate schema name is not just "/"
    if (!schemaName.trim() || schemaName.trim() === "/") {
      return;
    }

    // Validate description is not empty
    if (!schemaDescription.trim()) {
      return;
    }

    // Check if schema name already exists
    const schemaBaseName = schemaName.split("/").pop();
    const existingSchemaNames =
      schemas?.map((schema) =>
        typeof schema === "object" && schema.$id
          ? schema.$id.split("/").pop()
          : "",
      ) || [];

    if (
      schemaBaseName &&
      existingSchemaNames &&
      existingSchemaNames.includes(schemaBaseName)
    ) {
      return;
    }

    addNewSchema(schema);

    setSchema(true); // Reset to default state
    setDialogOpen(false);
  };

  const handleSchemaTypeChange = (newType: JSONSchema) => {
    if (typeof newType === "boolean") return;
    updateSchema(newType);
  };

  const handleSchemaNameChange = (name: string) => {
    // Replace invalid characters with lowercase or "_"
    name = name
      .split("")
      .map((char) => {
        if (/[a-z0-9/_]/.test(char)) return char;
        const lower = char.toLowerCase();
        return /[a-z0-9/_]/.test(lower) ? lower : "_";
      })
      .join("");
    if (!name.startsWith("/")) name = "/" + name;
    updateSchema({ title: name });
  };

  // Check if schema name already exists
  const schemaNameSplit = schemaName.split("/");
  const schemaBaseName = schemaNameSplit[schemaNameSplit.length - 1];
  const existingSchemaNames =
    schemas?.map((schema) =>
      typeof schema === "object" && schema.$id
        ? schema.$id.split("/").pop()
        : "",
    ) || [];
  const schemaNameExists =
    schemaBaseName && existingSchemaNames.includes(schemaBaseName);
  const isArrayTypeMissingItems =
    schemaType === "array" && (typeof schema !== "object" || !schema.items);

  // Check if form is valid
  const isFormValid =
    schemaName.trim() !== "/" &&
    schemaName.trim() !== "" &&
    schemaDescription.trim() !== "" &&
    schemaNameSplit.length >= 3 &&
    !isArrayTypeMissingItems &&
    !schemaNameExists;

  return (
    <>
      <Button
        onClick={() => setDialogOpen(true)}
        size="sm"
        className="flex items-center gap-1.5 group"
      >
        <CirclePlus
          size={16}
          className="group-hover:scale-110 transition-transform"
        />
        <span>Add Schema</span>
      </Button>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="md:max-w-[1200px] max-h-[85vh] w-[95vw] p-4 sm:p-6 text-white">
          <DialogHeader className="mb-4">
            <DialogTitle className="text-xl flex flex-wrap items-center gap-2">
              Add New Schema
              <Badge variant="secondary" className="text-xs">
                Schema Builder
              </Badge>
            </DialogTitle>
            <DialogDescription className="text-sm">
              Create a new schema
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4 min-w-[280px]">
                <div>
                  <div className="flex flex-wrap items-center gap-2 mb-1.5">
                    <label htmlFor="fieldName" className="text-sm font-medium">
                      Schema Name
                    </label>
                  </div>
                  <Input
                    id="fieldName"
                    value={schemaName}
                    onChange={(e) => handleSchemaNameChange(e.target.value)}
                    placeholder="e.g. firstName, age, isActive"
                    className="font-mono text-sm w-full"
                    required
                  />
                  {schemaName.trim() === "/" && (
                    <p className="text-red-500 text-xs mt-1">
                      Schema name cannot be just "/"
                    </p>
                  )}
                  {schemaNameExists && (
                    <p className="text-red-500 text-xs mt-1">
                      Schema name "{schemaBaseName}" already exists
                    </p>
                  )}
                  {schemaNameSplit.length < 3 && (
                    <p className="text-red-500 text-xs mt-1">
                      Schema name "{schemaName}" must have a folder
                    </p>
                  )}
                </div>

                <div>
                  <div className="flex flex-wrap items-center gap-2 mb-1.5">
                    <label htmlFor="fieldDesc" className="text-sm font-medium">
                      Description
                    </label>
                  </div>
                  <Input
                    id="schemaDescription"
                    value={schemaDescription}
                    onChange={(e) =>
                      updateSchema({ description: e.target.value })
                    }
                    placeholder="Describe the purpose of this field"
                    className="text-sm w-full"
                    required
                  />
                  {schemaDescription.trim() === "" && (
                    <p className="text-red-500 text-xs mt-1">
                      Description is required
                    </p>
                  )}
                </div>
                <SchemaTypeSelector
                  id="fieldType"
                  value={schemaType}
                  schemas={schemas}
                  onChange={handleSchemaTypeChange}
                />
              </div>
            </div>

            <DialogFooter className="mt-6 gap-2 flex-wrap">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" size="sm" disabled={!isFormValid}>
                Add Schema
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AddSchemaButton;
