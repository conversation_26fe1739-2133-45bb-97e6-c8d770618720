# Generated by Django 5.1.7 on 2025-03-24 17:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0023_retrieval_line_items'),
    ]

    operations = [
        migrations.AddField(
            model_name='investmentupdatedocumentfact',
            name='currency',
            field=models.CharField(choices=[('USD', 'US Dollar'), ('AUD', 'Australian Dollar'), ('EUR', 'Euro'), ('NZD', 'New Zealand Dollar'), ('GBP', 'British Pound'), ('BRL', 'Brazilian Real'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('DKK', 'Danish Krone'), ('HKD', 'Hong Kong Dollar'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MYR', 'Malaysian Ringgit'), ('MXN', 'Mexican Peso'), ('NOK', 'Norwegian Krone'), ('ZAR', 'South African Rand'), ('SGD', 'Singapore Dollar'), ('KRW', 'South Korean Won'), ('LKR', 'Sri Lankan Rupee'), ('SEK', 'Swedish Krona'), ('CHF', 'Swiss Franc'), ('TWD', 'Taiwan Dollar'), ('THB', 'Thai Baht'), ('VEB', 'Venezuelan Bolivar')], default='USD', max_length=3),
        ),
    ]
