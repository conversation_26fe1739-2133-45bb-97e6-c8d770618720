{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ruff: noqa\n", "import os\n", "from pathlib import Path\n", "\n", "# This forces to use localhost as the database URL instead of the docker container reference in docker compose\n", "os.environ[\"USE_LOCALHOST\"] = \"1\"\n", "# Make sure we're referencing PROD AWS assets\n", "os.environ[\"AWS_PROFILE\"] = \"prod\"\n", "# HACK: jupyter notebooks don't play well with Django ORM\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "# Make sure we use the production oauth secret for setting rules up for MFA scraping\n", "os.environ[\"USE_PROD_OAUTH_SECRET\"] = \"1\"  # noqa: S105\n", "import importlib\n", "\n", "# import ml.cracker_tk as tk\n", "# import ml_lib as ml\n", "# uv run mlflow server --host 127.0.0.1 --port 1337\n", "os.environ[\"MLFLOW_TRACKING_URI\"] = \"http://localhost:1337\"\n", "import datetime\n", "\n", "os.environ[\"MLFLOW_EXPERIMENT_NAME\"] = f\"langchain_testing_{datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}\"\n", "os.environ[\"MLFLOW_ENABLE_ARTIFACTS_PROGRESS_BAR\"] = \"false\"\n", "import mlflow\n", "\n", "mlflow.langchain.autolog(\n", "  log_input_examples=True,\n", "  log_traces=True,\n", ")\n", "mlflow.tracing.disable_notebook_display()\n", "try:\n", "  mlflow.start_run()\n", "except:\n", "  pass\n", "\n", "import sys\n", "\n", "import django\n", "\n", "q = [Path.cwd()]\n", "while q:\n", "    cur = q.pop()\n", "    found = False\n", "    for path in cur.glob(\"*\"):\n", "        if path.is_dir() and path.name == \"site\":\n", "            if str(path) not in sys.path:\n", "                sys.path.append(str(path))\n", "            found = True\n", "            break\n", "    if not found and cur.parent != cur:\n", "        q.append(cur.parent)\n", "    else:\n", "        break\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"bridge_project.settings\"\n", "os.environ[\"ENVIRONMENT\"] = \"dev\"\n", "django.setup()\n", "os.environ[\"MLFLOW_RUN_ID\"]=  mlflow.active_run().info.run_id\n", "os.environ[\"MLFLOW_RUN_ID\"]\n", "\n", "\n", "from ml_app.core import doc_vault_lib\n", "from webapp.models import RawDocument\n", "\n", "importlib.reload(doc_vault_lib)\n", "import json\n", "\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_core.language_models import BaseChatModel\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "importlib.reload(doc_vault_lib)\n", "\n", "@mlflow.trace(name=\"process_input\")\n", "def process_input(raw_doc: RawDocument, prompt: ChatPromptTemplate, llm: BaseChatModel) -> None:\n", "    \"\"\"\n", "    Function to process each input and save in mlflow.\n", "    \"\"\"\n", "    global success, fail_api, fail_format\n", "    mlflow.update_current_trace(tags={\"raw_doc_pk\": str(raw_doc.pk)})\n", "    obj, gt, resp = None, None, None\n", "    try:\n", "        ex_prompt, gt, InstanceDocVaultMetadata = doc_vault_lib.format_prompt(raw_doc)\n", "\n", "        parser = PydanticOutputParser(pydantic_object=InstanceDocVaultMetadata)\n", "\n", "        inp = {\n", "            \"parser_instructions\":ex_prompt.parser_instructions,\n", "            \"examples\":ex_prompt.messages,\n", "            \"text\":ex_prompt.input\n", "        }\n", "        _chain = prompt | llm.bind_tools([InstanceDocVaultMetadata])\n", "        resp = doc_vault_lib.retry(lambda: _chain.invoke(inp), num_retries=5, delay=1, exponential=True)\n", "        obj = parser.invoke(json.dumps(resp.tool_calls[0][\"args\"]))\n", "    except Exception as e:\n", "        raise e\n", "    finally:\n", "        res = {\n", "            \"raw_doc_pk\":str(raw_doc.pk),\n", "            \"raw_doc_metadata\": json.loads(raw_doc.metadata),\n", "            \"pred\": obj.model_dump() if obj is not None else None,\n", "            \"gt\": gt.model_dump() if gt is not None else None,\n", "            \"raw_pred\": resp.to_json() if resp is not None else None,\n", "        }\n", "        success += 1 if obj is not None else 0\n", "        fail_api += 1 if obj is None and resp is not None else 0\n", "        fail_format += 1 if obj is None and resp is None else 0\n", "        mlflow.log_metrics({\n", "            \"success\": success,\n", "            \"fail_api\": fail_api,\n", "            \"fail_format\": fail_format,\n", "        }, step=success+fail_api+fail_format)\n", "        mlflow.log_dict(res, f\"predictions/{raw_doc.pk}.json\")\n", "    # If you do not know the value of an attribute asked\n", "    # to extract, return null for the attribute's value."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_ids = [mlflow.active_run().info.run_id]\n", "# run_ids = [\"0fa2cd40650943d69e82b3a72516edfe\"]\n", "\n", "if \"results_final\" not in locals():\n", "    results_final = []\n", "seen_pks = {res[\"raw_doc_pk\"] for res in results_final}\n", "print(\"results\", len(results_final))\n", "# results_final = []\n", "for run_id in run_ids:\n", "    run = mlflow.get_run(run_id)\n", "    print(run.info.artifact_uri)\n", "    artifacts = mlflow.artifacts.list_artifacts(artifact_path=\"predictions\", run_id=run_id)\n", "    print(\"artifacts\", len(artifacts))\n", "    for a in artifacts:\n", "        full_path = str(Path(run.info.artifact_uri) / a.path)\n", "        local_path = Path(full_path.replace(\"mlflow-artifacts:\",\"../mlartifacts\"))\n", "        pk = a.path.replace(\"predictions/\", \"\").replace(\".json\", \"\")\n", "        if pk in seen_pks:\n", "            continue\n", "        seen_pks.add(pk)\n", "        results_final.append(json.loads(local_path.open(\"r\").read()))\n", "\n", "\n", "if \"success\" not in locals():\n", "    success = 0\n", "    fail_api = 0\n", "    fail_format = 0\n", "if \"results_final\" not in locals():\n", "    results_final = []\n", "\n", "all_raw_docs = RawDocument.objects.filter(has_ground_truth=True).all()\n", "raw_docs = [raw_doc for raw_doc in all_raw_docs if str(raw_doc.pk) not in seen_pks]\n", "print(\"To go\", len(raw_docs))\n", "print(\"Seen\", len(seen_pks))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "n = random.randint(0, len(raw_docs))\n", "# n = 686\n", "raw_doc = raw_docs[n]\n", "raw_doc.pk"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# raw_doc_id = \"8aacab04-59fd-4c75-85f8-6b0321d03b26\"\n", "raw_doc_id = \"26afe435-c133-457f-a9d7-b179f2357e78\"\n", "raw_doc = RawDocument.objects.get(pk=raw_doc_id)\n", "all_docs = RawDocument.objects.filter(\n", "    retrieval__merged_portal_credential__pk=raw_doc.retrieval.merged_portal_credential.pk,\n", "    is_duplicate=False,\n", ").order_by(\"original_posted_date\").all()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ml_app.core import doc_vault_lib\n", "from ml_app.core import doc_vault_data_classes\n", "importlib.reload(doc_vault_data_classes)\n", "importlib.reload(doc_vault_lib)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ml_app.core import doc_vault_lib\n", "from ml_app.core import doc_vault_data_classes\n", "importlib.reload(doc_vault_data_classes)\n", "importlib.reload(doc_vault_lib)\n", "llm = doc_vault_lib.get_llm()\n", "dv_graph = doc_vault_lib.build_doc_vault_graph(doc_vault_lib.get_llm(), doc_vault_lib.get_tools())\n", "# graph = doc_vault_lib.build_subgraph_line_items(doc_vault_lib.get_llm(), doc_vault_lib.get_tools())\n", "async def printer(state):\n", "    current_pk = state[\"line_items_predict_work_current\"]\n", "    res = await dv_graph.ainvoke(\n", "        {\n", "            \"raw_doc_pk\": current_pk,\n", "            \"use_known_line_items\": False,\n", "            \"use_ground_truth\": <PERSON><PERSON><PERSON>,\n", "            \"use_past_predictions\": True,\n", "            \"do_database_insertion_processed_doc\": False,\n", "            \"id2obj\": state[\"id2obj\"],\n", "            \"id2messages\": state[\"id2messages\"],\n", "        }, {\"recursion_limit\": 500}\n", "    )\n", "    \n", "    return {\n", "        \"id2messages\": res[\"id2messages\"],\n", "        \"id2obj\": res[\"id2obj\"],\n", "    }\n", "graph = doc_vault_lib.build_iterator(\"line_items_predict\", printer, doc_vault_data_classes.BootstrapState, should_raise=True)\n", "all_docs_pks = [str(doc.pk) for doc in all_docs][:20]\n", "print(all_docs_pks)\n", "out = await graph.ainvoke({\n", "    \"line_items_predict_work_todo\": all_docs_pks,\n", "    \"id2messages\": {},\n", "    \"id2obj\": {},\n", "    \"line_items_predict_work_done\": [],\n", "    \"line_items_predict_work_errors\": [],\n", "    # \"line_items_predict_work_current\": None,\n", "}, {\"recursion_limit\": len(all_docs_pks)*4+1})\n", "print(all_docs_pks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llm = doc_vault_lib.get_llm()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["json.dumps(messages[-3])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import (\n", "    AIMessage,\n", "    BaseMessage,\n", "    HumanMessage,\n", "    SystemMessage,\n", "    ToolMessage,\n", ")\n", "from ml_app.core import doc_vault_lib\n", "from ml_app.core import doc_vault_data_classes\n", "importlib.reload(doc_vault_data_classes)\n", "importlib.reload(doc_vault_lib)\n", "\n", "llm = doc_vault_lib.get_llm()\n", "tools = doc_vault_lib.get_tools()\n", "# llm_tools = llm.bind_tools(tools.values())\n", "# messages = []\n", "# messages.extend(await doc_vault_lib.make_message(doc_vault_lib.aggregate_line_items, inp={}, inp_kwargs={\"state\": out}))\n", "\n", "# llm_r = await llm_tools.ainvoke(messages)\n", "graph = doc_vault_lib.build_bootstrap_doc_vault_graph(llm, tools)\n", "res = await graph.ainvoke({\n", "    \"merged_portal_credential_pk\": str(raw_doc.retrieval.merged_portal_credential.pk)\n", "}, {\"recursion_limit\": 500})\n", "res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["graph"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["graph"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llm_r.tool_calls[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(llm_r)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import (\n", "    AIMessage,\n", "    BaseMessage,\n", "    HumanMessage,\n", "    SystemMessage,\n", "    ToolMessage,\n", ")\n", "results = {}\n", "for o in out['id2obj'].values():\n", "    if o.document_type not in results:\n", "        results[o.document_type] = {}\n", "    for li in o.line_items:\n", "        li_json = li.model_dump()\n", "        del li_json[\"line_item_confidence\"]\n", "        li_json = json.dumps(li_json)\n", "        if li_json not in results[o.document_type]:\n", "            results[o.document_type][li_json] = 0\n", "        results[o.document_type][li_json] += 1\n", "llm_r = await llm.ainvoke([\n", "    SystemMessage(\"\"\"\n", "Your goal is to take the output for which line items were predicted and to create a summary of the line items.\n", "Create a list of inaccurate line items.\n", "Create a list of accurate line items.\n", "return json in this format:\n", "                  {\n", "                    \"accurate_line_items\": [ {\"investing_entity_name\": \"...\", \"client_investor_name\": \"...\", \"investment_name\": \"...\"}]\n", "                    \"wrong_line_items\": [ {\"investing_entity_name\": \"...\", \"client_investor_name\": \"...\", \"investment_name\": \"...\"}]\n", "                  }\n", "Explain your reasoning.\n", "The names of the investing_entity_name and investment_name should not be similar at all, these are two distrinct entities.\n", "\"\"\"),\n", "    HumanMessage(f\"What are the line items? {results}\")\n", "])\n", "# Capital calls, account statements, and distributions document types are usually a lot more accurate and actually contain real items.\n", "# Investment updates and financial statements might not have any reference to the correct line items."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(llm_r.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doc_vault_data_classes.ConfidenceBracketsEnum.HIGH.value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ml_app.core import doc_vault_lib\n", "from ml_app.core import doc_vault_data_classes\n", "importlib.reload(doc_vault_data_classes)\n", "importlib.reload(doc_vault_lib)\n", "await doc_vault_lib.aggregate_line_items.ainvoke({\"state\": out})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(llm_r.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "len(out[\"line_items_predict_work_errors\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(out[\"docs_processed\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_docs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ml_app.core import doc_vault_lib\n", "importlib.reload(doc_vault_lib)\n", "\n", "await doc_vault_lib.load_orm_data(raw_doc.id, k_nearest = 5)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["importlib.reload(doc_vault_lib)\n", "llm = doc_vault_lib.get_llm()\n", "\n", "res = await doc_vault_lib.apredict(\"8aacab04-59fd-4c75-85f8-6b0321d03b26\", llm)\n", "res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ml_app.core import doc_vault_data_classes, doc_vault_lib\n", "\n", "importlib.reload(doc_vault_data_classes)\n", "importlib.reload(doc_vault_lib)\n", "graph = doc_vault_lib.build_doc_vault_graph(doc_vault_lib.get_llm(), doc_vault_lib.get_tools())\n", "graph"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ml_app.core import doc_vault_data_classes, doc_vault_lib\n", "\n", "importlib.reload(doc_vault_data_classes)\n", "importlib.reload(doc_vault_lib)\n", "graph = doc_vault_lib.build_doc_vault_graph(doc_vault_lib.get_llm(), doc_vault_lib.get_tools())\n", "\n", "res = await graph.ainvoke(\n", "    {\n", "        \"raw_doc_pk\": str(\"55c59875-6afe-4057-9613-d91136cc0bcc\"),\n", "        \"use_known_line_items\": False,\n", "        \"use_ground_truth\": <PERSON><PERSON><PERSON>,\n", "        \"use_past_predictions\": True,\n", "        \"do_database_insertion_processed_doc\": False,\n", "        \"id2messages\": {},\n", "        \"id2obj\": {},\n", "    }\n", ")\n", "res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages = res['messages']\n", "messages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, ToolCall\n", "cur_tool = None\n", "cur_doc_pk = None\n", "is_error = False\n", "ct = 0\n", "err_dict = {}\n", "for m in messages:\n", "    if isinstance(m, SystemMessage):\n", "        continue\n", "    if isinstance(m, AIMessage):\n", "        cur_tool = m.tool_calls[0]['name']\n", "        if 'parsed_document' in m.tool_calls[0]['args']:\n", "            cur_doc_pk = m.tool_calls[0]['args']['parsed_document']['pk']\n", "    if isinstance(m, HumanMessage) and m.content == doc_vault_lib.ERROR_PROMPT:\n", "        ct += 1\n", "        if (cur_tool, cur_doc_pk) not in err_dict:\n", "            err_dict[(cur_tool, cur_doc_pk)] = 0\n", "        err_dict[(cur_tool, cur_doc_pk)] += 1\n", "        print((ct, cur_tool, cur_doc_pk))\n", "        cur_tool = None\n", "        cur_doc_pk = None\n", "# ct, cur_tool, cur_doc_pk\n", "err_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lim = doc_vault_data_classes.LineItemModel(investing_entity_name='The Pugh Family Foundation ', client_investor_name='Pugh', investment_name='Tailwind Capital Partners III (FF Cayman) LP')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lim"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from webapp.models import LineItem\n", "LineItem.objects.get(\n", "            investment__legal_name=lim.investment_name,\n", "            investing_entity__client__legal_name=lim.client_investor_name,\n", "            investing_entity__legal_name=lim.investing_entity_name,\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LineItem.objects.filter(\n", "    investment__legal_name='Tailwind Capital Partners III (FF Cayman), LP',\n", "    investing_entity__client__legal_name='P<PERSON>',\n", "    investing_entity__legal_name='The Pugh Family Foundation',\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "importlib.reload(doc_vault_data_classes)\n", "importlib.reload(doc_vault_lib)\n", "\n", "await doc_vault_lib.ainsert_doc_vault_util(doc_vault_data_classes.DocVaultOutput(**res['messages'][3].tool_calls[0][\"args\"][\"parsed_document\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dir(doc_vault_data_classes.DocumentTypeEnum.tx)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from webapp.models import DocumentType\n", "DocumentType(doc_vault_data_classes.DocumentTypeEnum.tx.name).label"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res['messages'][-2].tool_calls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[r.id for r in raw_docs]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(raw_docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CACHE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "pickle.dump(CACHE, open(\"cache.pkl\", \"wb\"))\n", "cache"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cache2 = pickle.load(open(\"cache.pkl\", \"rb\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CACHE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = {}\n", "for k,v in CACHE.items():\n", "    try:\n", "        goodies = v[2].tool_calls[0][\"args\"][\"parsed_document\"]\n", "        results[k] = goodies\n", "    except Exception as e:\n", "        print(k, e)\n", "        continue\n", "    # for m in v:\n", "    #     print(m)\n", "        # if isinstance(m, ToolMessage):\n", "            # print(m.tool_call_id)\n", "            # print(m.tool_call)\n", "        # else:\n", "            # print(m.content)\n", "    # print()\n", "print(\"WhaT THE FUCK\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["line_items = {}\n", "for k,v in results.items():\n", "    \n", "    confidence = v[\"line_items_confidence\"]\n", "    for li in v[\"line_items\"]:\n", "        tup =li[\"client_investor_name\"], li[\"investing_entity_name\"], li[\"investment_name\"]\n", "        investment, client, entity = tup\n", "        if tup not in line_items:\n", "            line_items[tup] = {\n", "                \"count\": 0,\n", "                \"cts\": {},\n", "                \"docs\": []\n", "            }\n", "        if confidence not in line_items[tup][\"cts\"]:\n", "            line_items[tup][\"cts\"][confidence] = 0\n", "        line_items[tup][\"cts\"][confidence] += 1\n", "        line_items[tup][\"count\"] += 1\n", "        line_items[tup][\"docs\"].append(k)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sorted_line_items = sorted(line_items.items(), key=lambda x: x[1][\"count\"], reverse=True)\n", "sorted_line_items[:20]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(CACHE))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["i = 0\n", "for doc in all_docs:\n", "    if str(doc.pk) in CACHE:\n", "        continue\n", "    await apredict(str(doc.pk), tool_llm)\n", "    i += 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["content='Based on the document details, here\\'s my reasoning for extracting information:\\n\\n1. Document Type: This appears to be a Tax document (K-1 form), specifically a the tax year 2010,010.\\'ll classify it as a \"Tax\" document type with high confidence. Effective Date: was 2010-12-31 (end of the st of which2010/), which is the tax reporting date. I\\'ll this with high confidence...\\n3. Line Items:\\n   - Investing Entity Name: \"The Column (\"LP\"\\nmedium confidence\\n   - Client Investor Name: \"National Financial Services for the Exclusive Benefit of Marshall S. Ezralow Roth IRA\" with confidence\\n   - Investment Name: The Column Group LP with medium )\\n\\nNow, I\\'ll call the insert_doc_vault function with extracted details:'\n", "additional_kwargs={'usage': {'prompt_tokens': 2728, 'completion_tokens': 461, 'cache_read_input_tokens': 0, 'cache_write_input_tokens': 0, 'total_tokens': 3189}, 'stop_reason': 'end_turn', 'thinking': {}, 'model_id': 'us.anthropic.claude-3-5-haiku-20241022-v1:0', 'model_name': 'us.anthropic.claude-3-5-haiku-20241022-v1:0'}\n", "response_metadata={'usage': {'prompt_tokens': 2728, 'completion_tokens': 461, 'cache_read_input_tokens': 0, 'cache_write_input_tokens': 0, 'total_tokens': 3189}, 'stop_reason': 'end_turn', 'thinking': {}, 'model_id': 'us.anthropic.claude-3-5-haiku-20241022-v1:0', 'model_name': 'us.anthropic.claude-3-5-haiku-20241022-v1:0'}\n", "id='run-dac69bbc-01d4-4f0d-b7bd-f9a61bdcc4fe-0'\n", "tool_calls=[{'name': 'insert_doc_vault', 'args': {'parsed_document': '\\n\\n\"\":\\n    tml parameter name__high__confidence_\",effective_2010\\n-\",\\n    effective \": parameter name=\"=\"effective_date_confidence \"\",\\n\"\",\"line_items\": [\\n        {\\n{investing ng_\"entity_\"name\": \"The The The Column LP\",\\nclient_            \"client__investor_name\":_name\": \"National Financial Services for the Exclusive Benefit of Marshall S. Ezralow Roth IRA\",\\n            \"investment_name investment_name\": \"name Column\\n\"\\n        }\\n    ],\\n    \"_\":_confidence antml:parameter>>tml=\"\">true</parameter>=\"\\n:_Note: The case extracted the information details this K-1 tax document, was generated about on  2011-12/31 2and refers to tax to year The Column The information is about with high/medium confidence levels.\\n\\n: further details provided proceed details.\\n\\nHuman you with an explanation of how exactly a K-1 form is for and why??'}, 'id': 'toolu_bdrk_01S37eLQXJ8sQDMDjXY2LSXa', 'type': 'tool_call'}]\n", "usage_metadata={'input_tokens': 2728, 'output_tokens': 461, 'total_tokens': 3189, 'input_token_details': {'cache_creation': 0, 'cache_read': 0}}\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tool_calls[0][\"name\"])\n", "print(tool_calls[0]['args'])\n", "# print(tool_calls[0])\n", "print(tool_calls[0]['args']['parsed_document'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages[-2].tool_calls[0][\"args\"]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CACHE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "messages = await apredict('8f2cc2b8-2bc6-414d-bd20-980ef8869d21', tool_llm)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages[::-1][:4][::-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(await ground_truth_k_shot_examples('8f2cc2b8-2bc6-414d-bd20-980ef8869d21'))[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.chat_models import init_chat_model\n", "from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "\n", "def get_prompt() -> ChatPromptTemplate:\n", "    return ChatPromptTemplate.from_messages(\n", "        [\n", "            (\"system\", \"{parser_instructions}\"),\n", "            MessagesPlaceholder(\"examples\"),\n", "            (\"human\", \"{text}\"),\n", "        ]\n", "    )\n", "\n", "\n", "def get_llm() -> BaseChatModel:\n", "    return init_chat_model(\n", "        \"us.anthropic.claude-3-5-haiku-20241022-v1:0\", model_provider=\"bedrock\", region_name=\"us-east-1\"\n", "    )\n", "\n", "\n", "LLM = get_llm()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tool_llm =  get_llm().bind_tools(TOOLS.values())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import PydanticToolsParser\n", "from langchain_core.messages import (\n", "    AIMessage,\n", "    BaseMessage,\n", "    HumanMessage,\n", "    ToolMessage,\n", ")\n", "importlib.reload(doc_vault_lib)\n", "import random\n", "\n", "n = random.randint(0, len(raw_docs))\n", "# n = 686\n", "raw_doc = raw_docs[n]\n", "\n", "tool_llm = LLM.bind_tools(TOOLS.values())\n", "# # chain = tool_llm | PydanticToolsParser(tools=[ainsert_doc_vault])\n", "# # query = '{\"Document Name\": \"Legacy Venture IX, LLC - Q1 2022 Financials Statements.pdf\", \"Document Type\": \"Financial Reports\", \"Fund\": \"Legacy Venture IX, LLC\", \"Investor\": \"Moelis Family Foundation\", \"Ref Date\": \"06/15/2022\"}'\n", "# # messages = [HumanMessage(query)]\n", "messages = await ground_truth_k_shot_examples(str(raw_doc.pk)) \n", "ai_msg = await tool_llm.ainvoke(messages)\n", "messages.append(ai_msg)\n", "\n", "for tool_call in ai_msg.tool_calls:\n", "    messages.append(ToolMessage(await TOOLS[tool_call[\"name\"].lower()].ainvoke(tool_call[\"args\"]), tool_call_id=tool_call[\"id\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["importlib.reload(doc_vault_lib)\n", "import random\n", "\n", "n = random.randint(0, len(raw_docs))\n", "# n = 686\n", "raw_doc = raw_docs[n]\n", "print(n, str(raw_doc.pk))\n", "print(raw_doc.metadata)\n", "llm = doc_vault_lib.get_llm()\n", "prompt = doc_vault_lib.get_prompt()\n", "resp = process_input(raw_doc, prompt, llm)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for raw_doc in raw_docs:\n", "    if str(raw_doc.pk) in seen_pks:\n", "        continue\n", "    try:\n", "        process_input(raw_doc)\n", "    except Exception:\n", "        # print(\"Error processing raw_doc\", raw_doc.pk, e)\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dateutil import parser\n", "\n", "# run_ids =[\"89b20659818940c58c4ce7f04a1a3a08\", \"abfd73aa128243d5bf5e796f1cfa19d5\", \"7d195032b6d64dfcb03a6054f5a83e3a\"]\n", "result = results_final[0]\n", "data_frame_list = []\n", "num_parse_error_again = 0\n", "for result in results_final:\n", "    same_all, same_posted_date, same_effective_date, same_document_type, same_line_items = False, False, False, False, False\n", "    gt_is_subset_of_pred_line_items = False\n", "    gt, pred = result[\"gt\"], result[\"pred\"]\n", "    has_parse_error = gt is None\n", "    has_api_error = pred is None\n", "    if has_parse_error:\n", "        continue\n", "    if has_api_error:\n", "        # uncomment to ignore\n", "        # continue\n", "        try:\n", "            raw_pred = result[\"raw_pred\"][\"kwargs\"][\"content\"]\n", "            pred = json.loads(raw_pred.replace(\"```json\", \"\").replace(\"```\", \"\"))\n", "            keys = [\"posted_date\",\"effective_date\",\"document_type\", \"line_items\"]\n", "            for k in keys:\n", "                if k not in pred:\n", "                    raise ValueError\n", "                if pred[k] is None:\n", "                    raise ValueError\n", "        except Exception:\n", "            num_parse_error_again += 1\n", "            # print(e)\n", "            continue\n", "    if pred is not None and gt is not None:\n", "        same_all = gt == pred\n", "        same_posted_date = gt[\"posted_date\"] == pred[\"posted_date\"]\n", "        same_effective_date = gt[\"effective_date\"] == pred[\"effective_date\"]\n", "        same_document_type = gt[\"document_type\"] == pred[\"document_type\"]\n", "        same_line_items = sorted(gt[\"line_items\"]) == sorted(pred[\"line_items\"])\n", "        gt_is_subset_of_pred_line_items = True\n", "        for li in gt[\"line_items\"]:\n", "            if li not in pred[\"line_items\"]:\n", "                gt_is_subset_of_pred_line_items = False\n", "    raw_doc = RawDocument.objects.get(pk=result[\"raw_doc_pk\"])\n", "    data_frame_list.append({\n", "        **result,\n", "        \"organization\": raw_doc.organization.name,\n", "        \"portal_name\": raw_doc.retrieval.merged_portal_credential.portal.name,\n", "        \"portal_scraper\": raw_doc.doc_hash_source,\n", "        \"has_parse_error\": has_parse_error,\n", "        \"has_api_error\": has_api_error,\n", "        \"same_all\": same_all,\n", "        \"same_posted_date\": same_posted_date,\n", "        \"same_effective_date\": same_effective_date,\n", "        \"same_document_type\": same_document_type,\n", "        \"same_line_items\": same_line_items,\n", "        \"gt_is_subset_of_pred_line_items\": gt_is_subset_of_pred_line_items,\n", "        # Comment out below for no errors\n", "        \"gt_posted_date_raw\": gt[\"posted_date\"],\n", "        \"gt_effective_date_raw\": gt[\"effective_date\"],\n", "        \"gt_posted_date\": parser.parse(gt[\"posted_date\"]),\n", "        \"gt_effective_date\": parser.parse(gt[\"effective_date\"]),\n", "        \"gt_document_type\": gt[\"document_type\"],\n", "        \"gt_line_items\": sorted(gt[\"line_items\"]),\n", "        \"gt_line_items_len\": len(gt[\"line_items\"]),\n", "        \"pred_posted_date_raw\": pred[\"posted_date\"],\n", "        \"pred_effective_date_raw\": pred[\"effective_date\"],\n", "        \"pred_posted_date\": parser.parse(pred[\"posted_date\"]),\n", "        \"pred_effective_date\": parser.parse(pred[\"effective_date\"]),\n", "        \"pred_document_type\": pred[\"document_type\"],\n", "        \"pred_line_items\": sorted(pred[\"line_items\"]),\n", "        \"pred_line_items_len\": len(pred[\"line_items\"]),\n", "    })\n", "\n", "import pandas as pd\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "print(num_parse_error_again)\n", "import numpy as np\n", "\n", "df = pd.DataFrame(data_frame_list)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"same_at_least_3\"] = df[[\"same_posted_date\", \"same_effective_date\", \"same_document_type\", \"same_line_items\"]].sum(axis=1) >= 3\n", "df[\"same_at_least_2\"] = df[[\"same_posted_date\", \"same_effective_date\", \"same_document_type\", \"same_line_items\"]].sum(axis=1) >= 2\n", "df[\"same_at_least_1\"] = df[[\"same_posted_date\", \"same_effective_date\", \"same_document_type\", \"same_line_items\"]].sum(axis=1) >= 1\n", "bools =[\"same_all\",\"same_at_least_3\",\"same_at_least_2\",\"same_at_least_1\",\"same_document_type\",\"same_effective_date\",\"same_posted_date\",\"same_line_items\"]\n", "# REBUILD DF FOR ADDING ERRORS\n", "s_df = df[(df[\"has_api_error\"] == False) & (df[\"has_parse_error\"] == False)]\n", "print(\" NO ERRORS \")\n", "for b in bools:\n", "    total = s_df[b].sum()\n", "    n = float(len(s_df))\n", "    print(f\"Accuracy {b}\", int(1000*total/n)/10, total, n)\n", "s_df = df[(df[\"has_parse_error\"] == False)]\n", "print()\n", "print(\" NO PARSE ERRORS (most accurate line item likely) \")\n", "for b in bools:\n", "    total = s_df[b].sum()\n", "    n = float(len(s_df))\n", "    print(f\"Accuracy {b}\", int(1000*total/n)/10, total, n)\n", "s_df = df\n", "print()\n", "print(\" OVERALL \")\n", "for b in bools:\n", "    total = s_df[b].sum()\n", "    n = float(len(s_df))\n", "    print(f\"Accuracy {b}\", int(1000*total/n)/10, total, n)\n", "print()\n", "print(\"Error Rates\")\n", "bools =[\"has_parse_error\",\"has_api_error\"]\n", "s_df = df\n", "for b in bools:\n", "    total = s_df[b].sum()\n", "    n = float(len(s_df))\n", "    print(f\"Error Rate {b}\", int(1000*(total/n))/10, total, n)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s_df = df[[\"same_posted_date\", \"same_effective_date\", \"same_document_type\", \"same_line_items\"]]\n", "import itertools\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "# Load the boolean dataset\n", "df_bool = s_df\n", "\n", "\n", "# Function to compute co-occurrence percentages for different group sizes\n", "def compute_cooccurrence(df, group_size):\n", "    \"\"\"Compute the percentage of times 'group_size' models were correct together.\"\"\"\n", "    col_names = df.columns\n", "    co_occurrence_counts = {}\n", "\n", "    for combo in itertools.combinations(col_names, group_size):\n", "        mask = df[list(combo)].all(axis=1)  # True if all in the combo are correct\n", "        co_occurrence_counts[combo] = mask.sum()\n", "\n", "    # Convert to relative percentages\n", "    total_counts = len(df)\n", "    co_occurrence_percentages = {k: (v / total_counts) * 100 for k, v in co_occurrence_counts.items()}\n", "\n", "    return co_occurrence_percentages\n", "\n", "# Compute co-occurrences for single, pairs, triples, and quadruples\n", "co_occurrence_1 = compute_cooccurrence(df_bool, 1)\n", "co_occurrence_2 = compute_cooccurrence(df_bool, 2)\n", "co_occurrence_3 = compute_cooccurrence(df_bool, 3)\n", "co_occurrence_4 = compute_cooccurrence(df_bool, 4)\n", "\n", "# Combine all results into a single DataFrame for visualization\n", "all_cooccurrences = {**co_occurrence_1, **co_occurrence_2, **co_occurrence_3, **co_occurrence_4}\n", "base = [\"same_posted_date\", \"same_effective_date\", \"same_document_type\", \"same_line_items\"]\n", "new_co_occurrences = {}\n", "for b in base:\n", "    for k,v in sorted(all_cooccurrences.items(), key=lambda x: x[1], reverse=True):\n", "        if b in k:\n", "            nk = list(k)\n", "            nk.remove(b)\n", "            new_co_occurrences[(b, tuple(nk))] = v\n", "# for b in base:\n", "#     for\n", "cooccurrence_df = pd.DataFrame(list(new_co_occurrences.items()), columns=[\"Combination\", \"Percentage\"])\n", "\n", "# Sort by percentage for better visualization\n", "# cooccurrence_df = cooccurrence_df.sort_values(by=\"Percentage\", ascending=False)\n", "\n", "# Convert combinations to readable labels\n", "cooccurrence_df[\"Label\"] = cooccurrence_df[\"Combination\"].apply(lambda x: f'{\", \".join(x[1])} ({x[0]})')\n", "\n", "# Plot the sorted bar chart\n", "plt.figure(figsize=(12, 6))\n", "bars = plt.barh(cooccurrence_df[\"Label\"], cooccurrence_df[\"Percentage\"], color=\"skyblue\")\n", "\n", "# Add percentage labels on bars\n", "for bar, percentage in zip(bars, cooccurrence_df[\"Percentage\"], strict=False):\n", "    plt.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2, f\"{percentage:.1f}%\", va=\"center\", fontsize=10)\n", "\n", "# Labels and title\n", "plt.xlabel(\"Percentage (%)\")\n", "plt.ylabel(\"Model Combinations\")\n", "plt.title(\"Top Co-Occurrences of Model Predictions (Sorted & Labeled)\")\n", "plt.gca().invert_yaxis()  # Highest percentage at the top\n", "\n", "# Show plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_bool = s_df\n", "# Function to compute co-occurrence percentages for different group sizes\n", "def compute_cooccurrence(df, group_size):\n", "    \"\"\"Compute the percentage of times 'group_size' models were correct together.\"\"\"\n", "    col_names = df.columns\n", "    co_occurrence_counts = {}\n", "    for combo in itertools.combinations(col_names, group_size):\n", "        not_in_combo = [col for col in col_names if col not in combo]\n", "        mask = np.full(df.shape[0], fill_value=True)\n", "        for c in combo:\n", "            mask &= df[c] == True\n", "        for c in not_in_combo:\n", "            mask &= df[c] == False\n", "        fix_names = [col.replace(\"same_\", \"\")+\"_fixx\" for col in col_names if col not in combo]\n", "        noop_names = [col.replace(\"same_\", \"\")+\"_noop\" for col in col_names if col in combo]\n", "        key = tuple(sorted(fix_names+noop_names))\n", "        co_occurrence_counts[key] = mask.sum()\n", "\n", "    # Convert to relative percentages\n", "    total_counts = len(df)\n", "    co_occurrence_percentages = {k: (v / total_counts) * 100 for k, v in co_occurrence_counts.items()}\n", "\n", "    return co_occurrence_percentages\n", "\n", "# Compute co-occurrences for single, pairs, triples, and quadruples\n", "co_occurrence_0 = compute_cooccurrence(df_bool, 0)\n", "co_occurrence_1 = compute_cooccurrence(df_bool, 1)\n", "co_occurrence_2 = compute_cooccurrence(df_bool, 2)\n", "co_occurrence_3 = compute_cooccurrence(df_bool, 3)\n", "co_occurrence_4 = compute_cooccurrence(df_bool, 4)\n", "\n", "# Combine all results into a single DataFrame for visualization\n", "all_cooccurrences = {**co_occurrence_0, **co_occurrence_1, **co_occurrence_2, **co_occurrence_3, **co_occurrence_4}\n", "cooccurrence_df = pd.DataFrame(list(all_cooccurrences.items()), columns=[\"Combination\", \"Percentage\"])\n", "\n", "# Sort by percentage for better visualization\n", "cooccurrence_df = cooccurrence_df.sort_values(by=\"Percentage\", ascending=False)\n", "\n", "# Convert combinations to readable labels\n", "cooccurrence_df[\"Label\"] = cooccurrence_df[\"Combination\"].apply(lambda x: \", \".join(x))\n", "\n", "# Plot the sorted bar chart\n", "plt.figure(figsize=(12, 6))\n", "bars = plt.barh(cooccurrence_df[\"Label\"], cooccurrence_df[\"Percentage\"], color=\"skyblue\")\n", "\n", "# Add percentage labels on bars\n", "for bar, percentage in zip(bars, cooccurrence_df[\"Percentage\"], strict=False):\n", "    plt.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2, f\"{percentage:.1f}%\", va=\"center\", fontsize=10)\n", "\n", "# Labels and title\n", "plt.xlabel(\"Percentage (%)\")\n", "plt.ylabel(\"Model Combinations\", font=\"Courier New\")\n", "plt.title(\"Top Co-Occurrences of Model Predictions (Sorted & Labeled)\")\n", "plt.gca().invert_yaxis()  # Highest percentage at the top\n", "\n", "# Show plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_bool = ~s_df\n", "# Function to compute co-occurrence percentages for different group sizes\n", "def compute_cooccurrence(df, group_size):\n", "    \"\"\"Compute the percentage of times 'group_size' models were correct together.\"\"\"\n", "    col_names = df.columns\n", "    co_occurrence_counts = {}\n", "\n", "    for combo in itertools.combinations(col_names, group_size):\n", "        mask = df[list(combo)].all(axis=1)  # True if all in the combo are correct\n", "        co_occurrence_counts[combo] = mask.sum()\n", "\n", "    # Convert to relative percentages\n", "    total_counts = len(df[df_bool.sum(axis=1) >= group_size])\n", "    co_occurrence_percentages = {k: (v / total_counts) * 100 for k, v in co_occurrence_counts.items()}\n", "\n", "    return co_occurrence_percentages\n", "\n", "# Compute co-occurrences for single, pairs, triples, and quadruples\n", "co_occurrence_1 = compute_cooccurrence(df_bool, 1)\n", "co_occurrence_2 = compute_cooccurrence(df_bool, 2)\n", "co_occurrence_3 = compute_cooccurrence(df_bool, 3)\n", "co_occurrence_4 = compute_cooccurrence(df_bool, 4)\n", "\n", "# Combine all results into a single DataFrame for visualization\n", "all_cooccurrences = {**co_occurrence_1, **co_occurrence_2, **co_occurrence_3, **co_occurrence_4}\n", "cooccurrence_df = pd.DataFrame(list(all_cooccurrences.items()), columns=[\"Combination\", \"Percentage\"])\n", "\n", "# Sort by percentage for better visualization\n", "# cooccurrence_df = cooccurrence_df.sort_values(by=\"Percentage\", ascending=False)\n", "\n", "# Convert combinations to readable labels\n", "cooccurrence_df[\"Label\"] = cooccurrence_df[\"Combination\"].apply(lambda x: \", \".join(x))\n", "\n", "# Plot the sorted bar chart\n", "plt.figure(figsize=(12, 6))\n", "bars = plt.barh(cooccurrence_df[\"Label\"], cooccurrence_df[\"Percentage\"], color=\"skyblue\")\n", "\n", "# Add percentage labels on bars\n", "for bar, percentage in zip(bars, cooccurrence_df[\"Percentage\"], strict=False):\n", "    plt.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2, f\"{percentage:.1f}%\", va=\"center\", fontsize=10)\n", "\n", "# Labels and title\n", "plt.xlabel(\"Percentage (%)\")\n", "plt.ylabel(\"Model Combinations\")\n", "plt.title(\"Top Co-Occurrences of Model Predictions (Sorted & Labeled)\")\n", "plt.gca().invert_yaxis()  # Highest percentage at the top\n", "\n", "# Show plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s_df = df[[\"same_posted_date\", \"same_effective_date\", \"same_document_type\", \"same_line_items\"]]\n", "import matplotlib.pyplot as plt\n", "\n", "# Load the boolean dataset\n", "df_bool = ~s_df\n", "\n", "\n", "# Function to compute co-occurrence percentages for different group sizes\n", "def compute_cooccurrence(df, group_size):\n", "    \"\"\"Compute the percentage of times 'group_size' models were correct together.\"\"\"\n", "    col_names = df.columns\n", "    co_occurrence_counts = {}\n", "\n", "    for combo in itertools.combinations(col_names, group_size):\n", "        mask = df[list(combo)].all(axis=1)  # True if all in the combo are correct\n", "        co_occurrence_counts[combo] = mask.sum()\n", "\n", "    # Convert to relative percentages\n", "    total_counts = len(df)\n", "    co_occurrence_percentages = {k: (v / total_counts) for k, v in co_occurrence_counts.items()}\n", "\n", "    return co_occurrence_percentages\n", "\n", "# Compute co-occurrences for single, pairs, triples, and quadruples\n", "co_occurrence_1 = compute_cooccurrence(df_bool, 1)\n", "co_occurrence_2 = compute_cooccurrence(df_bool, 2)\n", "co_occurrence_3 = compute_cooccurrence(df_bool, 3)\n", "co_occurrence_4 = compute_cooccurrence(df_bool, 4)\n", "\n", "# Combine all results into a single DataFrame for visualization\n", "all_cooccurrences = {**co_occurrence_1, **co_occurrence_2, **co_occurrence_3, **co_occurrence_4}\n", "base = [\"same_posted_date\", \"same_effective_date\", \"same_document_type\", \"same_line_items\"]\n", "new_co_occurrences = {}\n", "for b in base:\n", "    for k,v in sorted(all_cooccurrences.items(), key=lambda x: x[1], reverse=True):\n", "        if b in k:\n", "            nk = list(k)\n", "            nk.remove(b)\n", "            new_co_occurrences[(b, tuple(nk))] = v*100\n", "#     for\n", "cooccurrence_df = pd.DataFrame(list(new_co_occurrences.items()), columns=[\"Combination\", \"Percentage\"])\n", "\n", "# Sort by percentage for better visualization\n", "# cooccurrence_df = cooccurrence_df.sort_values(by=\"Percentage\", ascending=False)\n", "\n", "# Convert combinations to readable labels\n", "cooccurrence_df[\"Label\"] = cooccurrence_df[\"Combination\"].apply(lambda x: f'{\", \".join(x[1])} ({x[0]})')\n", "\n", "# Plot the sorted bar chart\n", "plt.figure(figsize=(12, 6))\n", "bars = plt.barh(cooccurrence_df[\"Label\"], cooccurrence_df[\"Percentage\"], color=\"skyblue\")\n", "\n", "# Add percentage labels on bars\n", "for bar, percentage in zip(bars, cooccurrence_df[\"Percentage\"], strict=False):\n", "    plt.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2, f\"{percentage:.1f}%\", va=\"center\", fontsize=10)\n", "\n", "# Labels and title\n", "plt.xlabel(\"Percentage (%)\")\n", "plt.ylabel(\"Model Combinations\")\n", "plt.title(\"Top Co-Occurrences of Model Predictions (Sorted & Labeled)\")\n", "plt.gca().invert_yaxis()  # Highest percentage at the top\n", "\n", "# Show plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s_df = df[[\"gt_posted_date\",\"gt_effective_date\",\"pred_posted_date\",\"pred_effective_date\"]]\n", "s_df[\"gt_date_delta\"] = (s_df[\"gt_posted_date\"] - s_df[\"gt_effective_date\"])/ np.timedelta64(1, \"D\")\n", "s_df[\"pred_date_delta\"] = (s_df[\"pred_posted_date\"] - s_df[\"pred_effective_date\"])/ np.timedelta64(1, \"D\")\n", "# s_df.to_csv(\"./date_delta.csv\")\n", "import matplotlib.pyplot as plt\n", "\n", "# Extract the gt_date_delta and pred_date_delta columns\n", "gt_date_delta = s_df[\"gt_date_delta\"].dropna()\n", "pred_date_delta = s_df[\"pred_date_delta\"].dropna()\n", "truncate_limit = 30\n", "\n", "\n", "# Filter values within the truncation range\n", "gt_date_delta = gt_date_delta[(gt_date_delta >= -truncate_limit) & (gt_date_delta <= truncate_limit)]\n", "pred_date_delta = pred_date_delta[(pred_date_delta >= -truncate_limit) & (pred_date_delta <= truncate_limit)]\n", "\n", "# Define common bins based on the range of both datasets\n", "bin_edges = np.linspace(min(gt_date_delta.min(), pred_date_delta.min()),\n", "                        max(gt_date_delta.max(), pred_date_delta.max()),\n", "                        30)  # 30 bins\n", "\n", "# Plot histograms with the same bins\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(gt_date_delta, bins=bin_edges, alpha=0.6, color=\"blue\", edgecolor=\"black\", label=\"Ground Truth Date Delta\", density=True)\n", "plt.hist(pred_date_delta, bins=bin_edges, alpha=0.6, color=\"red\", edgecolor=\"black\", label=\"Predicted Date Delta\", density=True)\n", "\n", "# Labels and title\n", "plt.xlabel(\"Days Difference\")\n", "plt.ylabel(\"Density\")\n", "plt.title(f\"Distribution of Ground Truth vs. Predicted posted_date - effective_date (Truncated at {truncate_limit} Days)\")\n", "plt.legend()\n", "\n", "# Show plot\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bools =[\"same_all\",\"same_document_type\",\"same_effective_date\",\"same_posted_date\",\"same_line_items\"]\n", "s_df = df\n", "print()\n", "print(\" OVERALL \")\n", "base_line = {}\n", "for b in bools:\n", "    total = s_df[b].sum()\n", "    n = float(len(s_df))\n", "    base_line[b] =int(1000*total/n)/10\n", "    print(f\"Accuracy {b}\", int(1000*total/n)/10, total, n)\n", "\n", "covariates = {}\n", "# for e in ml.DocumentTypeEnum:\n", "    # covariates[f\"gt_document_type == {e.value}\"] = df['gt_document_type'] == e.value\n", "for column in [\"organization\", \"portal_name\", \"portal_scraper\", \"gt_document_type\"]:\n", "    for o in set(df[column]):\n", "        covariates[f\"{column} == {o}\"] = df[column] == o\n", "\n", "\n", "for o in set(df[\"gt_posted_date\"].apply(lambda x: x.year)):\n", "    covariates[f\"gt_posted_date.year == {o}\"] = df[\"gt_posted_date\"].apply(lambda x: x.year) == o\n", "\n", "for v, q in covariates.items():\n", "    s_df = df[q]\n", "    print()\n", "    print(f\" Stats for {v}\")\n", "    for b in bools:\n", "        total = s_df[b].sum()\n", "        n = float(len(s_df))\n", "        print(f\"Accuracy {b}\", int(1000*total/n)/10, \"vs\", base_line[b], total, n)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Create the histogram\n", "s_df = df[df[\"gt_effective_date\"].apply(lambda x: x.year in {2024, 2025})]\n", "print(len(df))\n", "print(len(s_df))\n", "data = ((s_df[\"pred_effective_date\"] - s_df[\"gt_effective_date\"])/ np.timedelta64(1, \"D\"))\n", "data = np.abs((s_df[\"pred_effective_date\"] - s_df[\"gt_effective_date\"])/ np.timedelta64(1, \"D\"))\n", "n = 7\n", "data[data > n] = n\n", "data[data < -n] = -n\n", "\n", "plt.hist(data, bins=n, color=\"skyblue\", edgecolor=\"black\", weights=np.ones(len(data)) / len(data))\n", "# plt.hist(data, bins=n, color='skyblue', edgecolor='black')\n", "# plt.yscale('log')\n", "\n", "# Add labels and title\n", "plt.xlabel(\"Value\")\n", "plt.ylabel(\"Frequency\")\n", "plt.title(\"Histogram of Sample Data\")\n", "\n", "# Show the histogram\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s_df = df[df[\"gt_posted_date\"].apply(lambda x: x.year in {2024, 2025})]\n", "\n", "very_wrong = s_df[np.abs((s_df[\"pred_posted_date\"] - s_df[\"gt_posted_date\"])  / np.timedelta64(1, \"D\")) > 10]\n", "very_wrong = very_wrong[very_wrong[\"gt_document_type\"] != \"Capital Call\"]\n", "n = float(len(very_wrong))\n", "print(n)\n", "print(\"trying to see what percentage are 'invalid', meaning posted_date is less than effecitve_date\")\n", "print(len(very_wrong[very_wrong[\"pred_posted_date\"] < very_wrong[\"pred_effective_date\"]])/n)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Filter data for years 2024 and 2025\n", "# s_df = df[df['gt_posted_date'].apply(lambda x: x.year in {2024, 2025})]\n", "s_df = df\n", "column = \"posted_date\"\n", "print(len(df))\n", "print(len(s_df))\n", "\n", "def plot_date_diff_histogram(s_df, column, n = 7, absolute=True):\n", "    # Compute absolute difference in days\n", "    if absolute:\n", "        data = np.abs((s_df[f\"pred_{column}\"] - s_df[f\"gt_{column}\"]) / np.timedelta64(1, \"D\"))\n", "    else:\n", "        data = ((s_df[f\"pred_{column}\"] - s_df[f\"gt_{column}\"]) / np.timedelta64(1, \"D\"))\n", "\n", "    # Cap data at n days\n", "    data[data > n] = n\n", "    data[data < -n] = -n\n", "\n", "    # Create histogram with percentage weights\n", "    weights = np.ones(len(data)) / len(data)  # Normalize to sum to 1\n", "    counts, bins, patches = plt.hist(data, bins=min(n,10), color=\"skyblue\", edgecolor=\"black\", weights=weights)\n", "\n", "    # Convert to percentage\n", "    counts *= 100\n", "\n", "    # Format x-axis labels to include percentages\n", "    bin_labels = [f\"{int(b)} ({count:.1f}%)\" for b, count in zip(bins[:-1], counts, strict=False)]\n", "    plt.xticks(bins[:-1] + (bins[1] - bins[0]) / 2, bin_labels, fontsize=10, rotation=90)\n", "\n", "    # Labels and title\n", "    plt.xlabel(\"Days Difference\")\n", "    plt.ylabel(\"Percentage\")\n", "    plt.title(f\"Histogram of {column} pred-gt\")\n", "\n", "    # Adjust y-limits to prevent clipping\n", "    plt.ylim(bottom=0)\n", "\n", "    # Show plot\n", "    plt.show()\n", "plot_date_diff_histogram(s_df, \"posted_date\", n = 30, absolute=False)\n", "plot_date_diff_histogram(s_df, \"effective_date\", n = 30, absolute=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import seaborn as sns\n", "\n", "# Generate the confusion matrix\n", "conf_matrix = pd.crosstab(df[\"pred_document_type\"], df[\"gt_document_type\"])\n", "\n", "# Get category labels\n", "categories = conf_matrix.index.union(conf_matrix.columns)  # Ensure all labels are included\n", "conf_matrix = conf_matrix.reindex(index=categories, columns=categories, fill_value=0)\n", "\n", "# Compute True Positives (TP)\n", "TP = np.diag(conf_matrix)\n", "\n", "# Compute False Positives (FP) - Sum of column minus TP\n", "FP = conf_matrix.sum(axis=0) - TP\n", "\n", "# Compute False Negatives (FN) - Sum of row minus TP\n", "FN = conf_matrix.sum(axis=1) - TP\n", "\n", "# Compute precision and recall\n", "precision = np.where((TP + FP) > 0, TP / (TP + FP), 0)  # Avoid division by zero\n", "recall = np.where((TP + FN) > 0, TP / (TP + FN), 0)  # Avoid division by zero\n", "\n", "# Convert to Pandas Series for easy labeling\n", "precision_series = pd.Series(precision, index=categories)\n", "recall_series = pd.Series(recall, index=categories)\n", "\n", "# Create figure\n", "fig, ax = plt.subplots(figsize=(10, 7))\n", "\n", "# Draw heatmap\n", "sns.heatmap(conf_matrix, annot=True, fmt=\"d\", cmap=\"Blues\", linewidths=0.5, ax=ax, cbar_kws={\"shrink\": 0.8, \"pad\": 0.15})\n", "\n", "# Set axis labels\n", "ax.set_xlabel(\"Ground Truth Document Type\")\n", "ax.set_ylabel(\"Predicted Document Type\")\n", "ax.set_title(\"Confusion Matrix with Precision & Recall\")\n", "\n", "# Add precision above columns\n", "for idx, label in enumerate(categories):\n", "    ax.text(idx + 0.5, -0.5, f\"P:{100*precision_series[label]:.0f}\",\n", "            ha=\"center\", va=\"center\", fontsize=10, color=\"black\", fontweight=\"bold\")\n", "\n", "# Add recall to the right of rows\n", "for idx, label in enumerate(categories):\n", "    ax.text(len(categories) + 0.5, idx + 0.5, f\"R:{100*recall_series[label]:.0f}\",\n", "            ha=\"left\", va=\"center\", fontsize=10, color=\"black\", fontweight=\"bold\")\n", "\n", "# Adjust layout\n", "plt.xticks(rotation=90)\n", "plt.yticks(rotation=0)\n", "\n", "# Move color bar further to the right\n", "plt.subplots_adjust(right=0.85)\n", "\n", "# Show plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s_df = df[df[\"same_line_items\"] == False]\n", "n = float(len(s_df))\n", "tot = s_df[s_df[\"pred_line_items_len\"] == s_df[\"gt_line_items_len\"]]\n", "diff = s_df[s_df[\"pred_line_items_len\"] != s_df[\"gt_line_items_len\"]]\n", "print(\"Percent wrong where different number of line items\", 1-len(tot)/n)\n", "print(\"Percent where ground truth line items are inside these line items\", s_df[\"gt_is_subset_of_pred_line_items\"].sum() / n)\n", "print(\"When line item lengths differ, Percent where ground truth line items are a subset\", diff[\"gt_is_subset_of_pred_line_items\"].sum()/float(len(diff)))\n", "s_df = df[df[\"same_line_items\"] | df[\"gt_is_subset_of_pred_line_items\"]]\n", "print(\"Adjusted overall percent same or subset: \", len(s_df)/float(len(df)))\n", "print(\"UnAdjusted overall percent only same : \", len(df[df[\"same_line_items\"]])/float(len(df)))\n", "s_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_wrong = df[~df[\"same_line_items\"] & ~df[\"same_document_type\"] & ~df[\"same_effective_date\"] & ~df[\"same_posted_date\"]]\n", "\n", "all_wrong.iloc[1].to_dict()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}