import json
import zoneinfo
from collections.abc import Callable

import structlog
from django.contrib.messages import get_messages
from django.http import HttpRe<PERSON>, HttpResponse
from django.shortcuts import redirect
from django.urls import Resolver404, resolve, reverse
from django.utils import timezone

logger = structlog.get_logger(__name__)


class TimezoneMiddleware:
    def __init__(self, get_response: Callable[[HttpRequest], HttpResponse]) -> None:
        self.get_response = get_response

    def __call__(self, request: HttpRequest) -> HttpResponse:
        try:
            # get django_timezone from cookie
            tzname = request.COOKIES.get("django_timezone")
            if tzname:
                timezone.activate(zoneinfo.ZoneInfo(tzname))
            else:
                timezone.deactivate()
        except Exception:
            logger.exception("Error getting/setting timezone")
            timezone.deactivate()

        return self.get_response(request)


class RedirectAuthenticatedUserMiddleware:
    """
    Middleware to redirect authenticated users from specific paths to the dashboard.
    """

    def __init__(self, get_response: Callable[[HttpRequest], HttpResponse]) -> None:
        self.get_response = get_response
        # Define the URL names to check
        self.protected_url_names = [
            "account_reset_password_from_key",  # Password reset key path
            "accept_invitation",  # Accept invitation path
        ]

    def __call__(self, request: HttpRequest) -> HttpResponse:
        if request.user.is_authenticated:
            # Resolve the URL name from the request path
            try:
                resolved_url_name = resolve(request.path).url_name
            except Resolver404:
                resolved_url_name = None
            if resolved_url_name in self.protected_url_names:
                return redirect("dashboard")  # Replace 'dashboard' with your dashboard URL name
        return self.get_response(request)


class HtmxMessageMiddleware:
    """
    Middleware that moves messages into the HX-Trigger header when request is made with HTMX
    from: https://github.com/bblanchon/django-htmx-messages-framework/blob/hx-trigger/htmx_messages/middleware.py#L22
    """

    def __init__(self, get_response: Callable[[HttpRequest], HttpResponse]) -> None:
        self.get_response = get_response

    def __call__(self, request: HttpRequest) -> HttpResponse:
        response = self.get_response(request)

        # The HX-Request header indicates that the request was made with HTMX
        if "HX-Request" not in request.headers or "HX-Boosted" in request.headers:
            return response

        # Ignore redirections because HTMX cannot read the headers
        if 300 <= response.status_code < 400:  # noqa: PLR2004
            return response

        # Extract the messages
        messages = [
            {
                "message": message.message,
                "tags": message.tags,
                "level": message.level,
                "extra_tags": message.extra_tags,
            }
            for message in get_messages(request)
        ]
        if not messages:
            return response

        # Get the existing HX-Trigger that could have been defined by the view
        hx_trigger = response.headers.get("HX-Trigger")

        if hx_trigger is None:
            # If the HX-Trigger is not set, start with an empty object
            hx_trigger = {}
        elif hx_trigger.startswith("{"):
            # If the HX-Trigger uses the object syntax, parse the object
            hx_trigger = json.loads(hx_trigger)
        else:
            # If the HX-Trigger uses the string syntax, convert to the object syntax
            hx_trigger = {hx_trigger: True}

        # Add the messages array in the HX-Trigger object
        hx_trigger["django_messages"] = messages

        # Add or update the HX-Trigger
        response.headers["HX-Trigger"] = json.dumps(hx_trigger)

        return response


class TermsOfServiceMiddleware:
    """
    Middleware to enforce that authenticated users accept the latest Terms of Service (TOS)
    before they can access the platform.

    - Redirects users to the TOS agreement page if they have never accepted or if the TOS has been updated.
    - Excludes the Django Admin panel, logout page, TOS agreement page, and static files from enforcement.
    """

    def __init__(self, get_response: Callable[[HttpRequest], HttpResponse]) -> None:
        """
        Initializes the middleware.

        Args:
            get_response (Callable[[HttpRequest], HttpResponse]):
                The next middleware or view function in the request chain.

        """
        self.get_response = get_response

    def __call__(self, request: HttpRequest) -> HttpResponse:
        """
        Middleware entry point. Called on every request.

        Args:
            request (HttpRequest): The incoming request object.

        Returns:
            HttpResponse: The response object, either processed further or redirected.

        """
        # Apply only to authenticated users who haven't accepted the latest ToS
        user = request.user
        if not user.is_authenticated or user.is_hijacked:
            return self.get_response(request)

        if user.tos_accepted_at is None or user.tos_last_updated_at > user.tos_accepted_at:
            # List of allowed paths where the middleware should not enforce the TOS check
            allowed_paths: list[str] = [
                reverse("tos_agreement_page"),  # Allow users to access the TOS agreement page
                reverse("account_logout"),  # Allow users to log out
            ]

            # Exclude specific paths:
            if (
                request.path.startswith("/static/")  # Allow static files to load
                or request.path in allowed_paths  # Allow TOS agreement & logout pages
            ):
                return self.get_response(request)  # Continue processing request

            # Redirect user to TOS agreement page if they have not accepted the latest terms
            return redirect("tos_agreement_page")

        # Continue processing the request if no conditions are met
        return self.get_response(request)
