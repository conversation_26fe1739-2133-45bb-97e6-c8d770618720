{% load customtags tz static %}
<div class="run_cell w-full flex flex-row items-center gap-4 min-h-[60px] h-full"
     id="run_cell_{{ merged_portal_credential.pk }}"
     hx-get="{% url 'run_retrieval' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
     hx-trigger="retrievalStarted[detail.merged_portal_credential_pk=='{{ merged_portal_credential.pk }}'] from:body, retrievalStatusUpdate[detail.merged_portal_credential_pk=='{{ merged_portal_credential.pk }}'] from:body"
     hx-swap="outerHTML">
    <div class="flex-shrink-0 flex flex-col items-center">
        {% if merged_portal_credential.last_retrieval and merged_portal_credential.last_retrieval.is_retrieval_running %}
            <!-- Active Retrieval Button -->
            <bridge-run-retrieval-button data-state="active"></bridge-run-retrieval-button>
        {% else %}
            {% if merged_portal_credential.connection_health_status == 'li' and not merged_portal_credential.is_first_retrieval %}
                {% if merged_portal_credential.last_retrieval and merged_portal_credential.last_retrieval.next_available_time|is_future %}
                    <!-- Disabled Run Retrieval Button with Tooltip -->
                    <bridge-run-retrieval-button data-state="disabled" data-tooltip-text="Next available at: {{ merged_portal_credential.last_retrieval.next_available_time|localtime|date:'g:i A' }}">
                    </bridge-run-retrieval-button>
                {% else %}
                    <!-- Enabled Run Retrieval Button -->
                    <bridge-run-retrieval-button data-state="enabled" data-hx-post="{% url 'run_retrieval' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}" data-hx-target="#run_cell_{{ merged_portal_credential.pk }}">
                    </bridge-run-retrieval-button>
                {% endif %}
            {% else %}
                <!-- Disabled Run Retrieval Button -->
                <bridge-run-retrieval-button data-state="disabled"></bridge-run-retrieval-button>
            {% endif %}
        {% endif %}
    </div>
    {% include "link/portal/run_cell_status.html" with merged_portal_credential=merged_portal_credential RetrievalStatus=RetrievalStatus OnboardingStatus=OnboardingStatus MFAType=MFAType %}
</div>
