{% load customtags static widget_tweaks %}
<div id="modal-container-{{ line_item.pk }}">
    <div class="relative w-full max-w-lg max-h-full">
        <!-- Modal content -->
        <div class="relative rounded-lg">
            <!-- Modal header -->
            <div class="flex items-center justify-between px-4 pt-4 rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    <div class="FeaturedIcon w-12 h-12 px-[13px] pt-[15px] pb-[13px] bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
                        <div class="Icon w-[22px] h-5 relative flex-col justify-start items-start flex">
                            <img alt="ImageIcon"
                                 width="22"
                                 height="21"
                                 src="{% static 'assets/images/email_icon.svg' %}">
                        </div>
                    </div>
                </h3>
                <button _="on click trigger closeModal"
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                    <img src="{% static 'assets/images/close.svg' %}"
                         class="w-3 h-3"
                         width="12"
                         height="12"
                         alt="closeButton">
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <form class="p-3"
                  hx-post="{% url 'emails_edit_setup' %}{% urlparams line_item_pk=line_item.pk %}"
                  hx-swap="outerHTML"
                  hx-target="#modal-container-{{ line_item.pk }}">
                {% csrf_token %}
                {% for field in form.visible_fields %}
                    <div class="px-2 w-full">
                        <div class="flex items-center mb-4">
                            <label class="text-gray-700 font-medium pr-2 w-1/5"
                                   for="{{ field.id_for_label }}">{{ field.label }}</label>
                            <div class="w-4/5 relative">
                                {{ field|attr:"class:w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400" }}
                            </div>
                        </div>
                    </div>
                {% endfor %}
                <div class="Content w-full flex-col justify-start items-start gap-5 inline-flex">
                    <div class="Fields self-stretch justify-start items-start gap-3 inline-flex">
                        <div class="ButtonsButton grow shrink basis-0 h-11 px-4 py-2.5 bg-[#924f34] rounded-lg border border-[#924f34] justify-center items-center gap-1.5 flex">
                            <button type="submit"
                                    name="submit"
                                    value="submit"
                                    class="TextPadding px-0.5 justify-center items-center flex">
                                <div class="Text text-white text-base font-semibold font-inter leading-normal">Save</div>
                            </button>
                        </div>
                        <button _="on click trigger closeModal"
                                type="button"
                                class="ButtonsButton grow shrink basis-0 h-11 px-4 py-2.5 rounded-lg border border-[#924f34] justify-center items-center gap-1.5 flex">
                            <div class="TextPadding px-0.5 justify-center items-center flex">
                                <div class="Text text-[#924f34] text-base font-semibold font-inter leading-normal">Close Popup</div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
