import json
import urllib.parse as urlparse

from django.core.serializers.json import DjangoJSONEncoder
from django.http import Http404, HttpRequest
from django.http.response import HttpResponseRedirect, ResponseHeaders
from django.urls import reverse

from webapp.models.line_item import LineItem
from webapp.models.portal import MergedPortalCredential, Portal


def redirect_with_url_params(url_name: str, **kwargs: str | None) -> HttpResponseRedirect:
    to_encode_wo_none = {k: v for k, v in kwargs.items() if v is not None}
    res_param = urlparse.urlencode(to_encode_wo_none)
    res_url = reverse(url_name)
    return HttpResponseRedirect(f"{res_url}?{res_param}")


def get_merged_portal_credential_from_url_params(request: HttpRequest) -> MergedPortalCredential:
    merged_portal_credential_pk = request.GET.get("merged_portal_credential_pk", None)
    if merged_portal_credential_pk is None:
        raise Http404
    try:
        merged_portal_credential = MergedPortalCredential.objects.for_user(request.user).get(
            id=merged_portal_credential_pk
        )
    except MergedPortalCredential.DoesNotExist as e:
        raise Http404 from e
    return merged_portal_credential


def get_portal_from_url_params(request: HttpRequest, *, raises: bool = False) -> Portal | None:
    portal_pk = request.GET.get("portal_pk", None)
    if portal_pk is None and raises:
        raise Http404
    if portal_pk is None:
        return None
    try:
        portal = Portal.objects.for_user(request.user).get(id=portal_pk)
    except MergedPortalCredential.DoesNotExist as e:
        if raises:
            raise Http404 from e
        return None
    return portal


def get_line_item_from_url_params(request: HttpRequest) -> LineItem:
    line_item_pk = request.GET.get("line_item_pk", None)
    if line_item_pk is None:
        raise Http404
    try:
        line_item = LineItem.objects.for_user(request.user).get(id=line_item_pk)
    except LineItem.DoesNotExist as e:
        raise Http404 from e
    return line_item


def add_hx_trigger_header(trigger: dict, header: ResponseHeaders | None = None) -> ResponseHeaders:
    if header is None:
        header = ResponseHeaders(data={})
    if "HX-Trigger" in header:
        existing_trigger = json.loads(header["HX-Trigger"])
        existing_trigger.update(trigger)
        trigger = existing_trigger
    header["HX-Trigger"] = json.dumps(trigger, cls=DjangoJSONEncoder)
    return header
