name: AWS Service CI/CD Rebuild Docker and Deploy

on:
  issue_comment:
    types: [created]

jobs:
  build:
    if: > 
      github.event.issue.pull_request &&
      (github.event_name == 'issue_comment') &&
      (startsWith(github.event.comment.body, '/deploy prod') || startsWith(github.event.comment.body, '/deploy demo'))
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
      pull-requests: write
    env:
      NERDCTL_VERSION: "1.7.7"
      SOCI_VERSION: "0.8.0"
      AWS_ROLE: > 
        ${{
          startsWith(github.event.comment.body, '/deploy prod') &&
          'arn:aws:iam::180294215839:role/ConfigStack-GitHubActionsRole4F1BBA26-71zxRqETdMkp' ||
          'arn:aws:iam::654654313761:role/ConfigStack-GitHubActionsRole4F1BBA26-Vf9PCMMYmDTK'
        }}
      BUILD_URL: "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
      DEPLOY_ARGS: "${{  github.event.comment.body }}"
    steps:

    - name: 🚀 Comment on PR - Build Started
      uses: actions/github-script@v7
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const prNumber = context.payload.issue.number;
          const buildUrl = process.env.BUILD_URL;
          github.rest.issues.createComment({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: prNumber,
            body: `🚀 Deployment started! Check progress [here](${buildUrl}).`
          });

    - name: Delete huge unnecessary tools folder
      run: |
        rm -rf /opt/hostedtoolcache

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        audience: sts.amazonaws.com
        aws-region: us-east-1
        role-to-assume: ${{ env.AWS_ROLE }}

    - name: 💥🥊 Checkout the Code 💥🥊
      uses: actions/checkout@v4
      with:
        ref: refs/pull/${{ github.event.issue.number }}/head

    - name: ☀️️🕶 Install uv 🕶☀
      uses: astral-sh/setup-uv@v5
      with:
        enable-cache: true
        cache-dependency-glob: "uv.lock"
        pyproject-file: "pyproject.toml"

    - name: 🐳 Set up Docker Buildx 🐳
      uses: docker/setup-buildx-action@v3
      with:
        install: true

    - name: 🤓 Set up nerdctl & soci 🤓
      run: |
        wget https://github.com/containerd/nerdctl/releases/download/v$NERDCTL_VERSION/nerdctl-full-$NERDCTL_VERSION-linux-amd64.tar.gz
        sudo tar Cxzvvf /usr/local nerdctl-full-$NERDCTL_VERSION-linux-amd64.tar.gz
        sudo systemctl enable --now buildkit
        wget https://github.com/awslabs/soci-snapshotter/releases/download/v$SOCI_VERSION/soci-snapshotter-$SOCI_VERSION-linux-amd64.tar.gz
        sudo tar -C /usr/local/bin -xvf soci-snapshotter-$SOCI_VERSION-linux-amd64.tar.gz soci soci-snapshotter-grpc

    - name: ☁ Deploy to AWS ECS ☁
      working-directory: .
      run: |
        ./deploy.py build-push-bounce --tag latest

    - name: ✅ Comment on PR - Build Finished
      if: always()
      uses: actions/github-script@v7
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const prNumber = context.payload.issue.number;
          const buildUrl = process.env.BUILD_URL;
          const status = "${{ job.status }}";
          github.rest.issues.createComment({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: prNumber,
            body: `✅ Deployment finished! Status: **${status}**. Check details [here](${buildUrl}).`
          });
