import uuid

import structlog
from django.db import models, transaction
from django.utils.translation import gettext_lazy as _

logger = structlog.get_logger(__name__)

import typing

from webapp.models.core import AbstractBaseModel, has_write_permissions
from webapp.models.user import BridgeUser

if typing.TYPE_CHECKING:
    from webapp.models import InvestingEntity, Investment, MergedPortalCredential, Retrieval
    from webapp.models.documents import (
        CapitalCallDocumentFact,
        DistributionNoticeDocumentFact,
        InvestmentUpdateDocumentFact,
        ProcessedDocument,
    )
    from webapp.models.emails import EmailRetrieval

ERROR_ADMIN_MANAGER_ONLY = _("Only admins and managers can grant permissions")
ERROR_CROSS_ORGANIZATION = _("Cannot grant access across different organizations")
ERROR_DUPLICATE_ACCESS = _("User already has access to this line item")
ERROR_LINE_ITEM_PERMISSION_NOT_FOUND = _("User does not have permission to access this line item")


class LineItemPermissionError(PermissionError):
    """Base exception for line item permission-related errors."""


class InvalidGranterError(LineItemPermissionError):
    """Raised when someone without proper permissions tries to grant/revoke access."""


class CrossOrganizationError(LineItemPermissionError):
    """Raised when trying to grant/revoke access across different organizations."""


class DuplicatePermissionError(LineItemPermissionError):
    """Raised when trying to grant duplicate permissions."""


class LineItemPermission(AbstractBaseModel):
    line_item = models.ForeignKey("LineItem", on_delete=models.CASCADE)
    permittee = models.ForeignKey("BridgeUser", on_delete=models.CASCADE, related_name="line_item_permissions")
    grantee = models.ForeignKey("BridgeUser", on_delete=models.CASCADE, related_name="granted_permissions")
    batch_id = models.UUIDField(
        default=uuid.uuid4,
        editable=False,
        help_text="Identifier for a batch of permission changes updated at the same time.",
    )

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_item"]


class LineItem(AbstractBaseModel):
    # NOTE: never add *actual* fields here. This is a join table.
    # NOTE: add as many derived fields, and methods as you want
    investing_entity: "InvestingEntity" = models.ForeignKey(
        "InvestingEntity", on_delete=models.CASCADE, related_name="line_items"
    )
    investment: "Investment" = models.ForeignKey("Investment", on_delete=models.CASCADE, related_name="line_items")

    last_retrieval: "Retrieval" = models.ForeignKey(
        "Retrieval", on_delete=models.SET_NULL, null=True, blank=True, related_name="latest_line_items"
    )
    merged_portal_credential: "MergedPortalCredential" = models.ForeignKey(
        "MergedPortalCredential", on_delete=models.CASCADE, related_name="line_items"
    )
    is_visible = models.BooleanField(default=True)
    email_retrieval: "EmailRetrieval"
    # Retrievals is a many to many relationship with line items.
    # This field represents all the retrievals run where this line item was involved.
    # Involvement is defined by the user's input when associating their MPC with their line items.
    retrievals: models.QuerySet["Retrieval"]
    processed_documents: models.QuerySet["ProcessedDocument"]
    # NOTE: this is legacy naming this should be "AccountStatementDocumentFact"
    investment_update_documents: models.QuerySet["InvestmentUpdateDocumentFact"]
    distribution_notice_documents: models.QuerySet["DistributionNoticeDocumentFact"]
    capital_call_documents: models.QuerySet["CapitalCallDocumentFact"]
    is_backfilled = models.BooleanField(default=False)

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["id"]

    class Meta(AbstractBaseModel.Meta):
        unique_together = ("organization", "investing_entity", "investment")

    def __str__(self) -> str:
        return (
            f"{self.investing_entity.client}: "
            f"{self.investing_entity.legal_name} investment in {self.investment.legal_name}"
        )

    @property
    def latest_processed_docs_count(self) -> int:
        return self.processed_documents.filter(
            is_visible=True, raw_retreival_document__retrieval=self.merged_portal_credential.last_retrieval
        ).count()

    @property
    def last_processed_doc(self) -> int:
        return self.processed_documents.filter(is_visible=True).order_by("-posted_date").first()

    @property
    def total_processed_docs_count(self) -> int:
        return self.processed_documents.filter(is_visible=True).count()

    @classmethod
    def create_from_merged_portal_credential(  # noqa: PLR0913
        cls,
        merged_portal_credential: "MergedPortalCredential",
        client_legal_name: str,
        entity_legal_name: str,
        investment_fund_legal_name: str,
        investment_managing_firm_name: str,
        *,
        is_visible: bool = False,  # Default to not visible, must mark as visible in admin panel
    ) -> "LineItem":
        from webapp.models import (
            Client,
            InvestingEntity,
            Investment,
        )

        user = merged_portal_credential.created_by

        with transaction.atomic():
            client, _ = Client.objects.update_or_create(user=user, legal_name=client_legal_name)
            investing_entity, _ = InvestingEntity.objects.update_or_create(
                user=user,
                legal_name=entity_legal_name,
                defaults={
                    "client": client,
                },
            )
            investment, _ = Investment.objects.update_or_create(
                user=user,
                legal_name=investment_fund_legal_name,
                managing_firm_name=investment_managing_firm_name,
            )

            line_item = LineItem.objects.create(
                user=user,
                investing_entity=investing_entity,
                investment=investment,
                merged_portal_credential=merged_portal_credential,
                is_visible=is_visible,
            )

            for retrieval in merged_portal_credential.retrievals.all():
                retrieval.line_items.add(line_item)
                retrieval.save()

            line_items = merged_portal_credential.line_items.all()
            for li in line_items:
                for retrieval in li.retrievals.all():
                    retrieval.line_items.add(line_item)
                    retrieval.save()

            last_retrieval = merged_portal_credential.retrievals.order_by("-created_at").first()
            if last_retrieval is not None:
                line_item.last_retrieval = last_retrieval
                line_item.save()
            return line_item

    def grant_access(self, permittee: BridgeUser, grantee: BridgeUser, batch_id: uuid.UUID) -> LineItemPermission:
        """
        Grant access to a line item for a specific user.
        """
        if not has_write_permissions(grantee):
            raise InvalidGranterError(ERROR_ADMIN_MANAGER_ONLY)

        if grantee.organization != self.organization or permittee.organization != self.organization:
            raise CrossOrganizationError(ERROR_CROSS_ORGANIZATION)

        # Currently we allow duplicate permissions within the same batch
        return LineItemPermission.objects.create(
            user=grantee,
            line_item=self,
            permittee=permittee,
            grantee=grantee,
            batch_id=batch_id,
        )

    def revoke_access(self, permittee: BridgeUser, grantee: BridgeUser, batch_id: uuid.UUID) -> None:
        """
        Revoke access to a line item for a specific user.
        """
        if not has_write_permissions(grantee):
            raise InvalidGranterError(ERROR_ADMIN_MANAGER_ONLY)

        if grantee.organization != self.organization or permittee.organization != self.organization:
            raise CrossOrganizationError(ERROR_CROSS_ORGANIZATION)

        try:
            line_item_permission = LineItemPermission.objects.get(
                line_item=self, permittee=permittee, batch_id=batch_id
            )
        except LineItemPermission.DoesNotExist:
            raise LineItemPermissionError(ERROR_LINE_ITEM_PERMISSION_NOT_FOUND) from None

        line_item_permission.delete()
