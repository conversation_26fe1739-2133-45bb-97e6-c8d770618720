{% load customtags %}
{% load static %}
{% if not latest_update.processed_doc.has_been_viewed %}
    {% alias "bg-secondary-orange-beige font-semibold" as background %}
{% else %}
    {% alias "bg-white" as background %}
{% endif %}
<tr class="border-b {{ background }} h-[47px]"
    {# djlint:off #}
    _="
    on openedProcessedDocPdf[pk=='{{ latest_update.processed_doc.pk }}'] from document
        remove .bg-secondary-orange-beige remove .font-semibold add .bg-white
    end
    "
    {# djlint:on #}
    >
    <td class="px-3 py-3 text-[#343D36] font-inter text-[14px] date-cell">{{ latest_update.date_received }}</td>
    <td class="px-3 py-3 text-[#343D36] font-inter text-[14px]">{{ latest_update.entity }}</td>
    <td class="px-3 py-3 text-[#343D36] font-inter text-[14px]">{{ latest_update.investment }}</td>
    <td class="px-3 py-3 text-[#343D36] font-inter text-[14px]">
        <div class="flex items-center gap-x-2">
            <a href="{% url 'pdf_modal' %}{% urlparams processed_doc_pk=latest_update.processed_doc.pk %}"
                target="_blank"
                title="{{ latest_update.processed_doc.name }}"
                {# djlint:off #}
               _="
                on click or auxclick
                    trigger openedProcessedDocPdf(pk:'{{ latest_update.processed_doc.pk }}')
                end
               "
                {# djlint:on #}
                >
                <img height="20"
                     width="20"
                     src="{% static 'assets/images/pdf.svg' %}"
                     alt="pdf icon"
                     class="inline-block" />
                <span>{% label_lookup label=latest_update.notice_type enum=DocumentType %}</span>
            </a>
        </div>
    </td>
</tr>
