{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[35mAXES: BEGIN version 7.0.2, blocking by ip_address or combination of username and user_agent\u001b[0m\n"]}], "source": ["# ruff: noqa\n", "import os\n", "from pathlib import Path\n", "\n", "# This forces to use localhost as the database URL instead of the docker container reference in docker compose\n", "os.environ[\"USE_LOCALHOST\"] = \"1\"\n", "# Make sure we're referencing PROD AWS assets\n", "os.environ[\"AWS_PROFILE\"] = \"prod\"\n", "# HACK: jupyter notebooks don't play well with Django ORM\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "# Make sure we use the production oauth secret for setting rules up for MFA scraping\n", "os.environ[\"USE_PROD_OAUTH_SECRET\"] = \"1\"  # noqa: S105\n", "import importlib\n", "\n", "import sys\n", "\n", "import django\n", "\n", "q = [Path.cwd()]\n", "while q:\n", "    cur = q.pop()\n", "    found = False\n", "    for path in cur.glob(\"*\"):\n", "        if path.is_dir() and path.name == \"site\":\n", "            if str(path) not in sys.path:\n", "                sys.path.append(str(path))\n", "            found = True\n", "            break\n", "    if not found and cur.parent != cur:\n", "        q.append(cur.parent)\n", "    else:\n", "        break\n", "os.environ[\"DJANGO_SETTINGS_MODULE\"] = \"bridge_project.settings\"\n", "os.environ[\"ENVIRONMENT\"] = \"dev\"\n", "django.setup()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from ml_app.core import doc_vault_lib\n", "from webapp.models import RawDocument\n", "\n", "importlib.reload(doc_vault_lib)\n", "import json\n", "\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_core.language_models import BaseChatModel"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[35mFound credentials in shared credentials file: ~/.aws/credentials\u001b[0m\n"]}, {"data": {"text/plain": ["<ml_app.core.pdf_summarizer.PdfAISummarizer at 0x163db9be0>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from ml_app.core.pdf_summarizer import PdfAISummarizer\n", "\n", "pdf_summarizer = PdfAISummarizer()\n", "\n", "pdf_summarizer"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["14084"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from ml_app.core.pdf_summarizer import PdfAISummarizer\n", "\n", "pdf_summarizer = PdfAISummarizer()\n", "\n", "file_path = \"/Users/<USER>/projects/bridge-python/site/ml_app/Blockchain Capital V LP Update Q1 2025.pdf\"\n", "\n", "text = pdf_summarizer.extract_full_text_from_file(file_path)\n", "\n", "len(text)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-25T16:59:13.445644Z\u001b[0m \u001b[1m\u001b[35mStarting PDF summarization\u001b[0m \u001b[36mfile_path\u001b[0m=\u001b[32m/Users/<USER>/projects/bridge-python/site/ml_app/Blockchain Capital V LP Update Q1 2025.pdf\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m \u001b[36mstrategy\u001b[0m=\u001b[32mstuff\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-25T16:59:13.476088Z\u001b[0m \u001b[1m\u001b[35mSummarizing text using the stuff strategy\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Here's a concise, structured summary of the Blockchain Capital Q1 2025 Investor Newsletter:\n", "\n", "**Market Overview**\n", "- Bitcoin reached new all-time highs before market correction\n", "- Ethereum maintained $72B total value locked (TVL)\n", "- Solana TVL rose 20% to $11B with $5.5B daily transaction volume\n", "- Stablecoins hit record levels: USDT over $140B, USDC up 20% to $55B\n", "- Regulatory environment improving with supportive executive orders and potential congressional legislation\n", "\n", "**Fund Performance**\n", "- Net NAV to LPs: 1.0x\n", "- Net IRR to LPs: 0%\n", "- Distribution Per Paid-In Capital (DPI): 0.2x\n", "- Total Portfolio Companies/Digital Assets: 61\n", "- Total Exits to Date: 23\n", "\n", "**Portfolio Highlights**\n", "- Key Portfolio Investments:\n", "  * EigenLayer: Exceeded $7B in restaked assets\n", "  * Gameplay Galaxy: Growing Web3 gaming platform\n", "  * Blocknative: Highest single portfolio allocation at 4.01%\n", "- Investment Sectors: DeFi (34%), L1s/L2s (28%), Infrastructure (23%)\n", "- Investment Types: Digital Asset (57%), Equity (25%), SAFE (16%)\n", "\n", "**Valuations & Metrics**\n", "- Total Unrealized Portfolio Value: $248,133,627\n", "- Total Realized Portfolio Value: $124,856,915\n", "- Total Portfolio Value: $372,990,542\n", "- Capital Called from LPs: 100%\n", "- Uninvested Capital: $4M\n", "\n", "**Strategic Updates**\n", "- Continued focus on foundational blockchain technologies\n", "- Emphasis on investments with measurable utility and institutional-grade infrastructure\n", "- Portfolio diversification across multiple digital asset and equity investments\n", "\n", "**Outlook**\n", "- Anticipating continued market maturation\n", "- Focusing on compliance-forward innovation\n", "- Committed to investing in transformative blockchain technologies\n"]}], "source": ["result = await pdf_summarizer.summarize_pdf_from_file(file_path, strategy='stuff')\n", "\n", "print(result['summary'])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-24T21:39:19.977791Z\u001b[0m \u001b[1m\u001b[35mStarting PDF summarization\u001b[0m \u001b[36mfile_path\u001b[0m=\u001b[32m/Users/<USER>/projects/bridge-python/site/ml_app/Blockchain Capital V LP Update Q1 2025.pdf\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m \u001b[36mstrategy\u001b[0m=\u001b[32mmap_reduce\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-24T21:39:20.011234Z\u001b[0m \u001b[1m\u001b[35mSplit document into 5 chunks for map-reduce\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-24T21:39:20.012335Z\u001b[0m \u001b[1m\u001b[35mProcessing chunk 1/5\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-24T21:39:25.475244Z\u001b[0m \u001b[1m\u001b[35mProcessing chunk 2/5\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-24T21:39:31.618724Z\u001b[0m \u001b[1m\u001b[35mProcessing chunk 3/5\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-24T21:39:37.147287Z\u001b[0m \u001b[1m\u001b[35mProcessing chunk 4/5\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-24T21:39:42.882886Z\u001b[0m \u001b[1m\u001b[35mProcessing chunk 5/5\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Comprehensive Summary of Blockchain Capital V, LP Q1 2025 Investor Document\n", "\n", "Portfolio Overview:\n", "- Total Portfolio Value: $372,990,542\n", "- Total Investment Cost: $343,631,555\n", "- Total Investments: 61 digital asset companies/investments\n", "- 23 exits to date\n", "- 100% of capital called from limited partners\n", "- $4M remains uninvested\n", "\n", "Market and Investment Highlights:\n", "\n", "1. Digital Asset Market Performance:\n", "- Bitcoin reached new all-time highs\n", "- Ethereum maintained $72B in total value locked (TVL)\n", "- Solana's TVL increased 20% to $11B\n", "\n", "2. Top Digital Asset Investments:\n", "- ETH: $31.7M (initial cost $34.3M)\n", "- Aave: $35.3M (initial cost $13.9M)\n", "- TIA (Celestia Foundation): $16.4M (initial cost $5.1M)\n", "\n", "3. Portfolio Composition:\n", "- Investment Types: Equity, SAFE, SAFT\n", "- Top Equity Investments:\n", "  1. Blocknative (4.01%)\n", "  2. Matter Labs (3.33%)\n", "  3. <PERSON><PERSON> (3.33%)\n", "  4. Monomer Labs (2.67%)\n", "  5. <PERSON> (2.33%)\n", "\n", "4. Notable Portfolio Developments:\n", "- EigenLayer surpassed $7B in restaked assets\n", "- Gameplay Galaxy's Trial Xtreme Freedom gaining Web3 gaming traction\n", "\n", "5. Stablecoin Ecosystem:\n", "- Tether exceeded $140B in circulation\n", "- USDC grew 20% to $55B\n", "- Increased institutional integration\n", "\n", "Regulatory and Strategic Context:\n", "- Supportive executive orders from President <PERSON>\n", "- More crypto-friendly SEC leadership\n", "- Bipartisan momentum for stablecoin regulation\n", "\n", "Fund Performance:\n", "- Net NAV to LPs: 1.0x\n", "- Net IRR to LPs: 0%\n", "\n", "Key Investment Strategy:\n", "- Continued focus on foundational technologies and protocols\n", "- Diverse investments in blockchain and digital asset ecosystem\n", "- High-risk, high-potential investment approach\n", "\n", "Investor Considerations:\n", "- Confidential and proprietary information\n", "- High-risk investment vehicle\n", "- Potential for entire principal loss\n", "- Recommended thorough due diligence\n", "- Consultation with professional advisors advised\n", "\n", "This summary provides a comprehensive overview of Blockchain Capital V, LP's Q1 2025 investment portfolio, market positioning, and strategic outlook in the digital asset space.\n"]}], "source": ["result = await pdf_summarizer.summarize_pdf_from_file(file_path, strategy='map_reduce')\n", "\n", "print(result['summary'])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-25T16:48:10.225593Z\u001b[0m \u001b[1m\u001b[35mStarting PDF summarization\u001b[0m \u001b[36mfile_path\u001b[0m=\u001b[32m/Users/<USER>/projects/bridge-python/site/ml_app/Blockchain Capital V LP Update Q1 2025.pdf\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m \u001b[36mstrategy\u001b[0m=\u001b[32mrefine\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-25T16:48:10.253882Z\u001b[0m \u001b[1m\u001b[35mSummarizing text using the refine strategy\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-25T16:48:10.255391Z\u001b[0m \u001b[1m\u001b[35mSplit document into chunks for refine\u001b[0m \u001b[36mchunk_count\u001b[0m=\u001b[32m5\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-25T16:48:16.499986Z\u001b[0m \u001b[1m\u001b[35mRefining with chunk\u001b[0m \u001b[36mchunk_number\u001b[0m=\u001b[32m2\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m \u001b[36mtotal_chunks\u001b[0m=\u001b[32m5\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-25T16:48:29.267141Z\u001b[0m \u001b[1m\u001b[35mRefining with chunk\u001b[0m \u001b[36mchunk_number\u001b[0m=\u001b[32m3\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m \u001b[36mtotal_chunks\u001b[0m=\u001b[32m5\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-25T16:48:42.580672Z\u001b[0m \u001b[1m\u001b[35mRefining with chunk\u001b[0m \u001b[36mchunk_number\u001b[0m=\u001b[32m4\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m \u001b[36mtotal_chunks\u001b[0m=\u001b[32m5\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n", "\u001b[1m\u001b[31minfo\u001b[0m \u001b[33m2025-07-25T16:48:59.576778Z\u001b[0m \u001b[1m\u001b[35mRefining with chunk\u001b[0m \u001b[36mchunk_number\u001b[0m=\u001b[32m5\u001b[0m \u001b[36mlogger\u001b[0m=\u001b[32mml_app.core.pdf_summarizer\u001b[0m \u001b[36mtotal_chunks\u001b[0m=\u001b[32m5\u001b[0m\n", "\u001b[1m\u001b[35mUsing Bedrock Invoke API to generate response\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The summary remains unchanged, as the provided additional context is a standard legal disclaimer and confidentiality notice that does not introduce any substantive new information or insights to modify the existing structured summary.\n", "\n", "1. Market Overview\n", "• Bitcoin reached new all-time highs before market correction\n", "• Ethereum maintained $72B total value locked (TVL)\n", "• Solana's TVL rose 20% to $11B\n", "• Stablecoins hit record volumes (Tether >$140B, USDC at $55B)\n", "\n", "2. Portfolio Highlights\n", "• EigenLayer surpassed $7B in restaked assets\n", "• Gameplay Galaxy's Trial Xtreme Freedom gaining traction in Web3 gaming\n", "• Total portfolio: 61 companies/digital assets\n", "• 23 exits to date\n", "• Top investments include:\n", "   - ETH (11.45% of portfolio, $31.7M value)\n", "   - Aave (4.64%, $35.3M value)\n", "   - Uniswap (3.99%, $12M value)\n", "   - Blocknative (4.01%, $22M valuation)\n", "   - Matter Labs (3.33%, $10M valuation)\n", "   - Abra (3.33%, $10M valuation)\n", "\n", "3. Valuations & Metrics\n", "• Net NAV to LPs: 1.0x\n", "• Net IRR to LPs: 0%\n", "• DPI: 0.2x\n", "• Total Portfolio Value: $372.9M\n", "• Total Capital Called: 100%\n", "• Uninvested Capital: $4M\n", "\n", "4. Strategic Updates\n", "• Investment Sector Breakdown:\n", "   - DeFi: 34%\n", "   - L1s/L2s: 28%\n", "   - Infrastructure: 23%\n", "   - Other sectors: 15%\n", "• Investment Types:\n", "   - Digital Asset: 57%\n", "   - Equity: 25%\n", "   - SAFE/SAFT: 18%\n", "\n", "5. Regulatory Landscape\n", "• President <PERSON> signed executive orders supporting digital financial technologies\n", "• SEC leadership change with potential more crypto-friendly approach\n", "• Bipartisan momentum for stablecoin regulation\n", "\n", "6. Outlook\n", "• Digital asset market maturing beyond hype cycles\n", "• Focus on foundational technologies and compliance-forward innovation\n", "• Continued institutional infrastructure development\n"]}], "source": ["result = await pdf_summarizer.summarize_pdf_from_file(file_path, strategy='refine')\n", "\n", "print(result['summary'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}