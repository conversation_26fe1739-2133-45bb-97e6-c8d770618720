{% load customtags %}
<div id="portal_dashboard_table"
     hx-post="{% url 'line_item_search' %}"
     hx-include=".line_item_search"
     hx-trigger="changeMergedPortalCredentialTable from:document"
     hx-swap="innerHTML">
    {% if merged_portal_credentials_with_line_items %}
        <div class="p-9">
            <table class="table-fixed w-full text-sm text-left rtl:text-right  border-b-2 border-secondary-orange-beige">
                <thead>
                    <tr>
                        <th class="w-2/5 px-3 py-2 text-left font-inter  text-[16px] border-b-2 border-r-2 border-secondary-orange-beige">
                            Portal / Inbox
                        </th>
                        <th class="w-1/8 px-3 py-2 text-left font-inter text-[16px] border-b-2 border-secondary-orange-beige">Credentials</th>
                        <th class="w-1/8 px-3 py-2 text-left font-inter text-[16px] border-b-2 border-secondary-orange-beige">
                            Multi-Factor Authentication
                        </th>
                        <th class="w-1/8 px-3 py-2 text-left font-inter text-[16px] border-b-2 border-secondary-orange-beige">
                            Connection Status
                        </th>
                        <th class="w-1/4 px-3 py-2 text-center font-inter text-[16px] border-b-2 border-l-2 border-secondary-orange-beige">
                            Retrieval
                        </th>
                    </tr>
                </thead>
                {% for merged_portal_credential_with_line_items in merged_portal_credentials_with_line_items %}
                    {% include "link/portal/merged_portal_credential.html" with merged_portal_credential_with_line_items=merged_portal_credential_with_line_items %}
                {% endfor %}
            </table>
        </div>
    {% else %}
        <p class="text-center text-gray-500 pt-12">No details available.</p>
    {% endif %}
</div>
