<!-- hijack/notification.html -->
{% load static %}
<link rel="stylesheet"
      type="text/css"
      href="{% static 'hijack/hijack.css' %}"
      media="screen">
<div class="djhj"
     id="djhj"
     {% if request.headers.hx_request == "true" %}style="display: none;"{% endif %}>
    <div class="djhj-notification">
        <div class="djhj-message">
            You are currently working on behalf of <em>{{ user }}</em>.
        </div>
        <form action="{% url 'hijack:release' %}"
              method="post"
              class="djhj-actions"
              hx-boost="false">
            {% csrf_token %}
            <input type="hidden"
                   name="next"
                   value="{% url 'admin:webapp_bridgeuser_changelist' %}">
            <button class="djhj-button"
                    onclick="document.getElementById('djhj').style.display = 'none';"
                    type="button">Hide</button>
            <button class="djhj-button" type="submit">Release</button>
        </form>
    </div>
</div>
