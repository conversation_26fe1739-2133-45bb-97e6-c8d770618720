import asyncio
import json
import os
import sys
import time
from pathlib import Path

import anyio
import django
from IPython.display import display
from playwright.async_api import Frame, async_playwright


def setup_django() -> None:
    q = [Path.cwd()]
    while q:
        cur = q.pop()
        found = False
        for path in cur.glob("*"):
            if path.is_dir() and path.name == "site":
                if str(path) not in sys.path:
                    sys.path.append(str(path))
                found = True
                break
        if not found and cur.parent != cur:
            q.append(cur.parent)
        else:
            break

    os.environ["DJANGO_SETTINGS_MODULE"] = "bridge_project.settings"
    os.environ["ENVIRONMENT"] = "dev"
    django.setup()


import typing

if typing.TYPE_CHECKING:
    from webapp.models.portal import MergedPortalCredential
class CrackPlaywright:


    def __init__(self, mpc: "MergedPortalCredential", *, cold: bool = True) -> None:
        self.p = None
        self.cold = cold
        self.browser = None
        self.context = None
        self.is_silent = False
        self.mpc = mpc

        self.has_init = False
        self.action_stack = []
        self.programatic_actions = []

        if self.mpc is not None:
            self.storage_state_folder = Path() / Path(
               f"state__{self.mpc.pk}"
            )
            self.storage_state_location = str(self.storage_state_folder / "storage_state_latest.json")
        else:
            raise ValueError

    async def silence(self) -> None:
        self.is_silent = not self.is_silent

    async def init(self) -> None:
        self.p = await async_playwright().start()
        self.browser = await self.p.chromium.launch(headless=False, channel="chrome")
        if self.cold:
            self.context = await self.browser.new_context()
        else:
            display("NOT COLD")
            display(self.storage_state_location)
            self.context = await self.browser.new_context(storage_state=self.storage_state_location)
        self.page = await self.context.new_page()

        async def record_action(data: dict[str, str]) -> None:
            self.action_stack.append(data)
            if not self.is_silent:
                display(data)

        # Expose the record_action function to the page as window.recordAction
        await self.context.expose_function("recordAction", record_action)

        # Inject listeners for the main page
        await self.page.evaluate(self.get_injection_script())

        # Also inject listeners whenever a new frame is attached
        def handle_frame_attached(frame: Frame) -> None:
            display("Frame attached!")
            asyncio.create_task(self.inject_script_into_frame(frame))  # noqa: RUF006

        self.page.on("frameattached", handle_frame_attached)
        self.page.on("framenavigated", lambda _: asyncio.create_task(self.inject_listeners_main_page()))

        # # Inject listeners into already-existing frames (e.g. if a page loads with iframes)
        for frame in self.page.frames:
            if frame != self.page.main_frame:
                await self.inject_script_into_frame(frame)

        self.has_init = True

    async def inject_listeners_main_page(self) -> None:
        """Re-inject the JS in the main frame after navigation."""
        await self.page.evaluate(self.get_injection_script())

        # Re-inject into iframes if needed
        for frame in self.page.frames:
            if frame != self.page.main_frame:
                await self.inject_script_into_frame(frame)

    async def inject_script_into_frame(self, frame: Frame) -> None:
        """Utility to inject the recorder script into an iframe, if same-origin."""
        await frame.evaluate("window.recordAction = window.top.recordAction;")
        await frame.evaluate(self.get_injection_script())

    async def goto_portal(self) -> None:
        await self.page.goto(self.mpc.get_portal_url())

    async def click_last_action(self, idx: int = -1) -> None:
        action = self.action_stack[idx]
        # If there's a frameChain, locate the correct frame
        target_frame = self.page
        if action.get("frameChain"):
            # Reconstruct the nested frame
            for iframe_selector in action["frameChain"]:
                target_frame = target_frame.frame_locator(iframe_selector)
        # Now click the target element
        await target_frame.locator(action["cssSelector"]).click()

        action["type"] = "click"
        self.programatic_actions.append(action)
        display(f"Clicked {action['cssSelector']}")

    async def fill_last_action(self, value: str, idx: int = -1) -> None:
        action = self.action_stack[idx]
        target_frame = self.page
        if action.get("frameChain"):
            for iframe_selector in action["frameChain"]:
                target_frame = target_frame.frame_locator(iframe_selector)
        await target_frame.locator(action["cssSelector"]).fill(value)

        action["type"] = "fill"
        self.programatic_actions.append(action)
        display(f"Filled {action['cssSelector']}")

    async def mark_last_action(self, idx: int = -1) -> None:
        action = self.action_stack[idx]
        action["type"] = "mark"
        self.programatic_actions.append(action)
        display(f"Marked {action['cssSelector']}")

    async def cleanup(self) -> None:
        if self.context is not None:
            await self.context.close()
        if self.browser is not None:
            await self.browser.close()
        if self.p is not None:
            await self.p.stop()

    async def save_storage_state(self) -> None:
        display("saving")
        if not self.storage_state_folder.exists():
            display("making")
            self.storage_state_folder.mkdir(parents=True)
        storage_state_json = json.dumps(await self.context.storage_state(), indent=4)
        action_stack_json = json.dumps(self.action_stack, indent=4)
        programatic_actions_json = json.dumps(self.programatic_actions, indent=4)

        async with await anyio.open_file(self.storage_state_location, "w") as f:
            await f.write(storage_state_json)
        async with await anyio.open_file(
            str(self.storage_state_folder / f"storage_state_{time.time()!s}.json"), "w"
        ) as f:
            await f.write(storage_state_json)
        async with await anyio.open_file(str(self.storage_state_folder / "action_stack.json"), "w") as f:
            await f.write(action_stack_json)
        async with await anyio.open_file(str(self.storage_state_folder / "programatic_actions.json"), "w") as f:
            await f.write(programatic_actions_json)

    def get_username(self) -> str:
        return self.mpc.username

    def get_password(self) -> str:
        return self.mpc.get_portal_secret()

    def get_mfa(self) -> str:
        return self.mpc.get_otp()

    def get_injection_script(self) -> str:
        """
        Returns the JS script as a string.
        This version has been augmented to detect iframes and add `frameChain` metadata.
        """
        return r"""
(function(){
    // We don't want to install multiple times in the same context.
    if (window.__playwrightRecorderInstalled) return;
    window.__playwrightRecorderInstalled = true;

    console.log("[Recorder] Initializing event capture...");

    function safeLogError(message, error) {
        console.error("[Recorder ERROR]", message, error);
    }

    function safeLogInfo(message) {
        console.info("[Recorder INFO]", message);
    }

    // Use CSS.escape if available, else fallback
    function cssEscapeIdent(ident) {
        if (window.CSS && typeof window.CSS.escape === 'function') {
            return CSS.escape(ident);
        }
        // Fallback escape: escape all characters outside [a-zA-Z0-9_-]
        return ident.replace(/([^a-zA-Z0-9_-])/g, '\\$1');
    }

    function isUniqueSelector(selector) {
        try {
            if (!selector) return false;
            const matches = document.querySelectorAll(selector);
            return matches.length === 1;
        } catch (e) {
            safeLogError("Invalid selector encountered: " + selector, e);
            return false;
        }
    }

    function looksLikeRandomClass(c) {
        if (c.length > 20) return true;
        if (/^[0-9a-fA-F_-]+$/.test(c) && c.length > 8) return true;
        return false;
    }

    function buildRobustSelector(el) {
        if (!(el instanceof Element)) return '';
        try {
            const pathSegments = [];
            let current = el;
            while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.documentElement) {
                let segment = cssSegment(current);
                if (segment) {
                    pathSegments.unshift(segment);
                    const partialSelector = pathSegments.join(' > ');
                    if (isUniqueSelector(partialSelector)) {
                        return partialSelector;
                    }
                }
                current = current.parentElement;
            }
            const fullSelector = 'html > ' + pathSegments.join(' > ');
            if (isUniqueSelector(fullSelector)) {
                return fullSelector;
            }
            return fullSelector;
        } catch (e) {
            safeLogError("Failed to build robust selector", e);
            // Fallback: just return tagName
            return el.tagName.toLowerCase();
        }
    }

    function cssSegment(el) {
        try {
            if (el.id && isUniqueSelector('#' + cssEscapeIdent(el.id))) {
                return '#' + cssEscapeIdent(el.id);
            }
            const tagName = el.tagName.toLowerCase();

            if (el.id) {
                return tagName + '#' + cssEscapeIdent(el.id);
            }

            let classes = Array.from(el.classList).filter(c => !looksLikeRandomClass(c));
            classes.sort((a,b) => a.length - b.length);

            if (classes.length > 0) {
                for (let i = 1; i <= classes.length; i++) {
                    const testSelector = tagName + classes.slice(0, i).map(c => '.' + cssEscapeIdent(c)).join('');
                    if (isUniqueSelector(testSelector)) {
                        return testSelector;
                    }
                }
            }

            const parent = el.parentElement;
            if (!parent) return tagName;
            const siblings = Array.from(parent.children).filter(n => n.tagName === el.tagName);
            if (siblings.length > 1) {
                const index = siblings.indexOf(el) + 1;
                return `${tagName}:nth-of-type(${index})`;
            } else {
                return tagName;
            }
        } catch (e) {
            safeLogError("Failed in cssSegment", e);
            // Fallback: just tagName
            return el.tagName.toLowerCase();
        }
    }

    function safeAttr(el, attr) {
        try {
            return el.hasAttribute(attr) ? el.getAttribute(attr) : null;
        } catch (e) {
            safeLogError("Error reading attribute: " + attr, e);
            return null;
        }
    }

    function findLabelText(el) {
        try {
            if (!(el instanceof Element)) return null;
            if (el.id) {
                const label = document.querySelector(`label[for='${cssEscapeIdent(el.id)}']`);
                if (label) return label.innerText.trim();
            }
            let current = el;
            for (let i = 0; i < 3 && current; i++) {
                if (current.tagName && current.tagName.toLowerCase() === 'label') {
                    return (current.innerText || '').trim();
                }
                current = current.parentElement;
            }
        } catch (e) {
            safeLogError("Error finding label text", e);
        }
        return null;
    }

    function gatherContextualHTML(el) {
        const context = [];
        try {
            let current = el.parentElement;
            let levels = 0;
            while (current && levels < 3) {
                context.push(current.outerHTML);
                current = current.parentElement;
                levels++;
            }
        } catch (e) {
            safeLogError("Error gathering contextual HTML", e);
        }
        return context;
    }

    function serializeElement(el) {
        if (!(el instanceof Element)) {
            return { tagName: el && el.tagName ? el.tagName : null };
        }
        try {
            const labelText = (['INPUT','TEXTAREA','SELECT'].includes(el.tagName))
                ? findLabelText(el) : null;

            return {
                tagName: el.tagName,
                id: el.id || null,
                className: el.className || null,
                textContent: (el.textContent || '').trim(),
                href: safeAttr(el, 'href'),
                name: safeAttr(el, 'name'),
                type: safeAttr(el, 'type'),
                cssSelector: buildRobustSelector(el),
                outerHTML: el.outerHTML || '',
                contextualHTML: gatherContextualHTML(el),
                associatedLabelText: labelText
            };
        } catch (e) {
            safeLogError("Error serializing element", e);
            // Fallback minimal data
            return {
                tagName: el.tagName,
                outerHTML: el.outerHTML || ''
            };
        }
    }

    /**
     * Build an array of robust selectors from the top-level document down to this window.
     * e.g. ["iframe#iframeID", "iframe:nth-of-type(1)"]
     * If empty, we are at top-level (no parent).
     */
    function getFrameChain() {
        const chain = [];
        let w = window;
        while (w && w !== w.top) {
            const frames = w.parent.document.querySelectorAll("iframe,frame");
            // Find which iframe in the parent doc corresponds to `w`
            for (let i=0; i<frames.length; i++) {
                if (frames[i].contentWindow === w) {
                    const selector = buildRobustSelector(frames[i]);
                    chain.unshift(selector);
                    break;
                }
            }
            w = w.parent;
        }
        return chain;
    }

    function commonPayload(type, element) {
        let data = {};
        try {
            data = element ? serializeElement(element) : {};
        } catch (e) {
            safeLogError("Error in commonPayload serialization", e);
        }

        // If we are inside an iframe, build the chain of iframe selectors
        let frameChain = [];
        try {
            // If not top-level, get the chain from top doc -> this iframe.
            if (window !== window.top) {
                frameChain = getFrameChain();
            }
        } catch (e) {
            safeLogError("Error computing frameChain", e);
        }

        return {
            type: type,
            pageURL: document.location.href,
            timestamp: new Date().toISOString(),
            windowSize: { width: window.innerWidth, height: window.innerHeight },
            frameChain: frameChain,
            ...data
        };
    }

    function captureEvent(type, event) {
        try {
            const element = event.target;
            const payload = commonPayload(type, element);
            window.recordAction(payload);
            // safeLogInfo(`Captured ${type} event`);
        } catch (e) {
            safeLogError(`Failed to capture ${type} event`, e);
            // Minimal fallback
            window.recordAction({
                type: type,
                pageURL: document.location.href,
                timestamp: new Date().toISOString()
            });
        }
    }

    function captureInput(event) {
        try {
            const element = event.target;
            if (['INPUT','TEXTAREA','SELECT'].includes(element.tagName)) {
                const payload = commonPayload('input', element);
                window.recordAction(payload);
            }
        } catch (e) {
            safeLogError("Failed to capture input event", e);
            window.recordAction({
                type: 'input',
                pageURL: document.location.href,
                timestamp: new Date().toISOString()
            });
        }
    }

    function captureSubmit(event) {
        try {
            const form = event.target;
            if (form && form.tagName === 'FORM') {
                const formData = new FormData(form);
                const formInputs = {};
                for (const [key,value] of formData.entries()) {
                    formInputs[key] = value;
                }

                const payload = {
                    type: 'submit',
                    pageURL: document.location.href,
                    timestamp: new Date().toISOString(),
                    windowSize: { width: window.innerWidth, height: window.innerHeight },
                    formOuterHTML: form.outerHTML,
                    formInputs: formInputs
                };

                const activeElement = document.activeElement;
                if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'BUTTON')) {
                    payload.triggerElement = serializeElement(activeElement);
                }

                window.recordAction(payload);
            }
        } catch (e) {
            safeLogError("Failed to capture submit event", e);
            window.recordAction({
                type: 'submit',
                pageURL: document.location.href,
                timestamp: new Date().toISOString()
            });
        }
    }

    // Attach all event listeners at the capturing phase
    // document.documentElement.addEventListener('pointerdown', e => captureEvent('pointerdown', e), true);
    // document.documentElement.addEventListener('pointerup', e => captureEvent('pointerup', e), true);
    // document.documentElement.addEventListener('mousedown', e => captureEvent('mousedown', e), true);
    // document.documentElement.addEventListener('mouseup', e => captureEvent('mouseup', e), true);
    document.documentElement.addEventListener('click', e => captureEvent('click', e), true);
    document.documentElement.addEventListener('contextmenu', e => captureEvent('contextmenu', e), true);
    document.documentElement.addEventListener('input', captureInput, true);
    document.documentElement.addEventListener('submit', captureSubmit, true);

    safeLogInfo("Event capture successfully initialized.");
})();
"""
