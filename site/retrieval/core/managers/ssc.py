import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON>enerator
from pathlib import Path

import structlog
from dateutil import parser
from playwright.async_api import Locator, expect
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.getLogger(__name__)


@register_strategy
class SSCManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "sscfundservices.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "SS&C"),
            ("<EMAIL>", "Gotham Green (SS&C)"),
        ]

    async def _fill_input_by_label_text(self, label_text: str, fill_str: str) -> None:
        label_for = await self.page.locator("label").filter(has_text=label_text).get_attribute("for")
        await self.page.locator(f"input#{label_for}").fill(fill_str)

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        await self._fill_input_by_label_text("Username", username)
        await asyncio.sleep(2)
        await self._fill_input_by_label_text("Password", password)
        await asyncio.sleep(2)
        await self.page.locator("#btnSubmit").click()
        await asyncio.sleep(2)

    async def _check_has_otp(self) -> bool:
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:
        pass

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.wait_for_load_state("networkidle")
        return await self.page.get_by_text("Sign In").count() == 0

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: C901
        if self.page is None:
            raise ManagerNotInitializedError
        notice_locator = self.page.locator(".page-edit__header.page-edit__header--small:visible button")
        if await notice_locator.count() == 1:
            await notice_locator.click()
        await self.page.get_by_text("All Documents").click()
        await asyncio.sleep(15)
        reset_buttons = self.page.get_by_text("Reset")
        for reset_button in await reset_buttons.all():
            if not await reset_button.is_visible():
                continue
            tag_name = await reset_button.evaluate("el => el.tagName.toLowerCase()")
            if tag_name == "button":
                await reset_button.click()
                await asyncio.sleep(5)
                break
        # Find the checkbox for ArchiveFlag and check it if not already checked
        archive_checkbox = self.page.locator("#ArchiveFlag")
        archive_label = self.page.locator("label[for='ArchiveFlag']")
        if (
            await archive_label.count() > 0
            and await archive_checkbox.count() > 0
            and not await archive_checkbox.is_checked()
        ):
            await archive_label.click()
            await asyncio.sleep(5)

        next_page_locator = self.page.locator("button[title='Next Page']")
        await expect(next_page_locator).to_have_count(1, timeout=120000)
        has_next_page = True
        local_deduplication: dict[str, int] = {}
        while has_next_page:
            for table_row in await self.page.locator(".table__table-body .table-body__table-row").all():
                async for doc in self._retrieve_row(table_row, local_deduplication):
                    yield doc
            has_next_page = not await next_page_locator.is_disabled()
            if has_next_page:
                await next_page_locator.click()
                await asyncio.sleep(5)

    async def _retrieve_row(  # noqa: C901
        self, table_row: Locator, local_deduplication: dict[str, int]
    ) -> AsyncGenerator[RawDocumentTuple, None]:
        document_name_locator = table_row.locator("[data-index='ReportClass']")
        document_name = None
        if await document_name_locator.count() != 0:
            document_name = (await document_name_locator.inner_text()).strip()

        raw_document_type_locator = table_row.locator("[data-index='ParentClass']")
        raw_document_type = None
        if await raw_document_type_locator.count() != 0:
            raw_document_type = (await raw_document_type_locator.inner_text()).strip()

        report_date_locator = table_row.locator("[data-index='ReportDate']")
        report_date = None
        if await report_date_locator.count() != 0:
            report_date = (await report_date_locator.inner_text()).strip()
        fund_name_locator = table_row.locator("[data-index='FundName']")
        fund_name = None
        if await fund_name_locator.count() != 0:
            fund_name = (await fund_name_locator.inner_text()).strip()

        account_locator = table_row.locator("[data-index='Account']")
        account = None
        if await account_locator.count() != 0:
            account = (await account_locator.inner_text()).strip()
        legal_name_locator = table_row.locator("[data-index='LegalName']")
        legal_name = None
        if await legal_name_locator.count() != 0:
            legal_name = (await legal_name_locator.inner_text()).strip()

        investor_locator = table_row.locator("[data-index='Investor']")
        investor = None
        if await investor_locator.count() != 0:
            investor = (await investor_locator.inner_text()).strip()

        posted_date_locator = table_row.locator("[data-index='UploadDate']")
        posted_date = None
        if await posted_date_locator.count() != 0:
            posted_date = (await posted_date_locator.inner_text()).strip()

        metadata = json.dumps(
            {
                "Document Name": document_name,
                "Document Type": raw_document_type,
                "Report Date": report_date,
                "Fund Name": fund_name,
                "Account": account,
                "Legal Name": legal_name,
                "Investor": investor,
                "Posted Date": posted_date,
            }
        )
        doc_hash = self._doc_hash(metadata)  # ensure we have a hash for the metadata
        if doc_hash not in local_deduplication:
            local_deduplication[doc_hash] = 1
        else:
            metadata_dict = json.loads(metadata)
            metadata_dict["Duplicate Count"] = local_deduplication[doc_hash]
            metadata = json.dumps(metadata_dict)
            local_deduplication[doc_hash] += 1
        if await self.should_download_document(metadata):
            document_type = self.__document_mapper(raw_document_type, document_name)
            parsed_date = parser.parse(report_date)
            doc = await self.__download_doc(table_row, metadata, parsed_date, document_type)
            await asyncio.sleep(2)
            if doc is not None:
                yield doc

    async def __download_doc(
        self, row_locator: Locator, metadata: str, parsed_date: datetime.datetime, doc_type: str
    ) -> RawDocumentTuple:
        if self.page is None:
            raise ManagerNotInitializedError
        check_box = row_locator.locator("[data-index='fid'] label")
        await check_box.click()
        await asyncio.sleep(1)
        async with self.page.expect_download(timeout=5000) as download_info:
            await self.page.get_by_text("Download").click()
        download = await download_info.value
        await asyncio.sleep(1)
        file_name = Path(f"/tmp/{download.suggested_filename}")  # noqa: S108 # nosec
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() in [".pdf", ".docx"]:
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.warning("Unsupported file format: %s", file_name.suffix)
            raise ValueError
        file_name.unlink()
        await check_box.click()
        await asyncio.sleep(1)

        return RawDocumentTuple(
            name=download.suggested_filename,
            date=parsed_date,
            doc_type=doc_type,
            content=content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    def __document_mapper(self, doc_type: str, doc_name: str) -> str:
        if doc_type == "Financials":
            if "Financial Statement" in doc_name:
                return DocumentType.FINANCIAL_STATEMENTS
            if "Account Statement" in doc_name:
                return DocumentType.ACCOUNT_STATEMENT
        return {
            "Capital Call": DocumentType.CAPITAL_CALL,
            "Distribution Notice": DocumentType.DISTRIBUTION_NOTICE,
            "General": DocumentType.OTHER,  # seeing brochure, investor letter, etc.
            "Tax": DocumentType.TAX,
        }.get(doc_type, DocumentType.UNKNOWN)
