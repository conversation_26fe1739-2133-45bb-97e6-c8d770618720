{% load customtags tz static %}
<!-- Retrieval Info -->
<div class="flex flex-col flex-wrap items-start min-w-[180px]"
     id="retrieval_status_{{ merged_portal_credential.pk }}"
     hx-get="{% url 'retrieval_status' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
     hx-trigger="retrievalStarted[detail.merged_portal_credential_pk=='{{ merged_portal_credential.pk }}'] from:body{% if merged_portal_credential.last_retrieval and merged_portal_credential.last_retrieval.is_retrieval_running %}, every 500ms{% endif %}"
     hx-target="this"
     hx-swap="outerHTML">
    {% if not merged_portal_credential.is_first_retrieval and merged_portal_credential.last_retrieval %}
        <div class="flex flex-col items-start">
            <div class="pt-2 text-left">
                <span class="text-[#343d36] text-xs font-bold">Last Status:</span>
                {% if user.is_demo %}
                    <span class="text-[#343d36] text-xs font-medium">{% label_lookup label=merged_portal_credential.last_retrieval.retrieval_status enum=RetrievalStatus %}</span>
                {% else %}
                    <span class="text-[#343d36] text-xs font-medium">{{ merged_portal_credential.last_retrieval.external_retrieval_status }}</span>
                {% endif %}
            </div>
            <div class="pt-2 text-left">
                <span class="text-[#343d36] text-xs font-bold">Last Retrieval:</span>
                <span class="text-[#343d36] text-xs font-medium">{{ merged_portal_credential.last_retrieval.updated_at|localtime }}</span>
            </div>
            <div class="py-2 text-left">
                <span class="text-[#343d36] text-xs font-bold">New Documents Retrieved:</span>
                <span class="text-[#343d36] text-xs font-medium">{{ merged_portal_credential.latest_processed_docs_count }}</span>
            </div>
        </div>
    {% else %}
        <div class="text-left">
            <span class="text-[#343d36] text-xs font-bold">Last Retrieval:</span>
            <span class="text-[#343d36] text-xs font-medium">Has never been retrieved</span>
        </div>
    {% endif %}
</div>
