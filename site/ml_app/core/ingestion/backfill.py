import json

import boto3
import structlog
from django.contrib.postgres.search import SearchQuery, SearchRank
from django.db.models import F, QuerySet
from pgvector.django import CosineDistance
from webapp.models.documents import DocumentType, RawDocument
from webapp.models.extraction import DocChunk, ExtractionSchema, RawExtraction
from webapp.utils.json_schema import map_schema_ids_to_data, validate_with_details

logger = structlog.get_logger(__name__)
import calendar

from dateutil import parser


def generate_date_search_formats(date_string: str) -> list[str]:
    """
    Generate a comprehensive list of date format variations for text search.

    Args:
        date_string: A string representing a datetime (e.g., "2024-12-20")

    Returns:
        List of date format strings for enhanced search recall

    """
    dt = parser.parse(date_string)
    formats = []

    # Get date components
    year = dt.year
    month = dt.month
    day = dt.day

    # Month names
    month_full = calendar.month_name[month]
    month_abbr = calendar.month_abbr[month]

    # Day names
    day_full = calendar.day_name[dt.weekday()]
    day_abbr = calendar.day_abbr[dt.weekday()]

    # Ordinal day suffixes
    def get_ordinal_suffix(day_num: int) -> str:
        if 10 <= day_num % 100 <= 20:  # noqa: PLR2004
            return "th"
        return {1: "st", 2: "nd", 3: "rd"}.get(day_num % 10, "th")

    day_ordinal = f"{day}{get_ordinal_suffix(day)}"

    # Basic numeric formats
    formats.extend(
        [
            f"{year}-{month:02d}-{day:02d}",  # 2024-12-20
            f"{year}/{month:02d}/{day:02d}",  # 2024/12/20
            f"{month:02d}/{day:02d}/{year}",  # 12/20/2024
            f"{day:02d}/{month:02d}/{year}",  # 20/12/2024
            f"{month}/{day}/{year}",  # 12/20/2024
            f"{day}/{month}/{year}",  # 20/12/2024
            f"{month}-{day}-{year}",  # 12-20-2024
            f"{day}-{month}-{year}",  # 20-12-2024
            f"{month}.{day}.{year}",  # 12.20.2024
            f"{day}.{month}.{year}",  # 20.12.2024
        ]
    )

    # Short year formats
    year_short = str(year)[2:]
    formats.extend(
        [
            f"{month:02d}/{day:02d}/{year_short}",  # 12/20/24
            f"{day:02d}/{month:02d}/{year_short}",  # 20/12/24
            f"{month}/{day}/{year_short}",  # 12/20/24
            f"{day}/{month}/{year_short}",  # 20/12/24
            f"{month}-{day}-{year_short}",  # 12-20-24
            f"{day}-{month}-{year_short}",  # 20-12-24
        ]
    )

    # Full month name formats
    formats.extend(
        [
            f"{month_full} {day}, {year}",  # December 20, 2024
            f"{month_full} {day_ordinal}, {year}",  # December 20th, 2024
            f"{day} {month_full} {year}",  # 20 December 2024
            f"{day_ordinal} {month_full} {year}",  # 20th December 2024
            f"{day} {month_full}, {year}",  # 20 December, 2024
            f"{month_full} {day}",  # December 20
            f"{month_full} {day_ordinal}",  # December 20th
            f"{day} {month_full}",  # 20 December
            f"{day_ordinal} {month_full}",  # 20th December
        ]
    )

    # Abbreviated month name formats
    formats.extend(
        [
            f"{month_abbr} {day}, {year}",  # Dec 20, 2024
            f"{month_abbr} {day_ordinal}, {year}",  # Dec 20th, 2024
            f"{day} {month_abbr} {year}",  # 20 Dec 2024
            f"{day_ordinal} {month_abbr} {year}",  # 20th Dec 2024
            f"{day} {month_abbr}, {year}",  # 20 Dec, 2024
            f"{month_abbr} {day}",  # Dec 20
            f"{month_abbr} {day_ordinal}",  # Dec 20th
            f"{day} {month_abbr}",  # 20 Dec
            f"{day_ordinal} {month_abbr}",  # 20th Dec
            f"{month_abbr}. {day}, {year}",  # Dec. 20, 2024
            f"{month_abbr}. {day_ordinal}, {year}",  # Dec. 20th, 2024
            f"{month_abbr}. {day}",  # Dec. 20
        ]
    )

    # Day of week formats
    formats.extend(
        [
            f"{day_full}, {month_full} {day}, {year}",  # Friday, December 20, 2024
            f"{day_full}, {month_abbr} {day}, {year}",  # Friday, Dec 20, 2024
            f"{day_abbr}, {month_full} {day}, {year}",  # Fri, December 20, 2024
            f"{day_abbr}, {month_abbr} {day}, {year}",  # Fri, Dec 20, 2024
            f"{day_full}, {month}/{day}/{year}",  # Friday, 12/20/2024
            f"{day_abbr}, {month}/{day}/{year}",  # Fri, 12/20/2024
            f"{day_full}",  # Friday
            f"{day_abbr}",  # Fri
        ]
    )

    # Special formats
    formats.extend(
        [
            f"{month_full} {year}",  # December 2024
            f"{month_abbr} {year}",  # Dec 2024
            f"{year}",  # 2024
            f"'{year_short}",  # '24
            f"{month:02d}/{year}",  # 12/2024
            f"{month}/{year}",  # 12/2024
        ]
    )

    # ISO and timestamp-like formats
    formats.extend(
        [
            f"{year}{month:02d}{day:02d}",  # 20241220
            f"{year}-{month:02d}-{day:02d}T00:00:00",  # 2024-12-20T00:00:00
            f"{year}-{month:02d}-{day:02d} 00:00:00",  # 2024-12-20 00:00:00
        ]
    )

    # Remove duplicates while preserving order
    seen = set()
    unique_formats = []
    for fmt in formats:
        if fmt not in seen:
            seen.add(fmt)
            unique_formats.append(fmt)

    return unique_formats


def find_similar_chunks(  # noqa: PLR0913
    raw_doc: RawDocument,
    search_query: str,
    semantic_query: str,
    semantic_bias: float = 0.1,
    search_cutoff: float = 0.01,
    k: int = 5,
) -> QuerySet[DocChunk]:
    sq_obj = SearchQuery(search_query)

    bedrock_runtime = boto3.client(service_name="bedrock-runtime", region_name="us-east-1")
    response = bedrock_runtime.invoke_model(
        body=json.dumps(
            {
                "inputText": semantic_query,
                "dimensions": 1024,
                "normalize": True,
            }
        ),
        modelId="amazon.titan-embed-text-v2:0",
        accept="application/json",
        contentType="application/json",
    )

    response_body = json.loads(response.get("body").read())
    embedding = response_body["embedding"]
    qs = (
        raw_doc.doc_chunks.annotate(
            keyword_score=SearchRank("search_vector", sq_obj),
            distance=CosineDistance("embedding_titan_text_1024", embedding),
        )
        .filter(keyword_score__gt=search_cutoff)
        .annotate(score=semantic_bias * (1 - F("distance")) + (1 - semantic_bias) * F("keyword_score"))
        .order_by("-score")
    )
    return qs[:k]


def backfill_processed_doc(
    raw_document_id: str,
) -> None:
    raw_doc = RawDocument.objects.get(pk=raw_document_id)
    id2rs = ExtractionSchema.get_resolved_schemas()
    processed_doc = raw_doc.processed_documents.filter(is_visible=True).first()
    if processed_doc is None:
        logger.warning("No processed document found for raw document", raw_document_id=raw_document_id)
        return
    is_ground_truth = processed_doc.is_ground_truth
    processed_doc_json = {
        "effective_date": str(processed_doc.effective_date),
        "document_type": str(DocumentType(processed_doc.document_type).label),
        "investing_entity_name": processed_doc.line_item.investing_entity.legal_name,
        "investment_name": processed_doc.line_item.investment.legal_name,
        "capital_call": {
            "due_date": str(processed_doc.capital_call_document.capital_call_due_date),
            "amount_due": float(processed_doc.capital_call_document.amount),
            "currency": "USD",
        },
    }
    column_name = "processed_document"
    keys = [k for k in id2rs if k.endswith(column_name)]
    if len(keys) != 1:
        raise ValueError
    schema = id2rs[keys[0]]
    success, err = validate_with_details(processed_doc_json, schema)
    if not success:
        logger.error("Processed document does not validate against schema", raw_document_id=raw_document_id, err=err)
        raise ValueError
    id2data = map_schema_ids_to_data(processed_doc_json, schema)
    res = []
    for schema_id, data in id2data.items():
        if data is None:
            continue
        schema = id2rs[schema_id]
        if schema["type"] == "object":
            continue
        json_schema = json.dumps(schema, indent=2)
        if "format" in schema and schema["format"] == "date":
            json_data = json.dumps(" ".join(generate_date_search_formats(data)), indent=2)
        else:
            json_data = json.dumps(data, indent=2)
        chunks = find_similar_chunks(raw_doc, json_data, f"{json_data}\n{json_schema}", k=1)
        res.append((schema_id, data, schema, chunks))
    for schema_id, data, _, chunks in res:
        # TODO: save the paths.
        RawExtraction.create(
            raw_doc=raw_doc,
            uri=schema_id,
            extraction_source="backfill_processed_doc",
            is_ground_truth=is_ground_truth,
            chunks=chunks,
            data=data,
        )
