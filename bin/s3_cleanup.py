#!/usr/bin/env -S uv run
import functools
import os
import sys
from collections.abc import Callable
from io import Text<PERSON><PERSON>rapper
from pathlib import Path

import boto3
import django
import structlog

sys.path.append("site")
os.environ["DJANGO_SETTINGS_MODULE"] = "bridge_project.settings"
os.environ["ENVIRONMENT"] = "dev"
os.environ["USE_LOCALHOST"] = "1"
django.setup()

from django.conf import settings

BUCKET = settings.AWS_STORAGE_BUCKET_NAME

from webapp.models import LineItem, ProcessedDocument, RawDocument, Retrieval

logger = structlog.get_logger(__name__)
s3_client = boto3.client("s3")
def soft_delete(key:str)->bool:
    try:
        s3_client.delete_object(Bucket=BUCKET, Key=key)
    except Exception:
        logger.exception("Could not delete", key=key)
        return False
    logger.exception("Soft Deleting", key=key)
    return True

def exists(key:str)->bool:
    try:
        s3_client.head_object(Bucket=BUCKET, Key=key)
    except Exception:  # noqa: BLE001
        return False
    return True

def undelete(key:str)->bool:
    versions = s3_client.list_object_versions(Bucket=BUCKET, Prefix=key)
    delete_markers = versions.get("DeleteMarkers", [])
    if not delete_markers:
        return False
    for marker in delete_markers:
        try:
            if marker.get("Key", None) == key and marker.get("IsLatest", False):
                delete_marker_version_id = marker.get("VersionId", None)
                if delete_marker_version_id is not None:
                    s3_client.delete_object(Bucket=BUCKET, Key=key, VersionId=delete_marker_version_id)
                    logger.info("Undeleteing", key=key)
                return True
        except Exception:
            logger.exception("Could not undelete", key=key)
    return False

def permanently_delete_marked_objects(bucket:str, log_file:TextIOWrapper, *, should_delete:bool=False)->None:  # noqa: C901, PLR0912

    s3 = boto3.client("s3")
    paginator = s3.get_paginator("list_object_versions")
    page_iterator = paginator.paginate(Bucket=bucket)

    # Dictionary to group object versions and delete markers by key.
    objects:dict[str,dict[str,list]] = {}

    for page in page_iterator:
        # Process data versions.
        for version in page.get("Versions", []):
            key = version.get("Key", None)
            version_id = version.get("VersionId", None)
            if key is None or version_id is None:
                continue
            objects.setdefault(key, {"versions": [], "delete_markers": []})
            objects[key]["versions"].append(version_id)
        # Process delete markers.
        for marker in page.get("DeleteMarkers", []):
            key = marker.get("Key", None)
            version_id = marker.get("VersionId", None)
            if key is None or version_id is None:
                continue
            objects.setdefault(key, {"versions": [], "delete_markers": []})
            objects[key]["delete_markers"].append(version_id)

    # Iterate over each object and check for a delete marker.
    for key, data in objects.items():
        if data["delete_markers"]:
            # Delete all data versions for this key.
            log_file.write(f"PERM: {key}\n")
            if should_delete:
                logger.info("PERMANENTLY deleting", key=key)
                for version_id in data["versions"]:
                    if version_id is None:
                        continue
                    s3.delete_object(Bucket=bucket, Key=key, VersionId=version_id)
                # Delete all delete markers for this key.
                for marker_version_id in data["delete_markers"]:
                    s3.delete_object(Bucket=bucket, Key=key, VersionId=marker_version_id)
            else:
                logger.debug("Would permanently delete", key=key)
        else:
            logger.debug("Skipping", key=key)


def list_all_objects(func:Callable)->Callable:
    functools.wraps(wrapped=func)
    def wrapper(bucket:str, prefix:str)->list[Path]:
        paginator = s3_client.get_paginator("list_objects_v2")
        page_iterator = paginator.paginate(Bucket=bucket, Prefix=prefix)
        all_objects = []
        counts = {
            prefix: {
                True: 0,
                False: 0,
            }
        }
        for page in page_iterator:
            for obj in page.get("Contents", []):
                if "Key" in obj:
                    path = Path(obj["Key"])
                    should_delete = func(path)
                    counts[prefix][should_delete] += 1
                    if should_delete:
                        all_objects.append(path)
        logger.info("Stats", counts=counts)
        return all_objects
    return wrapper

@list_all_objects
def delete_bulk_uploads(_:Path)->bool:
    return True

@list_all_objects
def delete_email_documents(path:Path)->bool:  # noqa: ARG001
    return True

@list_all_objects
def delete_inbound_emails(path:Path)->bool:  # noqa: ARG001
    # TODO: OOOF
    return False

@list_all_objects
def delete_manual_retrieval(path:Path)->bool:
    manual_retrievals = Retrieval.objects.filter(manager__in=["ManualRetrievalV1"])
    return all(man_ret.s3_key not in str(path) for man_ret in manual_retrievals)


@list_all_objects
def delete_pdf_zipfiles(path:Path)->bool:  # noqa: ARG001
    return True


@list_all_objects
def delete_postgres_dumps(path:Path)->bool:
    return path.name != "db-latest.json"


@list_all_objects
def delete_processed_docs(path:Path)->bool:
    line_item_pk = path.parts[1]
    try:
        _ = LineItem.objects.get(pk=line_item_pk)
    except LineItem.DoesNotExist:
        return True
    return False


@list_all_objects
def delete_retrieval_logs(path:Path)->bool:
    retrieval_pk = path.parts[1]
    try:
        _ = Retrieval.objects.get(pk=retrieval_pk)
    except Retrieval.DoesNotExist:
        return True
    return False

if __name__ == "__main__":
    if "AWS_PROFILE" not in os.environ:
        msg = "AWS_PROFILE not set"
        raise ValueError(msg)
    DO_SOFT_DELETE = True
    SOFT_DELETE = False
    REAL_DELETE = False
    # Initialize AWS Secrets Manager client
    known_folders = {
        "bulk_uploads": delete_bulk_uploads,
        "email_documents": delete_email_documents,
        "inbound_emails": delete_inbound_emails,
        "manual_retrieval": delete_manual_retrieval,
        "pdf_zipfiles": delete_pdf_zipfiles,
        "postgres_dumps": delete_postgres_dumps,
        "processed_docs": delete_processed_docs,
        "retrieval_logs": delete_retrieval_logs,
    }
    LOG_FILE = Path(f"DELETE_soft={SOFT_DELETE}_real={REAL_DELETE}_profile={os.environ['AWS_PROFILE']}.log")
    with LOG_FILE.open("w") as f:
        if DO_SOFT_DELETE:
            for folder, func in known_folders.items():
                to_delete = func(BUCKET, folder)
                for del_me in to_delete:
                    if SOFT_DELETE:
                        soft_delete(str(del_me))
                    else:
                        logger.debug("Would delete", key=del_me)
                        f.write(f"SOFT: {del_me}\n")
            i = 0
            for rd in RawDocument.objects.all():
                i+=1
                if i%100 == 0:
                    logger.info("iterating", i=i)
                key = rd.s3_key
                try:
                    s3_client.head_object(Bucket=BUCKET, Key=key)
                except Exception:  # noqa: BLE001
                    logger.info("RawDocument does not exist", pk=rd.pk, key=key)
                    if SOFT_DELETE:
                        f.write(f"UNDL: {key}\n")
                        undelete(key)
            for pd in ProcessedDocument.objects.all():
                i+=1
                if i%100 == 0:
                    logger.info("iterating", i=i)
                key = pd.s3_key
                try:
                    s3_client.head_object(Bucket=BUCKET, Key=key)
                except Exception:  # noqa: BLE001
                    logger.info("RawDocument does not exist", pk=pd.pk, key=key)
                    if SOFT_DELETE:
                        f.write(f"UNDL: {key}\n")
                        undelete(key)
        permanently_delete_marked_objects(BUCKET, log_file=f, should_delete=REAL_DELETE)
