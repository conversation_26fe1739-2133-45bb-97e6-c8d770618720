import json
import zoneinfo

from django.core.management.base import BaseCommand
from django_celery_beat.models import CrontabSchedule, PeriodicTask
from django_extensions.management.utils import signalcommand


class Command(BaseCommand):
    help = "Set up Celery Beat schedules"

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002
        est_6am_crontab, _ = CrontabSchedule.objects.get_or_create(
            minute="0",
            hour="6",
            day_of_week="*",
            day_of_month="*",
            month_of_year="*",
            timezone=zoneinfo.ZoneInfo("America/New_York"),
        )
        est_10am_weekdays_plus1_crontab, _ = CrontabSchedule.objects.get_or_create(
            minute="0",
            hour="10",
            day_of_week="tue-sat",
            day_of_month="*",
            month_of_year="*",
            timezone=zoneinfo.ZoneInfo("America/New_York"),
        )
        est_6am_weekdays_plus1_crontab, _ = CrontabSchedule.objects.get_or_create(
            minute="0",
            hour="6",
            day_of_week="tue-sat",
            day_of_month="*",
            month_of_year="*",
            timezone=zoneinfo.ZoneInfo("America/New_York"),
        )
        est_758pm_weekdays_crontab, _ = CrontabSchedule.objects.get_or_create(
            minute="58",
            hour="19",
            day_of_week="mon-fri",
            day_of_month="*",
            month_of_year="*",
            timezone=zoneinfo.ZoneInfo("America/New_York"),
        )
        est_8pm_to_10pm_every_5_min_weekdays_crontab, _ = CrontabSchedule.objects.get_or_create(
            minute="*/5",
            hour="20-21",
            day_of_week="mon-fri",
            day_of_month="*",
            month_of_year="*",
            timezone=zoneinfo.ZoneInfo("America/New_York"),
        )
        est_815pm_915_1115_weekdays_crontab, _ = CrontabSchedule.objects.get_or_create(
            minute="15",
            hour="20,21,23",
            day_of_week="mon-fri",
            day_of_month="*",
            month_of_year="*",
            timezone=zoneinfo.ZoneInfo("America/New_York"),
        )
        # Create the task
        PeriodicTask.objects.update_or_create(
            name="Daily Orion File Creation",
            defaults={
                "crontab": est_6am_crontab,
                "task": "integrations.tasks.orion.create_orion_files",
                "kwargs": json.dumps({}),
                "enabled": True,
            },
        )

        PeriodicTask.objects.update_or_create(
            name="External Daily Digest V2",
            defaults={
                "crontab": est_10am_weekdays_plus1_crontab,
                "task": "retrieval.tasks.send_mail.notify_new_processed_documents_v2",
                "kwargs": json.dumps({}),
                "enabled": True,
            },
        )

        PeriodicTask.objects.update_or_create(
            name="Schedule All Retrievals",
            defaults={
                "crontab": est_758pm_weekdays_crontab,
                "task": "retrieval.tasks.login_portal_task.schedule_all_portal_extractions",
                "kwargs": json.dumps({}),
                "enabled": True,
            },
        )

        PeriodicTask.objects.update_or_create(
            name="Execute All Retrievals",
            defaults={
                "crontab": est_8pm_to_10pm_every_5_min_weekdays_crontab,
                "task": "retrieval.tasks.login_portal_task.execute_portal_extractions",
                "kwargs": json.dumps({}),
                "enabled": True,
            },
        )

        PeriodicTask.objects.update_or_create(
            name="Internal Daily Digest",
            defaults={
                "crontab": est_815pm_915_1115_weekdays_crontab,
                "task": "retrieval.tasks.send_mail.send_internal_daily_digest",
                "kwargs": json.dumps({}),
                "enabled": True,
            },
        )
        PeriodicTask.objects.update_or_create(
            name="Internal Daily Digest Morning",
            defaults={
                "crontab": est_6am_weekdays_plus1_crontab,
                "task": "retrieval.tasks.send_mail.send_internal_daily_digest",
                "kwargs": json.dumps({}),
                "enabled": True,
            },
        )
        self.stdout.write(self.style.SUCCESS("Successfully set up Celery schedules"))
