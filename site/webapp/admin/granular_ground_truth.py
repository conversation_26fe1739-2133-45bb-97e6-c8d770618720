import json
import uuid
from pathlib import Path

import structlog
from django.contrib.postgres.search import SearchVector
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.template.response import TemplateResponse
from django.views.decorators.csrf import csrf_exempt
from ml_app.core.ingestion.config import EMBEDDERS

from webapp.models import (
    RawDocument,
)
from webapp.models.extraction import DocChunk, ExtractionSchema, RawExtraction
from webapp.utils.json_schema import compact_array_indices, json_paths_to_dict

logger = structlog.get_logger(__name__)


def rawdocument_get_doc_chunks_view(request: HttpRequest, pk: str) -> JsonResponse:
    raw_doc = get_object_or_404(RawDocument, pk=pk)
    chunks = raw_doc.doc_chunks.all()
    context = {
        "chunks": [
            {
                "id": chunk.pk,
                "content": chunk.content,
                "bbox_xmin": chunk.bbox_xmin,
                "bbox_ymin": chunk.bbox_ymin,
                "bbox_xmax": chunk.bbox_xmax,
                "bbox_ymax": chunk.bbox_ymax,
                "page_number": chunk.page_number,
                "chunker_name": chunk.chunker_name,
                "parser_name": chunk.parser_name,
            }
            for chunk in chunks
        ]
    }
    if request.method == "GET":
        return JsonResponse(context)
    return JsonResponse("Invalid request method", status=405)


@csrf_exempt
def schemas_view(request: HttpRequest) -> JsonResponse:
    if request.method == "POST":
        data = json.loads(request.body)
        logger.info("Received schema data", data=data)
        json_schema = data.get("json_schema")
        ExtractionSchema.update_or_create(user=request.user, json_schema=json_schema)
    context = {"schemas": list(ExtractionSchema.get_resolved_schemas().values())}
    return JsonResponse(context)


@csrf_exempt
def submit_target_document_view(request: HttpRequest, pk: str) -> JsonResponse:
    raw_doc = get_object_or_404(RawDocument, pk=pk)
    if request.method == "POST":
        data = json.loads(request.body)
        logger.info("Received data", data=data)
        uri = data.get("uri")
        target_uri = data.get("target_uri")
        raw_data = data.get("raw_data")
        if not uri or not raw_data:
            return JsonResponse({"error": "URI and raw data are required"}, status=400)
        path = data.get("path")
        if path != "$" or target_uri != uri:
            msg = "Path and target URI must match the original URI"
            logger.error(msg, path=path, target_uri=target_uri, uri=uri)
            raise ValueError(msg)
        raw_paths = [
            (raw_extraction.target_json_path, raw_extraction.data)
            for raw_extraction in raw_doc.raw_extractions.exclude(target_json_path="$")
        ]
        backend_data = json_paths_to_dict(raw_paths)
        if backend_data != raw_data:
            msg = "Data mismatch between raw data and backend data"
            logger.error(
                msg,
                raw_data=raw_data,
                backend_data=backend_data,
                raw_doc_pk=raw_doc.pk,
            )
            raise ValueError(msg)
        RawExtraction.create(
            raw_doc=raw_doc,
            uri=uri,
            target_uri=target_uri,
            target_json_path=path,
            extraction_source="granular_ground_truth",
            chunks=[],
            data=raw_data,
            is_ground_truth=True,
        )
    return JsonResponse(data={"status": 200})


@csrf_exempt
def extracted_data_view(request: HttpRequest, pk: str) -> JsonResponse:  # noqa: C901
    raw_doc = get_object_or_404(RawDocument, pk=pk)
    completed = set()
    id2rs = {}
    if request.method == "DELETE":
        data = json.loads(request.body)
        raw_extract_id = data.get("id")
        if not raw_extract_id:
            return JsonResponse({"error": "raw_extract_id is required"}, status=400)
        raw_doc.raw_extractions.filter(pk=raw_extract_id).delete()
        old_paths = [
            (raw_extraction.target_json_path, raw_extraction.data) for raw_extraction in raw_doc.raw_extractions.all()
        ]
        new_paths, changes = compact_array_indices(old_paths)
        logger.info("Compacted array indices", changes=changes, new_paths=new_paths, old_paths=old_paths)
        for raw_extraction in raw_doc.raw_extractions.all():
            if raw_extraction.target_json_path in changes:
                raw_extraction.target_json_path = changes[raw_extraction.target_json_path]
                raw_extraction.save()
    if request.method == "POST":
        data = json.loads(request.body)
        logger.info("Received extracted data", data=data)
        uri = data.get("uri")
        target_uri = data.get("target_uri")
        raw_data = data.get("raw_data")
        raw_chunk = data.get("raw_chunk")
        if not uri or not raw_data:
            return JsonResponse({"error": "URI and raw data are required"}, status=400)
        bounding_box = raw_chunk.get("bounding_box")
        content = raw_chunk.get("content")
        path = data.get("path")

        chunk_kwargs: dict[str, bool | float | list[float]] = {}
        chunk = None
        if content:
            for embedder in EMBEDDERS:
                chunk_kwargs[embedder.name()] = embedder.embed(content)
            if bounding_box is not None:
                chunk_kwargs["bbox_xmin"] = bounding_box.get("x")
                chunk_kwargs["bbox_ymin"] = bounding_box.get("y")
                chunk_kwargs["bbox_xmax"] = bounding_box.get("x") + bounding_box.get("width")
                chunk_kwargs["bbox_ymax"] = bounding_box.get("y") + bounding_box.get("height")
                chunk_kwargs["has_bounding_box"] = True

            chunk = DocChunk.objects.create(
                user=raw_doc.created_by,
                raw_document=raw_doc,
                content=content,
                ingestion_id=uuid.uuid4(),
                content_format="text",
                chunker_name="granular_ground_truth",
                parser_name="granular_ground_truth",
                page_number=raw_chunk.get("page_number", 0),
                chunk_length=len(content),
                chunk_idx=-1,
                **chunk_kwargs,
            )
            chunk.search_vector = SearchVector("content")
            chunk.save()

        RawExtraction.create(
            raw_doc=raw_doc,
            uri=uri,
            target_uri=target_uri,
            target_json_path=path,
            extraction_source="granular_ground_truth",
            chunks=[chunk] if chunk else [],
            data=raw_data,
            is_ground_truth=True,
        )

    raw_doc.refresh_from_db()

    # TODO: how do we chooose which chunk to return?
    res = []
    for raw_extraction in raw_doc.raw_extractions.all():
        """
        chunk = raw_extraction.doc_chunks.filter(chunker_name="granular_ground_truth").first()
        """
        raw_extraction.upgrade_schema()
        raw_extraction.upgrade_target_schema()
        chunk = raw_extraction.doc_chunks.first()
        if chunk is None:
            continue
        res.append(
            {
                "id": raw_extraction.pk,
                "chunk_id": chunk.pk,
                "content": chunk.content,
                "page_number": chunk.page_number,
                "bbox_xmin": chunk.bbox_xmin,
                "bbox_ymin": chunk.bbox_ymin,
                "bbox_xmax": chunk.bbox_xmax,
                "bbox_ymax": chunk.bbox_ymax,
                "chunker_name": chunk.chunker_name,
                "parser_name": chunk.parser_name,
                "data": raw_extraction.data,
                "schema": raw_extraction.current_extraction_schema.json_schema,
                "is_ground_truth": raw_extraction.is_ground_truth,
                "target_uri": raw_extraction.current_target_schema.uri,
                "path": raw_extraction.target_json_path,
                "is_valid": raw_extraction.is_current_extraction_schema_validated,
            }
        )

    return JsonResponse(
        {
            "raw_extractions": sorted(res, key=lambda x: x["id"]),
            "document_completion": {completed_uri: id2rs[completed_uri] for completed_uri in completed},
        }
    )


def rawdocument_granular_ground_truth_view(request: HttpRequest, pk: str) -> HttpResponse:
    template = "admin/webapp/rawdocument/granular_ground_truth.html"
    raw_doc = get_object_or_404(RawDocument, pk=pk)
    context = {
        "raw_doc_pk": raw_doc.pk,
        "s3_url": raw_doc.signed_url,
        "raw_doc_file_type": Path(raw_doc.name).suffix.lower(),
    }
    if request.method == "GET":
        return TemplateResponse(request, template, context)
    return HttpResponse("Invalid request method", status=405)
