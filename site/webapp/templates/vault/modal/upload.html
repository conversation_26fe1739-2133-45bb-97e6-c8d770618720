{% load static %}
<!-- Upload Modal -->
<div id="upload-modal" class="relative bg-white rounded-xl w-[550px] p-6">
    <div class="flex justify-between">
        <span class="font-semibold font-['Inter'] text-[#343d36]">Upload your
        documents</span>
        <button type="button"
                _="on click trigger closeModal"
                class=" text-gray-400 hover:text-gray-800 transition">
            <img src="{% static 'assets/images/close.svg' %}"
                 width="16"
                 height="16"
                 alt="Close">
        </button>
    </div>
    <div id="form-messages" class="mb-4"></div>
    <form id="upload-form"
          class="space-y-4"
          hx-encoding="multipart/form-data"
          hx-post="{% url 'vault_upload' %}"
          {% if line_item %} hx-vals='{"client": "{{ line_item.investing_entity.client.pk }}", "entity": "{{ line_item.investing_entity.pk }}", "investment": "{{ line_item.investment.pk }}" }' {% endif %}
          hx-target="#modal"
          hx-trigger="submit">
        <div class="flex flex-col gap-4">
            <label class="text-sm font-['Inter'] text-[#343d36]">
                Please only upload documents for the same
                client / entity / investment.
            </label>
            {% if line_item %}
                {% include "vault/selected_line_item_inputs.html" %}
            {% else %}
                {% include "vault/modal/dropdown_options.html" %}
            {% endif %}
            <div class="flex-1 relative border-2 rounded-lg p-4 m-4 text-center">
                <img class="justify-center mx-auto mb-2"
                     src="{% static 'assets/images/upload_btn.svg' %}"
                     width="32"
                     height="32"
                     alt="Upload">
                <input type="file"
                       id="visible-document-file"
                       name="document-input"
                       accept=".pdf,.doc,.docx,.xls,.xlsx"
                       required
                       multiple
                       class="absolute inset-0 opacity-0 w-full h-full cursor-pointer">
                <span>Click to upload or drag and drop</span>
            </div>
        </div>
        <input type="hidden"
               id="hidden-completed-uploads"
               name="hidden-completed-uploads"
               value="0" />
        <div class="overflow-auto max-h-96 px-4 my-0 flex flex-col gap-1"
             id="file-preview"></div>
        <div>
            <button type="submit"
                    id="labeling-submit"
                    class="w-full px-4 py-2.5 bg-[#4b554d] text-white rounded-lg hover:bg-[#424d44] transition-colors flex items-center justify-center gap-2 disabled:cursor-not-allowed disabled:opacity-50">
                <span>Proceed to Labelling</span>
            </button>
        </div>
    </form>
</div>
<script type="text/javascript">
    var getSignedUrl = async function(file) {
        const body = {
            fileName: file.name,
            fileType: file.type,
            s3_parent_path: "{{ s3_parent_path }}"
        };

        const response = await fetch("{% url 'get_upload_signed_url' %}", {
            method: "POST",
            body: JSON.stringify(body),
            headers: {
                "Content-Type": "application/json",
                "X-CSRFToken": "{{ csrf_token }}"
            }
        });
        const data = await response.json();

        return {
            url: data.url,
            fields: data.fields,
            raw_doc_id: data.raw_doc_id,
            s3_path: data.s3_path
        };
    };

    async function uploadFile(presignedData, file, onProgress) {
        var formData = new FormData();
        Object.keys(presignedData.fields).forEach(key => {
            formData.append(key, presignedData.fields[key]);
        });
        formData.append("file", file);

        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            // Progress event listener
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                    const percentComplete = Math.round((event.loaded / event.total) * 100);
                    onProgress(percentComplete, event.loaded, event.total);
                }
            });

            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    resolve(xhr);
                } else {
                    reject(new Error(`Upload failed: ${xhr.status}`));
                }
            });

            xhr.addEventListener('error', () => {
                reject(new Error('Upload failed'));
            });

            xhr.open('POST', presignedData.url);
            xhr.send(formData);
        });
    }


    async function handleUploadS3Submit(file) {
        const submitButton = document.querySelector('button[type="submit"]#labeling-submit');
        submitButton.disabled = true;
        const presignedData = await getSignedUrl(file);
        const progressContainer = createProgressElement(file.name, presignedData.s3_path);

        try {
            await uploadFile(presignedData, file, (percent, loaded, total) => {
                console.log("updating progress")
                updateProgress(progressContainer, percent, loaded, total);
                if (percent == 100) {
                    filePreviewContainerCount = document.querySelectorAll('.upload-progress-container').length
                    completeUploadsInput = document.getElementById('hidden-completed-uploads')
                    completeUploadsInput.value = (parseInt(completeUploadsInput.value) || 0) + 1;
                    if (completeUploadsInput.value == filePreviewContainerCount)
                        submitButton.disabled = false
                }
            });
        } catch (err) {
            console.log(err);
            alert("There was an error uploading your file.");
            throw err;
        }
    };

    async function onFileInputChange() {
        const visibleInput = document.getElementById('visible-document-file');
        for (let file of visibleInput.files) {
            await handleUploadS3Submit(file);
        }
    }

    function createProgressElement(fileName, s3Path) {
        const container = document.createElement('div');
        container.className = 'upload-progress-container';
        container.innerHTML = `
        <div class="flex flex-row gap-2 px-4 py-2.5 text-[#343d36] border-2 rounded-lg">
            <img class="" src="{% static 'assets/images/file.svg' %}" width="20" height="20" alt="File">
            <div class="w-5/6">
                <div class="flex flex-col">
                    <span class="file-name overflow-auto">${fileName}</span>
                    <span class="file-size">0 MB</span>
                </div>
                <div class="progress-bar-container rounded-lg bg-[#4b554d] h-2" style="height: 8px; width:0px">
                </div>
            </div>
            <div>
                <img class="trash-icon cursor-pointer justify-center mx-auto mb-2" src="{% static 'assets/images/trash.svg' %}" width="20" height="20" alt="Delete">
                <span class="percentage">0%</span>
            </div>
            <input type="hidden" name="hidden-raw-doc-pk-${fileName}" aria-hidden="true" class="hidden" value="${s3Path}" />
        </div>
    `;

        const trashIcon = container.querySelector('.trash-icon');
        trashIcon.addEventListener('click', () => deleteDoc(container));

        document.getElementById('file-preview').appendChild(container);
        return container;
    }


    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function updateProgress(container, percent, loaded, total) {
        const progressBar = container.querySelector('.progress-bar-container');
        const percentageSpan = container.querySelector('.percentage');
        const fileSizeSpan = container.querySelector('.file-size');

        progressBar.style.width = `${percent}%`;
        percentageSpan.textContent = `${percent}%`;
        fileSizeSpan.textContent = `${formatBytes(loaded)} / ${formatBytes(total)}`;
    }

    function deleteDoc(container) {
        container.remove();
        const hiddenInputs = document.querySelectorAll('input[name^="hidden-raw-doc-pk-"]');
        if (hiddenInputs.length === 0) {
            const visibleInput = document.getElementById('visible-document-file');
            visibleInput.value = '';
        }

        filePreviewContainerCount = document.querySelectorAll('.upload-progress-container').length
        completeUploadsInput = document.getElementById('hidden-completed-uploads')
        completeUploadsInput.value = (parseInt(completeUploadsInput.value) || 1) - 1;
    }

    document.getElementById('visible-document-file').addEventListener('change', onFileInputChange);
</script>
