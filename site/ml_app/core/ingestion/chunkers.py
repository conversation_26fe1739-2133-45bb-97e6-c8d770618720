from abc import ABC, abstractmethod

from bs4 import Tag
from langchain_text_splitters import HTMLSemanticPreservingSplitter

from ml_app.core.ingestion.core import BoundingBox, ParsedDocument, ParsedDocumentWithChunk


class Chunker(ABC):
    @abstractmethod
    def name(self) -> str:
        pass

    @abstractmethod
    def chunk(self, parsed_document: list[ParsedDocument]) -> list[ParsedDocumentWithChunk]:
        pass


class PassthroughChunker(Chunker):
    def name(self) -> str:
        return "passthrough"

    def chunk(self, parsed_document: list[ParsedDocument]) -> list[ParsedDocumentWithChunk]:
        return [
            ParsedDocumentWithChunk(pd, pd.text, i, self.name(), pd.bounding_box)
            for i, pd in enumerate(parsed_document)
        ]


class SemanticSegmentationChunker(Chunker):
    def name(self) -> str:
        return "langchain_html_semantic_preserving"

    def chunk(self, parsed_document: list[ParsedDocument]) -> list[ParsedDocumentWithChunk]:
        # We're just going to use the langchain html chunker here.
        # This is a placeholder for the actual implementation,
        # I think we can improve ordering using the bounding boxes and semantic similarity.
        # Could we do an local pairwise reordering using semantic similarity?
        # Could we do a joint search where distance is a(pairwise similarity) */+ b(distance) pairwise correlation?
        # Maybe most mathematically correct is keep is euclidean, but bias the angle of the vector directly?
        # Should I intergerize the distances to give power to the right to left, top to down bias?
        # D_combined(a, b) = D_vis(a, b) * (1 + a * D_sem(a, b))
        # Should we resort the chunks for the chunk index by the calculated bounding box weight?
        # Sorting/resorting == traveling salesman problem. Symmetric if you don't want to bias the direction.
        # Assymetric TSP if you want to bias the direction to a readable direction: left to right, top to bottom
        # Instead of being a sorting problem, is this a clustering/connected components problem?
        # Where we bootstrap KNN from the bottom up, and then use the KNN to find the connected components?
        # https://python.langchain.com/docs/how_to/split_html/#using-htmlsemanticpreservingsplitter

        def image_handler(element: Tag) -> str:
            element["src"] = f"image_{element.get('data-image-index', 'unknown')}.png"
            return str(element)

        def noop_handler(element: Tag) -> str:
            return str(element)

        to_handle = ["table", "ul", "ol", "p", "span", "b", "h1", "h2", "h3", "h4"]

        splitter = HTMLSemanticPreservingSplitter(
            headers_to_split_on=[
                ("h1", "Header 1"),
                ("h2", "Header 2"),
                ("h3", "Header 3"),
                ("h4", "Header 4"),
            ],
            elements_to_preserve=to_handle,
            custom_handlers={
                **dict.fromkeys(to_handle, noop_handler),
                "img": image_handler,
            },
        )
        res = []
        i = 0
        for doc in parsed_document:
            documents = splitter.split_text(doc.text)
            for doc_chunk in documents:
                res.append(
                    ParsedDocumentWithChunk(
                        doc,
                        doc_chunk.page_content,
                        i,
                        self.name(),
                        BoundingBox.from_html_element(doc_chunk.page_content) if doc_chunk.page_content else None,
                    )
                )
                i += 1
        return res
