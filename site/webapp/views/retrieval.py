import structlog
from celery.result import AsyncResult
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse
from django.utils import timezone
from retrieval.core.registry import RetrievalRegistry
from retrieval.tasks import login_portal_task

from webapp.models.portal import MergedPortalCredential, MFAType
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser
from webapp.views.utils import add_hx_trigger_header, get_merged_portal_credential_from_url_params

logger = structlog.get_logger(__name__)

RETRIEVAL_COOLDOWN_HOURS = 0.5  # 30 minutes cooldown


def trigger_task(user: BridgeUser, merged_portal_credential: MergedPortalCredential) -> bool:
    if merged_portal_credential.last_retrieval and merged_portal_credential.last_retrieval.is_retrieval_running:
        logger.error(
            "Task is already running",
            portal_id=merged_portal_credential.portal.id,
            portal_name=merged_portal_credential.portal.name,
            user_id=user.id,
        )
        return False
    if not merged_portal_credential.portal.is_portal_supported(user):
        logger.error(
            "Portal not supported",
            portal_id=merged_portal_credential.portal.id,
            portal_name=merged_portal_credential.portal.name,
            user_id=user.id,
        )
        return False

    # TODO: Consider how we rate limit success vs. failure
    now = timezone.now()
    if user.is_demo:
        if (
            not merged_portal_credential.is_first_retrieval
            and merged_portal_credential.last_retrieval.next_available_time > now
        ):
            logger.error(
                "Last retrieval was ran within the last 30 minutes",
                portal_id=merged_portal_credential.portal.id,
                portal_name=merged_portal_credential.portal.name,
                user_id=user.id,
                next_available_time=merged_portal_credential.last_retrieval.next_available_time,
                now=now,
            )
            return False
    elif merged_portal_credential.last_retrieval and merged_portal_credential.last_retrieval.next_available_time > now:
        logger.error(
            "Last retrieval was ran within the last 30 minutes",
            portal_id=merged_portal_credential.portal.id,
            portal_name=merged_portal_credential.portal.name,
            user_id=user.id,
        )

    logger.info(
        "Triggering retrieval",
        portal_id=merged_portal_credential.portal.id,
        portal_name=merged_portal_credential.portal.name,
        user_id=user.id,
    )

    manager_cls = RetrievalRegistry.get_retrieval_manager(
        user=user,
        name=merged_portal_credential.portal.name,
        portal_login_url=merged_portal_credential.portal.portal_login_url,
    )

    if not manager_cls:
        logger.error(
            "No manager found",
            portal_id=merged_portal_credential.portal.id,
            portal_name=merged_portal_credential.portal.name,
            user_id=user.id,
        )
        return False

    retrieval = Retrieval.create(
        user=user, merged_portal_credential=merged_portal_credential, manager=manager_cls.__name__
    )
    task = login_portal_task.apply_async(kwargs={"retrieval_id": str(retrieval.id)})
    retrieval.task_id = task.id
    retrieval.save()
    return True


@login_required
def run(request: HttpRequest) -> HttpResponse:
    template = "link/portal/run_cell.html"
    merged_portal_credential = get_merged_portal_credential_from_url_params(request)
    context = {
        "merged_portal_credential": merged_portal_credential,
        "OnboardingStatus": MergedPortalCredential.OnboardingStatus,
        "RetrievalStatus": Retrieval.RetrievalStatus,
        "MFAType": MFAType,
    }
    headers = {}

    if request.method == "POST":
        did_trigger = trigger_task(request.user, merged_portal_credential)
        if not did_trigger:
            context["message"] = "Portal not supported or already running."
        merged_portal_credential.refresh_from_db()
        headers = add_hx_trigger_header(
            {
                "changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk},
                "retrievalStarted": {"merged_portal_credential_pk": merged_portal_credential.pk},
            },
            headers,
        )
        return TemplateResponse(request, template, context, headers=headers)

    if request.method == "GET":
        if merged_portal_credential.last_retrieval and merged_portal_credential.last_retrieval.is_retrieval_running:
            # If retrieval is running, trigger another status update
            headers = add_hx_trigger_header(
                {"retrievalStatusUpdate": {"merged_portal_credential_pk": merged_portal_credential.pk}}, headers
            )
        else:
            # If retrieval is complete, trigger the completion event
            headers = add_hx_trigger_header(
                {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}},
                headers,
            )
        return TemplateResponse(request, template, context, headers=headers)

    # TODO: Test and confirm this is working
    if request.method == "DELETE":
        retrieval = merged_portal_credential.last_retrieval
        if retrieval and retrieval.is_retrieval_running:
            result = AsyncResult(retrieval.task_id)
            result.revoke(terminate=True, signal="SIGKILL")
            retrieval.update_login_status(Retrieval.RetrievalStatus.PENDING_CANCEL, should_raise=False)
        merged_portal_credential.refresh_from_db()
        headers = add_hx_trigger_header(
            {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}}, headers
        )
        return TemplateResponse(request, template, context, headers=headers)

    return HttpResponse(status=405)


@login_required
def status(request: HttpRequest) -> HttpResponse:
    """Endpoint for retrieving just the status partial."""
    template = "link/portal/run_cell_status.html"
    merged_portal_credential = get_merged_portal_credential_from_url_params(request)
    context = {
        "merged_portal_credential": merged_portal_credential,
        "OnboardingStatus": MergedPortalCredential.OnboardingStatus,
        "RetrievalStatus": Retrieval.RetrievalStatus,
        "MFAType": MFAType,
    }
    headers = {}

    if merged_portal_credential.last_retrieval and merged_portal_credential.last_retrieval.is_retrieval_running:
        # If retrieval is running, trigger another status update
        headers = add_hx_trigger_header(
            {"retrievalStatusUpdate": {"merged_portal_credential_pk": merged_portal_credential.pk}}, headers
        )
        if (
            merged_portal_credential.last_retrieval.retrieval_status == Retrieval.RetrievalStatus.BLOCKED_LOGIN_OTP
            and merged_portal_credential.multi_factor_authentication_type == MFAType.LIVE_LOGIN
        ):
            headers = add_hx_trigger_header(
                {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}},
                headers,
            )
    else:
        # If retrieval is complete, trigger the completion event
        headers = add_hx_trigger_header(
            {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}},
            headers,
        )
    return TemplateResponse(request, template, context, headers=headers)
