{% extends "base.html" %}
{% load customtags static %}
{% block title %}
    Settings
{% endblock title %}
{% block webapp_content %}
    <div class="main-content ml-16 bg-base-beige max-h-full min-h-screen transition-all duration-300">
        {% include "components/dashboard_header.html" with title="Settings" user=user %}
        <div class="w-full border-b py-2 px-12">
            <div class="h-9 rounded-lg justify-start items-center gap-1.5 inline-flex">
                <!-- My Profile Tab -->
                <a href="{% url 'settings_profile' %}"
                   class="px-3 py-2 rounded-lg justify-center items-center gap-2 flex overflow-hidden cursor-pointer {% if request.resolver_match.url_name in 'settings_profile, settings' %}bg-[#eceee8] border-[#eceee8]{% endif %}"
                   hx-get="{% url 'settings_profile' %}"
                   hx-target="body"
                   hx-push-url="true">
                    <div class="text-[#343d36] text-sm font-semibold font-['Inter'] leading-tight">My profile</div>
                </a>
                <!-- Password Tab -->
                <a href="{% url 'settings_password' %}"
                   class="px-3 py-2 rounded-lg justify-center items-center gap-2 flex overflow-hidden cursor-pointer {% if request.resolver_match.url_name == 'settings_password' %}bg-[#eceee8] border-[#eceee8]{% endif %}"
                   hx-get="{% url 'settings_password' %}"
                   hx-target="body"
                   hx-push-url="true">
                    <div class="text-[#4b554d] text-sm font-semibold font-['Inter'] leading-tight">Password</div>
                </a>
                <!-- Entity Tab -->
                <a href="{% url 'settings_entities' %}"
                   class="px-3 py-2 rounded-lg justify-center items-center gap-2 flex overflow-hidden cursor-pointer {% if request.resolver_match.url_name == 'settings_entities' %}bg-[#eceee8] border-[#eceee8]{% endif %}"
                   hx-get="{% url 'settings_entities' %}"
                   hx-target="body"
                   hx-push-url="true">
                    <div class="text-[#4b554d] text-sm font-semibold font-['Inter'] leading-tight">Entity Management</div>
                </a>
                <!-- Organization Tab -->
                <a href="{% url 'settings_organization' %}"
                   class="px-3 py-2 rounded-lg justify-center items-center gap-2 flex overflow-hidden cursor-pointer {% if request.resolver_match.url_name == 'settings_organization' %}bg-[#eceee8] border-[#eceee8]{% endif %}"
                   hx-boost="false">
                    <div class="text-[#4b554d] text-sm font-semibold font-['Inter'] leading-tight">Manage Team</div>
                </a>
            </div>
        </div>
        <div id="settings-content">
            {% block settings_content %}
            {% endblock settings_content %}
        </div>
    </div>
{% endblock webapp_content %}
