services:
  site:
    build:
      context: .
      dockerfile: Dockerfile
      target: bridge-prod
    tty: false
    command: ""
    volumes:
      - ~/.aws/credentials:/home/<USER>/.aws/credentials:ro
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-likeprod}
  celery:
    build:
      context: .
      dockerfile: Dockerfile
      target: bridge-prod
    command: ""
    volumes:
      - ~/.aws/credentials:/home/<USER>/.aws/credentials:ro
    depends_on:
      - db
      - redis 
    environment:
      # - DISPLAY=host.docker.internal:0  # Use host.docker.internal for Docker Desktop on macOS
      - ENVIRONMENT=${ENVIRONMENT:-likeprod}
    shm_size: '1gb'
