from typing import Any

import structlog
from django.core.management.base import BaseCommand
from integrations.tasks.orion import create_orion_files

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Creates Five Target Orion Files"

    def handle(self, *args: list[str], **kwargs: dict[str, Any]) -> None:
        if args and kwargs:
            pass

        logger.info("Starting manual Orion file creation")

        create_orion_files()

        logger.info("Manual Orion file creation completed")
