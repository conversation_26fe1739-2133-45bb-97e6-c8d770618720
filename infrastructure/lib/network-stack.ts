import * as cdk from "aws-cdk-lib";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import { NetworkStackOutputs, SharedProps, SharedStack } from "./schema";
import * as logs from "aws-cdk-lib/aws-logs";
import { Construct } from "constructs";

export class NetworkStack extends SharedStack {
  constructor(scope: Construct, id: string, props: SharedProps) {
    super(scope, id, props);

    // create a VPC, for now with public subnets
    // private subnets can be added later for greater security posture
    // this also creates a (cheaper) internet gateway and does not setup a NAT
    // maxAzs has dependency in storage-stack
    // TODO: make better, namely AZ count generification
    const vpc = new ec2.Vpc(this, "VPC", {
      maxAzs: 2, // Default is all AZs in the region
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: "public-subnet",
          subnetType: ec2.SubnetType.PUBLIC,
        },
      ],
    });

    const logGroup = new logs.LogGroup(this, "VpcFlowLogs", {
      retention: logs.RetentionDays.ONE_YEAR,
    });

    vpc.addFlowLog("FlowLogS3", {
      destination: ec2.FlowLogDestination.toCloudWatchLogs(logGroup),
    });

    // define a security group that allows all outbound traffic from the network
    const egressSG = new ec2.SecurityGroup(this, "EgressSecurityGroup", {
      vpc: vpc,
      description: "Security group allowing all outbound traffic from the VPC",
      allowAllOutbound: true,
    });

    // Create a S3 VPC endpoint to keep traffic directed to S3 within AWS
    const s3Endpoint = vpc.addGatewayEndpoint("S3Endpoint", {
      service: ec2.GatewayVpcEndpointAwsService.S3,
    });

    // Create a Redis VPC endpoint to keep traffic directed to S3 within AWS
    const redisEndpoint = vpc.addInterfaceEndpoint("RedisEndpoint", {
      service: ec2.InterfaceVpcEndpointAwsService.ELASTICACHE,
      privateDnsEnabled: true,
    });

    // Create CloudWatch Logs VPC endpoint to keep traffic directed to cloudwatch logs within AWS
    const logsEndpoint = vpc.addInterfaceEndpoint("LogsEndpoint", {
      service: ec2.InterfaceVpcEndpointAwsService.CLOUDWATCH_LOGS,
      privateDnsEnabled: true,
    });

    // Create ECR Docker VPC endpoint to pull in private docker images without going to public internet
    const dockerEndpoint = vpc.addInterfaceEndpoint("ECRDockerEndpoint", {
      service: ec2.InterfaceVpcEndpointAwsService.ECR_DOCKER,
      privateDnsEnabled: true,
    });

    // ECR API VPC endpoint to run ECR discovery and HEAD-meta information locally, without going to public internet
    const dockerApiEndpoint = vpc.addInterfaceEndpoint("ECRApiEndpoint", {
      service: ec2.InterfaceVpcEndpointAwsService.ECR,
      privateDnsEnabled: true,
    });

    // SSM VPC endpoint to keep SSM parameter traffic local
    const ssmEndpoint = vpc.addInterfaceEndpoint("SsmEndpoint", {
      service: ec2.InterfaceVpcEndpointAwsService.SSM,
      privateDnsEnabled: true,
    });

    // Secrets Manager VPC endpoint to keep secrets traffic local
    const secretsManagerEndpoint = vpc.addInterfaceEndpoint(
      "SecretsManagerEndpoint",
      {
        service: ec2.InterfaceVpcEndpointAwsService.SECRETS_MANAGER,
        privateDnsEnabled: true,
      },
    );

    const lambdaManagerEndpoint = vpc.addInterfaceEndpoint(
      "LambdaManagerEndpoint",
      {
        service: ec2.InterfaceVpcEndpointAwsService.LAMBDA,
        privateDnsEnabled: true,
      },
    );

    const cloudwatchEndpoint = vpc.addInterfaceEndpoint("CloudWatchEndpoint", {
      service: ec2.InterfaceVpcEndpointAwsService.CLOUDWATCH_MONITORING,
      privateDnsEnabled: true,
    });
    // endregion

    // Outputs
    new NetworkStackOutputs({
      VpcId: vpc.vpcId,
      VpcAvailabilityZones: vpc.availabilityZones,
      VpcPublicSubnetIds: vpc.publicSubnets.map((subnet) => subnet.subnetId),
      VpcPublicRouteTableIds: vpc.publicSubnets.map(
        (subnet) => subnet.routeTable.routeTableId,
      ),
      VpcCidrBlock: vpc.vpcCidrBlock,
      // VpcPrivateRouteTableIds: vpc.privateSubnets.map(subnet => subnet.routeTable.routeTableId),
      // VpcPrivateSubnetIds: vpc.privateSubnets.map(subnet => subnet.subnetId),
    }).toCdkOutput(this);
  }
}
