import datetime
import json
import uuid
from pathlib import Path
from typing import TypeVar

import boto3
from django.conf import settings
from django.utils import timezone
from pydantic import BaseModel
from webapp.models.portal import MergedPortalCredential

from ml_app.core.data_classes.doc_vault import ConfidenceBracketsEnum, DocumentTypeEnum
from ml_app.core.data_classes.state import BootstrapState

T = TypeVar("T")


def get_state(  # noqa: UP047
    merged_portal_credential: MergedPortalCredential,
    state_type: type[T],
    name: str = "BootstrapState",
    group: str = "",
) -> T | None:
    """
    Get the current state of the retrieval based on the merged portal credential.
    """
    group_id = ""
    if group:
        group_id = f"-{group}"
    state_location = f"ml/line_item_prediction/{merged_portal_credential.pk}/{name}/state{group_id}-latest.json"
    s3 = boto3.client("s3")
    try:
        state_object = s3.get_object(Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=state_location)
        state_data = json.loads(state_object["Body"].read().decode("utf-8"))
        return state_type(**state_data)
    except s3.exceptions.ClientError:
        return None


def json_defaults(obj: object) -> str:
    if isinstance(obj, BaseModel):
        return obj.model_dump()
    if isinstance(obj, ConfidenceBracketsEnum):
        return obj.value
    if isinstance(obj, DocumentTypeEnum):
        return obj.value
    return str(obj)


def save_state(  # noqa: UP047
    merged_portal_credential: MergedPortalCredential,
    state: T,
    name: str = "BootstrapState",
    *,
    parallel: bool = True,
    group: str = "",
) -> None:
    """
    Save the current state of the retrieval based on the merged portal credential.
    """
    uniq_id = ""
    if parallel:
        uniq_id = f"-{uuid.uuid4().hex[:8]}"
    group_id = ""
    if group:
        group_id = f"-{group}"
    file_name = f"state{uniq_id}{group_id}-{timezone.now().strftime('%Y-%m-%d_%H-%M-%S')}.json"
    latest_file_name = f"state{group_id}-latest.json"
    s3_location = Path(f"ml/line_item_prediction/{merged_portal_credential.pk}/{name}/")
    boto3.client("s3").put_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        Key=str(s3_location / file_name),
        Body=json.dumps(state, indent=2, default=json_defaults).encode("utf-8"),
    )
    boto3.client("s3").copy_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        CopySource={"Bucket": settings.AWS_STORAGE_BUCKET_NAME, "Key": str(s3_location / file_name)},
        Key=str(s3_location / latest_file_name),
    )


def delete_state(merged_portal_credential: MergedPortalCredential, name: str = "BootstrapState") -> None:
    """
    Save the current state of the retrieval based on the merged portal credential.
    """
    s3 = boto3.client("s3")
    s3_location = f"ml/line_item_prediction/{merged_portal_credential.pk}/{name}/"
    paginator = s3.get_paginator("list_objects_v2")
    page_iterator = paginator.paginate(Bucket=settings.AWS_STORAGE_BUCKET_NAME, Prefix=s3_location)
    state_files = []
    for page in page_iterator:
        state_files.extend([obj for obj in page.get("Contents", []) if obj["Key"].endswith(".json")])
    # Sort by LastModified (oldest to newest)
    for obj in state_files:
        s3.delete_object(Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=obj["Key"])


def get_li2doc2pk(state: BootstrapState) -> dict[str, dict[str, list[str]]]:
    if state is None or "id2obj" not in state:
        return {}
    id2obj = state["id2obj"]
    ret = {}
    for pk, values in id2obj.items():
        lis = []
        for li in values["line_items"]:
            li_str = (
                f"Client: {li['client_investor_name']}"
                f" Entity: {li['investing_entity_name']}"
                f" Investment: {li['investment_name']})"
            )
            lis.append(li_str)
        li_str = ", ".join(sorted(lis))
        if li_str not in ret:
            ret[li_str] = {}
        if values["document_type"] not in ret[li_str]:
            ret[li_str][values["document_type"]] = []
        ret[li_str][values["document_type"]].append(pk)
    return ret


import concurrent.futures


def reduce_state(merged_portal_credential: MergedPortalCredential, name: str = "BootstrapState") -> dict:  # noqa: C901
    """
    Reduce all state-*.json files (excluding state-latest.json) by merging their contents in order of creation time.
    """
    s3 = boto3.client("s3")
    s3_location = f"ml/line_item_prediction/{merged_portal_credential.pk}/{name}/"
    paginator = s3.get_paginator("list_objects_v2")
    page_iterator = paginator.paginate(Bucket=settings.AWS_STORAGE_BUCKET_NAME, Prefix=s3_location)
    state_files = []
    for page in page_iterator:
        state_files.extend(
            [
                obj
                for obj in page.get("Contents", [])
                if obj["Key"].startswith(s3_location + "state-")
                and obj["Key"].endswith(".json")
                and not obj["Key"].startswith(s3_location + "reduced-")
                and not obj["Key"].endswith("state-latest.json")
            ]
        )
    # Sort by LastModified (oldest to newest)
    state_files.sort(key=lambda x: x["LastModified"])

    def download_and_load(key: str) -> dict:
        obj = s3.get_object(Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=key)
        return json.loads(obj["Body"].read().decode("utf-8"))

    merged_state = {}
    with concurrent.futures.ThreadPoolExecutor() as executor:
        results = list(executor.map(lambda obj: download_and_load(obj["Key"]), state_files))
        for state in results:

            def deep_update(d: dict, u: dict) -> dict:
                for k, v in u.items():
                    if isinstance(v, dict) and isinstance(d.get(k), dict):
                        deep_update(d[k], v)
                    elif isinstance(v, list) and isinstance(d.get(k), list):
                        # If both lists contain dicts, merge by index
                        if all(isinstance(item, dict) for item in v) and all(isinstance(item, dict) for item in d[k]):
                            min_len = min(len(d[k]), len(v))
                            for i in range(min_len):
                                deep_update(d[k][i], v[i])
                            # If v is longer, append the extra items
                            # TODO: This is a bit jank
                            if len(v) > len(d[k]):
                                d[k].extend(v[min_len:])
                                # TODO: something like this: d[k] = list(set(d[k]))
                            # If d[k] is longer, keep the extra items
                        else:
                            # Overwrite the list entirely with the new value
                            d[k] = v
                    else:
                        d[k] = v

            deep_update(merged_state, state)

    # Write out the reduced state to S3
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")  # noqa: DTZ005
    reduced_file_name = f"reduced-{timestamp}.json"
    reduced_latest_file_name = "state-latest.json"
    s3.put_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        Key=s3_location + reduced_file_name,
        Body=json.dumps(merged_state, indent=2).encode("utf-8"),
    )
    s3.copy_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        CopySource={"Bucket": settings.AWS_STORAGE_BUCKET_NAME, "Key": s3_location + reduced_file_name},
        Key=s3_location + reduced_latest_file_name,
    )
    return merged_state
