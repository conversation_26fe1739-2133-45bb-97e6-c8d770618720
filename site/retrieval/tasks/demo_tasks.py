import datetime
from typing import Any, NamedTuple

import structlog
from celery import Task, shared_task
from dateutil import parser
from webapp.models.documents import (
    REQUIRED_SUB_TYPE_DOCUMENTS,
    ProcessedDocument,
    RawDocument,
)
from webapp.models.line_item import LineItem
from webapp.models.user import BridgeUser

logger = structlog.get_logger(__name__)
PROCESS_DOCUMENT_VERSION = 1


@shared_task(bind=True, track_started=True)
def demo_process_document(self: Task, *, raw_document_id: str, user_id: str, mark_as_read: bool = True) -> None:  # noqa: ARG001
    user = BridgeUser.objects.get(id=user_id)
    raw_document = RawDocument.objects.for_user(user).get(id=raw_document_id)

    line_item = (
        LineItem.objects.for_user(user)
        .filter(
            investing_entity__client__legal_name=raw_document.metadata.get("Client Name"),
            investing_entity__legal_name=raw_document.metadata.get("Investment Entity Legal Name"),
            investment__legal_name=raw_document.metadata.get("Investment / Fund Legal Name"),
        )
        .first()
    )
    if line_item is None:
        merged_portal_credential = raw_document.retrieval.merged_portal_credential
        line_item = LineItem.create_from_merged_portal_credential(
            merged_portal_credential=merged_portal_credential,
            client_legal_name=raw_document.metadata.get("Client Name"),
            entity_legal_name=raw_document.metadata.get("Investment Entity Legal Name"),
            investment_fund_legal_name=raw_document.metadata.get("Investment / Fund Legal Name"),
            investment_managing_firm_name=raw_document.metadata.get("Managing Firm Name"),
            is_visible=True,
        )

    document_type = raw_document.document_type
    sub_document_type = ""
    i = 0
    if document_type in REQUIRED_SUB_TYPE_DOCUMENTS:
        allowed_sub_document_types = ProcessedDocument.get_sub_document_types_for_document_type(document_type)
        sub_document_type = allowed_sub_document_types[i % len(allowed_sub_document_types)]
        i += 1

    effective_date = parser.parse(raw_document.metadata.get("Effective Date"))
    capital_call_facts = get_capital_call_facts(raw_document)
    distribution_notice_facts = get_distribution_notice_facts(raw_document)
    investment_update_facts = get_investment_update_facts(raw_document)

    def to_dict(x: Any) -> dict | None:  # noqa: ANN401
        return x._asdict() if x is not None else None

    ProcessedDocument.create(
        raw_document=raw_document,
        line_items=[line_item],
        document_type=document_type,
        sub_document_type=sub_document_type,
        posted_date=raw_document.posted_date,
        effective_date=effective_date,
        process_document_version=PROCESS_DOCUMENT_VERSION,
        process_document_source="demo",
        labeled_by=user,
        is_visible=True,
        is_ground_truth=True,
        has_been_viewed=mark_as_read,
        distribution_notice=to_dict(distribution_notice_facts),
        investment_update=to_dict(investment_update_facts),
        capital_call=to_dict(capital_call_facts),
    )


class CapitalCallFacts(NamedTuple):
    capital_call_due_date: datetime.datetime
    amount: float


class DistributionNoticeFacts(NamedTuple):
    amount: float


class InvestmentUpdateFacts(NamedTuple):
    invested: float
    total_value: float
    unfunded: float
    realized_value: float
    unrealized_value: float
    committed_capital: float


def get_capital_call_facts(raw_document: RawDocument) -> CapitalCallFacts | None:
    if raw_document.metadata is None:
        return None
    cc_amount = raw_document.metadata.get("Capital Call Amount")
    due_date = raw_document.metadata.get("Effective Date")
    if cc_amount is not None and due_date is not None:
        return CapitalCallFacts(due_date, cc_amount)
    return None


def get_distribution_notice_facts(raw_document: RawDocument) -> DistributionNoticeFacts | None:
    if raw_document.metadata is None:
        return None
    d_amount = raw_document.metadata.get("Distribution Amount")
    if d_amount is not None:
        return DistributionNoticeFacts(d_amount)
    return None


def get_investment_update_facts(raw_document: RawDocument) -> InvestmentUpdateFacts | None:
    if raw_document.metadata is None:
        return None
    committed = raw_document.metadata.get("Committed")
    invested = raw_document.metadata.get("Invested")
    vals = [
        committed,
        invested,
        raw_document.metadata.get("Total Value"),
        raw_document.metadata.get("Realized Value"),
        raw_document.metadata.get("Unrealized Value"),
    ]
    if all(v is not None for v in vals):
        unfunded = committed - invested
        return InvestmentUpdateFacts(
            invested=raw_document.metadata.get("Invested"),
            total_value=raw_document.metadata.get("Total Value"),
            unfunded=unfunded,
            realized_value=raw_document.metadata.get("Realized Value"),
            unrealized_value=raw_document.metadata.get("Unrealized Value"),
            committed_capital=raw_document.metadata.get("Committed"),
        )
    return None
