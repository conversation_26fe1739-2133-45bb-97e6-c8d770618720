import datetime
import re
from collections.abc import Callable
from decimal import Decimal, InvalidOperation
from typing import Any, NamedTuple

import numpy as np
import pandas as pd
import structlog
from django import forms
from django.contrib.auth.decorators import login_required
from django.db.models import Exists, F, OuterRef, Prefetch, Q, QuerySet, Subquery, Window
from django.db.models.functions import Rank
from django.http import Http404, HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.template.response import TemplateResponse
from django.utils import timezone
from django.views.decorators.http import require_POST

from webapp.models.documents import CapitalCallDocumentFact, CapitalCallStatus, DocumentType, ProcessedDocument
from webapp.models.investment_details import Client, InvestingEntity, Investment
from webapp.models.line_item import LineItem
from webapp.models.user import BridgeUser
from webapp.views.utils import add_hx_trigger_header

logger = structlog.get_logger(__name__)

HIDDEN_LAYER_NAME = "__HIDDEN__"


class LatestDocuments(NamedTuple):
    investment_update: ProcessedDocument | None
    distribution_notice: ProcessedDocument | None
    account_statement: ProcessedDocument | None
    capital_call: ProcessedDocument | None


@login_required
def dashboard(request: HttpRequest) -> HttpResponse:
    template = "dashboard/dashboard.html"
    processed_docs = get_processed_documents(request.user)
    line_items = get_line_items(request.user)
    cards_context = build_cards_context(line_items, request.user)
    table_context = build_table_context(line_items, request.user)
    context = {
        "DocumentType": DocumentType,
        "latest_updates": get_latest_updates(processed_docs),
        "upcoming_capital_calls": get_upcoming_capital_calls(processed_docs),
        "capital_call_status_choices": CapitalCallStatus.choices,
        **table_context,
        **cards_context,
    }
    return TemplateResponse(request, template, context=context)


@login_required
def export_modal(request: HttpRequest) -> HttpResponse:
    template = "modal.html"
    default_initial_template = "dashboard/investment_details/export_modal.html"
    modal_include_template = request.GET.get("modal_include_template", default_initial_template)
    line_items = get_line_items(request.user, is_export=True)
    context = {"modal_include_template": modal_include_template, "line_items": line_items}
    return TemplateResponse(request, template, context)


def build_table_context(
    line_items: dict[LineItem, LatestDocuments],
    user: BridgeUser,
    search_state: str | None = None,
) -> dict[str, Any]:
    # get base investment details - List of dicts, currently have total value raw and total value usd
    investment_details = get_investment_details(line_items)

    default_table_order = [
        {"name": "Client", "id": "client"},
        {"name": "Entity", "id": "entity"},
        {"name": "Investment", "id": "investment"},
    ]
    default_show_hide_states = {
        "client": True,
        "entity": True,
        "investment": True,
    }

    table_order = user.user_view_preferences.get("table_order", default_table_order)
    show_hide_states = user.user_view_preferences.get("show_hide_states", default_show_hide_states)

    if search_state is None:
        search_state = ""

    filtered_table_order = [x["id"] for x in table_order if show_hide_states[x["id"]]]
    if len(filtered_table_order) == 2:  # noqa: PLR2004
        # We hard code that only level 2 objects can be hidden.
        filtered_table_order = [filtered_table_order[0], HIDDEN_LAYER_NAME, filtered_table_order[1]]

    if filtered_table_order[1] == HIDDEN_LAYER_NAME:
        sub_obj_sums = {}
    else:
        # This needs to rollup to USD
        sub_obj_sums = get_sums(investment_details, by=filtered_table_order[1])

    # this needs the leafs to be in the original currency - so need two copies
    table_data = organize_structured_data(investment_details, filtered_table_order)

    # need this to correctly rollup to USD
    top_obj_sums = get_sums(investment_details, by=filtered_table_order[0])

    return {
        "table_data": table_data,
        "sub_obj_sums": sub_obj_sums,
        "top_obj_sums": top_obj_sums,
        "show_hide_states": show_hide_states,
        "search_state": search_state,
        "table_order": table_order,
    }


@login_required
def table(request: HttpRequest) -> HttpResponse:
    template = "dashboard/investment_details/table.html"

    if request.method == "POST":
        # Handle table modifications (show/hide, order)
        show_hide_states = {
            "client": request.POST.get("show_client", "false") == "client",
            "entity": True,
            "investment": True,
        }
        table_order = [
            {"name": key.capitalize(), "id": key}
            for key in request.POST.get("table_order", "client,entity,investment").split(",")
        ]

        dashboard_table_filters = {"show_hide_states": show_hide_states, "table_order": table_order}
        existing_user_view_preferences = request.user.user_view_preferences
        request.user.user_view_preferences = {**existing_user_view_preferences, **dashboard_table_filters}
        request.user.save()

        search_state = request.POST.get("search", "")
        search_query = search_state.strip()
        line_items = get_line_items(request.user, search_query)
        table_context = build_table_context(line_items, request.user, search_state)
        return TemplateResponse(request, template, context=table_context)

    return HttpResponse("Invalid request method", status=405)


def organize_structured_data(
    investment_details: list[dict[str, Any]], filtered_table_order: list[str]
) -> list[dict[str, Any]]:
    structured_data = {}

    # Step 1: Organize data hierarchically
    for item in investment_details:
        order_names = [
            (obj_type, item.get(obj_type), obj_type == HIDDEN_LAYER_NAME) for obj_type in filtered_table_order
        ]
        sub_obj = structured_data
        prev_sub_obj = structured_data
        for order_tuple in order_names:
            sub_obj[order_tuple] = sub_obj.get(order_tuple, {})
            prev_sub_obj = sub_obj
            sub_obj = sub_obj[order_tuple]
        prev_sub_obj[order_names[-1]] = item  # Store actual investment item

    has_hidden_second_layer = HIDDEN_LAYER_NAME in filtered_table_order

    def extract_first_valid(field_name: str, values: dict[str, Any]) -> str:
        """Extract the first available valid field from nested values"""
        for key, v in values.items():
            if isinstance(key, tuple):  # Handling tuple keys
                v = values[key]  # noqa: PLW2901

            if isinstance(v, dict):
                if field_name in v and v[field_name] not in [None, "N/A"]:
                    return str(v[field_name])

                # Recursive extraction in case of deeply nested structures
                nested_value = extract_first_valid(field_name, v)
                if nested_value not in ["N/A", None]:
                    return nested_value

        return "N/A"

    # Step 2: Convert structured data into desired output format
    return [
        {
            "type": obj_type,
            "name": obj_name,
            "object_id": extract_first_valid(f"{obj_type}_id", values),
            "length": len(values) if not has_hidden_second_layer else sum(len(v.values()) for v in values.values()),
            "values": [
                {
                    "type": obj_type,
                    "name": obj_name,
                    "is_hidden": is_hidden,
                    "object_id": extract_first_valid(f"{obj_type}_id", values),
                    "length": len(values),
                    "values": [
                        {
                            "type": obj_type,
                            "name": obj_name,
                            "object_id": str(values.get(f"{obj_type}_id", "N/A")),
                            "value": values,
                        }
                        for (obj_type, obj_name, is_hidden), values in sorted(values.items(), key=lambda x: x[0][1])
                    ],
                }
                for (obj_type, obj_name, is_hidden), values in sorted(values.items(), key=lambda x: x[0][1])
            ],
        }
        for (obj_type, obj_name, is_hidden), values in sorted(structured_data.items(), key=lambda x: x[0][1])
    ]


def get_sums(investment_details: list[dict[str, Any]], by: str) -> dict[str, dict[str, int]]:
    sums = {}
    for detail in investment_details:
        key = detail[by]
        if key not in sums:
            sums[key] = {
                "total_value": 0,
                "realized_value": 0,
                "unrealized_value": 0,
                "committed_capital": 0,
                "invested": 0,
                "count": 0,
                "total_value_usd": 0,
                "realized_value_usd": 0,
                "unrealized_value_usd": 0,
                "committed_capital_usd": 0,
                "invested_usd": 0,
            }
        sums[key]["total_value"] += detail.get("total_value", 0)
        sums[key]["realized_value"] += detail.get("realized_value", 0)
        sums[key]["unrealized_value"] += detail.get("unrealized_value", 0)
        sums[key]["committed_capital"] += detail.get("committed_capital", 0)
        sums[key]["invested"] += detail.get("invested", 0)
        sums[key]["count"] += 1
        sums[key]["total_value_usd"] += detail.get("total_value_usd", 0)
        sums[key]["realized_value_usd"] += detail.get("realized_value_usd", 0)
        sums[key]["unrealized_value_usd"] += detail.get("unrealized_value_usd", 0)
        sums[key]["committed_capital_usd"] += detail.get("committed_capital_usd", 0)
        sums[key]["invested_usd"] += detail.get("invested_usd", 0)

    return sums


@login_required
def notifications_card(request: HttpRequest) -> HttpResponse:
    template = "dashboard/cards/new_notifications.html"
    line_items = get_line_items(request.user)
    context = build_cards_context(line_items, request.user)
    return TemplateResponse(request, template, context=context)


@login_required
def pdf_modal(request: HttpRequest) -> HttpResponse:
    template = "render_pdf.html"
    if request.method == "GET":
        processed_doc_pk = request.GET.get("processed_doc_pk", None)
        if processed_doc_pk is None or processed_doc_pk == "":
            raise Http404
        try:
            processed_doc = ProcessedDocument.objects.for_user(request.user).get(pk=processed_doc_pk)
        except ProcessedDocument.DoesNotExist as e:
            raise Http404 from e
        if not processed_doc or not processed_doc.is_visible:
            raise Http404
        if not request.user.is_hijacked and not processed_doc.has_been_viewed:
            processed_doc.has_been_viewed = True
            processed_doc.first_viewed_date = timezone.now()
            processed_doc.save()
        # TODO: what happens when its not a PDF document? how do we deal with it?
        return TemplateResponse(
            request,
            template,
            {
                "doc_link": processed_doc.signed_url,
                "hide_sidebar": True,
                "title": processed_doc.name,
            },
        )
    return HttpResponse("Invalid request method", status=405)


@login_required
def add_note(request: HttpRequest) -> HttpResponse:
    template = "modal.html"
    modal_include_template = request.GET.get("modal_include_template", "add_note.html")
    name = request.GET.get("name", "")
    typeof_note = request.GET.get("type", "")
    object_id = request.GET.get("object_id", "")

    existing_notes = ""
    if typeof_note == "client" and object_id:
        client = get_object_or_404(Client, id=object_id)
        existing_notes = client.note if client.note else ""

    elif typeof_note == "entity" and object_id:
        entity = get_object_or_404(InvestingEntity, id=object_id)
        existing_notes = entity.note if entity.note else ""

    elif typeof_note == "investment" and object_id:
        investment = get_object_or_404(Investment, id=object_id)
        existing_notes = investment.note if investment.note else ""

    if request.method == "GET":
        return TemplateResponse(
            request,
            template,
            {
                "modal_include_template": modal_include_template,
                "name": name,
                "type": typeof_note,
                "object_id": object_id,
                "existing_notes": existing_notes,
            },
        )

    if request.method == "POST":
        new_note = request.POST.get("note", "").strip()
        name = request.POST.get("name", "")
        typeof_note = request.POST.get("type", "")
        object_id = request.POST.get("object_id", "")
        if not new_note:
            return TemplateResponse(
                request,
                "components/note_textarea.html",
                {
                    "error": "Note cannot be empty",
                    "existing_notes": existing_notes,
                    "name": name,
                    "type": typeof_note,
                    "object_id": object_id,
                },
            )

        if typeof_note == "client" and object_id:
            client = get_object_or_404(Client, id=object_id)
            client.note = f"{new_note}"
            client.save()

        elif typeof_note == "entity" and object_id:
            entity = get_object_or_404(InvestingEntity, id=object_id)
            entity.note = f"{new_note}"
            entity.save()

        elif typeof_note == "investment" and object_id:
            investment = get_object_or_404(Investment, id=object_id)
            investment.note = f"{new_note}"
            investment.save()

        else:
            return TemplateResponse(
                request,
                "components/note_textarea.html",
                {
                    "error": "Invalid type or missing ID",
                    "existing_notes": existing_notes,
                    "name": name,
                    "type": typeof_note,
                    "object_id": object_id,
                },
            )

        return TemplateResponse(
            request,
            "components/note_textarea.html",
            {
                "success": "Note added successfully!",
                "existing_notes": new_note,
                "name": name,
                "type": typeof_note,
                "object_id": object_id,
            },
        )
    return HttpResponse(status=405)


def get_line_items(
    user: BridgeUser, search_query: str = "", *, is_export: bool = False
) -> dict[LineItem, LatestDocuments]:
    line_items = LineItem.objects.for_user(user)
    if is_export and user.organization.name == "Ezralow":
        line_items = line_items.filter(is_backfilled=True)

    if search_query:
        line_items = line_items.filter(
            Q(investing_entity__client__legal_name__icontains=search_query)
            | Q(investing_entity__client__note__icontains=search_query)  # Search in Client Notes
            | Q(investing_entity__legal_name__icontains=search_query)
            | Q(investing_entity__note__icontains=search_query)  # Search in Entity Notes
            | Q(investment__legal_name__icontains=search_query)
            | Q(investment__note__icontains=search_query),  # Search in Investment Notes
        )

    latest_doc_subquery = (
        ProcessedDocument.objects.filter(
            is_visible=True,
            line_item__in=line_items,
            document_type__in=[
                DocumentType.ACCOUNT_STATEMENT,
                DocumentType.INVESTMENT_UPDATE,
                DocumentType.DISTRIBUTION_NOTICE,
                DocumentType.CAPITAL_CALL,
            ],
        )
        .filter(
            (Q(document_type=DocumentType.ACCOUNT_STATEMENT) & ~Q(investment_update_document_id=None))
            | ~Q(document_type=DocumentType.ACCOUNT_STATEMENT)
        )
        .annotate(
            rank=Window(
                expression=Rank(),
                partition_by=[F("line_item_id"), F("document_type")],
                order_by=[F("effective_date").desc(), F("name").asc()],
            )
        )
        .filter(rank=1)
        .values("id")
    )
    historical_account_statement = (
        ProcessedDocument.objects.filter(
            is_visible=True,
            line_item__in=line_items,
        )
        .filter(Q(document_type=DocumentType.ACCOUNT_STATEMENT) & ~Q(investment_update_document_id=None))
        .annotate(
            rank=Window(
                expression=Rank(),
                partition_by=[F("line_item_id"), F("document_type")],
                order_by=[F("effective_date").desc(), F("name").asc()],
            )
        )
        .filter(rank__lte=2)
        .values("id")
    )

    # Step 2: Use the subquery IDs in your prefetch
    line_items = (
        line_items.annotate(has_processed_documents=Exists(ProcessedDocument.objects.filter(line_item=OuterRef("pk"))))
        .select_related(
            "investment",
            "investing_entity",
            "investing_entity__client",
        )
        .prefetch_related(
            # Use the subquery to prefetch only the latest documents
            Prefetch(
                "processed_documents",
                queryset=ProcessedDocument.objects.filter(id__in=Subquery(latest_doc_subquery)).select_related(
                    "investment_update_document", "distribution_notice_document", "capital_call_document"
                ),
                to_attr="latest_documents",
            ),
            Prefetch(
                "processed_documents",
                queryset=ProcessedDocument.objects.filter(id__in=Subquery(historical_account_statement)).select_related(
                    "investment_update_document",
                ),
                to_attr="historical_account_statements",
            ),
        )
    ).order_by("investing_entity__client__legal_name", "investing_entity__legal_name", "investment__legal_name")

    # Process the data and create the LatestDocuments objects
    result = {}
    for li in line_items:
        # Group the latest documents by type
        docs_by_type = (
            {doc.document_type: doc for doc in li.latest_documents} if hasattr(li, "latest_documents") else {}
        )

        result[li] = LatestDocuments(
            investment_update=docs_by_type.get(DocumentType.INVESTMENT_UPDATE),
            distribution_notice=docs_by_type.get(DocumentType.DISTRIBUTION_NOTICE),
            account_statement=docs_by_type.get(DocumentType.ACCOUNT_STATEMENT),
            capital_call=docs_by_type.get(DocumentType.CAPITAL_CALL),
        )
    return result


def get_processed_documents(user: BridgeUser) -> QuerySet[ProcessedDocument]:
    return (
        ProcessedDocument.objects.for_user(user)
        .filter(is_visible=True)
        .select_related(
            "line_item",
            "investment_update_document",
            "capital_call_document",
            "distribution_notice_document",
        )
        .all()
    )


def build_cards_context(line_items: dict[LineItem, LatestDocuments], user: BridgeUser) -> dict[str, Any]:  # noqa: C901, PLR0912, PLR0915
    # TODO: ORM if this is slow
    number_of_investments = 0
    number_of_account_statements = 0
    total_invested_capital = Decimal(0)
    total_committed_capital = Decimal(0)
    # is this UTC time????, we should localize
    first_date = datetime.datetime(1970, 1, 1, tzinfo=datetime.UTC)
    last_processed_date: datetime.datetime | None = first_date
    values = []
    for line_item, ld in line_items.items():
        has_doc = False
        for doc in ld:
            if doc is not None:
                has_doc = True
                if last_processed_date is None:
                    last_processed_date = doc.created_at
                last_processed_date = max(last_processed_date, doc.created_at)
        if has_doc:
            number_of_investments += 1
        elif line_item.has_processed_documents:
            # If there are no latest documents, but there are processed documents, we still count it as an investment
            # TODO: rationalize if this is correct.
            number_of_investments += 1

        if ld.account_statement is not None and ld.account_statement.investment_update_document:
            number_of_account_statements += 1
            for account_statement_processed_doc in line_item.historical_account_statements:
                if (
                    account_statement_processed_doc != ld.account_statement
                    and account_statement_processed_doc.investment_update_document
                ):
                    values.append(
                        {
                            "value": account_statement_processed_doc.investment_update_document.total_value_usd,
                            "date": account_statement_processed_doc.effective_date,
                            "line_item": str(line_item),
                        }
                    )
                    break
            values.append(
                {
                    "value": ld.account_statement.investment_update_document.total_value_usd,
                    "date": ld.account_statement.effective_date,
                    "line_item": str(line_item),
                }
            )
            if ld.account_statement.investment_update_document.committed_capital_usd is not None:
                total_committed_capital += ld.account_statement.investment_update_document.committed_capital_usd
            if ld.account_statement.investment_update_document.invested_usd is not None:
                total_invested_capital += ld.account_statement.investment_update_document.invested_usd
    if last_processed_date == first_date:
        last_processed_date = None
    current_period_date, current_period_investment_ct, current_period_total_value = None, None, None
    previous_period_date, previous_period_investment_ct, previous_period_total_value = None, None, None
    if len(values) > 0:
        # TODO: we need to only use the latest account statement per period.
        # If mutlitple accounts statements arrive within the same quarter, use the latest for that quarter
        # When a new quarter starts, we show the last quarter date with 0 investments reporting for the most
        # recent quarter. We add a fake line item, and then remove it from the count to keep the logic
        current_time = timezone.now()
        if user.is_demo:
            current_time = datetime.datetime(2025, 1, 1)  # noqa: DTZ001
        most_recent_quarter_end_dt = (
            (pd.to_datetime(current_time).to_period("Q").to_timestamp(how="start") - pd.DateOffset(days=1))
            .to_period("Q")
            .to_timestamp(how="end")
        )
        previous_period_date = (
            (most_recent_quarter_end_dt.to_period("Q").to_timestamp(how="start") - pd.DateOffset(days=1))
            .to_period("Q")
            .to_timestamp(how="end")
            .date()
        )
        current_period_date = most_recent_quarter_end_dt.date()

        values_df = pd.DataFrame(values)
        values_df["date"] = (
            pd.to_datetime(values_df["date"]).dt.to_period("Q").apply(lambda x: x.to_timestamp(how="end").date())
        )
        values_df["delta_recent_quarter"] = np.abs(values_df["date"] - current_period_date)
        values_df["delta_previous_quarter"] = np.abs(values_df["date"] - previous_period_date)

        def get_min_row(field: str) -> Callable:
            def _get_min_row(group: pd.DataFrame) -> pd.DataFrame:
                return group.loc[[group[field].idxmin()]]

            return _get_min_row

        most_recent_data = (
            values_df.groupby("line_item").apply(get_min_row("delta_recent_quarter")).reset_index(drop=True)
        )
        current_period_investment_ct = int(
            most_recent_data[most_recent_data["delta_recent_quarter"] == pd.Timedelta(0)]["line_item"].count()
        )
        current_period_total_value = most_recent_data["value"].sum()

        previous_quarter_data = (
            values_df.groupby("line_item").apply(get_min_row("delta_previous_quarter")).reset_index(drop=True)
        )
        previous_period_investment_ct = int(
            previous_quarter_data[previous_quarter_data["delta_previous_quarter"] == pd.Timedelta(0)][
                "line_item"
            ].count()
        )
        previous_period_total_value = previous_quarter_data["value"].sum()

    context = {
        "new_notifications": (
            ProcessedDocument.objects.for_user(user)
            .filter(is_visible=True, line_item__is_visible=True, has_been_viewed=False)
            .count()
        ),
        "time_of_last_notification": last_processed_date,
        "number_of_investments": number_of_investments,
        "number_of_account_statements": number_of_account_statements,
        "total_committed_capital": total_committed_capital,
        "total_invested_capital": total_invested_capital,
        "current_period_date": current_period_date,
        # We use the cumulative total value instead of just the period with the latest values.
        # TODO: knowing this can we clean up the above pandas code?
        "current_period_total_value": current_period_total_value,
        "current_period_investment_ct": current_period_investment_ct,
        "previous_period_date": previous_period_date,
        "previous_period_total_value": previous_period_total_value,
        "previous_period_investment_ct": previous_period_investment_ct,
    }
    logger.info("dashboard card context", context=context)
    return context


def get_investment_details(line_items: dict[LineItem, LatestDocuments]) -> list[dict[str, Any]]:
    res_l = []
    for line_item, ld in line_items.items():
        res = {
            "client": line_item.investing_entity.client.legal_name,
            "client_id": line_item.investing_entity.client.id,
            "entity": line_item.investing_entity.legal_name,
            "entity_id": line_item.investing_entity.id,
            "investment": line_item.investment.legal_name,
            "investment_id": line_item.investment.id,
            "currency": "USD",
        }
        has_data = False
        if ld.account_statement is not None and ld.account_statement.investment_update_document:
            # NOTE: this is legacy naming, this should be "AccountStatementDocumentFact"
            #       aka account_statement.account_statement_document
            has_data = True
            res["account_statements_display"] = ld.account_statement.name
            res["account_statements_processed_doc"] = ld.account_statement
            res["account_statements_has_been_viewed"] = ld.account_statement.has_been_viewed

            # Gets investment amounts in default currency
            res["currency"] = ld.account_statement.investment_update_document.currency
            res["daily_currency_rate_usd_to_currency"] = (
                ld.account_statement.investment_update_document.daily_currency_rate_usd_to_currency
            )
            res["invested"] = ld.account_statement.investment_update_document.invested
            res["invested_usd"] = ld.account_statement.investment_update_document.invested_usd

            res["total_value"] = ld.account_statement.investment_update_document.total_value
            res["total_value_usd"] = ld.account_statement.investment_update_document.total_value_usd
            res["committed_capital"] = 0
            res["committed_capital_usd"] = 0
            if ld.account_statement.investment_update_document.committed_capital is not None:
                res["committed_capital"] = ld.account_statement.investment_update_document.committed_capital
                res["committed_capital_usd"] = ld.account_statement.investment_update_document.committed_capital_usd

            res["realized_value"] = 0
            res["realized_value_usd"] = 0
            if ld.account_statement.investment_update_document.realized_value is not None:
                res["realized_value"] = ld.account_statement.investment_update_document.realized_value
                res["realized_value_usd"] = ld.account_statement.investment_update_document.realized_value_usd

            res["unrealized_value"] = 0
            res["unrealized_value_usd"] = 0
            if ld.account_statement.investment_update_document.unrealized_value is not None:
                res["unrealized_value"] = ld.account_statement.investment_update_document.unrealized_value
                res["unrealized_value_usd"] = ld.account_statement.investment_update_document.unrealized_value_usd

        if ld.distribution_notice is not None:
            has_data = True
            res["distribution_notices_display"] = ld.distribution_notice.name
            res["distribution_notices_processed_doc"] = ld.distribution_notice
            res["distribution_notices_has_been_viewed"] = ld.distribution_notice.has_been_viewed

        if ld.investment_update is not None:
            has_data = True
            res["investment_updates_documents_display"] = ld.investment_update.name
            res["investment_updates_processed_doc"] = ld.investment_update
            res["investment_updates_has_been_viewed"] = ld.investment_update.has_been_viewed

        if ld.capital_call is not None:
            has_data = True
            res["capital_calls_display"] = ld.capital_call.name
            res["capital_calls_processed_doc"] = ld.capital_call
            res["capital_calls_has_been_viewed"] = ld.capital_call.has_been_viewed

        res["client_id"] = line_item.investing_entity.client.id
        res["entity_id"] = line_item.investing_entity.id
        res["investment_id"] = line_item.investment.id
        if has_data:
            res_l.append(res)
    return res_l


def get_upcoming_capital_calls(
    processed_docs: QuerySet[ProcessedDocument], at_least_n: int = 6, max_n: int = 100
) -> list[dict[str, Any]]:
    """
    Retrieve upcoming capital call documents with intelligent time-based filtering.

    Args:processed_docs (QuerySet[ProcessedDocument]): Queryset of processed documents
        n (int, optional): Number of capital call documents to retrieve. Defaults to 3.

    Returns:list[dict[str, Any]]: List of capital call document details
    """
    # Determine the time threshold for recent documents
    one_week_ago = timezone.now() - datetime.timedelta(weeks=1)

    # Base queryset for capital call documents
    base_qs = processed_docs.filter(document_type=DocumentType.CAPITAL_CALL, has_extracted_numbers=True)

    # First, try to get recent documents from the last week
    recent_docs = list(
        base_qs.filter(effective_date__gte=one_week_ago)
        .order_by("-effective_date", "name")
        .select_related(
            "line_item",
            "capital_call_document",
            "line_item__investing_entity",
            "line_item__investment",
            "line_item__investing_entity__client",
        )
        .filter(
            line_item__is_visible=True,
            capital_call_document__status=CapitalCallStatus.PENDING,
        )[:max_n]
    )

    # If not enough recent docs, supplement with older documents
    if len(recent_docs) < at_least_n:
        additional_needed = at_least_n - len(recent_docs)
        older_docs = list(
            base_qs.filter(effective_date__lt=one_week_ago)
            .order_by("-effective_date", "name")
            .select_related(
                "line_item",
                "capital_call_document",
                "line_item__investing_entity",
                "line_item__investment",
                "line_item__investing_entity__client",
            )
            .filter(
                line_item__is_visible=True,
                capital_call_document__status=CapitalCallStatus.PENDING,
            )[:additional_needed]
        )
        recent_docs.extend(older_docs)

    # Transform documents into required format
    return [
        {
            "due_date": doc.capital_call_document.capital_call_due_date,
            "entity": doc.line_item.investing_entity.legal_name,
            "investment": doc.line_item.investment.legal_name,
            "amount": doc.capital_call_document.amount,
            "status": doc.capital_call_document.get_status_display(),
            "processed_doc": doc,
            "has_been_viewed": doc.has_been_viewed,
        }
        for doc in recent_docs
    ]


class CapitalCallStatusForm(forms.Form):
    status = forms.ChoiceField(choices=CapitalCallStatus.choices)
    capital_call_document_pk = forms.UUIDField()


@login_required
@require_POST
def update_capital_call_status(request: HttpRequest) -> HttpResponse:
    form = CapitalCallStatusForm(request.POST)

    if not form.is_valid():
        return HttpResponse("Invalid data", status=400)

    status = form.cleaned_data["status"]
    capital_call_document_pk = form.cleaned_data["capital_call_document_pk"]

    capital_call_document = (
        CapitalCallDocumentFact.objects.for_user(request.user).filter(pk=capital_call_document_pk).first()
    )
    if capital_call_document is None:
        return HttpResponse("Capital call document not found", status=404)

    capital_call_document.status = status
    capital_call_document.save()

    if status == CapitalCallStatus.EXECUTED:
        return HttpResponse("", headers={"HX-Trigger": "removeRow"})

    return render(
        request,
        "dashboard/upcoming_capital_calls/status_dropdown.html",
        {
            "capital_call_document": capital_call_document,
            "capital_call_status_choices": CapitalCallStatus.choices,
        },
    )


def get_latest_updates(
    processed_docs: QuerySet[ProcessedDocument], at_least_n: int = 6, max_n: int = 100
) -> list[dict[str, Any]]:
    """
    Retrieve latest non-capital call document updates with intelligent time-based filtering.

    Args:processed_docs (QuerySet[ProcessedDocument]): Queryset of processed documents
        n (int, optional): Number of document updates to retrieve. Defaults to 3.

    Returns:list[dict[str, Any]]: List of latest document update details
    """
    # Determine the time threshold for recent documents
    one_week_ago = timezone.now() - datetime.timedelta(weeks=1)

    # Base queryset for non-capital call documents
    base_qs = processed_docs.exclude(document_type=DocumentType.CAPITAL_CALL)

    # First, try to get recent documents from the last week
    recent_docs = list(
        base_qs.filter(posted_date__gte=one_week_ago)
        .order_by("-posted_date", "name")
        .select_related(
            "line_item", "line_item__investing_entity", "line_item__investment", "line_item__investing_entity__client"
        )
        .filter(line_item__is_visible=True)[:max_n]
    )

    # If not enough recent docs, supplement with older documents
    if len(recent_docs) < at_least_n:
        additional_needed = at_least_n - len(recent_docs)
        older_docs = list(
            base_qs.filter(posted_date__lt=one_week_ago)
            .order_by("-posted_date", "name")
            .select_related(
                "line_item",
                "line_item__investing_entity",
                "line_item__investment",
                "line_item__investing_entity__client",
            )
            .filter(line_item__is_visible=True)[:additional_needed]
        )
        recent_docs.extend(older_docs)

    # Transform documents into required format
    return [
        {
            "date_received": doc.posted_date,
            "entity": doc.line_item.investing_entity.legal_name,
            "investment": doc.line_item.investment.legal_name,
            "notice_type": doc.document_type,
            "processed_doc": doc,
            "has_been_viewed": doc.has_been_viewed,
        }
        for doc in recent_docs
    ]


def clean_decimal_input(val: str) -> Decimal | None:
    cleaned = re.sub(r"[^\d.\-]", "", val)
    try:
        return Decimal(cleaned)
    except InvalidOperation:
        return None  # or log + skip


@login_required
def save_bulk_investments(request: HttpRequest) -> JsonResponse:  # noqa: C901, PLR0912
    try:
        logger.info("Saving bulk updates", post=request.POST)
        body: dict[str, dict[str, Decimal]] = {}
        for key, value in request.POST.lists():
            split = key.split("|")
            if len(split) != 2:  # noqa: PLR2004
                continue
            processed_doc_pk, field = split
            if not processed_doc_pk:
                # skip line items on dashboard without an account statement.
                continue
            if processed_doc_pk not in body:
                body[processed_doc_pk] = {}
            cleaned_value = clean_decimal_input(value[0])
            if cleaned_value is None:
                continue
            body[processed_doc_pk][field] = cleaned_value
        processed_docs = (
            ProcessedDocument.objects.for_user(request.user)
            .filter(pk__in=body.keys())
            .select_related("investment_update_document")
        )
        logger.info("Parsed bulk update body", body=body, pds=processed_docs)
        # TODO: what about unfunded and currency? currently not in form.
        columns = [
            "invested",
            "total_value",
            "unfunded",
            "currency",
            "committed_capital",
            "realized_value",
            "unrealized_value",
        ]
        # TODO: should we move to client side deltas?
        deltas: dict[str, dict[str, Decimal]] = {}
        new_data: dict[str, dict[str, Decimal]] = {}
        for processed_doc in processed_docs:
            processed_doc_pk = str(processed_doc.pk)
            edits = body[processed_doc_pk]
            if processed_doc.investment_update_document is None:
                continue
            for column in columns:
                if column in edits and getattr(processed_doc.investment_update_document, column) != edits[column]:
                    if processed_doc_pk not in deltas:
                        deltas[processed_doc_pk] = {}
                        new_data[processed_doc_pk] = {}
                    deltas[processed_doc_pk][column] = edits[column]
            if processed_doc_pk in new_data:
                for column in columns:
                    new_data[processed_doc_pk][column] = deltas[processed_doc_pk].get(
                        column, getattr(processed_doc.investment_update_document, column)
                    )
                ProcessedDocument.create(
                    raw_document=processed_doc.raw_retreival_document,
                    line_items=[processed_doc.line_item],
                    document_type=processed_doc.document_type,
                    sub_document_type=processed_doc.sub_document_type,
                    posted_date=processed_doc.posted_date,
                    effective_date=processed_doc.effective_date,
                    process_document_version=1,
                    process_document_source="user_ground_truth",
                    labeled_by=request.user,
                    has_been_viewed=False,  # or should we use processed_doc.has_been_viewed?
                    is_visible=True,
                    is_ground_truth=True,
                    investment_update=new_data[processed_doc_pk],
                )
            logger.info("edits", edits=edits, pd=processed_doc, new_data=new_data, deltas=deltas)

        headers = add_hx_trigger_header({"reloadClientDashboard": {}})
        return JsonResponse({"success": True}, headers=headers)

    except Exception as e:
        logger.exception("Failed to save bulk updates")
        return JsonResponse({"error": str(e)}, status=400)
