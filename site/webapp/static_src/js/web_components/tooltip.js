customElements.define(
  "bridge-tooltip",
  class extends HTMLElement {
    connectedCallback() {
      const template = document.getElementById("tooltip-template");
      const new_node = template.content.cloneNode(true);

      // Set the tooltip text
      const tooltipText = this.dataset.tooltipText || "";
      new_node.querySelector(".tooltip-text").textContent = tooltipText;

      // Set the content (the element that triggers the tooltip)
      const content = this.dataset.content || "";
      new_node.querySelector(".tooltip-content").innerHTML = content;

      this.appendChild(new_node);
    }
  },
);
