{% load customtags static %}
<div id="modal-container">
    <div class="relative w-full max-w-lg max-h-full">
        <!-- Modal content -->
        <div class="relative w-[600px]"></div>
        <div class="font-inter relative rounded-lg p-4">
            <!-- Modal header -->
            <div class="flex items-center justify-between rounded-t dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {% if modal_header_text %}{{ modal_header_text }}{% endif %}
                    {% if modal_header_icon %}
                        <div class="FeaturedIcon w-12 h-12 px-[13px] pt-[15px] pb-[13px] bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
                            <div class="Icon w-[22px] h-5 relative flex-col justify-start items-start flex">
                                <img alt="ImageIcon"
                                     width="22"
                                     height="21"
                                     src="{% static 'assets/images/email_icon.svg' %}">
                            </div>
                        </div>
                    {% endif %}
                </h3>
                <button _="on click trigger closeModal"
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                    <img src="{% static 'assets/images/close.svg' %}"
                         class="w-3 h-3"
                         width="12"
                         height="12"
                         alt="closeButton">
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <div class="mt-4">
                {% if modal_sub_header_text %}
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ modal_sub_header_text }}</h3>
                {% endif %}
            </div>
            <div class="mb-4">
                {% if modal_description_text_highlighted %}
                    <span class="text-[#924f34] text-md">{{ modal_description_text_highlighted }}</span>
                {% endif %}
                {% if modal_description_text %}<span class="text-sm">{{ modal_description_text }}</span>{% endif %}
            </div>
            {% with template_name=form_include_template %}
                {% include ""|add:template_name %}
            {% endwith %}
        </div>
    </div>
</div>
