import secrets
from uuid import uuid4

import structlog
from django import forms
from django.contrib import admin, messages
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.forms import ReadOnlyPasswordHashField
from django.http import HttpRequest
from retrieval.tasks.send_mail import send_invitation_email

from webapp.models.user import BridgeUser

logger = structlog.get_logger(__name__)


class UserCreationForm(forms.ModelForm):
    """
    A form for creating new users. Only includes required fields.
    """

    class Meta:
        model = BridgeUser
        fields = ("email", "organization", "roles")  # Required fields only

    def save(self, commit: bool = True) -> BridgeUser:  # noqa: FBT001, FBT002
        user = super().save(commit=False)
        user.username = self.cleaned_data["email"]  # Set username to email
        user.set_unusable_password()  # Mark password as unusable until reset
        if commit:
            user.save()
        return user


class UserChangeForm(forms.ModelForm):
    """
    A form for updating users. Includes all the fields on
    the user, but replaces the password field with admin's
    password hash display field.
    """

    password = ReadOnlyPasswordHashField()

    class Meta:
        model = BridgeUser
        fields = (
            "username",
            "email",
            "password",
            "organization",
            "roles",
            "first_name",
            "last_name",
            "is_active",
            "is_admin",
            "is_staff",
        )


def invite_user_email(modeladmin, request, queryset):  # noqa: ANN001,ARG001, ANN201
    for user in queryset:
        if user.invitation_token is None:
            messages.error(request, f"User {user.email} does not have an invitation token.")
            continue
        logger.info("Triggering invite email", user=user)
        send_invitation_email.apply_async(
            kwargs={
                "user_id": user.pk,
                "current_domain": request.get_host(),
            }
        )
        messages.success(request, f"Invitation email sent to {user.email}")


invite_user_email.short_description = "Send Invite Email"  # type: ignore[attr-defined]


@admin.register(BridgeUser)
class UserAdmin(BaseUserAdmin):
    # Use the custom forms
    form = UserChangeForm
    add_form = UserCreationForm
    actions = (invite_user_email,)

    # Fields to display in the admin list
    list_display = ("email", "organization", "is_active", "is_admin")
    list_filter = ("is_admin", "is_active")

    # Fields to display in the user detail page
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "username",
                    "email",
                    "organization",
                    "first_name",
                    "last_name",
                    "contact_number",
                    "roles",
                    "invitation_token",
                    "is_active",
                    "is_admin",
                    "tos_accepted_at",
                    "tos_last_updated_at",
                    "is_staff",
                    "is_superuser",
                    "user_view_preferences",
                )
            },
        ),
    )

    # Fields to display when adding a new user
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": ("email", "organization", "roles"),
            },
        ),
    )

    search_fields = ("email",)
    ordering = ("organization__name", "email")
    filter_horizontal = ()

    def save_model(self, request: HttpRequest, obj: BridgeUser, form: forms.ModelForm, change: bool) -> None:  # noqa: FBT001
        try:
            # Check if the user is new
            is_new_user = not BridgeUser.objects.filter(pk=obj.pk).exists()
            logger.info(f"Testing: Is new user? {is_new_user}")  # noqa: G004

            # Set username to email
            obj.username = obj.email

            if is_new_user:
                # Initialize fields for new users
                obj.first_name = ""
                obj.last_name = ""
                obj.contact_number = ""

                # Autogenerate a password
                password = secrets.token_urlsafe(12)
                obj.set_password(password)

                # Generate a unique invitation token
                obj.invitation_token = uuid4()

                # Save the user before sending the email
                super().save_model(request, obj, form, change)

            else:
                # Save changes for existing users
                super().save_model(request, obj, form, change)

        except Exception as e:
            logger.exception(f"Error in save_model for {obj.email}: {e}")  # noqa: G004, TRY401
            raise
