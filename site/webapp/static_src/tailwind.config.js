/** @type {import('tailwindcss').Config} */
/**
 * This is a minimal config.
 *
 * If you need the full config, get it from here:
 * https://unpkg.com/browse/tailwindcss@latest/stubs/defaultConfig.stub.js
 */

module.exports = {
    content: [
        /**
         * HTML. Paths to Django template files that will contain Tailwind CSS classes.
         */

        /*  Templates within theme app (<tailwind_app_name>/templates), e.g. base.html. */
        '../templates/**/*.html',

        /*
         * Main templates directory of the project (BASE_DIR/templates).
         * Adjust the following line to match your project structure.
         */
        '../../templates/**/*.html',

        /*
         * Templates in other django apps (BASE_DIR/<any_app_name>/templates).
         * Adjust the following line to match your project structure.
         */
        '../../**/templates/**/*.html',

        /**
         * Flowbite
         */
        './node_modules/flowbite/**/*.js',

        /**
         * JS: If you use Tailwind CSS in JavaScript, uncomment the following lines and make sure
         * patterns match your project structure.
         */
        /* JS 1: Ignore any JavaScript in node_modules folder. */
        '!../../**/node_modules',
        /* JS 2: Process all JavaScript files in the project. */
        '../../**/*.js',

        /**
         * Python: If you use Tailwind CSS classes in Python, uncomment the following line
         * and make sure the pattern below matches your project structure.
         */
        '../../**/*.py',
        './js/**/*.js',
        './js/**/*.ts',
        './js/**/*.tsx',
    ],
    safelist: [
      'rotate-0',
      'rotate-90',
  ],
    theme: {
        extend: {
          colors: {
            base: {
              white: '#FFFFFF',
              black: '#000000',
              'darker-olive': '#343D36',
              'dark-gray': '#424242',
              beige: '#FFFCF6',
              'darker-olive-muted': '#616863',
            },
            primary: {
              'burnt-orange': '#924F34',
              'dark-olive': '#4B554D',
              olive: '#D0D2BF',
            },
            secondary: {
              'orange-beige': '#F4EBE3',
              'light-olive': '#ECEEE8',
              'blue-stone': '#C1DDEE',
              'faded-green': '#D7E1D9',
              green: '#689757',
            },
          },
          fontFamily: {
            inter: ['Inter', 'sans-serif'],
            nanum: ['NanumMyeongjo', 'serif'],
          },
          fontSize: {
            'display-2xl': ['2rem', { lineHeight: '2.5rem', letterSpacing: '-0.02em' }],
            'display-xl': ['1.875rem', { lineHeight: '2.25rem', letterSpacing: '-0.02em' }],
            'display-lg': ['1.75rem', { lineHeight: '2.125rem', letterSpacing: '-0.02em' }],
            'display-md': ['1.5rem', { lineHeight: '2rem', letterSpacing: '-0.02em' }],
            'display-sm': ['1.25rem', { lineHeight: '1.75rem', letterSpacing: '-0.02em' }],
            'body-lg': ['1rem', { lineHeight: '1.5rem' }],
            'body-md': ['0.875rem', { lineHeight: '1.25rem' }],
            'body-sm': ['0.75rem', { lineHeight: '1rem' }],
          },
          borderRadius: {
            'xs': '0.25rem',  // 4px
            'sm': '0.375rem', // 6px
            'md': '0.5rem',   // 8px
            'lg': '0.75rem',  // 12px
            'full': '9999px', // Full radius for pill shapes
          },
          fontWeight: {
            extrabold: '800',
            bold: '700',
            semibold: '600',
            medium: '500',
            regular: '400',
          },
          spacing: {
            '1': '4px',
            '2': '8px',
            '3': '12px',
            '4': '16px',
            '5': '20px',
            '6': '24px',
            '7': '28px',
            '8': '32px',
            '9': '36px',
            '10': '40px',
          },
        },
      },
    plugins: [
      require('flowbite/plugin')({}),
    ],
}
