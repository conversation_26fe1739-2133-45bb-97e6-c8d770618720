from datetime import UTC, datetime, timedelta

import pytest
from freezegun import freeze_time

from webapp.models.portal import Portal, SMSMessage
from webapp.models.user import BridgeUser

pytestmark = pytest.mark.django_db


class TestPortalModel:
    """Tests for the Portal model."""

    @pytest.fixture
    def portals(self, admin_user: BridgeUser) -> list[Portal]:
        """Create some test portals."""
        return [
            Portal.objects.create(
                name="Vanguard",
                portal_login_url="https://investor.vanguard.com/login",
                organization=admin_user.organization,
                user=admin_user,
            ),
            Portal.objects.create(
                name="Fidelity Investments",
                portal_login_url="https://login.fidelity.com",
                organization=admin_user.organization,
                user=admin_user,
            ),
            Portal.objects.create(
                name="Chase Bank",
                portal_login_url="https://www.chase.com/login",
                organization=admin_user.organization,
                user=admin_user,
            ),
            Portal.objects.create(
                name="Blue Cross-Blue Shield",
                portal_login_url="https://www.bcbs.com/login",
                organization=admin_user.organization,
                user=admin_user,
            ),
            Portal.objects.create(
                name="401k-Provider",
                portal_login_url="https://www.401k-provider.com/login",
                organization=admin_user.organization,
                user=admin_user,
            ),
        ]

    def test_get_portal_by_exact_name(self, admin_user: BridgeUser, portals: list[Portal]) -> None:
        """Test finding portal by exact name."""
        # Test exact match
        portal = Portal.get_portal_by_name("Vanguard", admin_user.organization)
        assert portal is not None
        assert portal.name == "Vanguard"

        # Test case-insensitive match
        portal = Portal.get_portal_by_name("vanguard", admin_user.organization)
        assert portal is not None
        assert portal.name == "Vanguard"

        portal = Portal.get_portal_by_name("VANGUARD", admin_user.organization)
        assert portal is not None
        assert portal.name == "Vanguard"

    def test_get_portal_by_partial_name(self, admin_user: BridgeUser, portals: list[Portal]) -> None:
        """Test finding portal by partial name."""
        # Test partial match
        portal = Portal.get_portal_by_name("Fidelity", admin_user.organization)
        assert portal is not None
        assert portal.name == "Fidelity Investments"

        # Search with fragment that's in the middle
        portal = Portal.get_portal_by_name("Investments", admin_user.organization)
        assert portal is not None
        assert portal.name == "Fidelity Investments"

    def test_get_portal_by_simplified_name(self, admin_user: BridgeUser, portals: list[Portal]) -> None:
        """Test finding portal with simplified name matching (removing special characters)."""
        # Test with spaces and special characters
        portal = Portal.get_portal_by_name("Blue Cross Blue Shield", admin_user.organization)
        assert portal is not None
        assert portal.name == "Blue Cross-Blue Shield"

        # Test with different formatting
        portal = Portal.get_portal_by_name("BlueCross-BlueShield", admin_user.organization)
        assert portal is not None
        assert portal.name == "Blue Cross-Blue Shield"

        # Test with numbers
        portal = Portal.get_portal_by_name("401k Provider", admin_user.organization)
        assert portal is not None
        assert portal.name == "401k-Provider"

        # Test with completely different but simplified matching name
        portal = Portal.get_portal_by_name("401-k Provider", admin_user.organization)
        assert portal is not None
        assert portal.name == "401k-Provider"

    def test_get_portal_no_match(self, admin_user: BridgeUser, portals: list[Portal]) -> None:
        """Test when no portal matches the provided name."""
        portal = Portal.get_portal_by_name("Nonexistent Portal", admin_user.organization)
        assert portal is None

    def test_get_portal_with_empty_name(self, admin_user: BridgeUser, portals: list[Portal]) -> None:
        """Test behavior when searching with an empty name."""
        portal = Portal.get_portal_by_name("", admin_user.organization)
        assert portal is None


@pytest.mark.parametrize(
    ("message_body", "expected_code"),
    [
        ("Your verification code is 123456", "123456"),
        ("Use code 1234 to verify your account", "1234"),
        ("Your one-time password: 987654", "987654"),
        ("Enter PIN: 4321 to continue", "4321"),
        ("Random text without code", None),
        ("Code: ABC123", None),  # Non-numeric code
        ("Multiple codes 123456 and 789012", "123456"),  # Should extract first match
        ("Our secret security Code: 098765 for verify user. Msg&data rates may apply. Reply STOP to cancel.", "098765"),
        ("Your portal verification code is 432-567. We will never contact you for this code.", "432567"),
        ("Your verification code is ********. Message and data rates may apply.", "********"),
        ("Your verification code is 1234-5678. Do not share this code with anyone.", "********"),
    ],
)
def test_extract_code_from_message(admin_user: BridgeUser, message_body: str, expected_code: str | None) -> None:
    """Test extracting OTP codes from various message formats."""
    message = SMSMessage.objects.create(user=admin_user, user_phone_number="+**********", body=message_body)

    assert message.extract_code_from_message() == expected_code


def test_sms_message_is_within_time_window(admin_user: BridgeUser) -> None:
    """Test the is_within_time_window method of SMSMessage."""
    # Create a message at the current time
    current_time = datetime.now(UTC)
    with freeze_time(current_time):
        message = SMSMessage.objects.create(
            user=admin_user, user_phone_number="+15551234567", body="Your verification code is 123456"
        )

        # Message created now should be within time window
        assert message.is_within_time_window()

        # Message should be within custom time window
        assert message.is_within_time_window(seconds=300)

    # Test message outside default time window (10 minutes)
    with freeze_time(current_time + timedelta(minutes=11)):
        assert not message.is_within_time_window()

        # But still within a larger custom window
        assert message.is_within_time_window(seconds=900)  # 15 minutes

    # Test message at exactly the time window boundary
    with freeze_time(current_time + timedelta(seconds=600)):
        assert not message.is_within_time_window()

    # Test message just inside the time window
    with freeze_time(current_time + timedelta(seconds=599)):
        assert message.is_within_time_window()
