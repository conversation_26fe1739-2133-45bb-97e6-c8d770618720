{% load customtags %}
{% load static %}
{% with mpc=merged_portal_credential_with_line_items.merged_portal_credential %}
    <tbody hx-post="{% url 'merged_portal_credential_manage' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
           {% if mpc.is_pending_validation %} hx-trigger="every 5s" {% else %} hx-trigger="changeMergedPortalCredentialRow[detail.merged_portal_credential_pk=='{{ mpc.pk }}'] from:document" {% endif %}
           hx-swap="outerHTML"
           hx-include="[name='mpc_{{ mpc.pk }}_hidden'],[name^='investment_group_'][name$='_hidden']">
        <tr class="border-t-2 border-secondary-orange-beige bg-white">
            <td class="w-2/5 min-w-[200px] pl-2 align-middle border-r-2 border-secondary-orange-beige">
                <div class="flex flex-row justify-between items-stretch h-full min-h-[60px]">
                    <!-- LEFT: Wrapping content -->
                    <div class="flex flex-col lg:flex-row lg:flex-wrap items-start lg:items-center gap-2 w-full justify-between">
                        <div class="font-inter text-sm lg:text-[16px] font-bold">{{ mpc.portal.name }}</div>
                        {% if mpc.portal.portal_type == PortalType.EMAIL_BASED %}
                            <div class="lg:pr-4">
                                <button hx-get="{% url 'emails_add_line_item' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
                                        hx-target="body"
                                        hx-swap="beforeend"
                                        hx-include="none"
                                        class="inline-flex items-center rounded-md px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium border-2 border-[#924f34]">
                                    <img src="{% static 'assets/images/plus.svg' %}"
                                         class="w-3 h-3 mr-1 text-[#924f34]"
                                         width="12"
                                         height="12"
                                         alt="plus">
                                    <span class="text-[#924f34] hidden sm:inline">Add new line item</span>
                                    <span class="text-[#924f34] sm:hidden">Add item</span>
                                </button>
                            </div>
                        {% endif %}
                    </div>
                    <!-- RIGHT: Count + Dropdown -->
                    <div class="flex items-center justify-center self-center pr-2 cursor-pointer"
                         _="on click set hidden_input to first <input[name='mpc_{{ mpc.pk }}_hidden']/> in me trigger toggleRow(mpc_pk:'{{ mpc.pk }}', investment_pk:'__all__', is_closed:hidden_input.value is 'true')">
                    <img src="{% static 'assets/images/up_arrow.svg' %}"
                         class="w-3 h-3 ml-1 mr-2 transition-transform transform {% if mpc_row_hidden_state == 'true' or mpc_row_hidden_state is None %}rotate-180{% endif %}"
                         _="on toggleRow[mpc_pk=='{{ mpc.pk }}' and investment_pk=='__all__'] from elsewhere toggle .rotate-180 end"
                         width="12"
                         height="12"
                         alt="expand arrow">
                    <div class="inline-flex items-center rounded-md bg-white/50 px-1 py-0 text-[12px] font-medium text-gray-600 border border-gray-300">
                        {{ merged_portal_credential_with_line_items.total_line_items|default:0 }}
                    </div>
                    <!-- Hidden input to track investment rows visibility state -->
                    <input type="hidden"
                           name="mpc_{{ mpc.pk }}_hidden"
                           value="{{ mpc_row_hidden_state|default:'true' }}"
                           id="mpc_{{ mpc.pk }}_hidden"
                           _="on toggleRow[mpc_pk=='{{ mpc.pk }}' and investment_pk=='__all__'] from elsewhere if my.value is 'true' set my.value to 'false' else set my.value to 'true' end">
                </div>
            </div>
        </td>
        <td class="w-1/6 min-w-[120px] pl-2 text-left">
            {% if mpc.portal.portal_type == PortalType.WEB_BASED %}
                {% if mpc.portal_credential and mpc.portal_credential_user_login_status == UserLoginStatusCallToAction.NOT_STARTED %}
                    <button hx-get="{% url 'credentials_modal' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
                            hx-target="body"
                            hx-swap="beforeend"
                            hx-include="none"
                            class="inline-flex items-center rounded-md bg-red-100 px-1 lg:px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium ring-inset ring-red-800">
                        <div class="bg-red-400 rounded-full mr-1 w-[0.4rem] h-[0.4rem]"></div>
                        <span class="hidden sm:inline">Not Started</span>
                        <span class="sm:hidden">Not Started</span>
                    </button>
                {% elif mpc.portal_credential and mpc.portal_credential_user_login_status == UserLoginStatusCallToAction.PENDING %}
                    <button hx-get="{% url 'credentials_modal' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
                            hx-target="body"
                            hx-swap="beforeend"
                            hx-include="none"
                            class="inline-flex items-center rounded-md bg-red-100 px-1 lg:px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium ring-inset ring-red-800">
                        <div class="bg-yellow-400 rounded-full mr-1 w-[0.4rem] h-[0.4rem]"></div>
                        <span class="hidden sm:inline">Pending Validation</span>
                        <span class="sm:hidden">Pending</span>
                    </button>
                {% elif mpc.portal_credential and mpc.portal_credential_user_login_status == UserLoginStatusCallToAction.ACTION_REQUIRED %}
                    <button hx-get="{% url 'link_errors_modal' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
                            hx-target="body"
                            hx-swap="beforeend"
                            hx-include="none"
                            class="inline-flex items-center rounded-md bg-red-100 px-1 lg:px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium ring-inset ring-red-800">
                        <div class="bg-red-400 rounded-full mr-1 w-[0.4rem] h-[0.4rem]"></div>
                        <span class="hidden sm:inline">Action Required</span>
                        <span class="sm:hidden">Action Req.</span>
                    </button>
                {% elif mpc.portal_credential and mpc.portal_credential_user_login_status == UserLoginStatusCallToAction.SUCCESS %}
                    <button hx-get="{% url 'credentials_modal' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
                            hx-target="body"
                            hx-swap="beforeend"
                            hx-include="none"
                            class="inline-flex items-center font-inter text-[10px] lg:text-[12px] rounded-md bg-white px-1 lg:px-2 py-1 text-xs font-medium ring-1 ring-inset ring-black">
                        <div class="bg-green-500 rounded-full mr-1 w-[0.4rem] h-[0.4rem]"></div>
                        Completed
                    </button>
                {% endif %}
            {% endif %}
        </td>
        <td class="w-1/6 min-w-[140px] pl-2 text-left">
            {% if mpc.portal.portal_type == PortalType.WEB_BASED %}
                {% if mpc.multi_factor_authentication and mpc.multi_factor_authentication_user_login_status == UserLoginStatusCallToAction.NOT_STARTED %}
                    <button hx-get="{% url 'multi_factor_authentication_modal' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
                            hx-target="body"
                            hx-swap="beforeend"
                            hx-include="none"
                            class="inline-flex items-center rounded-md bg-red-100 px-1 lg:px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium ring-inset ring-red-800">
                        <div class="bg-red-400 rounded-full mr-1  w-[0.4rem] h-[0.4rem]"></div>
                        <span class="hidden sm:inline">Not Started</span>
                        <span class="sm:hidden">Not Started</span>
                    </button>
                {% elif mpc.multi_factor_authentication and mpc.multi_factor_authentication_user_login_status == UserLoginStatusCallToAction.PENDING %}
                    <button hx-get="{% url 'multi_factor_authentication_modal' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
                            hx-target="body"
                            hx-swap="beforeend"
                            hx-include="none"
                            class="inline-flex items-center rounded-md bg-red-100 px-1 lg:px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium ring-inset ring-red-800">
                        <div class="bg-yellow-400 rounded-full mr-1  w-[0.4rem] h-[0.4rem]"></div>
                        <span class="hidden sm:inline">Pending Validation</span>
                        <span class="sm:hidden">Pending</span>
                    </button>
                {% elif mpc.multi_factor_authentication and mpc.multi_factor_authentication_user_login_status == UserLoginStatusCallToAction.ACTION_REQUIRED %}
                    <button hx-get="{% url 'link_errors_modal' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
                            hx-target="body"
                            hx-swap="beforeend"
                            hx-include="none"
                            class="inline-flex items-center rounded-md bg-red-100 px-1 lg:px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium ring-inset ring-red-800">
                        <div class="bg-red-400 rounded-full mr-1 w-[0.4rem] h-[0.4rem]"></div>
                        <span class="hidden sm:inline">Action Required</span>
                        <span class="sm:hidden">Action Req.</span>
                    </button>
                {% elif mpc.multi_factor_authentication and mpc.multi_factor_authentication_user_login_status == UserLoginStatusCallToAction.SUCCESS %}
                    <button hx-get="{% url 'multi_factor_authentication_modal' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
                            hx-target="body"
                            hx-swap="beforeend"
                            hx-include="none"
                            class="inline-flex items-center rounded-md bg-white px-1 lg:px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium ring-1 ring-inset ring-black ">
                        <div class="bg-green-500 rounded-full mr-1 w-[0.4rem] h-[0.4rem]"></div>
                        Completed
                    </button>
                {% endif %}
            {% endif %}
            {% comment %} This invisible div is a placeholder for the end screens after an oauth callback {% endcomment %}
            <div style="display: none"
                 hx-trigger="popupEmailModalSuccess[detail.merged_portal_credential_pk==='{{ mpc.pk }}'] from:document"
                 hx-target="body"
                 hx-swap="beforeend"
                 hx-include="none"
                 hx-get="{% url 'email_integration_end' %}{% urlparams merged_portal_credential_pk=mpc.pk %}" />
            <div style="display: none"
                 hx-trigger="popupEmailModalError[detail.merged_portal_credential_pk==='{{ mpc.pk }}'] from:document"
                 hx-target="body"
                 hx-swap="beforeend"
                 hx-include="none"
                 hx-get="{% url 'email_integration_failed' %}{% urlparams merged_portal_credential_pk=mpc.pk %}" />
        </td>
        <td class="w-1/6 min-w-[120px] pl-2">
            {% if mpc.connection_health_status and mpc.connection_health_status.label == "Linked" %}
                <button class="inline-flex items-center rounded-md bg-white px-1 lg:px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium ring-1 ring-inset ring-black ">
                    <div class="bg-green-500 rounded-full mr-1 w-[0.4rem] h-[0.4rem]"></div>
                    {{ mpc.connection_health_status.label }}
                </button>
            {% elif mpc.connection_health_status and mpc.connection_health_status.label == "Pending" %}
                <button hx-get="{% url 'emails_edit_setup' %}{% urlparams merged_portal_credential_pk=mpc.pk %}"
                        hx-target="body"
                        hx-swap="beforeend"
                        hx-include="none"
                        class="inline-flex items-center rounded-md bg-red-100 px-1 lg:px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium ring-inset ring-red-800">
                    <div class="bg-yellow-400 rounded-full mr-1 w-[0.4rem] h-[0.4rem]"></div>
                    {{ mpc.connection_health_status.label }}
                </button>
            {% else %}
                <button class="inline-flex items-center rounded-md bg-red-100 px-1 lg:px-2 py-1 text-xs font-inter text-[10px] lg:text-[12px] font-medium ring-inset ring-red-800">
                    <div class="bg-red-400 rounded-full mr-1 w-[0.4rem] h-[0.4rem]"></div>
                    {{ mpc.connection_health_status.label }}
                </button>
            {% endif %}
        </td>
        <td class="w-1/4 min-w-[160px] pl-2 h-full border-l-2 border-secondary-orange-beige">
            {% if mpc.portal.portal_type == PortalType.WEB_BASED %}
                {% if user.is_demo %}
                    {% include "link/portal/run_cell_demo.html" with merged_portal_credential=mpc OnboardingStatus=OnboardingStatus RetrievalStatus=RetrievalStatus MFAType=MFAType %}
                {% else %}
                    {% include "link/portal/run_cell.html" with merged_portal_credential=mpc OnboardingStatus=OnboardingStatus RetrievalStatus=RetrievalStatus MFAType=MFAType %}
                {% endif %}
            {% endif %}
        </td>
    </tr>
    {% if merged_portal_credential_with_line_items.line_items_by_investment %}
        {% for investment_group in merged_portal_credential_with_line_items.line_items_by_investment %}
            <!-- Investment Parent Row -->
            <tr class="border-t-2 border-secondary-orange-beige bg-[#eceee8] {% if mpc_row_hidden_state == 'true' or mpc_row_hidden_state is None %}hidden{% endif %}"
                _="on toggleRow[mpc_pk=='{{ mpc.pk }}' and investment_pk=='__all__'] from elsewhere toggle .hidden end">
                <td class="w-2/5 min-w-[200px] pl-4 lg:pl-8 font-inter font-normal border-r-2 border-secondary-orange-beige">
                    <div class="flex flex-row justify-between items-stretch min-h-[50px]">
                        <div class="flex flex-row flex-wrap items-center gap-2 w-full justify-between font-inter text-sm lg:text-[16px]">
                            {{ investment_group.investment }}
                        </div>
                        <div class="flex items-center justify-center self-center pr-2 cursor-pointer"
                             _=" on click set hidden_input to first <input[name='investment_group_{{ investment_group.investment.pk }}_hidden']/> in me trigger toggleRow(mpc_pk:'{{ mpc.pk }}', investment_pk:'{{ investment_group.investment.pk }}', is_closed:hidden_input.value is 'true')">
                        <img src="{% static 'assets/images/up_arrow.svg' %}"
                             class="w-3 h-3 ml-1 mr-2 transition-transform transform {% if investment_group.investment_group_hidden_state == 'true' %}rotate-180{% endif %}"
                             _="on toggleRow[mpc_pk=='{{ mpc.pk }}' and investment_pk=='{{ investment_group.investment.pk }}'] from elsewhere toggle .rotate-180 end"
                             width="12"
                             height="12"
                             alt="expand arrow">
                        <div class="inline-flex items-center rounded-md bg-white/50 px-1 py-0 text-[12px] font-medium text-gray-600 border border-gray-300">
                            {{ investment_group.line_items|length|default:0 }}
                        </div>
                        <!-- Hidden input to track line item rows visibility state -->
                        <input type="hidden"
                               name="investment_group_{{ investment_group.investment.pk }}_hidden"
                               value="{{ investment_group.investment_group_hidden_state|default:'false' }}"
                               id="investment_group_{{ investment_group.investment.pk }}_hidden"
                               _="on toggleRow[mpc_pk=='{{ mpc.pk }}' and investment_pk=='{{ investment_group.investment.pk }}'] from elsewhere if my.value is 'true' set my.value to 'false' else set my.value to 'true' end">
                    </div>
                </div>
            </td>
            <td colspan="4"></td>
        </tr>
        {% for line_item in investment_group.line_items %}
            <!-- Line Item Rows -->
            <tr class="border-t-2 border-secondary-orange-beige {% if mpc_row_hidden_state == 'true' or mpc_row_hidden_state is None or investment_group.investment_group_hidden_state == 'true' %}hidden{% endif %}"
                {# djlint:off#}
                _="
                on toggleRow[mpc_pk=='{{ mpc.pk }}' and investment_pk=='{{ investment_group.investment.pk }}'] from elsewhere toggle .hidden end 
                on toggleRow[mpc_pk=='{{ mpc.pk }}' and investment_pk=='__all__' and not is_closed] from elsewhere add .hidden end 
                on toggleRow[mpc_pk=='{{ mpc.pk }}' and investment_pk=='__all__' and is_closed] from elsewhere
                    set investment_input to first <input[name='investment_group_{{ investment_group.investment.pk }}_hidden']/>  if investment_input.value is 'false' remove .hidden from me end
                end"
                {# djlint:on#}
                >
                <td colspan="4" class="w-2/5 p-2 lg:p-4 font-inter text-xs lg:text-[14px] font-normal">{{ line_item }}</td>
                <td class="w-1/4 min-w-[160px] pl-2 border-l-2 border-secondary-orange-beige">
                    <div class="w-full flex flex-col lg:flex-row items-start lg:items-center gap-2 lg:gap-4 min-h-[60px] h-full py-2">
                        <button hx-get="{% url 'emails_edit_line_item' %}{% urlparams merged_portal_credential_pk=mpc.pk line_item_pk=line_item.pk %}"
                                hx-target="body"
                                hx-swap="beforeend"
                                hx-include="none"
                                class="inline-flex items-center rounded-md px-2 py-1 text-xs font-inter font-medium border-2 border-[#924f34]">
                            <img src="{% static 'assets/images/pen_orange.svg' %}"
                                 class="w-3 h-3 mr-1"
                                 width="12"
                                 height="12"
                                 alt="edit">
                            <span class="text-[#924f34]">Edit</span>
                        </button>
                        {% if mpc.portal.portal_type == PortalType.EMAIL_BASED %}
                            <div class="flex flex-col flex-wrap gap-0.5 items-start min-w-[140px] lg:min-w-[180px]">
                                <div class="text-left">
                                    <span class="text-[#343d36] text-[10px] lg:text-xs font-bold">
                                        {% if mpc.user_forwarding_rule.receiving_email.email_integration_status == "bf" and line_item.email_retrieval.sender_emails %}
                                            Bridge automated
                                        {% else %}
                                            Manually forwarded
                                        {% endif %}
                                    </span>
                                </div>
                                <div class="text-left">
                                    <span class="text-[#343d36] text-[10px] lg:text-xs font-bold">Last Upload:</span>
                                    <span class="text-[#343d36] text-[10px] lg:text-xs font-medium">
                                        {% if line_item.last_processed_doc %}
                                            {{ line_item.last_processed_doc.posted_date }}
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </span>
                                </div>
                                <div class="text-left">
                                    <span class="text-[#343d36] text-[10px] lg:text-xs font-bold">Number of Documents:</span>
                                    <span class="text-[#343d36] text-[10px] lg:text-xs font-medium">{{ line_item.total_processed_docs_count }}</span>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </td>
            </tr>
        {% endfor %}
    {% endfor %}
{% else %}
    <tr _="on toggleRow[mpc_pk=='{{ mpc.pk }}' and investment_pk=='__all__'] from elsewhere toggle .hidden end"
        class="border-t-2 border-secondary-orange-beige {% if mpc_row_hidden_state == 'true' or mpc_row_hidden_state is None %}hidden{% endif %}">
        <td colspan="5" class="font-inter text-[14px] font-normal text-left p-4">No line items available.</td>
    </tr>
{% endif %}
</tbody>
{% endwith %}
