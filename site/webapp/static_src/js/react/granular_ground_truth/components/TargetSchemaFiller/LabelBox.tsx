import React, { useState } from "react";
import type { BoxData } from "types/pdfLabeler";
import { JSONSchema } from "types/jsonSchema";
import LabelSelectorForm from "./LabelSelectorForm";
import { RJSFSchema } from "@rjsf/utils/lib/types";

interface LabelBoxProps {
  boxData: BoxData;
  schema: JSONSchema;
  currentPageNumber: number;
  onLabelBox: (box: BoxData) => void;
  path: string;
}

const LabelBox: React.FC<LabelBoxProps> = ({
  boxData,
  schema,
  currentPageNumber,
  onLabelBox,
  path,
}) => {
  const [data, setData] = useState<any | null>(boxData.data);
  // const [schema, setSchema] = useState<RJSFSchema|null>(null);
  const handleLabelClick = () => {
    boxData.schema = schema;
    boxData.data = data;
    boxData.path = path;
    boxData.isValid = true;
    boxData.isSaved = false;
    onLabelBox(boxData);
  };
  return (
    <div
      key={boxData.id}
      data-box-id={boxData.id}
      data-page-number={boxData.pageNumber}
      className={`bg-gray-700 border border-gray-600 rounded-lg p-3 mb-2 ${
        boxData.pageNumber !== currentPageNumber ? "opacity-50" : ""
      }`}
    >
      <div className="flex justify-between items-start mb-2">
        <h4 className="text-sm font-medium text-white">
          PENDING page: {boxData.pageNumber}
        </h4>
      </div>
      <div className="text-xs text-gray-300 mb-2">
        <div className="mb-1">
          <strong>Text:</strong>{" "}
          <span>{boxData.content || "(No text found)"}</span>
        </div>
        <div className="mb-1">
          <strong>Coords:</strong>{" "}
          <span>
            x: {boxData.boundingBox.x}, y: {boxData.boundingBox.y}, w:{" "}
            {boxData.boundingBox.width}, h: {boxData.boundingBox.height}
          </span>
        </div>
      </div>
      <div className="mb-2">
        <img
          className="w-full h-16 object-contain bg-white rounded border"
          src={boxData.imageBinary}
          alt="Selection thumbnail"
        />
      </div>
      <LabelSelectorForm schema={schema} data={data} setData={setData} />
      <button
        // TODO: move to submit?
        onClick={() => handleLabelClick()}
        disabled={data === null}
        className={
          data === null
            ? `text-xs px-2 py-1 rounded w-full bg-gray-600 text-gray-400 cursor-not-allowed`
            : `text-xs px-2 py-1 rounded w-full bg-blue-600 hover:bg-blue-500 text-white`
        }
      >
        Label
      </button>
    </div>
  );
};

export default LabelBox;
