from dataclasses import dataclass, fields
from datetime import UTC, datetime
from pathlib import Path
from typing import ClassVar

import boto3
import gnupg
import structlog
from celery import Task, shared_task
from django.conf import settings
from retrieval.tasks.utils import singleton_task
from webapp.models import LineItem

logger = structlog.get_logger(__name__)


class OrionBaseError(Exception):
    pass


class OrionFileEncryptionError(OrionBaseError):
    pass


@dataclass
class OrionSecurityConfig:
    security_identifier: str
    security_name: str
    committment_date: str
    fund_family_code: str
    product_type: str
    product_sub_type: str
    product_classification: str
    direct_investment: str
    k1_asset: str


@dataclass
class OrionAccountConfig:
    firm_name: str = "FLP PUTNAM"
    account_number: str = "16954"
    download_symbol: str = "CASH:SWEEP"
    first_name: str = "SARAH JANE"
    last_name: str = "LORBER"
    rep_id: str = "35"
    address_1: str = "REDWOOD TRUST C/O CHOATE, 2 INTERNATIONAL PLACE"
    address_2: str = ""
    city: str = "BOSTON"
    state: str = "MA"
    zip: str = "02110"
    fund_family_code: str = "Bridge"
    custodian_code: str = "Bridge"
    organization_id: str = "d7e7c4ec-06d4-474b-a5e2-dbfd0ba3c1bc"
    securities: ClassVar[dict[str, OrionSecurityConfig]] = {
        # Kleiner Perkins XX Friends
        "c5467413-5a28-46f2-8c5b-569320a318a6": OrionSecurityConfig(
            security_identifier="994BN5990",
            security_name="Kleiner Perkins XX Friends",
            committment_date="02/01/2022",
            fund_family_code="Bridge",
            product_type="Alternative",
            product_sub_type="Venture Capital",
            product_classification="Technology",
            direct_investment="True",
            k1_asset="True",
        ),
        # Kleiner Perkins Select Fund II Friends
        "9dcfa5e0-0b05-4412-92f5-e5e7bd8062b1": OrionSecurityConfig(
            security_identifier="994BU3997",
            security_name="Kleiner Perkins Select Fund II Friends",
            committment_date="02/01/2022",
            fund_family_code="Bridge",
            product_type="Alternative",
            product_sub_type="Venture Capital",
            product_classification="Technology",
            direct_investment="True",
            k1_asset="True",
        ),
    }


@dataclass
class OrionNameFile:
    account_number: str
    download_symbol: str
    client_full_name: str
    first_name: str
    last_name: str
    rep_id: str
    address_1: str
    address_2: str
    city: str
    state: str
    zip: str
    registration_type: str
    ssn_tin_ein: str
    internal_use_only: str
    fund_family_code: str
    custodian_code: str
    orion_client_id: str
    orion_reg_id: str
    share_class: str
    notes: str
    firm_parse_id: str
    fee_schedule: str
    management_style: str
    model_name: str


@dataclass
class OrionSecurityFile:
    fund_security_identifier: str
    security_name: str
    effective_date: str
    fund_family_code: str
    product_type: str
    product_sub_type: str
    product_classification: str
    fund_manager: str
    direct_investment: str
    vintage_year: str
    property_type: str
    region: str
    k1_asset: str
    firm_parse_id: str


@dataclass
class OrionTransactionFile:
    account_number: str
    security_identifier: str
    transaction_date: str
    activity_code: str
    amount: str
    notes: str
    source_destination: str
    cash_date: str
    impacts_committment: str
    impacts_capital_balance: str
    trade_reference_number: str
    cost_basis: str
    cost_date: str
    firm_parse_id: str


@dataclass
class OrionPositionFile:
    account_number: str
    security_identifier: str
    activity_date: str
    market_value: str
    notes: str
    is_estimated: str
    committment_amount: str
    committment_date: str
    capital_invested_value: str
    unfunded_committment: str
    firm_parse_id: str


@dataclass
class OrionCostBasisFile:
    account_number: str
    security_identifier: str
    effective_date: str
    acquired_date: str
    cost_basis: str
    individual_lot_market_value: str
    firm_parse_id: str


def create_orion_name_file(orion_account: OrionAccountConfig) -> OrionNameFile:
    logger.info("Creating Orion name data class", firm_name=orion_account.firm_name)

    # TODO: Eventually pass entity ID, check if there is an SSN, otherwise hardcode
    ssn_or_tin = "*********"
    client_full_name = f"{orion_account.first_name} {orion_account.last_name}"

    name_file = OrionNameFile(
        account_number=orion_account.account_number,
        download_symbol=orion_account.download_symbol,
        client_full_name=client_full_name,
        first_name=orion_account.first_name,
        last_name=orion_account.last_name,
        rep_id=orion_account.rep_id,
        address_1=orion_account.address_1,
        address_2=orion_account.address_2,
        city=orion_account.city,
        state=orion_account.state,
        zip=orion_account.zip,
        registration_type="",
        ssn_tin_ein=ssn_or_tin,
        internal_use_only="",  # Leave Blank
        fund_family_code=orion_account.fund_family_code,
        custodian_code=orion_account.custodian_code,
        orion_client_id="",
        orion_reg_id="",  # Leave Blank, but may be for existing clients
        share_class="",
        notes="",
        firm_parse_id="",  # Leave Blank
        fee_schedule="",
        management_style="",
        model_name="",
    )

    logger.info("Orion name data class created", firm_name=orion_account.firm_name)
    return name_file


def create_orion_security_file(orion_account: OrionAccountConfig, line_item: LineItem) -> OrionSecurityFile | None:
    logger.info(
        "Creating Orion security data class", line_item_id=line_item.id, line_item_name=line_item.investment.legal_name
    )
    security_config = orion_account.securities.get(str(line_item.id))

    if not security_config:
        logger.warning(
            "Security config not found for line item",
            line_item_id=line_item.id,
            line_item_name=line_item.investment.legal_name,
        )
        return None

    security_file = OrionSecurityFile(
        fund_security_identifier=security_config.security_identifier,
        security_name=line_item.investment.legal_name,
        effective_date=(datetime.now(tz=UTC)).strftime("%m/%d/%Y"),
        fund_family_code=orion_account.fund_family_code,
        product_type=security_config.product_type,
        product_sub_type=security_config.product_sub_type,
        product_classification=security_config.product_classification,
        fund_manager="",
        direct_investment=security_config.direct_investment,
        vintage_year="",
        property_type="",
        region="",
        k1_asset=security_config.k1_asset,
        firm_parse_id="",  # Leave Blank
    )
    logger.info(
        "Orion security data class created",
        line_item_id=line_item.id,
        line_item_name=line_item.investment.legal_name,
        security_identifier=security_config.security_identifier,
    )
    return security_file


def create_orion_position_files(orion_account: OrionAccountConfig, line_item: LineItem) -> list[OrionPositionFile]:
    logger.info(
        "Creating Orion position data classes",
        line_item_id=line_item.id,
        line_item_name=line_item.investment.legal_name,
    )
    security_config = orion_account.securities.get(str(line_item.id))

    if not security_config:
        logger.warning(
            "Security config not found for line item",
            line_item_id=line_item.id,
            line_item_name=line_item.investment.legal_name,
        )
        return []

    all_docs = []
    all_docs.extend([("investment_update", doc) for doc in line_item.investment_update_documents.all()])
    all_docs.extend([("distribution", doc) for doc in line_item.distribution_notice_documents.all()])
    all_docs.extend([("capital_call", doc) for doc in line_item.capital_call_documents.all()])
    sorted_docs = sorted(all_docs, key=lambda x: x[1].processed_document.effective_date)

    array_of_positions = []
    for doc_type, doc in sorted_docs:
        if doc_type == "investment_update":
            position = OrionPositionFile(
                account_number=orion_account.account_number,
                security_identifier=security_config.security_identifier,
                activity_date=doc.processed_document.effective_date.strftime("%m/%d/%Y"),
                market_value=doc.total_value,
                notes="No Notes",
                is_estimated="True",
                committment_amount=doc.committed_capital,
                committment_date=orion_account.securities.get(str(line_item.id)).committment_date,
                capital_invested_value=doc.invested,
                unfunded_committment=doc.committed_capital - doc.invested,
                firm_parse_id="",
            )
            array_of_positions.append(position)
        elif doc_type == "distribution":
            if array_of_positions and array_of_positions[-1].security_identifier == security_config.security_identifier:
                previous_position = array_of_positions[-1]
                position = OrionPositionFile(
                    account_number=orion_account.account_number,
                    security_identifier=security_config.security_identifier,
                    activity_date=doc.processed_document.effective_date.strftime("%m/%d/%Y"),
                    market_value=previous_position.market_value - doc.amount,
                    notes="No Notes",
                    is_estimated="True",
                    committment_amount=previous_position.committment_amount,
                    committment_date=previous_position.committment_date,
                    capital_invested_value=previous_position.capital_invested_value - doc.amount,
                    unfunded_committment=previous_position.unfunded_committment,
                    firm_parse_id="",
                )
                array_of_positions.append(position)
            else:
                logger.info(
                    "No previous position found",
                    line_item_id=line_item.id,
                    line_item_name=line_item.investment.legal_name,
                )
        elif doc_type == "capital_call":
            if array_of_positions and array_of_positions[-1].security_identifier == security_config.security_identifier:
                previous_position = array_of_positions[-1]
                position = OrionPositionFile(
                    account_number=orion_account.account_number,
                    security_identifier=security_config.security_identifier,
                    activity_date=doc.processed_document.effective_date.strftime("%m/%d/%Y"),
                    market_value=previous_position.market_value + doc.amount,
                    notes="No Notes",
                    is_estimated="True",
                    committment_amount=previous_position.committment_amount,
                    committment_date=previous_position.committment_date,
                    capital_invested_value=previous_position.capital_invested_value + doc.amount,
                    unfunded_committment=previous_position.unfunded_committment - doc.amount,
                    firm_parse_id="",
                )
                array_of_positions.append(position)
            else:
                logger.info(
                    "No previous position found",
                    line_item_id=line_item.id,
                    line_item_name=line_item.investment.legal_name,
                )

    logger.info(
        "Orion position data classes created",
        line_item_id=line_item.id,
        line_item_name=line_item.investment.legal_name,
        num_positions=len(array_of_positions),
    )
    return array_of_positions


def create_orion_transaction_files(
    orion_account: OrionAccountConfig, line_item: LineItem
) -> list[OrionTransactionFile]:
    logger.info(
        "Creating Orion transaction data classes",
        line_item_id=line_item.id,
        line_item_name=line_item.investment.legal_name,
    )
    security_config = orion_account.securities.get(str(line_item.id))

    if not security_config:
        logger.warning(
            "Security config not found for line item",
            line_item_id=line_item.id,
            line_item_name=line_item.investment.legal_name,
        )
        return []

    all_docs = []
    all_docs.extend([("investment_update", doc) for doc in line_item.investment_update_documents.all()])
    all_docs.extend([("distribution", doc) for doc in line_item.distribution_notice_documents.all()])
    all_docs.extend([("capital_call", doc) for doc in line_item.capital_call_documents.all()])
    sorted_docs = sorted(all_docs, key=lambda x: x[1].processed_document.effective_date)

    transaction_array = []
    for doc_type, doc in sorted_docs:
        if doc_type == "capital_call":
            transaction = OrionTransactionFile(
                account_number=orion_account.account_number,
                security_identifier=security_config.security_identifier,
                transaction_date=doc.processed_document.effective_date.strftime("%m/%d/%Y"),
                activity_code="CapitalCall+",  # 2 for Unknown Capital Call
                amount=doc.amount,
                notes="No Notes",
                source_destination="Custodial Account",
                cash_date=doc.processed_document.effective_date.strftime("%m/%d/%Y"),
                impacts_committment="False",
                impacts_capital_balance="True",
                trade_reference_number="",
                cost_basis=doc.amount,
                cost_date=doc.processed_document.effective_date.strftime("%m/%d/%Y"),
                firm_parse_id="",  # Leave Blank
            )
            transaction_array.append(transaction)
        elif doc_type == "distribution":
            transaction = OrionTransactionFile(
                account_number=orion_account.account_number,
                security_identifier=security_config.security_identifier,
                transaction_date=doc.processed_document.effective_date.strftime("%m/%d/%Y"),
                activity_code="Distribution-",  # 3 for Unknown Distribution
                amount=doc.amount,
                notes="No Notes",
                source_destination="Custodial Account",
                cash_date=doc.processed_document.effective_date.strftime("%m/%d/%Y"),
                impacts_committment="False",
                impacts_capital_balance="True",
                trade_reference_number="",
                cost_basis=doc.amount,
                cost_date=doc.processed_document.effective_date.strftime("%m/%d/%Y"),
                firm_parse_id="",  # Leave Blank
            )
            transaction_array.append(transaction)

    logger.info(
        "Orion transaction data classes created",
        line_item_id=line_item.id,
        line_item_name=line_item.investment.legal_name,
        num_transactions=len(transaction_array),
    )
    return transaction_array


def create_orion_cost_basis_file() -> list[OrionCostBasisFile]:
    logger.info("Creating Orion cost basis data classes")
    # No-op for now
    cost_basis_array = []
    logger.info("Orion cost basis data classes created", num_cost_basis=len(cost_basis_array))
    return cost_basis_array


def create_raw_file(filename: str, data_class: dataclass, positions: list[dataclass]) -> None:
    logger.info("Creating raw file from data", filename=filename, data_class=data_class, num_positions=len(positions))

    csv_path = Path(settings.BASE_DIR) / "site/integrations/tmp"
    csv_path.mkdir(parents=True, exist_ok=True)

    file_path = csv_path / filename
    headers = "|".join(field.name for field in fields(data_class))

    with Path.open(file_path, "w") as f:
        f.write(headers + "\n")

        if positions:
            for position in positions:
                values = [str(getattr(position, field.name, "")) for field in fields(data_class)]
                f.write("|".join(values) + "\n")

    logger.info("Raw file created from data", filename=filename)


def encrypt_file(gpg: gnupg.GPG, filename: str) -> None:
    logger.info("Encrypting file", filename=filename)
    public_key_path = Path(settings.BASE_DIR) / "integrations/keys/advlynx.orion.new.pubkey.asc"
    with Path.open(public_key_path) as f:
        public_key = f.read()

    import_result = gpg.import_keys(public_key)
    gpg.trust_keys(import_result.fingerprints[0], "TRUST_ULTIMATE")
    fingerprint = import_result.fingerprints[0]
    csv_path = Path(settings.BASE_DIR) / "site/integrations/tmp"
    csv_path.mkdir(parents=True, exist_ok=True)

    file_to_encrypt = csv_path / filename
    encrypted_file = csv_path / (filename + ".pgp")

    with Path.open(file_to_encrypt, "rb") as f:
        status = gpg.encrypt_file(f, recipients=[fingerprint], output=encrypted_file)

    if not status.ok:
        error_message = f"Encryption for file {filename} failed: {status.status}"
        logger.error(error_message)
        raise OrionFileEncryptionError(error_message)

    logger.info("File encrypted", filename=filename)


def upload_files_to_s3(filename: str) -> None:
    s3 = boto3.client("s3")
    orion_bucket = settings.SFTP_BUCKET_NAME
    s3_directory = "sftp/orion-service-account/"

    csv_path = Path(settings.BASE_DIR) / "site/integrations/tmp"
    encrypted_file = csv_path / (filename + ".pgp")

    encrypted_file_s3_path = s3_directory + encrypted_file.name

    logger.info("Uploading file to S3", file=encrypted_file.name, bucket=orion_bucket, s3_directory=s3_directory)
    s3.upload_file(str(encrypted_file), orion_bucket, encrypted_file_s3_path)

    logger.info("Successfully uploaded all Orion files to S3", bucket=orion_bucket)


def cleanup_files(filename: str) -> None:
    logger.info("Cleaning up files", filename=filename)
    csv_path = Path(settings.BASE_DIR) / "site/integrations/tmp"
    unencrypted_file = csv_path / filename
    encrypted_file = csv_path / (filename + ".pgp")
    unencrypted_file.unlink(missing_ok=True)
    encrypted_file.unlink(missing_ok=True)

    logger.info("Successfully cleaned up files", filename=filename)


@shared_task(bind=True, track_started=True)
@singleton_task()
def create_orion_files(self: Task) -> None:  # noqa: ARG001
    """
    Celery task to create and upload Orion files.

    Args:
        self: The Celery task instance.

    Returns:
        None

    """
    try:
        gpg = gnupg.GPG()
        orion_account = OrionAccountConfig()
        firm_name = OrionAccountConfig.firm_name
        cost_basis_type = "realized"  # could be "unrealized"

        line_item_ids = list(orion_account.securities.keys())
        target_line_items = LineItem.objects.filter(
            id__in=line_item_ids, organization__id=orion_account.organization_id
        )

        name_file_data = create_orion_name_file(orion_account)

        list_of_security_file_data = []
        list_of_position_file_data = []
        list_of_transaction_file_data = []
        list_of_cost_basis_file_data = []
        for line_item in target_line_items:
            list_of_security_file_data.append(create_orion_security_file(orion_account, line_item))
            list_of_position_file_data.extend(create_orion_position_files(orion_account, line_item))
            list_of_transaction_file_data.extend(create_orion_transaction_files(orion_account, line_item))
            list_of_cost_basis_file_data.extend(create_orion_cost_basis_file())

        todays_date = datetime.now(tz=UTC).strftime("%m%d%Y")

        files = {
            f"{firm_name}_{todays_date}_ALT_nam.txt": (OrionNameFile, [name_file_data] if name_file_data else []),
            f"{firm_name}_{todays_date}_ALT_sec.txt": (OrionSecurityFile, list_of_security_file_data),
            f"{firm_name}_{todays_date}_ALT_pos.txt": (OrionPositionFile, list_of_position_file_data),
            f"{firm_name}_{todays_date}_ALT_trn.txt": (OrionTransactionFile, list_of_transaction_file_data),
            f"{firm_name}_{todays_date}_ALT_{cost_basis_type}_cost.txt": (
                OrionCostBasisFile,
                list_of_cost_basis_file_data,
            ),
        }

        for filename, (data_class, data) in files.items():
            try:
                create_raw_file(filename, data_class, data)
                encrypt_file(gpg, filename)
                if not settings.DEBUG:
                    upload_files_to_s3(filename)
                else:
                    logger.info("Skipping S3 upload in DEBUG mode", filename=filename)
            except Exception as e:
                logger.exception("Error creating Orion files", error=str(e))
                raise
            finally:
                cleanup_files(filename)

    except Exception as e:
        logger.exception("Error creating Orion files", error=str(e))
        raise
