<div hx-get="{% url 'line_item_aggregate' %}"
     hx-trigger="changeMergedPortalCredentialRow from:document"
     hx-swap='innerHTML'>
    <div class="h-60 w-full justify-center items-start gap-8 inline-flex">
        <div class="w-full pt-12 px-9 border pb-8 border-b-[#f4ebe3] justify-start items-start gap-6 inline-flex">
            <div class="grow shrink basis-0 flex-col justify-start items-start gap-3 inline-flex">
                <div class="self-stretch h-10 px-3.5 py-2.5 bg-[#4b554d] rounded-lg justify-start items-center gap-1 inline-flex">
                    <div class="px-0.5 justify-center items-center flex">
                        <div class="Text text-white text-base font-extrabold font-['Inter'] leading-[18px]">Connection Health</div>
                    </div>
                </div>
                <div class="self-stretch justify-start items-start gap-2 inline-flex">
                    <div class="grow shrink basis-0 p-6 bg-white rounded-xl border border-[#eceee8] flex-col justify-start items-start gap-6 inline-flex">
                        <div class="self-stretch justify-start items-center gap-6 inline-flex">
                            <div class="grow shrink basis-0 text-[#343d36] text-base font-semibold font-['Inter'] leading-normal">Line Items</div>
                        </div>
                        <div class="self-stretch justify-start items-end gap-4 inline-flex">
                            <div class="grow shrink basis-0 flex-col justify-start items-start gap-4 inline-flex">
                                <div class="self-stretch text-[#343d36] text-[28px] font-bold font-['Nanum Myeongjo'] leading-7">
                                    {{ line_item_count }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grow shrink basis-0 p-6 bg-gray-100 rounded-xl border border-[#eceee8] flex-col justify-start items-start gap-6 inline-flex">
                        <div class="self-stretch justify-start items-center gap-6 inline-flex">
                            <div class="grow shrink basis-0 text-[#343d36] text-base font-semibold font-['Inter'] leading-normal">Linked</div>
                        </div>
                        <div class="self-stretch justify-start items-end gap-4 inline-flex">
                            <div class="grow shrink basis-0 flex-col justify-start items-start gap-4 inline-flex">
                                <div class="self-stretch text-[#343d36] text-[28px] font-bold font-['Nanum Myeongjo'] leading-7">
                                    {{ linked_count }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grow shrink basis-0 p-6 bg-gray-100 rounded-xl border border-[#eceee8] flex-col justify-start items-start gap-6 inline-flex">
                        <div class="self-stretch justify-start items-center gap-6 inline-flex">
                            <div class="grow shrink basis-0 text-[#343d36] text-base font-semibold font-['Inter'] leading-normal">Pending</div>
                        </div>
                        <div class="self-stretch justify-start items-end gap-4 inline-flex">
                            <div class="grow shrink basis-0 flex-col justify-start items-start gap-4 inline-flex">
                                <div class="self-stretch text-[#343d36] text-[28px] font-bold font-['Nanum Myeongjo'] leading-7">
                                    {{ pending_count }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grow shrink basis-0 p-6 bg-gray-100 rounded-xl border border-[#eceee8] flex-col justify-start items-start gap-6 inline-flex">
                        <div class="self-stretch justify-start items-center gap-6 inline-flex">
                            <div class="grow shrink basis-0 text-[#343d36] text-base font-semibold font-['Inter'] leading-normal">
                                Action Required
                            </div>
                        </div>
                        <div class="self-stretch justify-start items-end gap-4 inline-flex">
                            <div class="grow shrink basis-0 flex-col justify-start items-start gap-4 inline-flex">
                                <div class="self-stretch text-[#343d36] text-[28px] font-bold font-['Nanum Myeongjo'] leading-7">
                                    {{ action_required_count }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
