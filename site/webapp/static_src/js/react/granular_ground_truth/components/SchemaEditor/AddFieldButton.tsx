import { Badge } from "../../components/ui/badge";
import { <PERSON><PERSON> } from "../../components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import type { JSONSchema, NewField } from "../../types/jsonSchema";
import { CirclePlus } from "lucide-react";
import React from "react";
import { useState } from "react";
import SchemaCustomObjectSelector from "./SchemaCustomObjectSelector";

interface AddFieldButtonProps {
  schemas?: JSONSchema[]; // List of all schemas for custom object selection
  onAddField: (field: NewField) => void;
  variant?: "primary" | "secondary";
}

const AddFieldButton: React.FC<AddFieldButtonProps> = ({
  schemas = [],
  onAddField,
  variant = "primary",
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);

  const [jsonSchema, setJsonSchema] = useState<JSONSchema | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    onAddField({
      name:
        typeof jsonSchema === "object" &&
        jsonSchema !== null &&
        "title" in jsonSchema
          ? jsonSchema.title
          : "",
      type:
        typeof jsonSchema === "object" &&
        jsonSchema !== null &&
        "type" in jsonSchema
          ? jsonSchema.type
          : "",
      description:
        typeof jsonSchema === "object" &&
        jsonSchema !== null &&
        "description" in jsonSchema
          ? jsonSchema.description
          : "",
      required: false,
      jsonSchema: jsonSchema,
    });

    setDialogOpen(false);
  };
  const handleFieldTypeChange = (newType: JSONSchema | null | undefined) => {
    if (!newType) return;
    setJsonSchema(newType);
  };
  return (
    <>
      <Button
        onClick={() => setDialogOpen(true)}
        variant={variant === "primary" ? "default" : "outline"}
        size="sm"
        className="flex items-center gap-1.5 group"
      >
        <CirclePlus
          size={16}
          className="group-hover:scale-110 transition-transform"
        />
        <span>Add Field</span>
      </Button>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="md:max-w-[1200px] max-h-[85vh] w-[95vw] p-4 sm:p-6">
          <DialogHeader className="mb-4">
            <DialogTitle className="text-xl flex flex-wrap items-center gap-2">
              Add New Field
              <Badge variant="secondary" className="text-xs">
                Schema Builder
              </Badge>
            </DialogTitle>
            <DialogDescription className="text-sm">
              Create a new field for your JSON schema
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4 min-w-[280px]">
                <div>
                  <div className="flex flex-wrap items-center gap-2 mb-1.5">
                    <label htmlFor="fieldName" className="text-sm font-medium">
                      Field Name
                    </label>
                  </div>
                  <div>
                    {typeof jsonSchema === "object" &&
                    jsonSchema !== null &&
                    "title" in jsonSchema ? (
                      jsonSchema.title?.split("/").pop()
                    ) : (
                      <span className="text-sm text-white italic">
                        (Will be set after selecting type)
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <div className="flex flex-wrap items-center gap-2 mb-1.5">
                    <label htmlFor="fieldDesc" className="text-sm font-medium">
                      Description
                    </label>
                  </div>
                  {typeof jsonSchema === "object" &&
                  jsonSchema !== null &&
                  "description" in jsonSchema ? (
                    jsonSchema.description
                  ) : (
                    <span className="text-sm text-white italic">
                      (Will be set after selecting type)
                    </span>
                  )}
                </div>
              </div>
              <div className="space-y-4 min-w-[280px]">
                <div>
                  <div className="flex flex-wrap items-center gap-2 mb-1.5">
                    <label htmlFor="fieldType" className="text-sm font-medium">
                      Field Type
                    </label>
                  </div>
                  <SchemaCustomObjectSelector
                    schemas={schemas}
                    onChange={handleFieldTypeChange}
                  />
                </div>
              </div>
            </div>

            <DialogFooter className="mt-6 gap-2 flex-wrap">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" size="sm">
                Add Field
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AddFieldButton;
