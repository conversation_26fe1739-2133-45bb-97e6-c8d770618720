import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON>enerator
from pathlib import Path

import pandas as pd
import structlog
from playwright.async_api import Locator
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class ArkPESPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "arkpes.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "ARK - Trinity Alps"),
        ]

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        login_email_locator = self.page.locator("input[id='login_email']")
        await login_email_locator.click()
        await asyncio.sleep(1)
        await login_email_locator.fill(username)
        await asyncio.sleep(1)
        login_pass_locator = self.page.locator("input[id='login_pass']")
        await login_pass_locator.click()
        await asyncio.sleep(1)
        await login_pass_locator.fill(password)
        await asyncio.sleep(1)
        await self.page.get_by_text("Submit").click()

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(5)
        return await self.page.locator("button[id='user-account']").count() > 0

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: PLR0912
        if self.page is None:
            raise ManagerNotInitializedError
        # Start with capital accounts page
        if await self.page.locator("h1.MuiTypography-pageTitle").inner_text() == "Capital Accounts":
            async for doc in self._download_capital_accounts():
                yield doc
        else:
            logger.error("Cannot find Capital Accounts page")
            raise ValueError

        # Go to capital calls page
        await self.page.get_by_text("Capital Calls").click()
        await asyncio.sleep(5)
        if await self.page.locator("h1.MuiTypography-pageTitle").inner_text() == "Capital Calls":
            async for doc in self._download_capital_calls():
                yield doc
        else:
            logger.error("Cannot find Capital Calls page")
            raise ValueError

        # Go to distributions page
        await self.page.get_by_text("Distributions").click()
        await asyncio.sleep(5)
        if await self.page.locator("h1.MuiTypography-pageTitle").inner_text() == "Distributions":
            async for doc in self._download_distributions():
                yield doc
        else:
            logger.error("Cannot find Distributions page")
            raise ValueError

        # Go to documents page
        await self.page.get_by_text("Documents").click()
        await asyncio.sleep(5)

        # for each row in each page, if the first row contains a folder icon, this is a folder page
        # we need to click into/iterate over every row in a folder page until we find a page that is a file page
        # can find a file page if the first row has a PDF icon (or even better a non folder icon)
        # download every row in a file page
        if await self.page.locator("h1.MuiTypography-pageTitle").inner_text() == "Documents":
            async for doc in self._download_documents():
                yield doc
        else:
            logger.error("Cannot find Documents page")
            raise ValueError

    async def _download_capital_accounts(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        # Assuming we are starting in the capital accounts page
        if await self.page.get_by_text("No Data Available").count() != 0:
            logger.info("No Capital Accounts to download")
            return
        rows_container = self.page.locator("div.MuiDataGrid-virtualScrollerRenderZone")
        columns_container = self.page.locator("div.MuiDataGrid-pinnedColumns")
        seen_row_ids: set[str] = set()
        unseen_rows = await self._create_unseen_rows(rows_container, seen_row_ids)
        while unseen_rows:
            row_id = unseen_rows.pop(0)
            seen_row_ids.add(row_id)
            row = rows_container.locator(f"div[role='row'][data-rowindex='{row_id}']")
            if await row.count() != 1 or not await row.is_visible():
                logger.info("Row not visible or distinct", row_id=row_id)
            await row.hover()
            row_metadata = await self._get_row_metadata(row)
            column_row = columns_container.locator(f"div[role='row'][data-rowindex='{row_id}']")
            quarter = await column_row.locator("div[role='cell'][data-field='quarter']").inner_text()
            row_metadata["quarter"] = quarter
            metadata = json.dumps(row_metadata)
            view_button = column_row.locator("button")
            metadata = json.dumps(row_metadata)
            if await self.should_download_document(metadata) and await view_button.count() != 0:
                await view_button.click()
                await asyncio.sleep(2)
                flyout_locator = self.page.locator("div.MuiPaper-root")
                download_pdf_button = flyout_locator.get_by_text("Download (PDF)")
                if await download_pdf_button.count() == 0:
                    logger.error("Cannot download PDF")
                    raise ValueError
                doc_name, doc_content = await self.__download_doc(download_pdf_button)
                doc_type = self.__document_mapper("Capital Accounts")
                datetime_date = self.__period_to_datetime_mapper(quarter)
                doc = RawDocumentTuple(
                    name=doc_name,
                    date=datetime_date,
                    doc_type=doc_type,
                    content=doc_content,
                    raw_metadata=metadata,
                    doc_hash=self._doc_hash(metadata),
                )

                if doc is not None:
                    yield doc
                close_flyout_button = flyout_locator.locator("svg[data-testid='CloseIcon']")
                await close_flyout_button.click()
                await asyncio.sleep(1)

            unseen_rows = await self._create_unseen_rows(rows_container, seen_row_ids)

    async def _download_capital_calls(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        if await self.page.get_by_text("No Data Available").count() != 0:
            logger.info("No Capital Calls to download")
            return
        seen_row_ids: set[str] = set()
        rows_container = self.page.locator("div.MuiDataGrid-virtualScrollerRenderZone")
        columns_container = self.page.locator("div.MuiDataGrid-pinnedColumns")
        unseen_rows = await self._create_unseen_rows(rows_container, seen_row_ids)
        while unseen_rows:
            row_id = unseen_rows.pop(0)
            seen_row_ids.add(row_id)
            row = rows_container.locator(f"div[role='row'][data-rowindex='{row_id}']")
            if await row.count() != 1 or not await row.is_visible():
                logger.info("Row not visible or distinct", row_id=row_id)
            await row.hover()
            row_metadata = await self._get_row_metadata(row)
            metadata = json.dumps(row_metadata)
            column_row = columns_container.locator(f"div[role='row'][data-rowindex='{row_id}']")
            view_button = column_row.locator("button")
            if await self.should_download_document(metadata) and await view_button.count() != 0:
                await view_button.click()
                await asyncio.sleep(2)
                flyout_locator = self.page.locator("div.MuiPaper-root")
                download_pdf_button = flyout_locator.get_by_text("Download")
                if await download_pdf_button.count() == 0:
                    logger.error("Cannot download PDF")
                    raise ValueError
                doc_name, doc_content = await self.__download_doc(download_pdf_button)
                doc_type = self.__document_mapper("Capital Calls")
                raw_date = row_metadata["callDate"]
                datetime_date = datetime.datetime.strptime(raw_date, "%m/%d/%Y")  # noqa: DTZ007
                doc = RawDocumentTuple(
                    name=doc_name,
                    date=datetime_date,
                    doc_type=doc_type,
                    content=doc_content,
                    raw_metadata=metadata,
                    doc_hash=self._doc_hash(metadata),
                )

                if doc is not None:
                    yield doc
                close_flyout_button = flyout_locator.locator("svg[data-testid='CloseIcon']")
                await close_flyout_button.click()
                await asyncio.sleep(1)

            unseen_rows = await self._create_unseen_rows(rows_container, seen_row_ids)

    async def _download_distributions(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        if await self.page.get_by_text("No Data Available").count() != 0:
            logger.info("No Distributions to download")
            return
        seen_row_ids: set[str] = set()
        rows_container = self.page.locator("div.MuiDataGrid-virtualScrollerRenderZone")
        columns_container = self.page.locator("div.MuiDataGrid-pinnedColumns")
        unseen_rows = await self._create_unseen_rows(rows_container, seen_row_ids)
        while unseen_rows:
            row_id = unseen_rows.pop(0)
            seen_row_ids.add(row_id)
            row = rows_container.locator(f"div[role='row'][data-rowindex='{row_id}']")
            if await row.count() != 1 or not await row.is_visible():
                logger.info("Row not visible or distinct", row_id=row_id)
            await row.hover()
            row_metadata = await self._get_row_metadata(row)
            metadata = json.dumps(row_metadata)
            column_row = columns_container.locator(f"div[role='row'][data-rowindex='{row_id}']")
            view_button = column_row.locator("button")
            if await self.should_download_document(metadata) and await view_button.count() != 0:
                await view_button.click()
                await asyncio.sleep(2)
                flyout_locator = self.page.locator("div.MuiPaper-root")
                download_pdf_button = flyout_locator.get_by_text("Download")
                if await download_pdf_button.count() == 0:
                    logger.error("Cannot download PDF")
                    raise ValueError
                doc_name, doc_content = await self.__download_doc(download_pdf_button)
                doc_type = self.__document_mapper("Distributions")
                raw_date = row_metadata["distributionDate"]
                datetime_date = datetime.datetime.strptime(raw_date, "%m/%d/%Y")  # noqa: DTZ007
                doc = RawDocumentTuple(
                    name=doc_name,
                    date=datetime_date,
                    doc_type=doc_type,
                    content=doc_content,
                    raw_metadata=metadata,
                    doc_hash=self._doc_hash(metadata),
                )

                if doc is not None:
                    yield doc
                close_flyout_button = flyout_locator.locator("svg[data-testid='CloseIcon']")
                await close_flyout_button.click()
                await asyncio.sleep(1)

            unseen_rows = await self._create_unseen_rows(rows_container, seen_row_ids)

    async def _download_documents(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: C901, PLR0912, PLR0915
        if self.page is None:
            raise ManagerNotInitializedError
        if await self.page.get_by_text("No Data Available").count() != 0:
            logger.info("No Documents to download")
            return
        folder_structure_locator = self.page.locator("nav[aria-label='documents breadcrumb']")
        folder_structure_count = await folder_structure_locator.count()
        folder_texts = None
        page_folder_structure = None
        if folder_structure_count > 0:
            folder_texts = folder_structure_locator.locator("li")
            show_path_button = folder_texts.locator("button[aria-label='Show path']")
            show_path_button_count = await show_path_button.count()
            if show_path_button_count > 0:
                await show_path_button.click()
            await asyncio.sleep(1)
            page_folder_structure = await self._get_page_folder_structure(folder_texts)

        seen_row_ids: set[str] = set()
        rows_container = self.page.locator("div.MuiDataGrid-virtualScrollerRenderZone")
        columns_container = self.page.locator("div.MuiDataGrid-pinnedColumns")
        unseen_rows = await self._create_unseen_rows(rows_container, seen_row_ids)
        while unseen_rows:
            row_id = unseen_rows.pop(0)
            seen_row_ids.add(row_id)
            row = rows_container.locator(f"div[role='row'][data-rowindex='{row_id}']")
            if await row.count() != 1 or not await row.is_visible():
                logger.info("Row not visible or distinct", row_id=row_id)
            await row.hover()

            row_name = row.locator("div[role='cell'][data-field='name']")
            clickable_link = row_name.locator("a")
            inner_svg = clickable_link.locator("svg")
            data_test_id = await inner_svg.get_attribute("data-testid")
            if data_test_id == "PictureAsPdfIcon":
                row_metadata = await self._get_row_metadata(row)
                if page_folder_structure is not None:
                    row_metadata["pageFolderStructure"] = page_folder_structure
                metadata = json.dumps(row_metadata)
                column_row = columns_container.locator(f"div[role='row'][data-rowindex='{row_id}']")
                column_cell = column_row.locator("div[role='cell'][data-field='action']")
                if await self.should_download_document(metadata) and await column_cell.count() != 0:
                    doc_name, doc_content = await self.__download_doc(column_cell)
                    doc_type = self.__document_mapper(page_folder_structure)
                    datetime_date = self.__period_to_datetime_mapper(row_metadata["quarter"])
                    doc = RawDocumentTuple(
                        name=doc_name,
                        date=datetime_date,
                        doc_type=doc_type,
                        content=doc_content,
                        raw_metadata=metadata,
                        doc_hash=self._doc_hash(metadata),
                    )
                    if doc is not None:
                        yield doc

            else:
                await clickable_link.click()
                await asyncio.sleep(3)
                async for doc in self._download_documents():
                    yield doc

            unseen_rows = await self._create_unseen_rows(rows_container, seen_row_ids)

        if folder_structure_count > 0 and folder_texts is not None:
            folder_paths = folder_texts.locator("a")
            count = await folder_paths.count()
            last_folder_path = folder_paths.nth(count - 1) if count > 0 else None
            if last_folder_path:
                await last_folder_path.click()
                await asyncio.sleep(3)

    async def _get_row_metadata(self, row: Locator) -> dict[str, str]:
        row_metadata = {}
        for cell in await row.locator("div[role='cell']").all():
            # Want to skip the checkbox div
            if await cell.get_attribute("data-field") == "__check__":
                continue
            data_field = await cell.get_attribute("data-field")
            if data_field is None:
                continue
            inner_text = await cell.inner_text()
            row_metadata[data_field] = inner_text

        return row_metadata

    async def _create_unseen_rows(self, table: Locator, seen: set[str]) -> list[str]:
        row_ids = []
        for e in await table.locator("div[role='row']").all():
            if not await e.is_visible():
                continue
            row_id = await e.get_attribute("data-rowindex")
            if row_id is None:
                continue
            if row_id not in seen:
                row_ids.append(row_id)
        row_ids.sort(key=int)
        return row_ids

    async def __download_doc(self, download_button: Locator) -> tuple[str, bytes]:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        async with self.page.expect_download() as download_info:
            await download_button.click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.error("Unsupported file format", file_name_suffix=file_name.suffix)
            raise ValueError
        file_name.unlink()
        await asyncio.sleep(1)
        return download.suggested_filename, content

    # Keeping this here for now in order to do deduplication
    async def __get_row_metadata_old(
        self, row_locator: Locator, page_folder_structure: str
    ) -> tuple[str, str, str, str, str, str, str]:
        doc_name = await row_locator.locator("div[data-field='name']").inner_text()
        investor = await row_locator.locator("div[data-field='investor']").inner_text()
        fund = await row_locator.locator("div[data-field='fund']").inner_text()
        period = await row_locator.locator("div[data-field='quarter']").inner_text()
        file_size = await row_locator.locator("div[data-field='sizeInBytes']").inner_text()
        metadata = json.dumps(
            {
                "File name": doc_name,
                "Investor": investor,
                "Fund": fund,
                "Period": period,
                "File size": file_size,
                "Folder path": page_folder_structure,
            }
        )
        return (doc_name, investor, fund, period, file_size, metadata, page_folder_structure)

    async def _get_page_folder_structure(self, folder_texts: Locator) -> str:
        folder_texts_count = await folder_texts.count()
        folder_structure = ""
        for i in range(folder_texts_count):
            folder_text = folder_texts.nth(i)
            class_name = await folder_text.get_attribute("class")
            if class_name == "MuiBreadcrumbs-li":
                folder_text_raw = await folder_text.inner_text()
                folder_structure += folder_text_raw + "/"

        return folder_structure[:-1]

    def __document_mapper(self, page_folder_structure: str | None) -> DocumentType | str:
        if page_folder_structure is None:
            return DocumentType.UNKNOWN
        mapping = {
            "Financial Statements": DocumentType.FINANCIAL_STATEMENTS,
            "Capital Calls": DocumentType.CAPITAL_CALL,
            "Tax Documents": DocumentType.TAX,
            "Fund Documents": DocumentType.INVESTMENT_UPDATE,
            "Capital Accounts": DocumentType.ACCOUNT_STATEMENT,
            "Distributions": DocumentType.DISTRIBUTION_NOTICE,
            "K-1": DocumentType.TAX,
            "Legal": DocumentType.LEGAL,
        }

        for mapping_text in mapping:
            if mapping_text in page_folder_structure:
                return mapping.get(mapping_text, DocumentType.UNKNOWN)

        return DocumentType.UNKNOWN

    def __period_to_datetime_mapper(self, period: str) -> datetime.datetime:
        # There is one folder where the period column is full of blank data for all docs
        if not any(char.isalnum() for char in period):
            # In this case, we will just pass the currrent time
            return datetime.datetime.now(tz=datetime.UTC)

        year_str, quarter = period.split()
        year = int(year_str)

        quarter_start_month_mapping = {
            "Q1": 1,  # January
            "Q2": 4,  # April
            "Q3": 7,  # July
            "Q4": 10,  # October
        }

        start_month = quarter_start_month_mapping.get(quarter)
        if not start_month:
            exception = f"Invalid quarter: {quarter}"
            raise ValueError(exception)

        return (
            pd.to_datetime(datetime.datetime(year, start_month, 1))  # noqa: DTZ001
            .to_period("Q")
            .to_timestamp(how="end")
            .to_pydatetime(warn=False)
        )
