from django.contrib.auth import get_user_model
from django.db import models
from django.utils import timezone
from webapp.models.core import AbstractBaseModel


class APIKey(AbstractBaseModel):
    prefix = models.Char<PERSON>ield(max_length=8)
    hashed_key = models.CharField(max_length=100)
    user = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)
    label = models.CharField(max_length=40)
    revoked = models.BooleanField(default=False)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "API key"
        verbose_name_plural = "API keys"

    def __str__(self) -> str:
        return f"{self.user.username}<{self.prefix}>"

    @property
    def is_valid(self) -> bool:
        if self.revoked:
            return False

        if not self.expires_at:
            return True  # No expiration

        return self.expires_at >= timezone.now()

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return []
