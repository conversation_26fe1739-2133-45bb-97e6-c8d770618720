import json
import os

import structlog

from . import base
from .base import *  # noqa: F403
from .logging_utils import configure_logging
from .utils import get_secret

LOGGING = configure_logging(
    "color_console", "INFO", str(base.BASE_DIR / f"test-{os.environ.get('APPLICATION', 'unknown')}.log")
)
logger = structlog.get_logger(__name__)

logger.info("OVERRIDING DEV SETTINGS")

DEBUG = True

DEFAULT_FROM_EMAIL = "<EMAIL>"
# AWS Bucket, TODO, have a dedicated dev bucket
AWS_STORAGE_BUCKET_NAME = "bridge-demo-userdata-bucket"
SFTP_BUCKET_NAME = "bridge-demo-sftpdata-bucket"
X_FRAME_OPTIONS = "ALLOW"
CSRF_COOKIE_SAMESITE = "None"
SESSION_COOKIE_SAMESITE = "None"

# MSFT OAUTH for email plug-in integration
# Do not call for secrets in cicd or test
OAUTH_SECRET = json.loads(get_secret("Development-Application-MsftEmailOauth"))

# Slack Secrets
SLACK_OAUTH_KEY = json.loads(get_secret("Application-Slack")).get("OAUTH_TOKEN")
SLACK_SIGNING_SECRET = json.loads(get_secret("Application-Slack")).get("SIGNING_SECRET")
