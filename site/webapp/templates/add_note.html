{% load static %}
<div id="note_modal" class="w-full">
    {# djlint:off #}
    <div class="relative" style="width:320px;"></div>
    <div class="p-4 md:p-5 flex items-center justify-between rounded-t w-full">
        
        <div style="min-width:300px;" class="FeaturedIcon p-2 w-[300] rounded-[10px] justify-start items-start inline-flex">
    {# djlint:on #}
    <h1>Notes: {{ name }}</h1>
</div>
<button _="on click trigger closeModal"
        type="button"
        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
    <img src="{% static 'assets/images/close.svg' %}"
         width="16"
         height="19"
         alt="closeButton">
    <span class="sr-only">Close modal</span>
</button>
</div>
<!-- Success Message -->
<div id="success_message"
     class="hidden p-2 mb-2 bg-green-100 border border-green-400 text-green-700 rounded">Note added successfully!</div>
<!-- Add New Note -->
<div class="p-4">
    <form hx-post="{% url 'add_note' %}"
          hx-target="#note_content"
          hx-swap="outerHTML"
          hx-trigger="submit">
        <div id="note_content">
            <!-- Move hidden fields inside this div -->
            <input type="hidden" name="type" value="{{ type }}">
            <input type="hidden" name="object_id" value="{{ object_id }}">
            {% include "components/note_textarea.html" %}
        </div>
        <!-- Modal Footer -->
        <div class="flex w-full gap-4 px-4 py-3 border-t">
            <button type="submit"
                    class="w-full h-9 px-4 py-1.5 bg-[#924f34] text-white rounded-lg justify-center items-center inline-flex overflow-hidden">
                <div class="text-sm font-semibold font-['Inter'] leading-normal">Save</div>
            </button>
            <button type="button"
                    _="on click trigger closeModal"
                    class="w-full h-9 px-4 py-1.5 bg-white rounded-lg shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]  border border-[#924f34] justify-center items-center inline-flex overflow-hidden">
                <div class="text-[#924f34] text-sm font-semibold font-['Inter'] leading-normal">Close</div>
            </button>
        </div>
    </form>
</div>
</div>
