{% load customtags static %}
<div id="modal-container">
    <div class="relative w-full max-w-lg max-h-full">
        <!-- Modal content -->
        <div class="relative w-[600px]"></div>
        <div class="font-inter relative rounded-lg p-4">
            <!-- Modal header -->
            <div class="flex items-center justify-between rounded-t dark:border-gray-600">
                {% if modal_header_icon %}
                    <div class="w-12 h-12 px-[13px] pt-[15px] pb-[13px] bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
                        <div class="Icon w-[22px] h-5 relative flex-col justify-start items-start flex">
                            <img src="{% static 'assets/images/trash_white.svg' %}"
                                 width="21"
                                 height="21"
                                 alt="trashIcon">
                        </div>
                    </div>
                {% endif %}
                {% if modal_header_text %}
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ modal_header_text }}</h3>
                {% endif %}
                <button _="on click trigger closeModal"
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                    <img src="{% static 'assets/images/close.svg' %}"
                         class="w-3 h-3"
                         width="12"
                         height="12"
                         alt="closeButton">
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            {% if modal_description_text %}
                <div class="mt-4 mb-4">
                    <span class="text-sm">{{ modal_description_text }}</span>
                </div>
            {% elif modal_description_text_with_line_item %}
                <div class="mt-4 mb-4">
                    <div>
                        <span class="text-md font-semibold">{{ modal_description_text_with_line_item }}</span>
                    </div>
                    <span class="text-xs">{{ line_item }}</span>
                </div>
            {% endif %}
            <button _="on click trigger closeModal"
                    type="button"
                    class="w-full mt-2 px-4 py-2.5 bg-[#924f34] text-md font-semibold text-white rounded-lg hover:bg-[#8a5f47] transition-colors">
                Done
            </button>
        </div>
    </div>
</div>
