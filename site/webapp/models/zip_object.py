from django.db import models
from django.utils.translation import gettext_lazy as _

from webapp.models.core import AbstractBaseModel


class ZipJobStatus(models.TextChoices):
    PROCESSING = "pr", _("Processing")
    COMPLETED = "co", _("Completed")
    FAILED = "fa", _("Failed")


class ZipJob(AbstractBaseModel):
    """Model to track the status of ZIP file creation jobs."""

    status = models.CharField(max_length=2, choices=ZipJobStatus.choices, default=ZipJobStatus.PROCESSING)
    progress = models.IntegerField(default=0)
    initial_document_count = models.IntegerField(default=0)
    successful_document_count = models.IntegerField(default=0)
    failed_document_count = models.IntegerField(default=0)
    s3_key = models.Char<PERSON>ield(max_length=255, default="", blank=True)
    s3_bucket = models.CharField(max_length=255, blank=True)
    exists_in_s3 = models.BooleanField(default=False)
    task_id = models.CharField(max_length=255, default="", blank=True)

    def __str__(self) -> str:
        return f"{self.pk} {self.s3_key} ({self.status})"

    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return []
