<div id="latest-updates-content"
     class="h-auto lg:h-[478px] p-4 lg:p-8 bg-base-beige flex-col justify-start items-start w-full">
    <div class="PaddingBottom3xl w-full h-auto lg:h-[52px] pb-4 lg:pb-6 flex-col justify-start items-start gap-2.5 flex">
        <div class="SectionHeader h-auto lg:h-40 flex-col justify-start items-start gap-5 flex">
            <div class="Content w-full justify-start items-start gap-4 flex">
                <div class="DisplayMd text-[#343d36] text-lg lg:text-xl lg:text-[20px] font-bold font-nanum leading-7">Latest Updates</div>
            </div>
        </div>
    </div>
    <div class="TextContent w-full flex-col justify-start items-start gap-4 flex">
        <div class="BodyXsMedium w-full text-[#616863] text-sm font-medium font-inter leading-[21px]">
            <div class="border-separate py-2 lg:py-4 w-full">
                {% if latest_updates %}
                    <div class="relative w-full">
                        <div class="overflow-auto max-h-[330px] w-full rounded-md border border-gray-200">
                            <table class="min-w-full w-full bg-white">
                                <thead class="bg-white sticky top-0 z-10">
                                    <tr class="border-b after:absolute after:inset-0 after:bg-white after:-z-10">
                                        <th class="px-3 py-2 text-left text-[#343D36] font-inter font-medium text-[14px]">Date Received</th>
                                        <th class="px-3 py-2 text-left text-[#343D36] font-inter font-medium text-[14px]">Entity</th>
                                        <th class="px-3 py-2 text-left text-[#343D36] font-inter font-medium text-[14px]">Investment</th>
                                        <th class="px-3 py-2 text-left text-[#343D36] font-inter font-medium text-[14px]">Notice Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for latest_update in latest_updates %}
                                        {% include "dashboard/latest_updates/row.html" %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% else %}
                    <p>No updates available</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
