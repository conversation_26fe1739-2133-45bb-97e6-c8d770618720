{% load customtags static %}
<form hx-post="{% url 'confirm_email_modal' %}{% urlparams merged_portal_credential_pk=mpc.pk %}{% if line_item %}&line_item_pk={{ line_item.pk }}{% endif %}"
      hx-vals='{"sender_email_count": "{{ sender_email_count }}"}'
      hx-swap="outerHTML"
      hx-target="#modal-container">
    <div class="flex mb-4">
        <span class="w-3/4 text-sm">
            Please enter the e-mail address(es) that sends you notices about this line-item. We suggest you search your inbox history.
        </span>
        {% if line_item %}
            <div class="w-full flex items-center">
                <button class="flex flex-row gap-1 flex-1 px-4 py-2.5 border text-xs text-[#343d36] rounded-md shadow-xs hover:bg-[#f7f4f2] transition-colors"
                        type="button"
                        hx-post="{% url 'line_item_delete' %}{% urlparams merged_portal_credential_pk=mpc.pk line_item_pk=line_item.pk %}"
                        hx-target="#modal-container">
                    <img src="{% static 'assets/images/trash.svg' %}"
                         width="14"
                         height="14"
                         alt="Delete">
                    <span class="flex items-center">Delete line item</span>
                </button>
            </div>
        {% endif %}
    </div>
    <div class="flex flex-col gap-4">
        {% if line_item %}
            {% include "vault/selected_line_item_inputs.html" %}
        {% else %}
            {% include "line_item_options.html" %}
        {% endif %}
    </div>
    <div id="sender-emails-container"
         class="max-h-40 flex flex-col gap-4 mt-4 py-2 overflow-auto">
        {% for sender_email in sender_emails %}
            <div class="flex items-center gap-2" id="sender-email-input-container">
                <label for="sender_emails"
                       class="w-32 text-left text-sm text-[#4b554d] font-medium">Manager / Sender Email:</label>
                <div class="w-full items-center flex">
                    <input type="text"
                           name="sender_emails"
                           class="w-full px-4 py-2.5 border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                           {% if forloop.counter == 1 %}required{% endif %}
                           value="{{ sender_email }}" />
                    {% if forloop.counter > 1 %}
                        <div class="relative flex items-center">
                            <button type="button"
                                    class="absolute right-3 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                                    onclick="this.closest('#sender-email-input-container').remove()">
                                <img src="{% static 'assets/images/trash.svg' %}"
                                     width="14"
                                     height="14"
                                     alt="Delete">
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
    <div id="button-containers" class="flex flex-col gap-4 mt-6">
        <button type="button"
                hx-get="{% url 'add_sender_email_field' %}"
                hx-target="#sender-emails-container"
                hx-swap="beforeend"
                hx-vals='{"sender_emails_count": "{{ field_count }}"}'
                hx-trigger="click"
                class="w-full px-4 py-2.5 border text-md font-semibold border-[#924f34] text-[#924f34] rounded-lg hover:bg-[#f7f4f2] transition-colors">
            Add Another Email
        </button>
        <button type="submit"
                class="w-full px-4 py-2.5 bg-[#924f34] text-md font-semibold text-white rounded-lg hover:bg-[#8a5f47] transition-colors">
            Next
        </button>
    </div>
</form>
