# Generated by Django 5.2.1 on 2025-06-06 21:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0046_alter_mergedportalcredential_multi_factor_authentication_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='mergedportalcredential',
            name='is_valid_credentials',
        ),
        migrations.RemoveField(
            model_name='mergedportalcredential',
            name='is_valid_mfa',
        ),
        migrations.RemoveField(
            model_name='portalcredential',
            name='otp_method',
        ),
        migrations.AddField(
            model_name='mergedportalcredential',
            name='last_retrieval',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='latest_merged_portal_credentials', to='webapp.retrieval'),
        ),
        migrations.AddField(
            model_name='mergedportalcredential',
            name='last_user_login_validation_retrieval',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='latest_user_login_merged_portal_credentials', to='webapp.retrieval'),
        ),
        migrations.AddField(
            model_name='retrieval',
            name='user_login_status',
            field=models.CharField(choices=[('ns', 'Not Started'), ('il', 'Invalid Login URL'), ('ic', 'Invalid Credentials'), ('im', 'Invalid MFA Type'), ('mn', 'MFA Not Received'), ('ul', 'Unable to Login'), ('up', 'Unable to Reach Landing Page'), ('ue', 'Unknown Error'), ('sl', 'Successfully Logged In')], default='ns', max_length=2),
        ),
        migrations.AlterField(
            model_name='mergedportalcredential',
            name='multi_factor_authentication',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='merged_portal_credential', to='webapp.multifactorauthentication'),
        ),
        migrations.AlterField(
            model_name='mergedportalcredential',
            name='portal_credential',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='merged_portal_credential', to='webapp.portalcredential'),
        ),
        migrations.AlterField(
            model_name='mergedportalcredential',
            name='user_forwarding_rule',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='merged_portal_credential', to='webapp.userforwardingrule'),
        ),
    ]
