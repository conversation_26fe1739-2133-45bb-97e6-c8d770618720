{% load static %}
<div class="fixed bottom-0 left-0 right-0 flex justify-center z-50 pointer-events-none pb-4">
    <div data-toast-container
         class="flex flex-col items-center space-y-2 pointer-events-auto">
        {% for message in messages %}
            <bridge-toast data-variant="{{ message.tags }}" data-message="{{ message.message }}" data-timeout="4000">
            </bridge-toast>
        {% endfor %}
    </div>
</div>
