#!/usr/bin/env -S uv run
import os
import shlex
import signal
import subprocess  # nosec
from pathlib import Path
from types import FrameType

import structlog

logger = structlog.get_logger(__name__)
from logging.config import dictConfig

dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json_formatter": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.processors.JSONRenderer(),
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "json_formatter",
            },
        },
        "loggers": {
            "": {
                "handlers": ["console"],
                "level": "DEBUG",
                "propagate": True,
            },
        },
    }
)


def cleanup(signum: int, frame: FrameType | None) -> None:  # noqa: ARG001
    # This will shut down all subprocess as well.
    raise SystemExit(signum)


def get_cpu_core_count() -> int:
    """Returns the number of CPU cores on the host system."""
    try:
        with Path("/proc/cpuinfo").open() as f:
            cores = f.read().count("core id")
        return int(cores)
    except BaseException:
        logger.exception("Error getting CPU core count")
        return 1  # Default to 1 core if unable to determine


def run_command(command: str, *, check: bool = False, shell: bool = False) -> None:
    try:
        if shell:
            subprocess.run(command, check=check, shell=True)  # nosec  # noqa: S602
        else:
            subprocess.run(shlex.split(command), check=check, shell=False)  # nosec # noqa: S603
    except BaseException:
        logger.exception("Error running", command=command)
        raise


def main() -> None:  # noqa: C901, PLR0912, PLR0915
    # We add SIGINT and SIGTERM signal handling to ensure that the subprocesses
    # are killed when the parent process is killed.
    # Also this is what docker sends when container is going down.
    # Otherwise container shutdown is delayed 10s between SIGTERM and SIGINT signals as per docker spec.
    signal.signal(signal.SIGINT, cleanup)
    signal.signal(signal.SIGTERM, cleanup)
    environment = os.environ["ENVIRONMENT"].lower()
    application = os.environ["APPLICATION"].lower()
    site_init_action = os.getenv("SITE_INIT_ACTION", "none").lower()
    core_count = get_cpu_core_count()

    if environment not in {"prod", "demo", "dev", "likeprod"}:
        logger.error("Unknown environment", environment=environment)
        raise SystemExit(1)

    if application not in {"site", "celery"}:
        logger.error("Unknown application", application=application)
        raise SystemExit(1)

    cmd = None
    try:
        if environment in {"prod", "demo", "likeprod"}:
            if application == "site":
                workers = 2 * core_count + 1
                cmd = (
                    f"uv run gunicorn bridge_project.wsgi:application "
                    f"--bind 0.0.0.0:8000 --worker-class gthread -w {workers}"
                )
            elif application == "celery":
                # TODO: I don't love running background processes in a production container.
                # Should we use supervisord?
                run_command(  # nosec # noqa: S604
                    "Xvfb -screen 0 1280x800x24 -ac -nolisten tcp -dpi 96 +extension RANDR $DISPLAY &",
                    shell=True,
                )
                concurrency = 2 * core_count + 1
                cmd = (
                    "uv run celery -A bridge_project worker"
                    " -Q high,medium,low"
                    " --beat"
                    " -l info"
                    f" --concurrency={concurrency}"
                )
        elif environment == "dev":
            if application == "site":
                if site_init_action == "reset":
                    run_command("./site/unsafe_ops.py --reset-db")
                    run_command("./site/safe_ops.py --migrate --create-superuser --collect-static")
                elif site_init_action == "load":
                    run_command("./site/unsafe_ops.py --reset-db")
                    run_command("./site/safe_ops.py --migrate --collect-static")
                    run_command("./site/unsafe_ops.py --trunc-db --load")
                elif site_init_action == "migrate":
                    run_command("./site/safe_ops.py --migrate")
                elif site_init_action == "none":
                    pass

                # build once without waiting for files to change
                cd_static_src = "cd /bridge-python/site/webapp/static_src"
                install = "npm install"
                rebuild = "npm run dev"
                run_command(f"({cd_static_src} && {install} && {rebuild}) &", shell=True)  # nosec # noqa: S604
                run_command(  # nosec # noqa: S604
                    (
                        "exec uv run -m watchdog.watchmedo shell-command "
                        "--wait --pattern='*.html;*.css;*.json;*.js' "
                        f"--recursive --command='({cd_static_src} && {rebuild})' &"
                    ),
                    shell=True,
                )

                cmd = (
                    "uv run -m watchdog.watchmedo auto-restart --directory=./ --pattern='*.py;*.html' "
                    "--recursive -- gunicorn bridge_project.wsgi:application --bind 0.0.0.0:8000 --worker-class gthread"
                )

            elif application == "celery":
                # args from: https://chromium.googlesource.com/chromium/src/+/6a162e1ce238e83145921cf55e1477d33d7ba7a1/testing/xvfb.py#92
                # I was getting cases of this file not being removed in development, stopping the server from running.
                Path("/tmp/.X99-lock").unlink(missing_ok=True)  # nosec # noqa: S108
                if os.environ.get("DISPLAY") != "host.docker.internal:0":
                    run_command(  # nosec # noqa: S604
                        "Xvfb -screen 0 1280x800x24 -ac -nolisten tcp -dpi 96 +extension RANDR $DISPLAY &",
                        shell=True,
                    )
                cmd = (
                    "uv run -m watchdog.watchmedo auto-restart "
                    "--directory=./ "
                    "--pattern=*.py "
                    "--recursive -- "
                    "celery -A bridge_project worker"
                    " -Q high,medium,low"
                    " --beat"
                    " -l info"
                    " --concurrency=10"
                )

        logger.info("Starting", application=application, environment=environment, cmd=cmd)
        if cmd is None:
            logger.error("No command to run")
        else:
            run_command(cmd, check=True)
    except Exception:
        logger.exception("Error starting", application=application, environment=environment, cmd=cmd)
        raise


if __name__ == "__main__":
    main()
