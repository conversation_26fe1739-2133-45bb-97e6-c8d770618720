import json
import random
from collections.abc import AsyncGenerator
from pathlib import Path
from typing import Any

import boto3
import numpy as np
import structlog
from asgiref.sync import sync_to_async
from dateutil import parser
from django.conf import settings

from retrieval.core.managers.mock import MockTestManagerForDev
from retrieval.core.registry import register_strategy

logger = structlog.get_logger(__name__)
import pandas as pd
from webapp.models.documents import DocumentType

from retrieval.core.strategy import RawDocumentTuple


def get_all_files() -> list[Path]:
    s3_client = boto3.client("s3")
    paginator = s3_client.get_paginator("list_objects_v2")
    page_iterator = paginator.paginate(Bucket=settings.AWS_STORAGE_BUCKET_NAME, Prefix="static_demo_update_4/")
    all_objects = []
    for page in page_iterator:
        for obj in page.get("Contents", []):
            if "Key" in obj:
                path = Path(obj["Key"])
                all_objects.append(path)
    return all_objects


def get_demo_data(
    portals: list[dict[str, str]] | None = None,
) -> tuple[dict[str, dict[str, Any]], dict[str, list[Path]]]:
    df = pd.read_csv("./site/retrieval/core/managers/demo/demo_data.csv").replace({np.nan: None})
    df["Posted Date"] = df["Posted Date"].apply(parser.parse).apply(lambda x: x.strftime("%Y-%m-%d"))
    df["Effective Date"] = df["Effective Date"].apply(parser.parse).apply(lambda x: x.strftime("%Y-%m-%d"))
    doctype_lookup = {x.label: str(x) for x in DocumentType}
    df["Document Type"] = df["Document Type"].apply(doctype_lookup.get)

    files = get_all_files()

    key2fn: dict[str, list[Path]] = {}
    for file in files:
        path = Path(file)
        key = path.name.removesuffix(path.suffix)
        if key not in key2fn:
            key2fn[key] = []
        key2fn[key].append(path)

    lookup_cols = [
        "Portal Name",
    ]
    lookup: dict[str, list[dict[str, Any]]] = {}
    for _, row in df.iterrows():
        zip_path = row["Doc File Name"]
        row_lookup = row[lookup_cols].to_dict()
        if portals is not None and row_lookup not in portals:
            continue
        if zip_path not in lookup:
            lookup[zip_path] = []
        lookup[zip_path].append(row.to_dict())

    lookup_dict = {zp: v[0] for zp, v in lookup.items()}
    return lookup_dict, key2fn


@register_strategy
class DemoGeneric(MockTestManagerForDev):
    # class DemoGeneric():
    def __init__(self, *args, **kwargs) -> None:  # noqa: ANN002, ANN003
        super().__init__(*args, **kwargs)

    @classmethod
    def needs_playwright(cls) -> bool:
        return False

    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return True

    # TODO: Delete this
    @sync_to_async
    def get_line_items(self) -> list[dict[str, str]]:
        return [
            {
                "Client Name": li.investing_entity.client.legal_name,
                "Investment Entity Legal Name": li.investing_entity.legal_name,
                "Investment / Fund Legal Name": li.investment.legal_name,
            }
            for li in self.retrieval.merged_portal_credential.line_items.all()
        ]

    @sync_to_async
    def get_portals(self) -> list[dict[str, str]]:
        return [{"Portal Name": self.retrieval.merged_portal_credential.portal.name}]

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        portals = await self.get_portals()
        lookup_dict, key2fn = get_demo_data(portals)
        i = 0
        # TODO: should iterate over lookup_dict and raise if not found in s3.
        for zp in sorted(key2fn, key=lambda _: random.randint(0, 100)):  # noqa: S311 # nosec
            v = lookup_dict.get(zp)
            if v is None:
                continue
            i += 1
            if await self.should_download_document(json.dumps(v)):
                yield RawDocumentTuple(
                    f"{v['Doc File Name']}.pdf",
                    v["Posted Date"],
                    v["Document Type"],
                    raw_metadata=v,
                    s3_key=str(key2fn[zp][0]),
                    s3_bucket=settings.AWS_STORAGE_BUCKET_NAME,
                    doc_hash=self._doc_hash(str(key2fn[zp][0])),
                )
