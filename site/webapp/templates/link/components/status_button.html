{# templates/link/components/status_button.html #}
{% load customtags %}
<button hx-get="{{ button_url }}"
        hx-target="body"
        hx-swap="beforeend"
        class="inline-flex items-center font-inter text-[12px] rounded-md px-2 py-1 text-xs font-medium {% if is_complete %} bg-white ring-1 ring-inset ring-black {% else %} bg-red-100 ring-inset ring-red-800 {% endif %}">
    <div style="width: 0.4rem;
                height: 0.4rem"
         class="rounded-full mr-1 {% if is_complete %} bg-green-500 {% else %} bg-red-400 {% endif %}"></div>
    {% if is_complete %}
        Completed
    {% else %}
        Not Started
    {% endif %}
</button>
