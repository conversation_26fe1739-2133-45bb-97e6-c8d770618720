# Generated by Django 5.1.6 on 2025-02-20 15:46

from django.db import migrations, models


class Migration(migrations.Migration):
    def set_processed_docs_extracted_numbers(apps, schema_editor):
        ProcessedDocuments = apps.get_model('webapp', 'processeddocument')
        for pd in ProcessedDocuments.objects.all().iterator():
            pd.has_extracted_numbers = False
            if pd.capital_call_document:
                pd.has_extracted_numbers = True
            if pd.investment_update_document:
                pd.has_extracted_numbers = True
            if pd.distribution_notice_document:
                pd.has_extracted_numbers = True
            pd.save()

    def set_raw_docs_extracted_numbers(apps, schema_editor):
        RawDocuments = apps.get_model('webapp', 'rawdocument')
        for rd in RawDocuments.objects.all().iterator():
            rd.has_extracted_numbers = False
            for pd in rd.processed_documents.all():
                if pd.has_extracted_numbers:
                    rd.has_extracted_numbers = True
                    break
            rd.save()

    def reverse_func(apps, schema_editor):
        pass  # code for reverting migration, if any

    dependencies = [
        ('webapp', '0015_processeddocument_effective_date'),
    ]

    operations = [
        migrations.AddField(
            model_name='processeddocument',
            name='has_extracted_numbers',
            field=models.BooleanField(default=False),
        ),
        migrations.RunPython(set_processed_docs_extracted_numbers, reverse_func),
        migrations.AddField(
            model_name='rawdocument',
            name='has_extracted_numbers',
            field=models.BooleanField(default=False),
        ),
        migrations.RunPython(set_raw_docs_extracted_numbers, reverse_func),
    ]
