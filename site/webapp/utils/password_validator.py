import re

from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _

from webapp.models.user import BridgeUser


class ContainsNumberValidator:
    """Validator to ensure the password contains at least one numeric character."""

    def validate(self: any, password: str, user: BridgeUser | None = None) -> str:  # noqa: ARG002
        if not re.search(r"\d", password):
            raise ValidationError(
                _("The password must contain at least one numeric character."),
                code="password_no_number",
            )

    def get_help_text(self) -> str:
        return _("Your password must contain at least one numeric character.")


class ContainsSpecialCharacterValidator:
    """Validator to ensure the password contains at least one special character."""

    def validate(self, password: any, user: any = None) -> str:  # noqa: ARG002, RUF013
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise ValidationError(
                _("The password must contain at least one special character."),
                code="password_no_special",
            )

    def get_help_text(self) -> str:
        return _("Your password must contain at least one special character.")
