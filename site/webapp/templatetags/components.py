# components.py
from uuid import UUID

from django import template

register = template.Library()


@register.inclusion_tag("link/components/status_button.html")
def status_button(*, is_complete: bool, merged_portal_credential_pk: UUID, button_url: str) -> dict:
    return {
        "button_url": button_url,
        "is_complete": is_complete,
        "merged_portal_credential_pk": merged_portal_credential_pk,
    }
