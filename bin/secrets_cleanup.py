#!/usr/bin/env -S uv run
import datetime
import os
import sys

import boto3
import django
import structlog
from botocore.exceptions import ClientError

sys.path.append("site")
os.environ["DJANGO_SETTINGS_MODULE"] = "bridge_project.settings"
os.environ["ENVIRONMENT"] = "dev"
os.environ["USE_LOCALHOST"] = "1"
django.setup()

from django.apps import apps

logger = structlog.get_logger(__name__)

if __name__ == "__main__":
    if "AWS_PROFILE" not in os.environ:
        msg = "AWS_PROFILE not set"
        raise ValueError(msg)
    # Initialize AWS Secrets Manager client
    client = boto3.client("secretsmanager")

    # Get the current date
    n_days = 7
    now = datetime.datetime.now(datetime.UTC)
    threshold_date = now - datetime.timedelta(days=n_days)

    # Lists to store categorized ARNs
    cls_set = set()
    epoch_datetime = datetime.datetime(1970, 1, 1, 0, 0, 0, tzinfo=datetime.UTC)
    models_set = {
        # TODO: Delete this once all deleted in s3 "MergedPortalCredential",
        "PortalCredential",
        "CustomerEmailCredential",
        "MultiFactorAuthentication"
    }
    models_dict = {}
    for m in models_set:
        models_dict[m] = apps.get_model("webapp", m)

    # Paginate through secrets
    answers :dict[tuple[bool,bool], list[str]]= {}
    paginator = client.get_paginator("list_secrets")
    for page in paginator.paginate():
        for secret in page.get("SecretList", []):
            arn = secret.get("ARN", None)
            name = secret.get("Name", None)
            if arn is None or name is None:
                continue
            if not name.startswith("User-"):
                logger.warning("Not a User- secret", name=name)
                continue
            else:
                _, cls, pk = name.split("-", 2)
                cls_set.add(cls)
            exists_in_database = False
            if cls not in models_dict:
                logger.warning("Class name not known", clazz=cls)
                continue
            else:
                model = models_dict[cls]
                if model.objects.filter(pk=pk).exists():
                    exists_in_database = True
            last_accessed_date = max(
                secret.get("LastAccessedDate", epoch_datetime),
                secret.get("LastChangedDate", epoch_datetime),
                secret.get("CreatedDate", epoch_datetime),
            )
            if not last_accessed_date:
                last_accessed_date = epoch_datetime

            was_recently_used = last_accessed_date >= threshold_date
            tup = (was_recently_used, exists_in_database)
            if tup not in answers:
                answers[tup] = []
            answers[(was_recently_used, exists_in_database)].append(arn)

    # Output results
    for (was_recently_used, exists_in_database), arns in answers.items():
        logger.info(
            "Secret info",
            was_recently_used=was_recently_used,
            exists_in_database=exists_in_database,
            length_arns=len(arns),
        )
    if (True, True) not in answers and (False, True) not in answers:
        msg = "No secrets found that are in the database, you are hooked up to the wrong database."
        raise ValueError(msg)

    if input("Type YES to continue: ") == "YES":
        scheduled_for_deletion = answers.get((False, False), [])
        logger.info("Deleting secrets", length_scheduled_for_deletion=len(scheduled_for_deletion))
        if input("Are you sure? Type YES to continue: ") == "YES":
            for arn in scheduled_for_deletion:
                del_response = client.delete_secret(SecretId=arn, RecoveryWindowInDays=30)
                logger.info("Deleted secret", arn=arn, response=del_response)
        else:
            logger.info("Not deleting secrets")
    else:
        logger.info("Not deleting secrets")

    region_name = os.environ.get("AWS_REGION", "us-east-1")
    session = boto3.session.Session()
    client = session.client(
        service_name="secretsmanager",
        region_name=region_name,
    )
    for model_name, models in models_dict.items():
        logger.info("Checking", model_name=model_name, models_ct=models.objects.count())
        for o in models.objects.all():
            response = None
            secret_name = o.get_secret_name()
            does_secret_exist = o.has_secret()
            if does_secret_exist:
                secret_name = o.secret_arn
            try:
                response = client.describe_secret(SecretId=secret_name)
                does_secret_exist = True
            except ClientError:
                does_secret_exist = False
            if response is not None and "DeletedDate" in response:
                client.restore_secret(SecretId=secret_name)
                logger.info(
                    "SECRET SHOULD NOT BE SCHEDULED FOR DELETION, restoring",
                    secret_name=secret_name,
                    response=response,
                    does_secret_exist=does_secret_exist,
                )

    logger.info("Done, please check if any should not have been deleted")
