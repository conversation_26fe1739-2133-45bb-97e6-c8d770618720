{% load static customtags %}
<div id="slide-alert-container"
     class="fixed top-4 right-0 z-50 transform translate-x-full transition-transform duration-300 ease-in-out"></div>
<div id="docvault_container"
     class="w-full flex flex-col"
     hx-post="{% url 'vault' %}"
     hx-include=".vaultinputs"
     hx-target="#docvault_rows"
     hx-swap="outerHTML"
     hx-select="#docvault_rows"
     hx-trigger="change[event.target.matches('.vaultinputs')], changeDate from:.vaultinputs, clearFilter, sortDates"
     hx-push-url="true"
     hx-disinherit="*">
    <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between mt-4 pb-4 gap-4 lg:gap-0">
        <!-- Client Search Input -->
        <div class="w-full lg:w-1/3">
            <div class="h-[37px] py-2.5 w-full bg-white rounded-lg shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border border-[#eceee8] flex items-center gap-2 px-3">
                <div class="relative">
                    <img src="{% static 'assets/images/search.svg' %}"
                         alt="search"
                         width="20"
                         height="21">
                </div>
                <input type="text"
                       title="Search by Client Name and user can search by document type by starting with @ and then type cc for capital call or ds for distribution statement or any other type dn,as,iu,fs,tx,lg,ot,un"
                       name="q"
                       placeholder="Search by: Client / Entity / Investment / Document Type"
                       value="{{ vault_query_data.query_filters.q }}"
                       hx-post="{% url 'vault' %}"
                       hx-include=".vaultinputs"
                       hx-trigger="keyup[!shiftKey&&!altKey&&!ctrlKey&&!metaKey] changed delay:500ms, keyup[key=='Backspace'||key=='Delete'] delay:500ms"
                       hx-target="#docvault_rows"
                       hx-swap="outerHTML"
                       hx-select="#docvault_rows"
                       hx-push-url="true"
                       hx-disinherit="*"
                       class="vaultinputs w-full h-full grow text-[#4b554d] text-sm font-normal bg-transparent border-none focus:outline-none focus:ring-0 focus:border-transparent">
            </div>
        </div>
        <!-- Loader (Centered) -->
        {% if request.session.zip_job_id %}
            {% alias "" as hidden %}
        {% else %}
            {% alias "hidden" as hidden %}
        {% endif %}
        <div id="zip-loader"
             class="hidden fixed z-10 inset-0 modal-underlay bg-gray-500 bg-opacity-75 transition-opacity">
            <div id="pdf-loader"
                 class="flex flex-col justify-center items-center h-full">
                <lottie-player src="{% static 'assets/images/bridge_green_notxt_loading_animation.json' %}" class="w-36 h-36" background="transparent" speed="1" loop autoplay>
                </lottie-player>
                <span class="text-primary-dark-olive font-bold">Preparing your Download...</span>
            </div>
        </div>
        <!-- Button Group -->
        <div class="flex flex-col sm:flex-row justify-end pb-5 gap-3 w-full lg:w-auto">
            <button id="mark_as_viewed"
                disabled
                hx-post="{% url 'mark_documents_viewed' %}?viewed=true"
                hx-target="#notifications-card"
                hx-swap="outerHTML"
                hx-trigger="click"
                hx-include=".item-checkbox"
                hx-push-url="false"
                class="h-[37px] px-3 bg-[#343d36] text-white rounded-lg flex items-center justify-center gap-2 opacity-50 w-full sm:w-auto"
                {# djlint:off #}
                    _="on change from document or vaultPageChange from document
                        set count to (document.querySelectorAll('.item-checkbox:checked').length)
                        if count > 0
                            remove .opacity-50 remove @disabled add .cursor-pointer
                        else
                            add .opacity-50 add @disabled='true' remove .cursor-pointer
                        end
                        "
                {# djlint:on #}
                >
                <img src="{% static 'assets/images/email.svg' %}"
                     width="18"
                     height="18"
                     alt="Mark as read">
                Mark as read
            </button>
            <button id="mark_as_viewed"
                disabled
                hx-post="{% url 'mark_documents_viewed' %}?viewed=false"
                hx-target="#notifications-card"
                hx-swap="outerHTML"
                hx-trigger="click"
                hx-include=".item-checkbox"
                hx-push-url="false"
                class="h-[37px] px-3 bg-[#343d36] text-white rounded-lg flex items-center justify-center gap-2 opacity-50"
                {# djlint:off #}
                    _="on change from document or vaultPageChange from document
                        set count to (document.querySelectorAll('.item-checkbox:checked').length)
                        if count > 0
                            remove .opacity-50 remove @disabled add .cursor-pointer
                        else
                            add .opacity-50 add @disabled='true' remove .cursor-pointer
                        end
                    "
                {# djlint:on #}
                >
                <img src="{% static 'assets/images/email_closed.svg' %}"
                     width="18"
                     height="18"
                     alt="Mark as unread">
                Mark as unread
            </button>
            <button disabled
                hx-get="{% url 'vault_edit_processed_documents' %}"
                hx-target="body"
                hx-swap="beforeend"
                hx-trigger="click"
                hx-include=".item-checkbox"
                hx-push-url="false"
                id="edit_selected_button"
                class="h-[37px] px-3 py-2 bg-[#343d36] text-white rounded-lg flex items-center gap-2 opacity-50"
                {# djlint:off #}
                    _="on change from document or vaultPageChange from document
                        set count to (document.querySelectorAll('.item-checkbox:checked').length)
                        if count == 1
                            remove .opacity-50 remove @disabled  add .cursor-pointer
                        else
                            add .opacity-50 add @disabled='true' remove .cursor-pointer
                        end
                    "
                {# djlint:on #}
                >
                <img src="{% static 'assets/images/pen_white.svg' %}"
                     width="18"
                     height="18"
                     alt="Edit">
                Edit
            </button>
            {% include "vault/zip_files/zip_button.html" %}
            <!-- Upload Button -->
            <button hx-get="{% url 'vault_upload' %}"
                    hx-target="body"
                    hx-swap="beforeend"
                    hx-trigger="click"
                    hx-include="none"
                    hx-push-url="false"
                    {% if disable_upload_button %}disabled{% endif %}
                    class="h-[37px] px-3 py-2 bg-[#343d36] text-white rounded-lg flex items-center gap-2 cursor-pointer {% if disable_upload_button %}opacity-50 cursor-not-allowed{% endif %}">
                <img src="{% static 'assets/images/upload.svg' %}"
                     width="18"
                     height="18"
                     alt="Upload">
                Upload
            </button>
        </div>
    </div>
    <!-- Table scroll indicator for mobile -->
    <div class="lg:hidden mb-2 text-xs text-gray-500 text-center">
        ← Scroll horizontally to view all columns →
    </div>
    <div class="overflow-auto table-container">
        <table class="w-full min-w-[800px]" id="docvault">
            <thead id="docvault_header" class="bg-white border-b border-gray-200">
                <tr>
                    <th class="px-2 lg:px-4 py-2 text-left text-xs lg:text-sm font-semibold text-gray-600 min-w-[50px]">
                        <span>
                            <div class="mb-2">
                                <label class="flex items-center">
                                    <input type="checkbox"
                                           class="mr-1 lg:mr-2"
                                           id="select-all-checkbox"
                                           onchange="toggleSelectAll(this)">
                                </label>
                            </div>
                        </span>
                    </th>
                    <th class="px-2 lg:px-4 py-2 text-left text-xs lg:text-sm font-semibold text-gray-600 min-w-[120px]">
                        <div id="client-header"
                             hx-post="{% url 'vault_header' %}"
                             hx-vals='{"column_name": "client"}'
                             hx-target="#client-header"
                             hx-swap="innerHTML"
                             hx-trigger="load, conditionalFilter[detail.column_name!=='client'&&detail.column_name!=='category'] from:document, clearFilter[detail.clear_column_filter!=='client'] from:document"
                             hx-include="#docvault_header, .vaultinputs">Client</div>
                    </th>
                    <th class="px-2 lg:px-4 py-2 text-left text-xs lg:text-sm font-semibold text-gray-600 min-w-[120px]">
                        <div id="entity-header"
                             hx-post="{% url 'vault_header' %}"
                             hx-vals='{"column_name": "entity"}'
                             hx-target="#entity-header"
                             hx-swap="innerHTML"
                             hx-trigger="load, conditionalFilter[detail.column_name!=='entity'&&detail.column_name!=='category'] from:document, clearFilter[detail.clear_column_filter!=='entity'] from:document"
                             hx-include="#docvault_header, .vaultinputs">Entity</div>
                    </th>
                    <th class="px-2 lg:px-4 py-2 text-left text-xs lg:text-sm font-semibold text-gray-600 min-w-[140px]">
                        <div id="investment-header"
                             hx-post="{% url 'vault_header' %}"
                             hx-vals='{"column_name": "investment"}'
                             hx-target="#investment-header"
                             hx-swap="innerHTML"
                             hx-trigger="load, conditionalFilter[detail.column_name!=='investment'&&detail.column_name!=='category'] from:document, clearFilter[detail.clear_column_filter!=='investment'] from:document"
                             hx-include="#docvault_header, .vaultinputs">Investment</div>
                    </th>
                    <th class="px-2 lg:px-4 py-2 text-left text-xs lg:text-sm font-semibold text-gray-600 min-w-[100px]">
                        <div id="category-header"
                             hx-post="{% url 'vault_header' %}"
                             hx-vals='{"column_name": "category"}'
                             hx-target="#category-header"
                             hx-swap="innerHTML"
                             hx-trigger="load, clearFilter[detail.clear_column_filter==='category'] from:document"
                             hx-include="#docvault_header, .vaultinputs">Category</div>
                    </th>
                    <th class="px-2 lg:px-4 py-2 text-left text-xs font-semibold text-base-darker-olive-muted font-inter min-w-[100px]">
                        {% include "vault/table/header.html" with title="Posted Date" column_name="posted_date" dropdown_type="date" min_date=date_ranges.min_posted_date max_date=date_ranges.max_posted_date sorted_by_most_recent=posted_date_sorted_by_most_recent %}
                    </th>
                    <th class="px-2 lg:px-4 py-2 text-left text-xs font-semibold text-base-darker-olive-muted font-inter min-w-[100px]">
                        {% include "vault/table/header.html" with title="Effective Date" column_name="effective_date" dropdown_type="date" min_date=date_ranges.min_effective_date max_date=date_ranges.max_effective_date sorted_by_most_recent=effective_date_sorted_by_most_recent %}
                    </th>
                    <th class="px-2 lg:px-4 py-2 text-left text-xs font-semibold text-base-darker-olive-muted font-inter min-w-[200px]">Document</th>
                </tr>
            </thead>
            <tbody id="docvault_rows" class="bg-white">
                {% include "vault/table/rows.html" %}
            </tbody>
        </table>
    </div>
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center px-2 lg:px-4 gap-4 lg:gap-0">
        <!-- Dropdown and "Per Page" -->
        <div class="flex items-center space-x-2 w-full lg:w-auto">
            <select class="vaultinputs px-2.5 py-2 bg-white rounded-lg border border-secondary-light-olive text-[#343d36] text-sm font-semibold font-['Inter'] leading-tight"
                    name="per_page">
                <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                <option value="25" {% if per_page == 25 %}selected{% endif %}>25</option>
                <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
            </select>
            <span class="text-sm text-gray-700">per page</span>
        </div>
        <!-- Help Text and Buttons -->
        <div class="flex flex-col lg:flex-row items-center space-y-2 lg:space-y-0 lg:space-x-4 w-full lg:w-auto">
            <!-- Help Text -->
            <div class="flex flex-col items-center">
                <span class="text-xs lg:text-sm text-gray-700 dark:text-gray-400 text-center">
                    Showing
                    <span id="vault_from" class="font-semibold text-gray-900 dark:text-white">1</span>
                    to
                    <span id="vault_to" class="font-semibold text-gray-900 dark:text-white">10</span>
                    of
                    <span id="vault_ct" class="font-semibold text-gray-900 dark:text-white">100</span>
                    Entries
                </span>
                <input id="vault_current_page"
                       name="current_page"
                       type="number"
                       class="vaultinputs hidden"
                       value="1">
                <input id="vault_page_change"
                       name="page_change"
                       type="number"
                       class="vaultinputs hidden"
                       value="0">
            </div>
            <!-- Buttons -->
            <div class="inline-flex mt-2 lg:mt-0 space-x-2">
                <!-- Previous Button -->
                <button id="vault_prev"
                        class="flex items-center justify-center px-3 lg:px-4 h-8 lg:h-10 text-sm lg:text-base font-medium text-black bg-white rounded-tl-lg rounded-bl-lg shadow border border-secondary-light-olive hover:bg-secondary-light-olive gap-1">
                    <!-- Left Arrow Icon -->
                    <svg class="w-4 lg:w-5 h-4 lg:h-5"
                         xmlns="http://www.w3.org/2000/svg"
                         fill="none"
                         viewBox="0 0 24 24"
                         stroke="currentColor"
                         stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <!-- Next Button -->
                <button id="vault_next"
                        class="flex items-center justify-center px-3 lg:px-4 h-8 lg:h-10 text-sm lg:text-base font-medium text-black bg-white rounded-tr-lg rounded-br-lg shadow border border-secondary-light-olive  hover:bg-secondary-light-olive gap-1">
                    <!-- Right Arrow Icon -->
                    <svg class="w-4 lg:w-5 h-4 lg:h-5"
                         xmlns="http://www.w3.org/2000/svg"
                         fill="none"
                         viewBox="0 0 24 24"
                         stroke="currentColor"
                         stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>
<script>
    function toggleSelection(itemId, element) {
        if (!element.checked) {
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            if (selectAllCheckbox && selectAllCheckbox.checked) selectAllCheckbox.checked = false;
        }

    }

    function toggleSelectAll(element) {
        const checkboxes = document.querySelectorAll('.item-checkbox');
        if (element.checked) {
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                checkbox.dispatchEvent(new Event('change'));
            });

        } else {
            checkboxes.forEach(checkbox => {
                checkbox.checked = false
                checkbox.dispatchEvent(new Event('change'));
            });
        }
    }

    document.body.addEventListener("htmx:configRequest", function(evt) {
        const event = evt.detail.triggeringEvent;
        if (event?.type === "clearFilter") {
            const clearColumnFilter = event.detail?.clear_column_filter || "";
            evt.detail.parameters["clear_column_filter"] = clearColumnFilter;
        }
    });
</script>
