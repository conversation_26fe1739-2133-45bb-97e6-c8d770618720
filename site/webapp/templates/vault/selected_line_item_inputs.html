{% load static %}
<div id="line-items-disabled-inputs" class="flex flex-col gap-4">
    <div class="flex items-center gap-2">
        <label for="client_name"
               class="w-32 text-left text-sm text-[#4b554d] font-medium">Client:</label>
        <input type="text"
               name="client_name"
               id="client_name"
               {% if line_item.investing_entity.client %}value="{{ line_item.investing_entity.client }}"{% endif %}
               class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d] cursor-not-allowed"
               disabled />
    </div>
    <div class="flex items-center gap-2">
        <label for="entity_name"
               class="w-32 text-left text-sm text-[#4b554d] font-medium">
            Investing
            Entity:
        </label>
        <input type="text"
               name="entity_name"
               id="entity_name"
               {% if line_item.investing_entity.legal_name %}value="{{ line_item.investing_entity.legal_name }}"{% endif %}
               class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d] cursor-not-allowed"
               disabled />
    </div>
    <div class="flex items-center gap-2">
        <label for="investment_name"
               class="w-32 text-left text-sm text-[#4b554d] font-medium">Investment:</label>
        <input type="text"
               name="investment_name"
               id="investment_name"
               {% if line_item.investment.legal_name %}value="{{ line_item.investment.legal_name }}"{% endif %}
               class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d] cursor-not-allowed"
               disabled />
    </div>
</div>
