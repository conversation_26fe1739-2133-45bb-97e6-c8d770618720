import { isObjectSchema } from "../../types/jsonSchema";
import React, { useState, useRef, useEffect } from "react";
import type { JSONSchema } from "../../types/jsonSchema";

interface SchemaCustomObjectSelectorProps {
  schemas?: JSONSchema[]; // List of all schemas for custom object selection
  onChange: (value: JSONSchema | null | undefined) => void;
}

const SchemaCustomObjectSelector: React.FC<SchemaCustomObjectSelectorProps> = ({
  schemas = [],
  onChange,
}) => {
  const [jsonSchema, setJsonSchema] = useState<JSONSchema | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const objectSchemas = schemas.filter((schema) => isObjectSchema(schema));

  // Filter schemas based on search term
  const filteredSchemas = objectSchemas.filter(
    (schema) =>
      schema.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schema.$id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schema.description?.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSelect = (schema: JSONSchema | null) => {
    setJsonSchema(schema);
    onChange(schema);
    setIsOpen(false);
    setSearchTerm("");
  };

  const handleToggle = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchTerm("");
    }
  };

  return (
    <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 gap-2">
      <div className="col-span-1 md:col-span-3 relative" ref={dropdownRef}>
        <label
          htmlFor="custom-object-schema"
          className="block text-sm font-medium text-white mb-2"
        >
          Select Custom Object Schema
        </label>

        {/* Custom Dropdown Button */}
        <button
          type="button"
          onClick={handleToggle}
          className="flex w-full px-3 py-4 border border-gray-600 bg-gray-800 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left justify-between items-center"
        >
          <span>
            {jsonSchema && isObjectSchema(jsonSchema)
              ? jsonSchema?.title || "Select a schema..."
              : "Select a schema..."}
          </span>
          <svg
            className={`w-5 h-5 transition-transform ${isOpen ? "rotate-180" : ""}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>

        {/* Dropdown Options */}
        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-gray-800 border border-gray-600 rounded-md shadow-lg">
            {/* Search Input - Fixed at top */}
            <div className="p-2 border-b border-gray-600 bg-gray-800 rounded-t-md">
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search schemas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 text-white border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-400"
              />
            </div>

            {/* Scrollable Options Container */}
            <div className="max-h-60 overflow-y-auto">
              <div
                className="px-3 py-2 text-white hover:bg-gray-700 cursor-pointer border-b border-gray-700 last:border-b-0"
                onClick={() => handleSelect(null)}
              >
                Select a schema...
              </div>

              {filteredSchemas.length > 0 ? (
                filteredSchemas.map((schema) => (
                  <div
                    key={schema.$id}
                    className="px-3 py-2 text-white hover:bg-gray-700 cursor-pointer border-b border-gray-700 last:border-b-0"
                    onClick={() => handleSelect(schema)}
                  >
                    <div className="font-medium">{schema.title}</div>
                    {schema.description && (
                      <div className="text-sm text-gray-400 truncate">
                        {schema.description}
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="px-3 py-2 text-gray-400 italic">
                  No schemas found matching "{searchTerm}"
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SchemaCustomObjectSelector;
