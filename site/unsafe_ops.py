#!/usr/bin/env -S uv run
import os
from pathlib import Path

import boto3
import click
import django
import structlog
from django.conf import settings
from django.core.management import call_command
from psycopg import sql
from safe_ops import get_cursor

logger = structlog.get_logger(__name__)

django.setup()

# we assume AWS_PROFILE is 1:1 with environment name
AWS_PROFILE = os.environ.get("AWS_PROFILE", "default")
if AWS_PROFILE == "default":
    AWS_PROFILE = None


def truncate_all_tables() -> None:
    with get_cursor() as cursor:
        cursor.execute("""
            SELECT tablename
            FROM pg_tables
            WHERE schemaname = 'public';
        """)
        tables = cursor.fetchall()

        # TODO: Should we run this all in batch query?
        for table in tables:
            table_name = table[0]
            # We skip django migrations because that is not loaded by loaddata
            # TODO: we should probably migrate from loaddata to pg_dump
            if table_name == "django_migrations":
                continue
            truncate_query = sql.SQL("TRUNCATE TABLE {} RESTART IDENTITY CASCADE").format(
                sql.Identifier(table_name),
            )
            cursor.execute(truncate_query)
            click.echo(f"Truncated table: {table_name}")

    click.echo("All tables truncated successfully.")


def load_database(
    postgres_backup_folder: Path = Path("postgres_dumps"),
    postgres_dump_local_folder: Path = Path("site"),
    postgres_dump_latest: str = "db-latest.json",
) -> None:
    bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    if AWS_PROFILE is not None:
        bucket_name = f"bridge-{AWS_PROFILE}-userdata-bucket"
    else:
        click.echo("AWS_PROFILE not set cannot load without it.")
        raise ValueError
    file_name = str(postgres_dump_local_folder / postgres_dump_latest)
    click.echo(
        f"Loading database from "
        f"{bucket_name}/{(postgres_backup_folder / postgres_dump_latest)!s} "
        f"using profile {AWS_PROFILE}"
    )
    try:
        boto3.client("s3").download_file(bucket_name, str(postgres_backup_folder / postgres_dump_latest), file_name)
        call_command("loaddata", file_name)
    finally:
        Path.unlink(file_name)

    disable_periodic_tasks()


def disable_periodic_tasks() -> None:
    from django_celery_beat.models import PeriodicTask

    tasks = PeriodicTask.objects.all()
    for task in tasks:
        task.enabled = False
        task.save()
    click.echo("Disabled all periodic tasks.")


@click.command()
@click.option("--reset-db", is_flag=True, default=False, help="Reset the database")
@click.option("--interactive", is_flag=True, default=False, help="Run in interactive mode")
@click.option("--load", is_flag=True, default=False, help="Load the database")
@click.option("--trunc-db", is_flag=True, default=False, help="Truncate all tables in the database")
@click.option("--dry-run", is_flag=True, default=False, help="Truncate all tables in the database")
def manage(*, reset_db: bool, interactive: bool, load: bool, trunc_db: bool, dry_run: bool) -> None:
    click.echo("Running reset management script...")
    if not settings.DEBUG:
        click.echo("This script should only be run in development mode. Doing dry run")
        dry_run = True

    if reset_db and settings.DEBUG and not dry_run:
        call_command("reset_db", interactive=interactive)
        click.echo("Database reset successfully.")
    elif reset_db and settings.DEBUG and dry_run:
        click.echo("Would have run 'reset_db' command.")
    else:
        click.echo("Database not reset. Use --reset-db flag to reset the database. Can never run in prod")

    if trunc_db and settings.DEBUG and not dry_run:
        truncate_all_tables()
        click.echo("Database trunctated successfully.")
    elif trunc_db and dry_run:
        click.echo("Would have truncated all tables.")
    else:
        click.echo("Database not trunctated. Use --trunc-db flag to reset the database. Can never run in prod")

    if load and settings.DEBUG and not dry_run:
        load_database()
        click.echo("Database loaded successfully.")
    elif load and settings.DEBUG and dry_run:
        click.echo("Would have loaded the database.")
    else:
        click.echo(
            "Database not loaded. "
            "Use --load to load a database from AWS (set envvar AWS_PROFILE). "
            "Can never run in prod."
        )


if __name__ == "__main__":
    manage()
