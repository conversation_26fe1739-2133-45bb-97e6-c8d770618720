import asyncio
import json

import structlog
from celery import Task, shared_task
from django.core.cache import cache
from langchain_core.messages import (
    ToolMessage,
)
from langgraph.graph.state import CompiledStateGraph
from retrieval.tasks.utils import singleton_task
from webapp.models.portal import MergedPortalCredential

from ml_app.core.data_classes.state import BootstrapState
from ml_app.core.doc_vault_lib import build_doc_vault_graph, get_llm, get_tools, summarize_line_items
from ml_app.core.state_lib import get_li2doc2pk, get_state, reduce_state, save_state

logger = structlog.get_logger(__name__)


@shared_task(bind=True, track_started=True)
def multi_process_doc_vault(self: Task, *, raw_document_ids: list[str]) -> None:
    asyncio.run(async_multi_process_doc_vault(self.request.id, raw_document_ids))


async def async_multi_process_doc_vault(task_id: str, raw_document_ids: list[str]) -> None:
    graph = build_doc_vault_graph(get_llm(), get_tools())

    # sort them
    tasks = [apredict_raw_doc(task_id, raw_document_id, graph=graph) for raw_document_id in raw_document_ids]
    await asyncio.gather(*tasks)


@shared_task(bind=True, track_started=True)
# we use a singleton effectively as distributed lock on the state.
@singleton_task(blocking=True, unique_kwargs=["merged_portal_credential_pk"])
def stateful_process_doc_vault(self: Task, *, raw_document_id: str, merged_portal_credential_pk: str) -> None:
    mpc = MergedPortalCredential.objects.get(pk=merged_portal_credential_pk)
    asyncio.run(async_stateful_process_doc_vault(self.request.id, raw_document_id, mpc))


@shared_task(bind=True, track_started=True)
# we use a singleton effectively as distributed lock on the state.
@singleton_task(blocking=False, unique_kwargs=["merged_portal_credential_pk"])
def stateful_process_doc_vault_singleton_backfill(self: Task, *, merged_portal_credential_pk: str) -> None:
    mpc = MergedPortalCredential.objects.get(pk=merged_portal_credential_pk)

    bootstrap_state = get_state(mpc, BootstrapState)
    li2doc2pks = get_li2doc2pk(bootstrap_state)
    doc2pks = {}
    for l_doc2pks in li2doc2pks.values():
        for doc_type, raw_ids in l_doc2pks.items():
            if doc_type not in doc2pks:
                doc2pks[doc_type] = []
            # TODO: is this the right partition? Should we partition on predicted doc type or original?
            # TODO: does order matter? should we feed it in a certain order?
            doc2pks[doc_type].extend(raw_ids)

    async def process_doc_type(doc_type: str, pks: list[str]) -> None:
        for raw_document_id in pks:
            await async_stateful_process_doc_vault(
                self.request.id,
                str(raw_document_id),
                mpc,
                use_known_line_items=True,
                use_ground_truth=False,
                use_past_predictions=True,
                do_database_insertion_processed_doc=True,
                parallel=False,
                group=doc_type,
            )

    async def process_all_doc_types() -> None:
        await asyncio.gather(*[process_doc_type(doc_type, pks) for doc_type, pks in doc2pks.items()])

    # TODO: don't redo good work. If tasks keep failing after doing a lot of work,
    # this needs to be made resumable.
    asyncio.run(process_all_doc_types())


async def async_stateful_process_doc_vault(  # noqa: PLR0913#
    task_id: str,
    raw_document_id: str,
    mpc: MergedPortalCredential,
    *,
    use_known_line_items: bool = False,
    use_ground_truth: bool = False,
    use_past_predictions: bool = False,
    do_database_insertion_processed_doc: bool = False,
    parallel: bool = True,
    group: str = "",
) -> None:
    cache_key = f"prediction_task_{task_id}_{raw_document_id}"
    cache.set(cache_key, "started")
    graph = build_doc_vault_graph(get_llm(), get_tools())
    bootstrap_state = get_state(mpc, BootstrapState, group=group)
    if bootstrap_state is None:
        bootstrap_state = BootstrapState(
            merged_portal_credential_pk=str(mpc.pk),
        )
    new_state = await graph.ainvoke(
        {
            "raw_doc_pk": raw_document_id,
            "id2obj": bootstrap_state.get("id2obj", {}),
            "id2messages": bootstrap_state.get("id2messages", {}),
            "line_items": bootstrap_state.get("line_items"),
            "use_known_line_items": use_known_line_items,
            "use_ground_truth": use_ground_truth,
            "use_past_predictions": use_past_predictions,
            "do_database_insertion_processed_doc": do_database_insertion_processed_doc,
        }
    )
    save_state(
        mpc,
        BootstrapState(
            **{
                **bootstrap_state,
                **new_state,
            }
        ),
        parallel=parallel,
        group=group,
    )
    last_message = new_state["messages"][-1]
    if isinstance(last_message, ToolMessage):
        content = json.loads(last_message.content)
        if "exception" in content:
            logger.error("Error in prediction", raw_document_id=raw_document_id, error=content["exception"])
            cache.set(cache_key, "failed")
            return []
        if "pks" in content:
            cache.set(cache_key, "success")
            return content["pks"]
    logger.error("Unexpected message type", raw_document_id=raw_document_id, message_type=type(last_message))
    cache.set(cache_key, "failed")
    return []


@shared_task(bind=True, track_started=True)
# we use a singleton effectively as distributed lock on the state.
@singleton_task(blocking=False, unique_kwargs=["merged_portal_credential_pk"])
def predict_line_items(self: Task, *, merged_portal_credential_pk: str) -> None:
    mpc = MergedPortalCredential.objects.get(pk=merged_portal_credential_pk)
    asyncio.run(async_predict_line_items(self.request.id, mpc))


async def async_predict_line_items(
    task_id: str,  # noqa: ARG001
    mpc: MergedPortalCredential,
) -> None:
    reduce_state(mpc)
    graph = summarize_line_items(get_llm(), get_tools())
    bootstrap_state = get_state(mpc, BootstrapState)
    if bootstrap_state is None:
        bootstrap_state = BootstrapState(
            merged_portal_credential_pk=str(mpc.pk),
        )
    bootstrap_state["messages"] = []
    new_state = await graph.ainvoke(
        {
            "id2obj": bootstrap_state.get("id2obj", {}),
            "id2messages": bootstrap_state.get("id2messages", {}),
            "line_items": bootstrap_state.get("line_items"),
            "do_database_insertion_line_item": False,
            **bootstrap_state,
        }
    )
    save_state(
        mpc,
        BootstrapState(
            **{
                **bootstrap_state,
                **new_state,
                "id2obj": {
                    **bootstrap_state.get("id2obj", {}),
                    **new_state["id2obj"],
                },
                "id2messages": {
                    **bootstrap_state.get("id2messages", {}),
                    **new_state["id2messages"],
                },
            }
        ),
    )


async def apredict_raw_doc(task_id: str, raw_document_id: str, graph: CompiledStateGraph | None = None) -> list[str]:
    cache_key = f"prediction_task_{task_id}_{raw_document_id}"
    cache.set(cache_key, "started")
    try:
        if graph is None:
            graph = build_doc_vault_graph(get_llm(), get_tools())
        res = await graph.ainvoke(
            {
                "messages": [],
                "line_items": None,
                "id2messages": {},
                "id2obj": {},
                "raw_doc_pk": raw_document_id,
                "use_known_line_items": True,
                "use_ground_truth": True,
                "use_past_predictions": False,
                "do_database_insertion_processed_doc": True,
            }
        )
        last_message = res["messages"][-1]
        if isinstance(last_message, ToolMessage):
            content = json.loads(last_message.content)
            if "exception" in content:
                logger.error("Error in prediction", raw_document_id=raw_document_id, error=content["exception"])
                cache.set(cache_key, "failed")
                return []
            if "pks" in content:
                cache.set(cache_key, "success")
                return content["pks"]
        logger.error("Unexpected message type", raw_document_id=raw_document_id, message_type=type(last_message))
        cache.set(cache_key, "failed")
    except Exception as e:
        logger.exception("Exception in prediction", raw_document_id=raw_document_id, error=str(e))
        cache.set(cache_key, "failed")
    return []
