{% load static %}
<form class="flex justify-between items-center"
      hx-post="{% url 'client_dashboard_table' %}"
      hx-swap="outerHTML"
      hx-target="#client_dashboard_table">
    <div class="w-1/2">
        {% csrf_token %}
        <div class="h-[37px] py-2.5 w-full bg-white rounded-lg shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border border-[#eceee8] flex items-center gap-2">
            <div class="relative ">
                <img src="{% static 'assets/images/search.svg' %}"
                     alt="search"
                     width="20"
                     height="21">
            </div>
            <input type="text"
                   name="search"
                   placeholder="Search by: Client / Entity / Investment / Notes"
                   value="{{ search_state }}"
                   hx-post="{% url 'client_dashboard_table' %}"
                   hx-trigger="keyup changed delay:500ms"
                   hx-target="#client_dashboard_table"
                   hx-sync="this:replace"
                   hx-swap="outerHTML"
                   class="w-full h-full grow text-[#4b554d] text-base font-normal font-['Inter'] leading-normal bg-transparent border-none focus:outline-none focus:ring-0 focus:border-transparent"
                   onfocus="var temp_value=this.value; this.value=''; this.value=temp_value">
        </div>
        {{ table_order|json_script:"table_order" }}
    </div>
    <div class="flex items-center gap-3">
        <div _="on click from elsewhere add .hidden to #filter-dropdown">
            <button type='button'
                    class="h-[37px] px-3 py-2 bg-[#343d36] rounded-lg border border-[#eceee8] flex items-center gap-2 overflow-hidden"
                    _="on click toggle .hidden on #filter-dropdown">
                <div class="w-3.5 h-3.5 flex justify-center items-center">
                    <img height="18"
                         width="18"
                         src="{% static 'assets/images/filter.svg' %}"
                         alt="filter icon" />
                </div>
                <div class="text-white text-sm font-semibold font-['Inter'] leading-[21px]">Customize</div>
            </button>
            <div id="filter-dropdown"
                 class="hidden bg-white rounded-lg shadow-lg w-[250px] h-auto p-3 absolute z-50">
                <div class="flex justify-between items-center pb-1">
                    <p class="text-sm text-[#343d36]">Show / Hide</p>
                    <!-- Reset Filters -->
                    <button type="button" class="h-9 px-4 py-1.5" id="reset-form">
                        <div class="text-[#924f34] text-sm font-semibold">Reset</div>
                    </button>
                </div>
                <div class="h-[0px] border border-[#eceee8]"></div>
                <!-- Hidden input to track investment order -->
                <input type="hidden" id="table-order-input" name="table_order" value="" />
                <!-- Show / Hide Section -->
                <div class="px-2 py-3 space-y-2">
                    <div class="flex items-center gap-2">
                        <input enabled
                               {% if show_hide_states.client %}checked{% endif %}
                               type="checkbox"
                               name="show_client"
                               value="client"
                               class="w-5 h-5">
                        <label for="show_client" class="text-[#616863] text-base">Client</label>
                    </div>
                    <div class="flex items-center gap-2">
                        <input disabled
                               checked
                               type="checkbox"
                               name="show_entity"
                               value="entity"
                               class="w-5 h-5">
                        <label for="show_entity" class="text-[#B0B3B1] text-base">Entity</label>
                    </div>
                    <div class="flex items-center gap-2">
                        <input disabled
                               checked
                               type="checkbox"
                               name="show_investment"
                               value="investment"
                               class="w-5 h-5">
                        <label for="show_investment" class="text-[#B0B3B1] text-base">Investment</label>
                    </div>
                </div>
                <div class="h-[0px] border border-[#eceee8]"></div>
                <!-- Investment List -->
                <div class="flex justify-between items-center text-[#343d36] text-sm font-['Inter'] leading-[21px] py-3">
                    <span class="text-sm text-[#343d36]">Sort order</span>
                </div>
                <div class="h-[0px] border border-[#eceee8]"></div>
                <div class="pt-2 pb-3">
                    <span class="text-xs text-[#616863]">Drag to reorder</span>
                    <div id="table-order-container">
                        {% for order_item in table_order %}
                            <div class="table-order h-6 flex justify-between items-center gap-2 mt-1 px-2 py-5 border border-[#eceee8] rounded-md cursor-move"
                                 data-value="{{ order_item.id }}"
                                 id="table-order-{{ order_item.id }}"
                                 draggable="true">
                                <div class="text-[#616863] text-sm">{{ order_item.name }}</div>
                                <img src="{% static 'assets/images/dragger.svg' %}"
                                     width="7"
                                     height="12"
                                     alt="dragger"
                                     draggable="false">
                            </div>
                        {% empty %}
                            <p class="text-center text-gray-500">Error, could not get list to order</p>
                        {% endfor %}
                    </div>
                </div>
                <div class="h-[0px] border border-[#eceee8]"></div>
                <div class="flex flex-row gap-2 pt-2">
                    <!-- Cancel Button -->
                    <button type="button"
                            _="on click add .hidden to #filter-dropdown"
                            class="h-9 px-4 py-1.5 bg-white rounded-md border border-[#962c2c] w-full">
                        <div class="text-[#924f34] text-sm font-semibold">Cancel</div>
                    </button>
                    <!-- Apply Filters Button -->
                    <button class="h-9 px-4 py-1.5 bg-[#924f34] rounded-md border border-[#924f34] w-full"
                            type="submit">
                        <div class="text-white text-sm font-semibold">Save view</div>
                    </button>
                </div>
            </div>
        </div>
        {% if user.is_demo or user.organization.name == "NewEdge" or user.organization.name == "Summit Trail" or user.organization.name == "Ezralow" %}
            {% if user.organization.name == "Summit Trail" or user.organization.name == "Ezralow" or user.is_demo %}
                <!-- Export Button -->
                <button id="export_button"
                        hx-get="{% url 'dashboard_export_modal' %}"
                        hx-target="body"
                        hx-swap="beforeend"
                        class="h-[37px] px-3 py-2 bg-[#343d36] rounded-lg border border-[#eceee8] flex items-center gap-2 overflow-hidden">
                    <div class="w-3.5 h-3.5 flex justify-center items-center">
                        <img height="18"
                             width="18"
                             src="{% static 'assets/images/download_icon.svg' %}"
                             alt="download icon" />
                    </div>
                    <div class="text-white text-sm font-semibold font-['Inter'] leading-[21px]">Export</div>
                </button>
            {% else %}
                {% if user.organization.name == "NewEdge" %}
                    {% static 'data/Dashboard Export_NewEdge_(06.2025)_vF.xlsx' as download_url %}
                {% elif user.is_demo_post_email %}
                    {% static 'data/post_email_dashboard.xlsx' as download_url %}
                {% elif user.is_demo_has_docs %}
                    {% static 'data/pre_email_dashboard.xlsx' as download_url %}
                {% else %}
                    {% static 'data/empty_dashboard.xlsx' as download_url %}
                {% endif %}
                <a id="dashboard_download"
                   target="_blank"
                   class="hidden"
                   href="{{ download_url }}"
                   download="Bridge_Dashboard_Export.xlsx"></a>
                <button onclick="document.getElementById('dashboard_download').click()"
                        class="h-[37px] px-3 py-2 bg-[#343d36] rounded-lg border border-[#eceee8] flex items-center gap-2 overflow-hidden">
                    <div class="w-3.5 h-3.5 flex justify-center items-center">
                        <img height="18"
                             width="18"
                             src="{% static 'assets/images/download_icon.svg' %}"
                             alt="download icon" />
                    </div>
                    <div class="text-white text-sm font-semibold font-['Inter'] leading-[21px]">Export</div>
                </button>
            {% endif %}
        {% endif %}
        <!-- Edit Button -->
        <button id="edit_button"
            class="h-[37px] px-3 py-2 bg-[#343d36] rounded-lg border border-[#eceee8] flex items-center gap-2 overflow-hidden"
            {# djlint:off #}
                        _="
                        on click
                            add .hidden
                            remove .hidden from #cancel_button
                            remove .hidden from #save_button
                            remove .hidden from .edit_value
                            add .hidden to .display_value
                        "
            {# djlint:on #}
            >
            <div class="w-3.5 h-3.5 flex justify-center items-center">
                <img height="18"
                     width="18"
                     src="{% static 'assets/images/pen_white.svg' %}"
                     alt="edit icon" />
            </div>
            <div class="text-white text-sm font-semibold font-['Inter'] leading-[21px]">Edit</div>
        </button>
        <!-- Cancel Button -->
        <button id="cancel_button"
            class="hidden h-[37px] px-3 py-2 bg-[#343d36] rounded-lg border border-[#eceee8] flex items-center gap-2 overflow-hidden"
            {# djlint:off #}
                        _="
                        on click
                            add .hidden
                            add .hidden to #save_button
                            remove .hidden from #edit_button
                            add .hidden to .edit_value
                            remove .hidden from .display_value
                            for input in <.edit_value />
                                set input.value to input@data-original
                            end
                            window.formatCurrencyInputElements('.currency_input')
                        "
            {# djlint:on #}
            >
            <div class="w-3.5 h-3.5 flex justify-center items-center">
                <img height="18"
                     width="18"
                     src="{% static 'assets/images/close_white.svg' %}"
                     alt="cancel icon" />
            </div>
            <div class="text-white text-sm font-semibold font-['Inter'] leading-[21px]">Cancel</div>
        </button>
        <button id="save_button"
                hx-post="{% url 'save_bulk_investments' %}"
                hx-trigger="click"
                hx-include=".edit_value"
                hx-swap="none"
                class="hidden h-[37px] px-3 py-2 bg-[#343d36] rounded-lg text-white text-sm font-semibold">
            Save
        </button>
    </div>
</form>
<script>
    const updateTableOrderInput = function() {
        const table_order_input_elem = document.getElementById('table-order-input');
        if (!table_order_input_elem) {
            return;
        }
        table_order_input_elem.value = [].map.call(document.getElementsByClassName('table-order'), function(el) {
            return el.getAttribute('data-value');
        }).join(',');
    }

    const addResetButtonClickHandler = function() {
        const reset_form_elem = document.getElementById('reset-form');
        if (!reset_form_elem) {
            return;
        }
        reset_form_elem.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });

            const defaultTableOrder = [
                "client", "entity", "investment"
            ];
            const wrapper = document.getElementById('table-order-container');
            const elements = document.createDocumentFragment();
            defaultTableOrder.forEach(function(order_item) {
                const cloned_node = document.getElementById('table-order-' + order_item).cloneNode(true);
                window._hyperscript.processNode(cloned_node);
                elements.appendChild(cloned_node);
            })
            wrapper.innerHTML = null;
            wrapper.appendChild(elements);
            updateTableOrderInput();
        });
    }
    const sortable = function() {
        // referenced this https://github.com/SortableJS/Sortable/wiki/Sorting-with-the-help-of-HTML5-Drag'n'Drop-API/

        const rootEl = document.getElementById('table-order-container')
        var dragEl, nextEl;

        function _onDragOver(evt) {
            evt.preventDefault();
            evt.dataTransfer.dropEffect = 'move';

            var target = evt.target;
            var tableOrderEl = target.closest('.table-order');
            if (tableOrderEl && tableOrderEl !== dragEl) {
                var rect = tableOrderEl.getBoundingClientRect();
                var next = (evt.clientY - rect.top) / (rect.bottom - rect.top) > .5;
                rootEl.insertBefore(dragEl, next && tableOrderEl.nextSibling || tableOrderEl);
            }
        }

        function _onDragEnd(evt) {
            evt.preventDefault();

            updateTableOrderInput()

            dragEl.classList.remove('bg-[#eceee8]');
            rootEl.removeEventListener('dragover', _onDragOver, false);
            rootEl.removeEventListener('dragend', _onDragEnd, false);
        }

        rootEl.addEventListener('dragstart', function(evt) {
            dragEl = evt.target;
            nextEl = dragEl.nextSibling;

            evt.dataTransfer.effectAllowed = 'move';
            evt.dataTransfer.setData('Text', dragEl.textContent);

            rootEl.addEventListener('dragover', _onDragOver, false);
            rootEl.addEventListener('dragend', _onDragEnd, false);

            setTimeout(function() {
                dragEl.classList.add('bg-[#eceee8]');
            }, 0)
        }, false);
    }

    document.addEventListener('DOMContentLoaded', sortable);
    document.addEventListener("htmx:afterSwap", sortable);
    document.addEventListener('DOMContentLoaded', updateTableOrderInput);
    document.addEventListener("htmx:afterSwap", updateTableOrderInput);
    document.addEventListener('DOMContentLoaded', addResetButtonClickHandler);
    document.addEventListener("htmx:afterSwap", addResetButtonClickHandler);
</script>
