import json
from http import HTTPStatus
from json.decoder import <PERSON><PERSON><PERSON><PERSON>ode<PERSON><PERSON><PERSON>
from typing import Any

import structlog
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.template.response import TemplateResponse
from django.views.decorators.csrf import csrf_exempt

from webapp.repository.sms_repository import InvalidMessageError, SMSRepository

logger = structlog.get_logger(__name__)


def _extract_params(request: HttpRequest) -> tuple[str | None, str | None, str | None, str | None, str | None]:
    if request.method == "GET":
        raw_data_structure = request.GET
    elif request.method == "POST" and request.content_type in [
        "application/x-www-form-urlencoded",
        "multipart/form-data",
        None,
    ]:
        raw_data_structure = request.POST
    elif request.method == "POST" and request.content_type == "application/json":
        raw_data_structure = json.loads(request.body)
    else:
        return None, None, None, None, None

    data_structure = {}
    for key, value in raw_data_structure.items():
        normalized_key = key.lower().strip().replace(" ", "_")
        if normalized_key in data_structure:
            logger.error(
                "Duplicate key found in request data, skipping...",
                normalized_key=normalized_key,
                value=value,
                key=key,
                raw_data_structure=raw_data_structure,
                data_structure=data_structure,
            )
        data_structure[normalized_key] = value

    def _get_param(data_structure: dict[str, Any], keys: str | list[str]) -> str | None:
        """Helper function to check multiple possible keys"""
        if isinstance(keys, str):
            keys = [keys]
        for key in keys:
            value = data_structure.get(key)
            if value:
                return value
        return None

    logger.info("Extracted SMS Integration Message Data", data_structure=data_structure)

    user_phone_number = _get_param(data_structure, ["to", "user_phone_number"])
    portal_phone_number = _get_param(data_structure, ["from", "portal_phone_number"])
    message = _get_param(data_structure, ["message", "body"])
    portal_name = _get_param(data_structure, ["portal", "portal_name"])
    email = _get_param(data_structure, "email")

    return user_phone_number, portal_phone_number, message, portal_name, email


@csrf_exempt
def forward_sms(request: HttpRequest) -> HttpResponse:
    """
    Flexible webhook endpoint for receiving SMS messages from iPhone shortcuts or other sources.

    Accepts:
    - GET or POST requests
    - Form data, query parameters, or JSON bodies
    - Common parameter variations (from, from_number, sender, etc.)
    - Gracefully handles validation errors

    Returns a JSON response with success status and message details.
    """
    user_phone_number = None
    portal_phone_number = None
    message = None
    portal_name = None
    email = None

    logger.info("Received Raw SMS Integration Message Request", request=request, body=request.body)

    try:
        user_phone_number, portal_phone_number, message, portal_name, email = _extract_params(request)
    except JSONDecodeError:
        logger.exception(
            "JSON decode error extracting parameters",
            request=request,
            body=request.body,
        )
        return JsonResponse({"success": True, "message": "Successfully contacted Bridge servers"}, status=HTTPStatus.OK)

    try:
        normalized_user_phone_number = SMSRepository.normalize_phone_number(user_phone_number)
        normalized_portal_phone_number = SMSRepository.normalize_phone_number(portal_phone_number)

        logger.info(
            "Processing SMS Integration Message Phone Numbers",
            user_phone_number=user_phone_number,
            normalized_user_phone_number=normalized_user_phone_number,
            portal_phone_number=portal_phone_number,
            normalized_portal_phone_number=normalized_portal_phone_number,
            message=message,
            portal_name=portal_name,
            email=email,
        )

        _ = SMSRepository.receive_message_as_webhook(
            user_phone_number=normalized_user_phone_number,
            portal_phone_number=normalized_portal_phone_number,
            body=message,
            portal_name=portal_name,
            email=email,
        )

        logger.info(
            "Successfully processed SMS Integration Message",
            user_phone_number=normalized_user_phone_number,
            portal_phone_number=normalized_portal_phone_number,
            body=message,
            portal_name=portal_name,
            email=email,
        )
        return JsonResponse(
            {
                "success": True,
                "message": "Message received and processed",
                "user_phone_number": normalized_user_phone_number,
                "portal_phone_number": normalized_portal_phone_number,
                "email": email,
                "portal": portal_name,
                "length": len(message),
            }
        )
    except InvalidMessageError as e:
        logger.exception(
            "Failed to process SMS Message",
            user_phone_number=user_phone_number,
            portal_phone_number=portal_phone_number,
            message=message,
            portal_name=portal_name,
            email=email,
        )

        # For iPhone shortcuts, we might want to be more lenient about certain validation errors
        # But let the repository handle all validations rather than bypassing them here
        # This maintains separation of concerns between the view and the repository

        return JsonResponse(
            {
                "success": False,
                "error": str(e),
                "suggestion": "If configured in iPhone shortcuts, ensure the message contains a verification code",
            },
            status=HTTPStatus.BAD_REQUEST,
        )
    except Exception:
        logger.exception("Unexpected error processing SMS")
        return JsonResponse(
            {"success": False, "error": "Server error processing message"}, status=HTTPStatus.INTERNAL_SERVER_ERROR
        )


@login_required
def sms_review_view(request: HttpRequest) -> HttpResponse:
    template = "settings/settings.html"
    context = {
        "organization": getattr(request.user, "organization", None),
        "selected_roles": request.user.roles.all(),
        "tab": request.GET.get("tab", "profile"),
        "sms_messages": SMSRepository.get_most_recent_messages(request.user),
    }

    return TemplateResponse(request, template, context)
