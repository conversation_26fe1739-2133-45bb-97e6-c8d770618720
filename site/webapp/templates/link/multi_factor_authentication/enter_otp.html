{% load customtags widget_tweaks static %}
<div id="modal-container-{{ merged_portal_credential.pk }}">
    <div class="relative w-[600px]"></div>
    <div id="modal-wrapper-{{ merged_portal_credential.pk }}"
         class="p-4 md:p-5 flex items-center justify-between rounded-t w-full">
        <div class="p-2 bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
            <div class="relative flex-col justify-start items-start flex">
                <div class="Group relative">
                    <img src="{% static 'assets/images/fin_tag.svg' %}"
                         width="38"
                         height="20"
                         alt="closeButton">
                </div>
            </div>
        </div>
        <button _="on click trigger closeModal"
                type="button"
                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <img src="{% static 'assets/images/close.svg' %}"
                 width="16"
                 height="19"
                 alt="closeButton">
            <span class="sr-only">Close modal</span>
        </button>
    </div>
    <form class="p-3"
          hx-post="{% url 'multi_factor_authentication_enter_otp' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
          hx-swap="outerHTML"
          hx-target="#modal-container-{{ merged_portal_credential.pk }}">
        <div class="px-5 pb-4 space-y-4">
            <div class="text-[#616863] text-sm font-normal font-inter leading-[21px] modal-line-item">
                Portal: <span id="encryption_modal_line_item">{{ merged_portal_credential.portal.name }}</span>
            </div>
            <div class="text-[#343d36] text-lg font-semibold font-inter leading-[27px] modal-line-item">
                Portal Provider:
                <a class="underline"
                   href="{{ merged_portal_credential.portal.portal_login_url }}">{{ merged_portal_credential.portal.name }}</a>
            </div>
        </div>
        {% csrf_token %}
        {% for field in form.visible_fields %}
            <div class="px-5 w-full">
                <div class="flex items-center mb-4">
                    <label class="text-gray-700 font-medium pr-4 w-1/5 relative"
                           for="{{ field.id_for_label }}">{{ field.label }}</label>
                    <div class="w-4/5 relative">
                        {{ field|attr:"class:w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400" }}
                        <button type="button"
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600"
                                tabindex="-1"
                                onclick="togglePasswordVisibility('{{ field.id_for_label }}', 'toggleIcon{{ forloop.counter }}')">
                            <img id="toggleIcon{{ forloop.counter }}"
                                 src="{% static 'assets/images/hide_icon.svg' %}"
                                 alt="Toggle visibility"
                                 width="20"
                                 height="20">
                        </button>
                    </div>
                </div>
            </div>
        {% endfor %}
        <div class="flex items-center p-4 md:p-4 pr-4 border-gray-200 rounded-b dark:border-gray-600">
            <button type="submit"
                    class="self-stretch h-11 w-full px-4 py-2 bg-[#924f34] rounded-lg shadow-inner justify-center items-center gap-1.5 inline-flex">
                <div class="text-white text-base font-semibold font-inter leading-normal">Submit OTP</div>
            </button>
        </div>
    </form>
</div>
