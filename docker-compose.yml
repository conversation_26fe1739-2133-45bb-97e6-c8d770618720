services:
  site:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile
      target: bridge-dev
    tty: true
    volumes:
      - ./site:/bridge-python/site
      - ~/.aws/credentials:/home/<USER>/.aws/credentials:ro
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
      - celery 
    environment:
      - APPLICATION=site
      - DJANGO_SUPERUSER_PASSWORD=asdf
      - ENVIRONMENT=${ENVIRONMENT:-dev}
      - SITE_INIT_ACTION=${SITE_INIT_ACTION:-reset}
      - AWS_PROFILE=${AWS_PROFILE:-default}

  celery:
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile
      target: bridge-dev
    tty: true
    volumes:
      - ./site:/bridge-python/site
      - ~/.aws/credentials:/home/<USER>/.aws/credentials:ro
    depends_on:
      - db
      - redis 
    environment:
      - APPLICATION=celery
      - ENVIRONMENT=${ENVIRONMENT:-dev}
      - AWS_PROFILE=${AWS_PROFILE:-default}
      # Use host.docker.internal:0 for Docker Desktop on macOS
      # If you're running an x11 server, this will allow you to display GUI applications
      # You must set the DISPLAY variable in your environment to be ":0"
      # By default for headed browsers, we use xvfb to run the browser in a virtual framebuffer.
      # - DISPLAY=host.docker.internal:0  
    deploy:
      mode: replicated
      replicas: ${NUM_CELERY_PODS:-1} 
    shm_size: '1gb'

  db:
    image: pgvector/pgvector:0.8.0-pg16
    environment:
      - POSTGRES_DB=mydatabase
      - POSTGRES_USER=myuser
      - POSTGRES_PASSWORD=mypassword
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7
    ports:
      - "6379:6379"

volumes:
  postgres_data: