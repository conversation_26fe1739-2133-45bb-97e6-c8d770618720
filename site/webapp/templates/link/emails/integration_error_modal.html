{% load customtags static %}
<div id="modal-container">
    <div class="w-full max-w-lg max-h-full">
        <!-- Modal content -->
        <div class="relative w-[600px]"></div>
        <div class="font-inter relative rounded-lg p-4">
            <!-- Modal header -->
            <div class="flex items-center justify-between px-4 pt-4 rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    <div class="FeaturedIcon w-12 h-12 px-[13px] pt-[15px] pb-[13px] bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
                        <div class="Icon w-[22px] h-5 relative flex-col justify-start items-start flex">
                            <img alt="ImageIcon"
                                 width="22"
                                 height="21"
                                 src="{% static 'assets/images/exclaimation.svg' %}">
                        </div>
                    </div>
                </h3>
                <button _="on click trigger closeModal"
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                    <img src="{% static 'assets/images/close.svg' %}"
                         class="w-3 h-3"
                         width="12"
                         height="12"
                         alt="closeButton">
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div class="mt-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Error</h3>
            </div>
            <div class="mb-4">
                {% if error_message %}<span class="text-[#924f34] text-md">{{ error_message }}</span>{% endif %}
            </div>
            <!-- Modal footer -->
            <div class="relative w-[600px]"></div>
            <div class="flex items-center p-4 md:p-4 pr-4 border-gray-200 rounded-b dark:border-gray-600">
                <button _="on click trigger closeModal"
                        class="Buttons self-stretch h-11 w-full px-4 py-2 border border-[#924f34] text-[#924f34] hover:bg-[#f7f4f2] rounded-lg justify-center items-center gap-1.5 inline-flex">
                    <div id="refresh"
                         class="BodySSemibod text-base font-semibold font-inter leading-normal">Close</div>
                </button>
            </div>
        </div>
    </div>
</div>
