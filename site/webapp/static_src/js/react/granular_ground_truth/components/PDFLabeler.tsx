import React, { useState, useRef, useEffect } from "react";
import Moveable from "moveable";
import { usePDF } from "../hooks/usePDF";
import PDFViewer from "./PDFViewer";
import IndexedBoxesList from "./IndexedBoxesList";
import JsonSchemaEditor from "./SchemaEditor/JsonSchemaEditor";
import type { BoxData, SelectionData } from "../types/pdfLabeler";
import type { JSONSchema } from "../types/jsonSchema";
import { set } from "zod";
import AddSchemaButton from "./SchemaEditor/AddSchemaButton";
import TargetSchema from "./TargetSchemaFiller/TargetSchema";

interface PDFLabelerProps {
  s3_url: string;
  raw_doc_pk: string;
}

const PDFLabeler: React.FC<PDFLabelerProps> = ({ s3_url, raw_doc_pk }) => {
  const {
    pdf,
    allPages,
    currentPageNumber,
    totalPages,
    loading,
    scale,
    setScale,
    overlaysRef,
    pdfContainerRef,
    updateCurrentPageFromScroll,
    extractTextFromBoundingBox,
  } = usePDF(s3_url);

  const [labeledBoxes, setLabeledBoxes] = useState<BoxData[]>([]);
  const [indexedBoxes, setIndexedBoxes] = useState<BoxData[]>([]);
  const [schemas, setSchemas] = useState<JSONSchema[]>([]);
  const [targetSchema, setTargetSchema] = useState<JSONSchema | null>(null);
  const [hideLabels, setHideLabels] = useState(true);
  const [isEditingTargetSchema, setIsEditingTargetSchema] = useState(false);
  const [overlays, setOverlays] = useState<Map<number, HTMLElement | null>>(
    new Map(),
  );

  // Helper function to get current page overlay
  const getCurrentPageOverlay = () => {
    return overlaysRef.current[currentPageNumber] || null;
  };

  // Create a ref object for compatibility with existing components
  const currentOverlayRef = {
    get current() {
      return getCurrentPageOverlay();
    },
  };

  const setLabeledBoxesFromServer = (data: any) => {
    let res = [];
    for (const chunk of data["raw_extractions"]) {
      res.push({
        id: chunk.id,
        content: chunk.content,
        pageNumber: chunk.page_number,
        boundingBox: {
          x: chunk.bbox_xmin,
          y: chunk.bbox_ymin,
          width: chunk.bbox_xmax - chunk.bbox_xmin,
          height: chunk.bbox_ymax - chunk.bbox_ymin,
        },
        data: chunk.data,
        schema: chunk.schema,
        isGroundTruth: chunk.is_ground_truth,
        isSaved: true,
        isValid: chunk.is_valid,
        path: chunk.path,
        targetUri: chunk.target_uri,
      });
    }

    setLabeledBoxes(res);
  };

  useEffect(() => {
    // Clear overlays when loading
    if (loading) {
      const overlays = overlaysRef.current;
      for (const pageNumber in overlays) {
        const overlay = overlays[pageNumber];
        if (overlay) {
          overlay.innerHTML = "";
        }
      }
    }
    setOverlays(
      new Map(
        Object.entries(overlaysRef.current).map(([k, v]) => [Number(k), v]),
      ),
    );
  }, [loading, allPages.length]);

  useEffect(() => {
    fetch(`/admin/webapp/rawdocument/${raw_doc_pk}/get_doc_chunks`).then(
      (response) => {
        if (!response.ok) {
          console.error(
            "Failed to fetch document chunks:",
            response.statusText,
          );
          return;
        }
        response.json().then((data) => {
          let res = [];
          for (const chunk of data["chunks"]) {
            res.push({
              id: chunk.id,
              pageNumber: chunk.page_number,
              boundingBox: {
                x: chunk.bbox_xmin,
                y: chunk.bbox_ymin,
                width: chunk.bbox_xmax - chunk.bbox_xmin,
                height: chunk.bbox_ymax - chunk.bbox_ymin,
              },
              content: chunk.content,
            });
          }

          setIndexedBoxes(res);
        });
      },
    );
    fetch(`/admin/webapp/rawdocument/${raw_doc_pk}/extracted_data`).then(
      (response) => {
        if (!response.ok) {
          console.error(
            "Failed to fetch document chunks:",
            response.statusText,
          );
          return;
        }
        response.json().then((data) => {
          setLabeledBoxesFromServer(data);
        });
      },
    );
    fetch("/admin/webapp/rawdocument/schemas").then((response) => {
      if (!response.ok) {
        console.error("Failed to fetch schemas:", response.statusText);
        return;
      }
      response.json().then((data) => {
        for (const s of data.schemas) {
          if (s.$id.endsWith("/processed_document")) {
            setTargetSchema(s);
            break;
          }
        }
        setSchemas(data.schemas);
      });
    });
  }, []);

  const handleSetSchema = (schema: JSONSchema) => {
    const uri =
      typeof schema === "object" && schema.$id ? schema.$id : undefined;
    fetch("/admin/webapp/rawdocument/schemas", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        uri: uri,
        json_schema: schema,
      }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to add new schema");
        }
        return response.json();
      })
      .then((data) => {
        for (const s of data.schemas) {
          if (s.$id.endsWith("/processed_document")) {
            setTargetSchema(s);
            break;
          }
        }
        setSchemas(data.schemas);
      })
      .catch((error) => {
        console.error("Error adding new schema:", error);
      });
  };

  const addNewSchema = (schema: JSONSchema) => {
    if (typeof schema !== "object") {
      console.error("Invalid schema data:", schema);
      return;
    }
    const newSchema: JSONSchema = {
      ...schema,
      $schema: "http://json-schema.org/draft-07/schema",
      type: schema.type,
      title: schema.title,
      $id: schema.title,
      description: schema.description,
    };
    if (schema.type === "object" && typeof newSchema === "object") {
      newSchema.properties = {};
    }
    handleSetSchema(newSchema);
  };

  const handleIndex = () => {
    fetch(`/admin/webapp/rawdocument/${raw_doc_pk}/index_document`, {
      method: "POST",
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to index document");
        }
      })
      .catch((error) => {
        console.error("Error indexing document:", error);
      });
  };

  const handleIndexAndBackfill = () => {
    fetch(
      `/admin/webapp/rawdocument/${raw_doc_pk}/index_and_backfill_document`,
      {
        method: "POST",
      },
    )
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to index document");
        }
      })
      .catch((error) => {
        console.error("Error indexing document:", error);
      });
  };
  const onDeleteBox = (boxes: BoxData[]) => {
    fetch(`/admin/webapp/rawdocument/${raw_doc_pk}/extracted_data`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        id: boxes[0].id,
      }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to add new schema");
        }
        response.json().then((data) => {
          setLabeledBoxesFromServer(data);
        });
      })
      .catch((error) => {
        console.error("Error adding new schema:", error);
      });
  };

  const onLabelBox = (boxes: BoxData[]) => {
    const box = boxes[0];
    const final_obj = boxes[boxes.length - 1];
    if (box.isSaved) {
      return;
    }
    fetch(`/admin/webapp/rawdocument/${raw_doc_pk}/extracted_data`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        uri: typeof box.schema === "object" ? box.schema?.$id : undefined,
        target_uri:
          typeof final_obj.schema === "object"
            ? final_obj.schema?.$id
            : undefined,
        raw_data: box.data,
        raw_chunk: {
          bounding_box: {
            x: box.boundingBox.x,
            y: box.boundingBox.y,
            width: box.boundingBox.width,
            height: box.boundingBox.height,
          },
          page_number: box.pageNumber,
          content: box.content,
        },
        path: box.path,
      }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to add new schema");
        }
        response.json().then((data) => {
          setLabeledBoxesFromServer(data);
        });
      })
      .catch((error) => {
        console.error("Error adding new schema:", error);
      });
  };

  const onSaveTargetSchema = (box: BoxData | null) => {
    if (box === null) {
      return;
    }
    fetch(`/admin/webapp/rawdocument/${raw_doc_pk}/submit_target_document`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        uri: typeof box.schema === "object" ? box.schema?.$id : undefined,
        target_uri:
          typeof box.schema === "object" ? box.schema?.$id : undefined,
        raw_data: box.data,
        raw_chunk: {
          bounding_box: {
            x: box.boundingBox.x,
            y: box.boundingBox.y,
            width: box.boundingBox.width,
            height: box.boundingBox.height,
          },
          page_number: box.pageNumber,
          content: box.content,
        },
        path: box.path,
      }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to add new schema");
        }
      })
      .catch((error) => {
        console.error("Error adding new schema:", error);
      });
  };

  const toggleHideShowLabels = () => {
    const newHideLabels = !hideLabels;
    setHideLabels(newHideLabels);

    // Clear all target schema boxes from all overlays when hiding
    if (newHideLabels) {
      const overlays = overlaysRef.current;
      for (const pageNumber in overlays) {
        const overlay = overlays[pageNumber];
        if (overlay) {
          // Remove target schema boxes and indexed boxes
          overlay
            .querySelectorAll(".target-schema-box, .data-index-boxes")
            .forEach((box: Element) => {
              box.remove();
            });
        }
      }
    }
    // When showing again, the individual components will handle recreation via useEffect
  };
  const setOverlayData = (pageNumber: number, element: HTMLElement | null) => {
    if (element) {
      overlaysRef.current[pageNumber] = element;
      setOverlays(
        new Map(
          Object.entries(overlaysRef.current).map(([k, v]) => [Number(k), v]),
        ),
      );
    } else {
      overlaysRef.current[pageNumber] = null;
      setOverlays(
        new Map(
          Object.entries(overlaysRef.current).map(([k, v]) => [Number(k), v]),
        ),
      );
    }
  };

  return (
    <div
      className="bg-gray-900 text-white p-4"
      style={{ height: "calc(100vh - 11rem)", minHeight: "600px" }}
    >
      {/* Navigation */}
      <div className="mb-4 flex justify-between items-center">
        {/* Left-aligned current page and scale controls */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-gray-300">Page:</span>
            <span className="text-white font-medium">{currentPageNumber}</span>
            <span className="text-gray-300">of</span>
            <span className="text-gray-300">{totalPages || "?"}</span>
          </div>
        </div>

        {/* Right-aligned index buttons */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleIndex()}
            className="border border-gray-600 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Index Document
          </button>
          <button
            onClick={() => handleIndexAndBackfill()}
            className="border border-gray-600 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Index and Backfill Document
          </button>
        </div>
      </div>

      <div
        className="flex flex-row w-full"
        style={{ height: "calc(100% - 6rem)" }}
      >
        {/* PDF Viewer */}
        <div className="flex-1 bg-gray-800 rounded-lg p-4 h-full resize-x overflow-auto">
          <PDFViewer
            allPages={allPages}
            scale={scale}
            overlaysRef={overlaysRef}
            setOverlayData={setOverlayData}
            loading={loading}
            onCurrentPageChange={updateCurrentPageFromScroll}
          />
        </div>

        {/* Sidebar - Independent scrolling */}
        <div className="resize-x overflow-auto w-2/5 bg-gray-800 rounded-lg ml-4 flex flex-col h-full">
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center space-x-2 mb-4">
              <button
                onClick={() => toggleHideShowLabels()}
                className="border border-gray-600 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {hideLabels ? "Show All Labels" : "Hide All Labels"}
              </button>
              <button
                onClick={() => setIsEditingTargetSchema(!isEditingTargetSchema)}
                className="border border-gray-600 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isEditingTargetSchema ? "Stop Editing" : "Edit Schema"}
              </button>
              <select
                className="border border-gray-600 px-4 py-2 bg-gray-800 text-white rounded ml-2 max-w-full max-h-full h-10"
                value={
                  typeof targetSchema === "object" && targetSchema
                    ? targetSchema.$id
                    : ""
                }
                onChange={(e) => {
                  const selectedId = e.target.value;
                  const found = schemas.find(
                    (s) =>
                      typeof s === "object" &&
                      s !== null &&
                      s.$id === selectedId,
                  );
                  setTargetSchema(found || null);
                }}
              >
                {schemas
                  .filter(
                    (s) =>
                      typeof s === "object" &&
                      s !== null &&
                      typeof s.title === "string" &&
                      s.title.startsWith("/target"),
                  )
                  .sort((a, b) => {
                    if (a.title.endsWith("/processed_document")) return -1;
                    if (a.title < b.title) return -1;
                    if (a.title > b.title) return 1;
                    return 0;
                  })
                  .map((s) => (
                    <option key={s.$id} value={s.$id}>
                      {s.title}
                    </option>
                  ))}
              </select>
            </div>
          </div>

          {/* Scrollable content area */}
          {loading ? (
            <div></div>
          ) : (
            <div className="flex-1 overflow-y-auto p-4">
              {isEditingTargetSchema ? (
                <JsonSchemaEditor
                  key={
                    typeof targetSchema === "object" && targetSchema.$id
                      ? targetSchema.$id
                      : undefined
                  }
                  setSchema={handleSetSchema}
                  schema={targetSchema}
                  schemas={schemas}
                  collapse={false}
                  className="shadow-lg animate-in border-border/50 backdrop-blur-sm"
                />
              ) : (
                <div>
                  {schemas.map((schema, idx) => {
                    if (
                      typeof schema !== "object" ||
                      typeof targetSchema !== "object" ||
                      schema === null ||
                      targetSchema === null ||
                      schema.$id !== targetSchema.$id
                    ) {
                      return;
                    }
                    return (
                      <TargetSchema
                        key={schema.$id}
                        schema={schema}
                        is_required={true}
                        labeledBoxes={labeledBoxes}
                        path="$"
                        hideLabels={hideLabels}
                        overlays={overlays}
                        scale={scale}
                        currentPageNumber={currentPageNumber}
                        onSaveTargetSchema={onSaveTargetSchema}
                        onDeleteBox={onDeleteBox}
                        onLabelBox={onLabelBox}
                        extractTextFromBoundingBox={extractTextFromBoundingBox}
                      />
                    );
                  })}
                </div>
              )}

              {/* JSON Schema Editor */}
              <h2 className="text-xl font-semibold mb-4 mt-6 text-white">
                Available Schemas
              </h2>
              <AddSchemaButton addNewSchema={addNewSchema} schemas={schemas} />
              <div className="flex-1 bg-gray-800 rounded-lg p-4">
                {schemas.map((schema) => (
                  <JsonSchemaEditor
                    key={
                      typeof schema === "object" && schema.$id
                        ? schema.$id
                        : undefined
                    }
                    setSchema={handleSetSchema}
                    schema={schema}
                    schemas={schemas}
                    className="shadow-lg animate-in border-border/50 backdrop-blur-sm"
                  />
                ))}
              </div>
              <IndexedBoxesList
                indexedBoxes={indexedBoxes}
                currentPageNumber={currentPageNumber}
                overlaysRef={overlaysRef}
                scale={scale}
                hideLabels={hideLabels}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PDFLabeler;
