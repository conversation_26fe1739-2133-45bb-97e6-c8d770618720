import typing
from collections.abc import AsyncGenerator, Generator
from contextlib import asynccontextmanager, contextmanager

import msal
import requests
import structlog
from asgiref.sync import sync_to_async
from django.conf import settings
from requests.adapters import HTTPAdapter
from urllib3 import Retry

from webapp.models.emails import CustomerEmailCredential
from webapp.models.portal import MergedPortalCredential, MFAType
from webapp.models.user import BridgeUser

if typing.TYPE_CHECKING:
    from retrieval.core.strategy import RetrievalManager
logger = structlog.get_logger(__name__)

CLIENT_ID = settings.OAUTH_SECRET.get("CLIENT_ID", None)
CLIENT_SECRET = settings.OAUTH_SECRET.get("CLIENT_SECRET", None)
AUTHORITY = "https://login.microsoftonline.com/common"  # Use to control the account that the user authenticates with


@asynccontextmanager
async def forwarding_rules_context(manager: "RetrievalManager") -> AsyncGenerator[None, None]:
    msft_session = None
    rule_id = None
    is_email_mfa = False
    is_msft_email = False
    try:
        is_email_mfa = (await manager.get_multi_factor_authentication_type()) == MFAType.EMAIL
        is_msft_email = (
            await manager.get_email_provider_bridge_managed()
        ) == CustomerEmailCredential.EmailProvider.OUTLOOK
        if is_email_mfa and is_msft_email and not manager.is_demo():
            # TODO: If we cannot make a session, we should raise here instead of waiting for an OTP
            # TODO: How to we refresh the main token? We only refresh the access token here.
            msft_session = await _amake_microsoft_session(await manager.get_merged_portal_credential_pk())

            otp_rules = manager.email_otp_rules()
            display_name = (
                f"Bridge - Capture MFA {await manager.get_portal_name()} - {await manager.get_retrieval_pk()}"
            )
            message_rule = {
                "displayName": display_name,
                "sequence": 1,
                "conditions": otp_rules,
                "actions": {
                    "forwardTo": [
                        {
                            "emailAddress": {
                                "address": f"mfa+{await manager.get_retrieval_pk()}@{settings.DOMAIN_NAME}",
                            },
                        },
                    ],
                    "stopProcessingRules": False,
                },
                "isEnabled": True,
            }

            rule_id = add_or_update_rule(msft_session, message_rule)

        yield

    finally:
        if rule_id is not None and msft_session is not None and is_msft_email and is_email_mfa:
            delete_existing_rule(msft_session, rule_id)
        else:
            logger.info("No need to delete rule", rule_id=rule_id)


def create_or_update_credential_and_forwarding_rules_msft(email: str, cache_json: str, user: BridgeUser) -> None:
    cec, _ = CustomerEmailCredential.objects.update_or_create(
        user=user,
        email=email,
        defaults={
            "email_provider_bridge_managed": CustomerEmailCredential.EmailProvider.OUTLOOK,
            "email_integration_status": CustomerEmailCredential.EmailIntegrationStatus.BRIDGE_MANAGED_FILTERS,
            "secret_format": CustomerEmailCredential.EmailSecretFormat.MSAL_CACHE,
            "secret_format_version": 1,
        },
    )
    cec.set_secret(cache_json)


@contextmanager
def serializeable_token_cache_context_manager(
    customer_email_credential: CustomerEmailCredential,
) -> Generator[msal.SerializableTokenCache | None, None, None]:
    # TODO: need to schedule a rehydration daily/weekly to make sure noone goes out of spec.
    cache = None
    try:
        logger.info("Deserializing msal cache", customer_email_credential=customer_email_credential)
        cache = msal.SerializableTokenCache()
        cache.deserialize(customer_email_credential.get_secret())
    except Exception:
        logger.exception("Failed to deserialize cache", customer_email_credential=customer_email_credential)
    yield cache
    try:
        if cache:
            logger.info("Serializing cache", customer_email_credential=customer_email_credential)
            customer_email_credential.set_secret(cache.serialize())
        else:
            logger.error("No cache to serialize", customer_email_credential=customer_email_credential)
    except Exception:
        logger.exception("Failed to serialize cache", customer_email_credential=customer_email_credential)


def get_bearer_token_msft(merged_portal_credential: MergedPortalCredential) -> str | None:
    if merged_portal_credential.multi_factor_authentication_type != MFAType.EMAIL:
        logger.error("OTP method is not email")
        raise ValueError
    if (
        merged_portal_credential.multi_factor_authentication.receiving_email is None
        or merged_portal_credential.multi_factor_authentication.receiving_email.email_provider_bridge_managed
        != CustomerEmailCredential.EmailProvider.OUTLOOK
    ):
        logger.error("Email provider is not outlook")
        raise ValueError
    return get_bearer_token_msft_with_customer_email_credential(
        merged_portal_credential.multi_factor_authentication.receiving_email
    )


def get_bearer_token_msft_with_customer_email_credential(
    customer_email_credential: CustomerEmailCredential,
) -> str | None:
    if customer_email_credential.email_provider_bridge_managed != CustomerEmailCredential.EmailProvider.OUTLOOK:
        logger.error("Email provider is not outlook")
        raise ValueError
    with serializeable_token_cache_context_manager(customer_email_credential) as cache:
        app = msal.ConfidentialClientApplication(
            client_id=CLIENT_ID,
            client_credential=CLIENT_SECRET,
            authority=AUTHORITY,
            token_cache=cache,
        )
        scopes = ["MailboxSettings.ReadWrite", "User.Read"]
        accounts = app.get_accounts()
        if len(accounts) == 0:
            logger.error("No account found", email=customer_email_credential.email)
            return None
        account = accounts[0]
        if account is None:
            logger.error("No account found", email=customer_email_credential.email)
            raise ValueError
        logger.info("Found account", account=account)
        result = app.acquire_token_silent_with_error(scopes, account=account, force_refresh=True)
        if result is None or "access_token" not in result:
            logger.error("Failed to get MSFT access token", result=result)
            return None
        return result.get("access_token", None)


@sync_to_async
def _amake_microsoft_session(merged_portal_credential_pk: str) -> requests.Session:
    merged_portal_credential = MergedPortalCredential.objects.get(pk=merged_portal_credential_pk)
    return _make_microsoft_session(merged_portal_credential)


def _make_microsoft_session(merged_portal_credential: MergedPortalCredential) -> requests.Session:
    access_token = get_bearer_token_msft(merged_portal_credential)
    retry_strategy = Retry(
        total=5,
        backoff_factor=0.1,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS"],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session = requests.Session()
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    session.headers.update(
        {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
    )
    return session


def make_microsoft_session_with_customer_email_credential(
    customer_email_credential: CustomerEmailCredential,
) -> requests.Session:
    access_token = get_bearer_token_msft_with_customer_email_credential(customer_email_credential)
    retry_strategy = Retry(
        total=5,
        backoff_factor=0.1,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS"],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session = requests.Session()
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    session.headers.update(
        {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
    )
    return session


def _make_microsoft_request(
    msft_session: requests.Session,
    method: str = "GET",
    extra_url: str = "",
    base_url: str = "https://graph.microsoft.com/v1.0/me/mailFolders/Inbox/messageRules",
    body: dict[str, str] | None = None,
) -> dict:
    endpoint = f"{base_url}{extra_url}"
    response = msft_session.request(method, endpoint, json=body, timeout=10)
    if not (200 <= response.status_code < 300):  # noqa: PLR2004
        logger.error("Failed to make MSFT request", endpoint=endpoint)
        logger.error(response)
    return response


def get_existing_rules(msft_session: requests.Session) -> list:
    response = _make_microsoft_request(msft_session)
    return response.json().get("value", [])


def get_existing_rules_one(msft_session: requests.Session, rule_id: str) -> dict:
    return _make_microsoft_request(msft_session, extra_url=f"/{rule_id}").json()


def delete_existing_rule(msft_session: requests.Session, rule_id: str) -> dict:
    logger.info("Deleting existing rule", rule_id=rule_id)
    res = _make_microsoft_request(msft_session, extra_url=f"/{rule_id}", method="DELETE")
    existing_rules = get_existing_rules(msft_session)
    existing_rule_id = None
    for rule in existing_rules:
        if rule.get("id") == rule_id:
            # Further checks can be added here to compare conditions and actions
            logger.error("Failed to delete", rule_id=rule_id)
            existing_rule_id = rule_id
            break
    if existing_rule_id is None:
        logger.info("Successfully deleted rule", rule_id=rule_id)
    return res


def add_or_update_rule(msft_session: requests.Session, message_rule: dict) -> dict:
    display_name = message_rule.get("displayName")
    existing_rules = get_existing_rules(msft_session)
    existing_rule_id = None
    for rule in existing_rules:
        if rule.get("displayName") == display_name:
            # Further checks can be added here to compare conditions and actions
            existing_rule_id = rule.get("id")
            logger.info("A rule already exists", display_name=display_name)
            break

    if existing_rule_id is not None:
        response = _make_microsoft_request(
            msft_session, method="PATCH", extra_url=f"/{existing_rule_id}", body=message_rule
        )
        logger.info("Forwarding rule has been patched", display_name=display_name)
    else:
        response = _make_microsoft_request(msft_session, method="POST", body=message_rule)
        logger.info("Forwaring rule has been created", dispaly_name=display_name)
    if response.status_code in {201, 200}:
        logger.info("Forwarding rule with filter added successfully", display_name=display_name)
    else:
        logger.error(
            "Failed to add forwarding rule",
            display_name=display_name,
            status_code=response.status_code,
            error=response.text,
        )
        # TODO: retry
    existing_rules = get_existing_rules(msft_session)
    existing_rule_id = None
    for rule in existing_rules:
        if rule.get("displayName") == display_name:
            # Further checks can be added here to compare conditions and actions
            existing_rule_id = rule.get("id")
            logger.info("Have check that rule exists", display_name=display_name)
            break
    if existing_rule_id is None:
        logger.error("Failed to find forwarding rule", display_name=display_name)
    return existing_rule_id
