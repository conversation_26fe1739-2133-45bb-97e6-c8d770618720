"""
URL configuration for bridge_project project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/

Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))

"""

import re

from django.conf import settings
from django.contrib import admin
from django.urls import include, path, re_path
from django.views.static import serve

from .api import external_api_v1, internal_api

urlpatterns = [
    path("admin/", admin.site.urls),
    path("up/", include("health_check.urls")),
    path("accounts/", include("allauth.urls")),
    path("", include("webapp.urls")),
    path("api/", internal_api.urls),
    path("api/v1/", external_api_v1.urls),
    path("__reload__/", include("django_browser_reload.urls")),
    # Logic stolen from django.conf.urls.static,
    # since we don't use runserver, we need to serve static files when DEBUG=True
    re_path(
        rf"^{re.escape(settings.STATIC_URL.lstrip('/'))}(?P<path>.*)$",
        serve,
        kwargs={"document_root": settings.STATIC_ROOT},
    ),
    path("hijack/", include("hijack.urls")),
    path("__debug__/", include("debug_toolbar.urls")),
]
