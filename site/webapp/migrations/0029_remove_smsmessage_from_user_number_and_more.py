# Generated by Django 5.1.8 on 2025-04-09 18:33

import phonenumber_field.modelfields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0028_investingentity_additional_entity_types_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='smsmessage',
            name='from_user_number',
        ),
        migrations.AddField(
            model_name='multifactorauthentication',
            name='phone_type',
            field=models.CharField(blank=True, choices=[('ap', 'Apple'), ('ad', 'Android'), ('ot', 'Other')], max_length=2),
        ),
        migrations.AddField(
            model_name='smsmessage',
            name='portal_phone_number',
            field=phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None),
        ),
        migrations.AddField(
            model_name='smsmessage',
            name='user_phone_number',
            field=phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None),
        ),
    ]
