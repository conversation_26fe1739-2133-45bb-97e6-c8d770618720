{% load static customtags widget_tweaks %}
<div id="modal-container-{{ merged_portal_credential.pk }}">
    <!-- Modal content -->
    <div class="relative">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-5 rounded-t dark:border-gray-600">
            <button _="on click trigger closeModal"
                    type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                <img src="{% static 'assets/images/close.svg' %}"
                     width="16"
                     height="19"
                     alt="closeButton">
                <span class="sr-only">Close modal</span>
            </button>
        </div>
        <!-- Modal body -->
        <div class="px-5 pb-4">
            <div class="text-[#343d36] text-lg font-semibold font-inter leading-[27px] pb-5">
                Portal provider: {{ merged_portal_credential.portal.name }}
            </div>
            <div class="text-[#616863] text-sm font-semibold font-inter leading-[21px] pb-2">
                Please keep an eye out for your one-time password, to input once the screen loads.
            </div>
            <div class="flex flex-col justify-center items-center h-32">
                <lottie-player src="{% static 'assets/images/bridge_green_txt_loading_animation.json' %}" class="w-36 h-36" background="transparent" speed="1" loop autoplay></lottie-player>
            </div>
            <div class="text-[#343d36] text-lg font-semibold font-inter leading-[27px] modal-line-item flex justify-center items-center gap-2">
                <div id="status-container"
                     hx-get="{% url 'check_retrieval_status' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
                     hx-trigger="every 2s"
                     hx-target="#status-container"
                     hx-swap="innerHTML">
                    <div id="retrieval_status"
                         class="text-[#616863] font-normal font-inter text-center">Loading...</div>
                </div>
            </div>
        </div>
    </div>
</div>
