import re
import uuid

import structlog
from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.db import models, transaction
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from django.urls import URLPattern, path, reverse
from django.utils.html import format_html
from ml_app.core.data_classes.state import BootstrapState
from ml_app.core.state_lib import delete_state, get_li2doc2pk, get_state, reduce_state, save_state
from ml_app.tasks.doc_vault_tasks import predict_line_items, stateful_process_doc_vault_singleton_backfill
from retrieval.tasks import login_portal_task

from webapp.models import (
    LineItem,
    Portal,
)
from webapp.models.documents import RawDocument
from webapp.models.portal import MergedPortalCredential
from webapp.models.retrieval import Retrieval
from webapp.views.utils import redirect_with_url_params

BUCKET = settings.AWS_STORAGE_BUCKET_NAME

logger = structlog.get_logger(__name__)


class LineItemPartialCreateForm(forms.Form):
    client_legal_name = forms.CharField(label="Client Legal Name")
    entity_legal_name = forms.CharField(label="Entity Legal Name")
    investment_fund_legal_name = forms.CharField(label="Investment Fund Legal Name")
    investment_managing_firm_name = forms.CharField(label="Investment Managing Firm Name")


class SelectPortalForm(forms.Form):
    def __init__(self, *args, merged_portal_credential: MergedPortalCredential | None = None, **kwargs) -> None:  # noqa: ANN002, ANN003
        super().__init__(*args, **kwargs)
        portal_field = self.fields["portal"]
        if not isinstance(portal_field, forms.ModelChoiceField):
            raise TypeError
        if merged_portal_credential is None:
            portal_field.queryset = Portal.objects.none()
        else:
            portal_field.queryset = Portal.objects.filter(organization=merged_portal_credential.organization)

    portal: "forms.ModelChoiceField[Portal]" = forms.ModelChoiceField(queryset=None)


@admin.action(description="Reassign portal")
def reassign_portal_action(
    modeladmin: "MergedPortalCredentialAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> HttpResponseRedirect | None:
    raw_pks = queryset.values_list("pk", flat=True)
    selected = ",".join([str(pk) for pk in raw_pks])
    merged_portal_credentials = MergedPortalCredential.objects.active().filter(pk__in=raw_pks).all()
    if merged_portal_credentials.count() != 1:
        messages.error(request, "Please only select one non-deleted MPC")
        return None
    return redirect_with_url_params("admin:webapp_reassign_portal_for_mpc", pks=selected)


def reassign_portal_view(request: HttpRequest) -> HttpResponse:
    template = "admin/webapp/lineitem/reassign_portal.html"
    raw_pks = request.GET.get("pks", "")
    pks = raw_pks.split(",")
    merged_portal_credential = MergedPortalCredential.objects.active().filter(pk__in=pks).first()
    if merged_portal_credential is None:
        messages.error(request, "Please select a valid merged portal credential")
        return HttpResponseRedirect(reverse("admin:webapp_mergedportalcredential_changelist"))
    if request.method == "GET":
        return TemplateResponse(
            request,
            template,
            {
                "form": SelectPortalForm(merged_portal_credential=merged_portal_credential),
            },
        )
    if request.method == "POST":
        form = SelectPortalForm(merged_portal_credential=merged_portal_credential, data=request.POST)
        if form.is_valid():
            logger.info("Reassigning portal", pks=pks, request=request.POST, form=form.cleaned_data)
            portal = form.cleaned_data["portal"]
            if not isinstance(portal, Portal):
                raise TypeError

            with transaction.atomic():
                merged_portal_credential.portal = portal
                merged_portal_credential.save()
                messages.success(request, f"Portal reassigned successfully MPC ({merged_portal_credential.pk})")
                merged_portal_credential.portal_credential.portal = portal
                messages.success(
                    request, f"Portal reassigned successfully PC ({merged_portal_credential.portal_credential.pk})"
                )
                merged_portal_credential.portal_credential.save()
            return HttpResponseRedirect(
                reverse("admin:webapp_mergedportalcredential_change", kwargs={"object_id": merged_portal_credential.pk})
            )

    return HttpResponse("Invalid request method", status=405)


@admin.action(description="Create Line Item for this MPC")
def create_line_item_for_mpc_action(
    modeladmin: "MergedPortalCredentialAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> HttpResponseRedirect | None:
    raw_pks = queryset.values_list("pk", flat=True)
    selected = ",".join([str(pk) for pk in raw_pks])
    merged_portal_credentials = MergedPortalCredential.objects.active().filter(pk__in=raw_pks).all()
    if merged_portal_credentials.count() != 1:
        messages.error(request, "Please only select one non-deleted MPC")
        return None
    return redirect_with_url_params("admin:webapp_create_line_item_for_mpc", pks=selected)


def create_line_item_for_mpc_view(request: HttpRequest) -> HttpResponse:
    template = "admin/webapp/lineitem/create_line_item_for_mpc.html"
    raw_pks = request.GET.get("pks", "")
    pks = raw_pks.split(",")
    merged_portal_credential = MergedPortalCredential.objects.all().active().filter(pk__in=pks).first()
    if merged_portal_credential is not None and request.method == "GET":
        return TemplateResponse(request, template, {"form": LineItemPartialCreateForm()})
    if merged_portal_credential is not None and request.method == "POST":
        form = LineItemPartialCreateForm(request.POST)
        if form.is_valid():
            try:
                line_item = LineItem.create_from_merged_portal_credential(
                    merged_portal_credential=merged_portal_credential,
                    client_legal_name=form.cleaned_data["client_legal_name"],
                    entity_legal_name=form.cleaned_data["entity_legal_name"],
                    investment_fund_legal_name=form.cleaned_data["investment_fund_legal_name"],
                    investment_managing_firm_name=form.cleaned_data["investment_managing_firm_name"],
                )
            except Exception:
                logger.exception("Error creating line item")
                messages.error(request, "Error creating line item, likely due != CustomerEmailCredential")
                return TemplateResponse(request, template, {"form": form})
            messages.success(request, "Line item created successfully")
            return HttpResponseRedirect(reverse("admin:webapp_lineitem_change", kwargs={"object_id": line_item.pk}))
    return HttpResponse("Invalid request method", status=405)


@admin.action(description="Trigger a retrieval and manage predictions")
def backfill_portal_and_bootstrap(
    modeladmin: "MergedPortalCredentialAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> HttpResponseRedirect | None:
    raw_pks = queryset.values_list("pk", flat=True)
    merged_portal_credentials = MergedPortalCredential.objects.all().active().filter(pk__in=raw_pks).all()
    if merged_portal_credentials.count() != 1:
        messages.error(request, "Please only select one non-deleted MPC")
        return None
    merged_portal_credential = merged_portal_credentials.first()
    user = merged_portal_credential.created_by
    if user is None:
        messages.error(request, "Merged Portal Credential must have a user")
        return None
    if merged_portal_credential is not None:
        retrieval = Retrieval.create(user=user, merged_portal_credential=merged_portal_credential)
    retrieval_id = str(retrieval.id)
    task = login_portal_task.apply_async(kwargs={"retrieval_id": retrieval_id})
    retrieval.task_id = task.id
    retrieval.is_backfill = True
    retrieval.save()
    messages.success(
        request,
        f"Retrieval started successfully for MPC ({merged_portal_credential} {merged_portal_credential.pk})"
        f" with retrieval ID {task.id}",
        f" with task ID {task.id}",
    )
    return redirect_with_url_params("admin:webapp_bootstrap_mpc", pk=str(merged_portal_credential.pk))


@admin.action(description="Upload Documents")
def upload_document(
    modeladmin: "MergedPortalCredentialAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> HttpResponseRedirect | None:
    raw_pks = queryset.values_list("pk", flat=True)
    merged_portal_credentials = MergedPortalCredential.objects.all().active().filter(pk__in=raw_pks).all()
    if merged_portal_credentials.count() != 1:
        messages.error(request, "Please only select one non-deleted MPC")
        return None
    merged_portal_credential = merged_portal_credentials.first()
    user = merged_portal_credential.created_by
    if user is None:
        messages.error(request, "Merged Portal Credential must have a user")
        return None
    return redirect_with_url_params("admin:webapp_rawdocument_raw_doc_upload", pk=merged_portal_credential.pk)


@admin.action(description="Mark as completely backfilled")
def mark_as_backfilled(
    modeladmin: "MergedPortalCredentialAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> None:
    raw_pks = queryset.values_list("pk", flat=True)
    merged_portal_credentials = MergedPortalCredential.objects.all().active().filter(pk__in=raw_pks).all()
    for mpc in merged_portal_credentials:
        messages.success(request, f"Marking MPC {mpc.pk} as backfilled")
        mpc.is_backfilled = True
        mpc.save()


def get_args(k: str, prefix: str = "") -> list[str]:
    """
    Extracts the arguments from a string formatted like 'prefix[1][a][b]'.
    Returns a list of keys, e.g., ['1', 'a', 'b'].
    """
    pattern = rf"{re.escape(prefix)}((?:\[\w+\])*)$" if prefix else r"^[^\[]*((?:\[\w+\])*)$"
    matches = re.match(pattern, k)
    if not matches:
        return []
    keys = re.findall(r"\[(\w+)\]", matches.group(1))
    if not keys:
        return []
    return keys


def parse_array(prefix: str, data: dict) -> dict:
    results = {}
    for k, v in data.items():
        keys = get_args(k, prefix=prefix)
        if not keys:
            continue
        d = results
        for i, key in enumerate(keys):
            if i == len(keys) - 1:
                d[key] = v
            else:
                if key not in d or not isinstance(d[key], dict):
                    d[key] = {}
                d = d[key]
    return results


def bootstrap_mpc_view(request: HttpRequest) -> HttpResponse:  # noqa: C901, PLR0912, PLR0915
    template = "admin/webapp/merged_portal_credential/bootstrap_mpc.html"
    pk = request.GET.get("pk")
    merged_portal_credential = MergedPortalCredential.objects.get(pk=pk)
    retrievals = merged_portal_credential.retrievals.filter(is_backfill=True).order_by("-created_at")
    state = get_state(merged_portal_credential, BootstrapState)
    li2doc2pk = get_li2doc2pk(state)
    if request.method == "GET":
        return TemplateResponse(
            request,
            template,
            {
                "merged_portal_credential": merged_portal_credential,
                "retrievals": retrievals,
                "real_line_items": merged_portal_credential.line_items.filter(deleted_at__isnull=True),
                "state": state,
                "state_items": state.items() if state else {},
                "RetrievalStatus": Retrieval.RetrievalStatus,
                "li2doc2pk": li2doc2pk,
            },
        )

    if request.method == "POST":
        logger.info("Bootstrap MPC view POST request", request=request.POST, pk=pk)
        action = request.POST.get("submit")
        action_args = get_args(action)
        good_line_items = parse_array("good_line_items", request.POST)
        real_line_items = parse_array("real_line_items", request.POST)
        new_line_item = parse_array("new_line_items", request.POST)
        if state is None:
            state = {}
        if action == "predict_line_items":
            predict_line_items.apply_async(
                kwargs={"merged_portal_credential_pk": str(merged_portal_credential.pk)},
                queue="medium",
            )

        if action == "refresh-reduce":
            state = reduce_state(merged_portal_credential)

        if action == "add_line_item":
            if state is not None and "line_items" not in state:
                state["line_items"] = {
                    "accurate_line_items": [],
                    "wrong_line_items": [],
                }
            if new_line_item in state["line_items"]["accurate_line_items"]:
                messages.error(request, "Line item already exists in accurate line items")
            else:
                state["line_items"]["accurate_line_items"].append(new_line_item)
                save_state(merged_portal_credential, state)

        if action.startswith("delete_line_item"):
            old = state["line_items"]["accurate_line_items"].pop(int(action_args[0]))
            if old in state["line_items"]["wrong_line_items"]:
                messages.error(request, "Line item already exists in wrong line items")
            else:
                state["line_items"]["wrong_line_items"].append(old)
                save_state(merged_portal_credential, state)

        if action.startswith("delete_real_line_item"):
            real = real_line_items[action_args[0]]
            LineItem.objects.get(pk=real["pk"]).soft_delete()
            del real["pk"]
            state["line_items"]["accurate_line_items"].append(real)
            save_state(merged_portal_credential, state)

        if action.startswith("delete_permanently_line_item"):
            state["line_items"]["wrong_line_items"].pop(int(action_args[0]))
            save_state(merged_portal_credential, state)

        if action.startswith("promote_line_item"):
            old = state["line_items"]["wrong_line_items"].pop(int(action_args[0]))
            if old in state["line_items"]["accurate_line_items"]:
                messages.error(request, "Line item already exists in accurate line items")
            else:
                state["line_items"]["accurate_line_items"].append(old)
                save_state(merged_portal_credential, state)

        if action.startswith("promote_real_line_item"):
            idx = int(action_args[0])
            to_promote = good_line_items.get(str(idx))
            try:
                line_item = LineItem.create_from_merged_portal_credential(
                    merged_portal_credential=merged_portal_credential,
                    client_legal_name=to_promote["client_investor_name"],
                    entity_legal_name=to_promote["investing_entity_name"],
                    investment_fund_legal_name=to_promote["investment_name"],
                    investment_managing_firm_name="unknown",
                )
            except Exception:
                logger.exception("Error creating line item")
                messages.error(request, "Error creating line item")
            else:
                messages.success(request, f"Line item created successfully {line_item.pk}")
                save_state(merged_portal_credential, state)

        if action.startswith("save_line_item"):
            idx = int(action_args[0])
            old = state["line_items"]["accurate_line_items"].pop(idx)
            new = good_line_items[str(idx)]
            if old == new:
                messages.error(request, "Line item has not changed")
            elif new in state["line_items"]["accurate_line_items"]:
                messages.error(request, "Line item already exists in accurate line items")
            else:
                state["line_items"]["accurate_line_items"].insert(idx, new)
                if old in state["line_items"]["wrong_line_items"]:
                    messages.warning(request, "Line item already exists in wrong line items")
                else:
                    state["line_items"]["wrong_line_items"].append(old)
                save_state(merged_portal_credential, state)
        if action.startswith("predict_documents"):
            if not merged_portal_credential.line_items.exists():
                messages.error(request, "Cannot predict documents without line items")
                return HttpResponseRedirect(
                    reverse("admin:webapp_bootstrap_mpc") + "?pk=" + str(merged_portal_credential.pk)
                )
            retrieval_ids = merged_portal_credential.retrievals.values_list("pk", flat=True)
            raw_docs = RawDocument.objects.filter(retrieval_id__in=retrieval_ids).order_by(
                "-original_document_type", "-original_posted_date"
            )

            raw_doc_pks = [raw_doc.pk for raw_doc in raw_docs]
            selected = ",".join([str(pk) for pk in raw_doc_pks])
            res = stateful_process_doc_vault_singleton_backfill.apply_async(
                kwargs={"merged_portal_credential_pk": str(merged_portal_credential.pk)},
                queue="medium",
            )
            messages.success(request, "Prediction task started successfully")
            s_id = str(uuid.uuid4())
            request.session[s_id] = selected
            return redirect_with_url_params(
                "admin:webapp_rawdocument_bulk_label", session_post_data=s_id, task_id=res.task_id
            )
    return HttpResponseRedirect(reverse("admin:webapp_bootstrap_mpc") + "?pk=" + str(merged_portal_credential.pk))


def check_line_items(line_items: models.QuerySet[LineItem], accurate_line_items: list[dict[str, str]]) -> bool:
    s_li = {
        (li.investing_entity.client.legal_name, li.investing_entity.legal_name, li.investment.legal_name)
        for li in line_items
    }
    s_la = {
        (li["client_investor_name"], li["investing_entity_name"], li["investment_name"]) for li in accurate_line_items
    }
    return s_li == s_la


@admin.action(description="Delete Past Predictions")
def delete_past_predictions(
    modeladmin: "MergedPortalCredentialAdmin",  # noqa: ARG001
    request: HttpRequest,
    queryset: models.QuerySet,
) -> HttpResponseRedirect | None:
    raw_pks = queryset.values_list("pk", flat=True)
    merged_portal_credentials = MergedPortalCredential.objects.active().filter(pk__in=raw_pks).all()
    if merged_portal_credentials.count() != 1:
        messages.error(request, "Please only select one non-deleted MPC")
        return None
    delete_state(merged_portal_credentials.first())


@admin.register(MergedPortalCredential)
class MergedPortalCredentialAdmin(admin.ModelAdmin):
    actions = (
        create_line_item_for_mpc_action,
        reassign_portal_action,
        backfill_portal_and_bootstrap,
        delete_past_predictions,
        upload_document,
        mark_as_backfilled,
    )
    list_display = (
        "pk",
        "organization",
        "is_deleted",
        "num_line_items",
        "valid_creds",
        "is_backfilled",
        "scheduled",
        "portal",
        "username",
        "multi_factor_authentication_type",
        "prediction_page_url",
    )
    list_filter = ("organization", "deleted_at", "portal")
    search_fields = ("pk", "organization__name", "portal__name")
    ordering = ("-organization", "portal")
    readonly_fields = ("line_items_url",)
    raw_id_fields = (
        "last_retrieval",
        "last_user_login_validation_retrieval",
        "portal_credential",
        "multi_factor_authentication",
        "user_forwarding_rule",
    )

    def line_items_url(self, obj: MergedPortalCredential) -> str:
        count = obj.line_items.count()
        return format_html(
            "<a href='{}?merged_portal_credential__id__exact={}'>View Line Items ({})</a>",
            reverse("admin:webapp_lineitem_changelist"),
            str(obj.pk),
            count,
        )

    def prediction_page_url(self, obj: MergedPortalCredential) -> str:
        return format_html(
            "<a href='{}?pk={}'>View Predictions</a>",
            reverse("admin:webapp_bootstrap_mpc"),
            str(obj.pk),
        )

    def num_line_items(self, obj: MergedPortalCredential) -> int:
        return obj.line_items.count()

    def valid_creds(self, obj: MergedPortalCredential) -> bool:
        if obj.last_user_login_validation_retrieval is None:
            return False
        return obj.last_user_login_validation_retrieval.user_login_status == Retrieval.UserLoginStatus.SUCCESS_LOGGED_IN

    valid_creds.boolean = True

    def scheduled(self, obj: MergedPortalCredential) -> bool:
        return obj.retrievals.filter(
            is_backfill=False, retrieval_status=Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL
        ).exists()

    scheduled.boolean = True

    def get_urls(self) -> list[URLPattern]:
        return [
            path(
                "create_line_item_for_mpc",
                self.admin_site.admin_view(create_line_item_for_mpc_view),
                name="webapp_create_line_item_for_mpc",
            ),
            path(
                "reassign_portal_for_mpc",
                self.admin_site.admin_view(reassign_portal_view),
                name="webapp_reassign_portal_for_mpc",
            ),
            path(
                "bootstrap_mpc",
                self.admin_site.admin_view(bootstrap_mpc_view),
                name="webapp_bootstrap_mpc",
            ),
            *super().get_urls(),
        ]

    def is_deleted(self, obj: MergedPortalCredential) -> bool:
        return obj.deleted_at is not None

    is_deleted.boolean = True
