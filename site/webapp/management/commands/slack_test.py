from argparse import ArgumentParser

import structlog
from django.core.management.base import BaseCommand
from django_extensions.management.utils import signalcommand

from webapp.utils.slack import send_slack_thread_messages

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Send a slack message to a channel. Feel free to delete"

    # AWS_PROFILE=prod ENVIRONMENT=dev ./site/manage.py slack_test -c "#tech" -m "hello world"
    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("-m", "--message", type=str)
        parser.add_argument("-c", "--channel", type=str)

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002
        message = kwargs.get("message")
        channel = kwargs.get("channel")
        logger.info("sending message to slack", message=message, channel=channel)
        if channel is None or message is None:
            logger.error("channel and message are required")
            return

        send_slack_thread_messages(channel=channel, messages=[message])
