# Bridge CDK Project

## High-Level Goals

This project aims to create a glidepath for migrating Bridge Invest from Render to AWS. The primary objectives are:

1. Engage the AWS CDK to stand up basic architectural components.
2. Identify and implement the necessary AWS resources and services.
3. Provide a foundation for future development and scaling.

### Why AWS CDK?

This project uses CDK rather than Terraform for the following reasons, but the IaC decision shouldn't be regarded as finalized. Terraform CDK could be a particularly viable choice to carry over the code here. This project is simply an expression of what architecture and infrastructure components will be needed in AWS.

- **Simplicity**: AWS CDK provides a more straightforward approach to defining cloud infrastructure.
- **Direct Cloud Footprint**: CDK gives you a visible and manageable footprint in the AWS cloud.
- **Permissions Management**: It's easier to manage permissions with CDK apps because CloudFormation has IAM auto-expand capabilities.
- **Native AWS Integration**: Being an AWS-native tool, CDK offers seamless integration with AWS services.

While Terraform is powerful and cloud-agnostic, CDK's tight integration with AWS makes it an excellent choice for projects fully committed to the AWS ecosystem.

## Getting Started

### Prerequisites

- Node.js (v14.x or later)
- AWS CLI
- AWS account and credentials

# AWS Profiles

We assume two profiles exist in your account (one for each environment demo and prod).

Always pass AWS_PROFILE in as an environment variable:

```
AWS_PROFILE=demo cdk diff
```

# Parameters

We use SSM Parameter store to store all parameters used between CDK modules. These values are cached in cdk.context.json to be able to see the difference between the repo cached values and the actual cached values, run:

```
AWS_PROFILE=demo ./bin/compare-ssm-params.ts
```
