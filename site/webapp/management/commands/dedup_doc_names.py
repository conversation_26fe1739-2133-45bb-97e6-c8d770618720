import os

import structlog
from django.core.management.base import BaseCommand
from django_extensions.management.utils import signalcommand

from webapp.models.documents import ProcessedDocument, RawDocument

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Deduplicate file names with repeated text"

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002
        raw_documents = RawDocument.objects.all().only("name")
        processed_documents = ProcessedDocument.objects.all().only("name")

        def dedup_name(filename: str) -> str:
            name, ext = os.path.splitext(filename)  # noqa: PTH122
            half = len(name) // 2
            if name[:half] == name[half:]:
                return name[:half] + ext
            return filename

        raw_doc_changed = 0
        processed_doc_changed = 0

        for raw_doc in raw_documents:
            original_name = raw_doc.name
            new_name = dedup_name(original_name)
            if new_name != original_name:
                raw_doc.name = new_name
                raw_doc.save()
                raw_doc_changed += 1

        for processed_doc in processed_documents:
            original_name = processed_doc.name
            new_name = dedup_name(original_name)
            if new_name != original_name:
                processed_doc.name = new_name
                processed_doc.save()
                processed_doc_changed += 1

        logger.info("Number of raw docs changed", raw_doc_changed=raw_doc_changed)
        logger.info("Number of processed docs changed", processed_doc_changed=processed_doc_changed)
