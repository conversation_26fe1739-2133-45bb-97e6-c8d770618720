# Bridge Investment Platform - Questions for Lead Developer

## Overview
This document contains organized questions about the Bridge Investment platform codebase to help understand the business domain, technical architecture, and implementation details.

## Business Domain Questions

### Core Business Understanding
1. **What is the primary business problem Bridge Investment solves?**
   - Who are the main users (investors, advisors, fund managers)?
   - What pain points does it address in the investment management space?

2. **Investment Portfolio Management**
   - What types of investments does the platform support (private equity, hedge funds, real estate)?
   - How are capital calls and distribution notices processed?
   - What is the relationship between LineItems, Investments, and InvestingEntities?

3. **Document Processing Workflow**
   - What types of documents are typically processed (PDFs, Excel, emails)?
   - How critical is the document retrieval feature to the business?
   - What happens when document retrieval fails?

4. **Portal Integrations**
   - Why are there so many portal integrations (30+ managers)?
   - Which portals are most critical/frequently used?
   - How do we handle portal API changes or portal downtime?

## Architecture & Technical Stack Questions

### Infrastructure
1. **AWS Services**
   - Why use ECS over EKS or Lambda for the main application?
   - What's stored in S3 vs RDS?
   - How is Secrets Manager used for credential management?
   - Why are there separate AWS accounts for demo (************) and prod (************)?

2. **Containerization & Deployment**
   - What's the purpose of `docker-compose.likeprod.yml`?
   - How are deployments handled (`deploy.py` script)?
   - What's the deployment pipeline/CI-CD process?

3. **CDK Infrastructure**
   - Why TypeScript for CDK when everything else is Python?
   - What resources are managed by CDK vs manually created?

### Application Architecture
1. **Django Structure**
   - Why Django over FastAPI/Flask for this use case?
   - How are permissions handled (PermissionManager pattern)?
   - What's the rationale behind the `objects` vs `objects_unsafe` managers?

2. **Celery & Task Processing**
   - What tasks run asynchronously via Celery?
   - How is task monitoring handled (celery-queue-monitor)?
   - What's the typical task queue load?

3. **Authentication & Security**
   - How does the OAuth flow work for email integrations?
   - What's the MFA implementation for portal access?
   - How are customer credentials secured (SecretAbstractBaseModel)?

## Core Features Questions

### Document Retrieval System
1. **Retrieval Strategy Pattern**
   - How does the RetrievalRegistry determine which manager to use?
   - What's the difference between regular and demo retrieval managers?
   - How are retrieval failures handled and retried?

2. **Web Scraping**
   - Why Playwright/Patchright for web scraping?
   - How do you handle anti-bot measures from investment portals?
   - What's the checkpoint logging system for (`log_checkpoint`)?

3. **State Management**
   - Can you explain the Retrieval state machine flow?
   - What triggers state transitions?
   - How are stuck retrievals handled?

### Email Integration
1. **Email Processing**
   - How does the SNS webhook for email processing work?
   - What's the email forwarding rules setup?
   - How are email attachments processed?

2. **Email MFA**
   - How does the system capture MFA codes from emails?
   - What happens if an MFA email is delayed?
   - How are email rules cleaned up after use?

### ML/AI Components
1. **Document Processing**
   - What AI models are used (Bedrock, custom models)?
   - What's the purpose of DocVault?
   - How are documents classified and summarized?

2. **LangChain Integration**
   - What's the LangGraph state machine used for?
   - How are tool errors handled in the AI pipeline?
   - What's the chunking strategy for large documents?

## Data Model Questions

1. **Core Entities**
   - What's the relationship between Organization, BridgeUser, and Role?
   - How are multi-tenant permissions enforced?
   - What's the purpose of MergedPortalCredential?

2. **Document Hierarchy**
   - What's the difference between RawDocument and ProcessedDocument?
   - How are DocumentType and SubDocumentType used?
   - What are the different DocumentFact models for?

3. **Investment Data**
   - How is investment performance tracked?
   - What's the data flow from portal retrieval to dashboard display?
   - How are currency conversions handled?

## Operations & Maintenance Questions

1. **Monitoring & Debugging**
   - What logging/monitoring tools are used?
   - How do you debug failed retrievals in production?
   - What alerts are set up for critical failures?

2. **Performance**
   - What are the typical retrieval times?
   - How many concurrent retrievals can the system handle?
   - Are there any performance bottlenecks?

3. **Testing**
   - What's the testing strategy (unit vs integration)?
   - How do you test portal integrations without hitting real portals?
   - What's the purpose of the demo account (<EMAIL>)?

## Development Process Questions

1. **Local Development**
   - Why the emphasis on Docker-only development?
   - What's the purpose of the UV package manager?
   - How do you handle AWS credentials locally?

2. **Code Quality**
   - What linting rules are enforced?
   - Why strict type checking (mypy/pyright)?
   - What's the PR review process?

3. **Database Migrations**
   - How are migrations tested before production?
   - What's the rollback strategy for failed migrations?
   - How do you handle data migrations vs schema migrations?

## Business Logic Questions

1. **Demo Account**
   - What's special about the Tom Jones demo account?
   - How is demo data isolated/cleaned up?
   - What's the demo data update process?

2. **Bulk Upload**
   - What's the Excel template format for bulk uploads?
   - How are validation errors handled?
   - Can uploads be rolled back?

3. **Public API**
   - Who uses the public API?
   - What are the rate limits?
   - How is API versioning handled?

## Security & Compliance Questions

1. **Data Security**
   - How is sensitive investment data encrypted?
   - What's the data retention policy?
   - How are user sessions managed?

2. **Compliance**
   - What compliance requirements does the platform meet?
   - How is audit logging implemented?
   - Are there any regulatory constraints on data storage?

## Scaling & Future Questions

1. **Scalability**
   - What's the current user/data scale?
   - What are the scaling bottlenecks?
   - Are there plans to move to microservices?

2. **Future Features**
   - What features are on the roadmap?
   - Are there plans for mobile apps?
   - Any planned AI/ML enhancements?

## Troubleshooting Questions

1. **Common Issues**
   - What are the most common production issues?
   - How do you handle portal credential updates?
   - What causes retrieval timeouts?

2. **Support Process**
   - How do you handle customer support requests?
   - What's the SLA for fixing portal integrations?
   - How are urgent issues escalated?

## Notes Section
_Space for answers and additional notes during discussion:_

### Business Context Notes:
- 

### Technical Architecture Notes:
- 

### Key Workflows Notes:
- 

### Pain Points & Improvements:
- 

### Action Items:
- 
