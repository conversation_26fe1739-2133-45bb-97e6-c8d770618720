import datetime
import io
from decimal import ROUND_HALF_UP, Decimal

import boto3
from openpyxl import load_workbook
from openpyxl.styles import Alignment, Border, Font, Side
from webapp.models import CapitalCallDocumentFact, DistributionNoticeDocumentFact, InvestmentUpdateDocumentFact
from webapp.models.line_item import LineItem
from webapp.models.user import BridgeUser


def get_excel_export_documents(
    user: BridgeUser, line_item_pks: list[str]
) -> list[tuple[int, str | None, str | None, str | None]]:
    excel_exports_s3_bucket = "bridge-prod-userdata-bucket"
    res = []
    line_items = LineItem.objects.for_user(user).filter(pk__in=line_item_pks, is_visible=True).all()

    for i, line_item in enumerate(line_items):
        if not line_item:
            res.append((i, None, None, None))
            continue
        unique_filename, excel_export_s3_key = create_excel_export_file(user, line_item)
        res.append((i, unique_filename, excel_exports_s3_bucket, excel_export_s3_key))
    return res


def get_summit_trail_excel_export_documents(
    user: BridgeUser, line_item_pks: list[str]
) -> list[tuple[int, str | None, str | None, str | None]]:
    from retrieval.tasks.create_zip import sanitize_filename_part

    excel_exports_s3_bucket = "bridge-prod-userdata-bucket"
    res = []
    line_items = LineItem.objects.for_user(user).filter(pk__in=line_item_pks, is_visible=True).all()
    line_item_pk_to_s3_key_mapping = {
        "0bc23d74-c318-4951-8139-be4c1391cc62": "excel_exports/summit_trail/Snowcrest Partners_Brookfield Capital Partners IV.xlsx",  # snowcrest brookfield  # noqa: E501
        "b965959c-3a0e-44fc-a29d-82c71793693f": "excel_exports/summit_trail/Woods_Ascent Private Real Assets Lending Fund.xlsx",  # brandon woods individual ascent private  # noqa: E501
        "8ca7e100-90b5-4503-bf47-006d949a7ad7": "excel_exports/summit_trail/Woods_Ares Core Infrastructure Fund CL I.xlsx",  # brandon woods individual ares core  # noqa: E501
        "9eb5b303-4907-4b62-8c5b-86427d4621ee": "excel_exports/summit_trail/Scarborough Trust_ARC Aspenleaf Co-Investment Fund.xlsx",  # scarborough trust arc aspen  # noqa: E501
        "a4213713-4f63-43da-9cb2-4ee3bc2fe724": "excel_exports/summit_trail/Waterman Rev Trust_Gotham Green Fund 1 (Q) LP.xlsx",  # justin waterman rev trust gotham green  # noqa: E501
        "e35945ea-83b7-42a3-a77f-42c9289ee86a": "excel_exports/summit_trail/Waterman_Irradiant CLO Partners Fund II LP.xlsx",  # justin waterman individual irradiant clo  # noqa: E501
        "fc2f807c-d777-4559-9597-382a24e18830": "excel_exports/summit_trail/IRA FBO Peter C Lee Pershing_Horizon Opportunity Fund.xlsx",  # ira fbo peter c lee pershing horizon opportunities fund  # noqa: E501
        "d91cb397-ad19-4d31-bb0b-d7a4cb89e033": "excel_exports/summit_trail/Lake Mary Ventures_Shore Capital Partners Fund II L P.xlsx",  # lake mary ventures shore capital partners  # noqa: E501
        "56c0a91d-36e5-4ffd-aa24-7dad373931fb": "excel_exports/summit_trail/Peter Lee Rollover IRA_Monarch Capital Partners V LP.xlsx",  # peter c lee rollover ira monarch  # noqa: E501
        "e4a44f7b-6fdc-41fc-bfac-ae3ea1595032": "excel_exports/summit_trail/Woods_Audax SLF I (Offshore).xlsx",  # ira fbo brandon woods pershing audax fund  # noqa: E501
    }

    for i, line_item in enumerate(line_items):
        line_item_pk_str = str(line_item.pk)
        if not line_item or line_item_pk_str not in line_item_pk_to_s3_key_mapping:
            res.append((i, None, None, None))
            continue
        unique_filename = (
            f"{sanitize_filename_part(line_item.investing_entity.legal_name)}_"
            f"{sanitize_filename_part(line_item.investment.legal_name)}_"
            f"{datetime.datetime.now(tz=datetime.UTC).strftime('%Y-%m-%d')}.xlsx"
        )
        res.append((i, unique_filename, excel_exports_s3_bucket, line_item_pk_to_s3_key_mapping.get(line_item_pk_str)))
    return res


def create_excel_export_file(user: BridgeUser, line_item: LineItem) -> tuple[str, str]:  # noqa: C901, PLR0912, PLR0915
    """Creates an Excel export file for a given line item."""
    from retrieval.tasks.create_zip import sanitize_filename_part

    s3 = boto3.client("s3")
    bucket_name = "bridge-prod-userdata-bucket"
    template_key = "excel_exports/Excel Export Template.xlsx"

    template_stream = io.BytesIO()
    s3.download_fileobj(bucket_name, template_key, template_stream)

    template_stream.seek(0)
    wb = load_workbook(template_stream)

    line_item_pk = line_item.pk
    investment_update_docs = (
        InvestmentUpdateDocumentFact.objects.for_user(user)
        .filter(line_item__pk=line_item_pk)
        .order_by("processed_document__effective_date")
        .all()
    )
    distribution_notice_docs = (
        DistributionNoticeDocumentFact.objects.for_user(user).filter(line_item__pk=line_item_pk).all()
    )

    capital_call_docs = CapitalCallDocumentFact.objects.for_user(user).filter(line_item__pk=line_item_pk)

    def round_half_up(n: float) -> int:
        return int(Decimal(n).to_integral_value(rounding=ROUND_HALF_UP))

    def format_number_cell(cell: any, value: any, *, show_dollar_sign: bool) -> None:
        if value in (None, 0):
            cell.value = "--"
            cell.number_format = "General"
        else:
            cell.value = value
            if show_dollar_sign:
                cell.number_format = "$#,##0;($#,##0)"
            else:
                cell.number_format = "#,##0;(#,##0)"

    def insert_row_with_formatting(  # noqa: C901, PLR0913
        ws: any,
        row_idx: int,
        col_idx: int,
        row: list,
        number_starting_idx: int,
        left_allign_idx: int,
        *,
        show_dollar_sign: bool = False,
        font: Font | None,
        include_cell_borders: bool = False,
        is_pcap_row: bool = False,
    ) -> int:
        for i, value in enumerate(row):
            cell = ws.cell(
                row=row_idx,
                column=col_idx + i,
                value=round_half_up(value) if isinstance(value, (int, float, Decimal)) else value,
            )
            if i >= number_starting_idx:
                format_number_cell(cell, cell.value, show_dollar_sign=show_dollar_sign)
            if i < left_allign_idx:
                cell.alignment = Alignment(horizontal="left")
            else:
                cell.alignment = Alignment(horizontal="right")
            if isinstance(cell.value, datetime.date):
                cell.number_format = "mm/dd/yy"
            cell.font = font
            if include_cell_borders:
                if is_pcap_row:
                    if i == 0:
                        cell.border = Border(left=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))
                    elif i in (5, 7, 10):
                        cell.border = Border(
                            right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
                        )
                    else:
                        cell.border = Border(top=Side(style="thin"), bottom=Side(style="thin"))
                elif i == 0:
                    cell.border = Border(left=Side(style="thin"))
                elif i in (5, 7, 10):
                    cell.border = Border(right=Side(style="thin"))
        row_idx += 1
        return row_idx

    # SUMMARY SHEET
    ws = wb["Summary"]
    ws.title = f"{sanitize_filename_part(line_item.investing_entity.client.legal_name)} - Summary"
    # Starting row and col
    row_idx = 4
    col_idx = 3

    all_docs = sorted(
        list(investment_update_docs) + list(distribution_notice_docs) + list(capital_call_docs),
        key=lambda doc: doc.processed_document.effective_date,
    )
    seen_all_docs = set()
    cummulative_contributions = 0
    cummulative_distributions = 0
    for doc in all_docs:
        if isinstance(doc, InvestmentUpdateDocumentFact):
            if cummulative_contributions != round_half_up(doc.invested) or cummulative_distributions != round_half_up(
                doc.realized_value
            ):
                # DISCREPANCY ROW
                """
                row = [
                    "Bridge Imputed",
                    "Discrepancy",
                    "Unknown",
                    None,
                    invested_difference := round_half_up(doc.invested - cummulative_contributions),
                    realized_difference := round_half_up(doc.realized_value - cummulative_distributions),
                    None,
                    cummulative_contributions := round_half_up(invested_difference + cummulative_contributions),
                    cummulative_distributions := round_half_up(realized_difference + cummulative_distributions),
                    None,
                    None,
                ]
                row_idx = insert_row_with_formatting(
                    ws,
                    row_idx,
                    col_idx,
                    row,
                    2,
                    2,
                    show_dollar_sign=False,
                    font=Font(color="FF0000", bold=True, italic=True),
                    include_cell_borders=True,
                )
                """

            # Append blank row in summary before PCAP
            row = ["" for i in range(11)]
            row_idx = insert_row_with_formatting(
                ws, row_idx, col_idx, row, 2, 2, show_dollar_sign=False, font=None, include_cell_borders=True
            )

            row = [
                "Manager Reported",
                "PCAP",
                doc.processed_document.effective_date,
                None,
                None,
                None,
                doc.committed_capital,
                doc.invested,
                doc.realized_value,
                doc.unrealized_value,
                doc.total_value,
            ]
            row_tuple = tuple(row)
            if row_tuple not in seen_all_docs:
                seen_all_docs.add(row_tuple)
                row_idx = insert_row_with_formatting(
                    ws,
                    row_idx,
                    col_idx,
                    row,
                    2,
                    2,
                    show_dollar_sign=True,
                    font=Font(bold=True),
                    include_cell_borders=True,
                    is_pcap_row=True,
                )
        elif isinstance(doc, DistributionNoticeDocumentFact):
            cummulative_distributions = round_half_up(cummulative_distributions + doc.amount)
            row = [
                "Manager Reported",
                "Distribution",
                doc.processed_document.effective_date,
                doc.amount,
                None,
                doc.amount,
                None,
                cummulative_contributions,
                cummulative_distributions,
                None,
                None,
            ]
            row_tuple = tuple(row)
            if row_tuple not in seen_all_docs:
                seen_all_docs.add(row_tuple)
                row_idx = insert_row_with_formatting(
                    ws,
                    row_idx,
                    col_idx,
                    row,
                    2,
                    2,
                    show_dollar_sign=False,
                    font=Font(italic=True),
                    include_cell_borders=True,
                )
        elif isinstance(doc, CapitalCallDocumentFact):
            cummulative_contributions = round_half_up(cummulative_contributions + doc.amount)
            raw_doc_acmount = -abs(doc.amount)
            row = [
                "Manager Reported",
                "Capital Call",
                doc.processed_document.effective_date,
                raw_doc_acmount,
                doc.amount,
                None,
                None,
                cummulative_contributions,
                cummulative_distributions,
                None,
                None,
            ]
            row_tuple = tuple(row)
            if row_tuple not in seen_all_docs:
                seen_all_docs.add(row_tuple)
                row_idx = insert_row_with_formatting(
                    ws,
                    row_idx,
                    col_idx,
                    row,
                    2,
                    2,
                    show_dollar_sign=False,
                    font=Font(italic=True),
                    include_cell_borders=True,
                )

    # Add a border to the bottom-most row on the summary page
    last_row = ws.max_row
    starting_idx = 2
    for i, cell in enumerate(ws[last_row]):
        if i < starting_idx:
            continue
        if i == starting_idx:
            cell.border = Border(left=Side(style="thin"), bottom=Side(style="thin"))
        elif i in (7, 9, 12):
            cell.border = Border(right=Side(style="thin"), bottom=Side(style="thin"))
        else:
            cell.border = Border(bottom=Side(style="thin"))

    # CASH FLOW SHEET
    ws = wb["CF"]
    ws.title = f"{sanitize_filename_part(line_item.investing_entity.client.legal_name)} - CF"
    row_idx = 4
    col_idx = 3

    cash_flow_docs = sorted(
        list(distribution_notice_docs) + list(capital_call_docs), key=lambda doc: doc.processed_document.effective_date
    )
    seen_cash_flow_docs = set()
    for cash_flow_doc in cash_flow_docs:
        cash_flow_amount = (
            cash_flow_doc.amount
            if isinstance(cash_flow_doc, DistributionNoticeDocumentFact)
            else -abs(cash_flow_doc.amount)
        )
        row = [
            cash_flow_doc.processed_document.effective_date,
            cash_flow_amount,
            "Distribution" if isinstance(cash_flow_doc, DistributionNoticeDocumentFact) else "Capital Call",
        ]
        row_tuple = tuple(row)
        if row_tuple not in seen_cash_flow_docs:
            seen_cash_flow_docs.add(row_tuple)
            row_idx = insert_row_with_formatting(ws, row_idx, col_idx, row, 1, 0, show_dollar_sign=True, font=None)

    # PCAP SHEET
    ws = wb["PCAPS"]
    ws.title = f"{sanitize_filename_part(line_item.investing_entity.client.legal_name)} - PCAP"
    row_idx = 4
    col_idx = 3

    seen_investment_update_docs = set()
    for investment_update_doc in investment_update_docs:
        row = [
            investment_update_doc.processed_document.effective_date,
            investment_update_doc.committed_capital,
            investment_update_doc.invested,
            investment_update_doc.realized_value,
            investment_update_doc.unrealized_value,
            investment_update_doc.total_value,
        ]
        row_tuple = tuple(row)
        if row_tuple not in seen_investment_update_docs:
            seen_investment_update_docs.add(row_tuple)
            row_idx = insert_row_with_formatting(ws, row_idx, col_idx, row, 1, 0, show_dollar_sign=True, font=None)

    buffer = io.BytesIO()
    wb.save(buffer)
    buffer.seek(0)
    unique_filename = (
        f"{sanitize_filename_part(line_item.investing_entity.legal_name)}_"
        f"{sanitize_filename_part(line_item.investment.legal_name)}_"
        f"{datetime.datetime.now(tz=datetime.UTC).strftime('%Y-%m-%d')}.xlsx"
    )
    object_key = f"excel_exports/{user.organization.name}/{unique_filename}"

    s3.put_object(
        Bucket=bucket_name,
        Key=object_key,
        Body=buffer.getvalue(),
        ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    )

    return unique_filename, object_key
