# Generated by Django 5.2.4 on 2025-07-31 22:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0063_processeddocument_is_document_summary_approved'),
    ]

    operations = [
        migrations.AlterField(
            model_name='retrieval',
            name='retrieval_status',
            field=models.CharField(choices=[('ns', 'Not Started'), ('su', 'Submitted'), ('pc', 'Cancel Pending'), ('lp', 'Login Pending'), ('2p', 'OTP Pending'), ('2b', 'OTP Blocked'), ('ca', 'Canceled'), ('lf', 'Login Failed'), ('2f', 'OTP Required and could not get OTP'), ('pd', 'Login Failed - Portal Down'), ('fr', 'Portal Down - Retryable'), ('ps', 'Pending Scheduling'), ('li', 'Successfully Logged In'), ('dr', 'Document Retrieval Pending'), ('df', 'Document Retrieval Failed'), ('ds', 'Successfully Retrieved Documents'), ('mt', 'Document Retrieval Exceeded Max Time Limit')], default='ns', max_length=2),
        ),
    ]
