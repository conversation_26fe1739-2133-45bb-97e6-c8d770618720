{% load static %}
<!-- Upload Modal -->
<div id="upload-modal" class="relative bg-white rounded-xl w-[450px] p-6">
    <button type="button"
            _="on click trigger closeModal"
            class="absolute top-3 right-3 text-gray-400 hover:text-gray-800 transition">
        <img src="{% static 'assets/images/close.svg' %}"
             width="16"
             height="16"
             alt="Close">
    </button>
    <div id="form-messages" class="mb-4"></div>
    <form id="upload-form"
          class="space-y-4"
          hx-encoding="multipart/form-data"
          hx-post="{% url 'vault_confirm' %}"
          hx-target="#modal"
          hx-vals='{"raw_document_ids": [{% for id in raw_document_ids %}"{{ id }}"{% if not forloop.last %},{% endif %}{% endfor %}]}'
          hx-trigger="submit">
        <div id="form-messages" class="mb-4"></div>
        {% include "vault/modal/dropdown_options.html" %}
        <div class="flex items-center gap-2">
            <label for="posted-date"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Posted Date:</label>
            <input type="date"
                   id="posted-date"
                   name="posted_date"
                   {% if selected_posted_date %} value="{{ selected_posted_date }}" {% else %} value='{% now "Y-m-d" %}' {% endif %}
                   class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                   required>
        </div>
        <div class="flex items-center gap-2">
            <label for="effective-date"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Effective Date:</label>
            <input type="date"
                   id="effective-date"
                   name="effective_date"
                   {% if selected_effective_date %}value="{{ selected_effective_date }}"{% endif %}
                   class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                   required>
        </div>
        <div class="flex items-center gap-2">
            <label for="document-type"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Category:</label>
            <select name="document_type"
                    id="document-type"
                    class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                    required
                    hx-get="{% url 'get_valid_sub_document_types' %}"
                    hx-target="#sub-document-type-container"
                    hx-trigger="change"
                    hx-include="[name='document_type']">
                <option value="">Select Document Type</option>
                {% for choice in DocumentType.choices %}
                    <option value="{{ choice.0 }}"
                            {% if choice.0 == selected_document_type %}selected{% endif %}>{{ choice.1 }}</option>
                {% endfor %}
            </select>
        </div>
        <div id="sub-document-type-container">
            {% if selected_document_type in REQUIRED_SUB_TYPE_DOCUMENTS %}
                <div class="flex items-center gap-2">
                    <label for="sub-document-type"
                           class="w-32 text-left text-sm text-[#4b554d] font-medium">Sub Category:</label>
                    <select name="sub_document_type"
                            id="sub-document-type"
                            class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                            required>
                        <option value="">Select Sub Document Type</option>
                        {% for choice in SubDocumentType.choices %}<option value="{{ choice.0 }}">{{ choice.1 }}</option>{% endfor %}
                    </select>
                </div>
            {% endif %}
        </div>
        <div>
            <button type="submit"
                    class="w-full px-4 py-2.5 bg-[#343d36] text-white rounded-lg hover:bg-[#424d44] transition-colors flex items-center justify-center gap-2">
                <span>Confirm</span>
            </button>
        </div>
    </form>
</div>
