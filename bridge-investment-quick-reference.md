# Bridge Investment - Quick Reference Guide

## Key Directories and Their Purpose

```
site/
├── bridge_project/     # Django project settings
├── webapp/            # Main web application
│   ├── models/        # Core data models
│   ├── views/         # View controllers
│   └── templates/     # HTML templates (HTMX-based)
├── retrieval/         # Portal scraping engine
│   └── core/managers/ # Portal-specific implementations
├── ml_app/           # AI/ML document processing
├── email_webhook/    # Email processing system
├── public_api/       # REST API endpoints
└── integrations/     # Third-party integrations
```

## Critical Business Flows

### 1. **Document Retrieval Flow**
```
User Login → Add Portal Credentials → Schedule Retrieval → 
Login to Portal → Download Documents → Process with AI → 
Store in S3 → Display in Dashboard
```

### 2. **Email MFA Flow**
```
Portal Login Attempt → MFA Required → Setup Email Rule → 
Trigger MFA Email → Capture Code via Webhook → 
Complete Login → Clean Up Rules
```

### 3. **Bulk Upload Flow**
```
Upload Excel → Parse Line Items → Create Portal Credentials → 
Queue Retrievals → Process Documents → Update Dashboard
```

## Key Models to Understand

1. **User & Permissions**
   - `BridgeUser` - User model
   - `Organization` - Multi-tenancy
   - `Role` - Permission roles

2. **Investment Data**
   - `LineItem` - Core investment record
   - `Investment` - Investment details
   - `InvestingEntity` - Investor entities

3. **Portal Integration**
   - `Portal` - Portal definitions
   - `PortalCredential` - User credentials
   - `Retrieval` - Retrieval jobs

4. **Documents**
   - `RawDocument` - Original files
   - `ProcessedDocument` - Analyzed documents
   - `DocumentFact` - Extracted data

## Important Environment Variables

```bash
AWS_PROFILE          # demo or prod
SITE_INIT_ACTION     # reset, migrate, load, none
AWS_STORAGE_BUCKET_NAME
DJANGO_SECRET_KEY
DATABASE_URL
REDIS_URL
```

## Common Commands

```bash
# Local development
docker compose up --build

# Deploy to AWS
AWS_PROFILE=demo ./deploy.py list
AWS_PROFILE=demo ./deploy.py interactive SiteService

# Run retrieval manually
./site/manage.py trigger_retrieval_adhoc -u "email" -p "Portal Name"

# Linting
./lint.py --fix

# Django migrations
./site/manage.py makemigrations
./site/manage.py migrate
```

## Key Technologies

- **Backend**: Django 5.1 + Django Ninja
- **Task Queue**: Celery + Redis
- **Web Scraping**: Playwright/Patchright
- **AI/ML**: LangChain + AWS Bedrock
- **Infrastructure**: AWS CDK + ECS
- **Frontend**: HTMX + Tailwind CSS

## Portal Integration Status Codes

- `ns` - Not Started
- `lp` - Login Pending
- `2b` - OTP Blocked (needs MFA)
- `li` - Successfully Logged In
- `dr` - Document Retrieval Pending
- `ds` - Successfully Retrieved Documents
- `lf` - Login Failed
- `ca` - Canceled

## Database Patterns

1. **Soft Deletes**: `deleted_at` field
2. **Audit Fields**: `created_by`, `updated_by`
3. **Multi-tenant**: `organization` foreign key
4. **Permissions**: Custom `PermissionManager`

## Security Considerations

- Credentials in AWS Secrets Manager
- Row-level permissions
- Organization-based isolation
- API key authentication
- OAuth for email providers

## Debugging Tips

1. **Check Retrieval Status**:
   ```python
   Retrieval.objects.filter(user__email='<EMAIL>')
   ```

2. **View Logs**:
   - Structured logging with `structlog`
   - Check CloudWatch for production logs

3. **Test Portals**:
   - Use demo account
   - Mock managers for testing

## Contact Points

- **Slack**: Internal team channel
- **AWS Accounts**: 
  - Demo: ************
  - Prod: ************
- **Demo User**: <EMAIL>

## Red Flags to Watch For

1. Portal UI changes breaking scrapers
2. MFA email delays causing timeouts
3. Rate limiting from investment portals
4. S3 bucket permission issues
5. Celery queue backlog

## Performance Metrics

- Typical retrieval: 2-10 minutes
- Document processing: 30s-2min per doc
- Concurrent retrievals: Limited by browser instances
- Database connections: Monitor pool usage

## Next Steps for Learning

1. Run the demo flow locally
2. Explore a portal manager implementation
3. Trace a retrieval through the state machine
4. Understand the permission system
5. Review the AI document processing