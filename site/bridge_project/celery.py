import structlog

logger = structlog.get_logger(__name__)
import os

from celery import Celery, Task
from celery.signals import setup_logging
from django_structlog.celery.steps import DjangoStructLogInitStep

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "bridge_project.settings")


class ExtendedTask(Task):
    def apply_async(  # noqa: ANN201, # type: ignore[arguments-differ]
        self,
        args=None,  # noqa: ANN001
        kwargs=None,  # noqa: ANN001
        task_id=None,  # noqa: ARG002,ANN001
        **options,  # noqa: ANN003
    ):
        # This is a hack to get the args and kwargs to show up in the task result as parseable
        options["argsrepr"] = args
        options["kwargsrepr"] = kwargs

        return super().apply_async(
            args=args,
            kwargs=kwargs,
            **options,
        )


app = Celery("bridge_project", task_cls=ExtendedTask)

app.conf.update(result_extended=True)

app.config_from_object("django.conf:settings", namespace="CELERY")
app.steps["worker"].add(DjangoStructLogInitStep)


@setup_logging.connect
def config_loggers(*args, **kwargs) -> None:  # noqa: ANN002, ANN003, ARG001
    from logging.config import dictConfig

    from django.conf import settings

    dictConfig(settings.LOGGING)


app.autodiscover_tasks()
