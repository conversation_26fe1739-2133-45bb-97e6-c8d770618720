{% extends "account/base_entrance.html" %}
{% comment %}
    From: https://docs.allauth.org/en/dev/common/templates.html
    Source: https://codeberg.org/allauth/django-allauth/src/branch/main/allauth/templates/account/password_reset_from_key_done.html

{% endcomment %}
{% load widget_tweaks static allauth account %}
{% block head_title %}
    Forgot Password
{% endblock head_title %}
{% block extra_head %}
    {% include "components/bridge_header.html" %}
{% endblock extra_head %}
{% block content %}
    <main>
        <div class="Container h-[636px] w-full px-8 flex flex-col justify-center items-center">
            <div class="Content w-4/5 flex flex-col justify-center items-center gap-20">
                <div class="Container h-[636px] px-8 flex-col justify-center items-center inline-flex">
                    <div class="Content self-stretch h-56 flex-col justify-start items-start gap-20 flex">
                        <div class="HeaderAndForm self-stretch h-56 flex-col justify-start items-start gap-8 flex">
                            <div class="Header self-stretch h-24 flex-col justify-start items-center flex">
                                <div class="TextAndSupportingText self-stretch h-24 flex-col justify-start items-start gap-3 flex">
                                    <div class="Text self-stretch text-[#343d36] text-4xl font-bold font-['Nanum Myeongjo'] leading-[44px]">
                                        Password reset!
                                    </div>
                                    <div class="Text self-stretch text-[#343d36] text-sm font-normal font-['Inter'] leading-tight">
                                        Your password has been successfully reset. Click below to log in.
                                    </div>
                                </div>
                            </div>
                            <a href="{% url 'account_login' %}"
                               class="Content self-stretch h-11 rounded-xl flex-col justify-start items-start gap-6 flex">
                                <div class="Actions self-stretch h-11 flex-col justify-start items-start gap-4 flex">
                                    <div class="ButtonsButton self-stretch px-4 py-2.5 bg-[#924f34] rounded-lg shadow border border-[#924f34] justify-center items-center gap-1.5 inline-flex">
                                        <div class="TextPadding px-0.5 justify-center items-center flex">
                                            <div class="Text text-white text-base font-semibold font-['Inter'] leading-normal">Continue</div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
{% endblock content %}
