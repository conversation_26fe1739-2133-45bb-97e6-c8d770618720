import asyncio
import contextlib
import hashlib
import json
import re
from asyncio import CancelledError
from asyncio import TimeoutError as AsyncTimeoutError
from collections.abc import AsyncGenerator
from datetime import datetime

import structlog
from bs4 import BeautifulSoup as bs  # noqa: N813
from dateutil import parser
from playwright.sync_api import Frame
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.get_logger(__name__)


@register_strategy
class EFrontCloudPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "efrontcloud.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "eFront"),
            ("<EMAIL>", "State Street Corporations"),
            ("<EMAIL>", "Monarch"),
        ]

    async def _enter_credentials(self, username: str, password: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError

        # Focus and fill the username
        username_locators = [
            self.page.locator("#FORMLOGINid"),
            self.page.get_by_placeholder("Email or User ID"),
            self.page.get_by_placeholder("Username"),
            self.page.get_by_placeholder("User ID"),
        ]
        for username_locator in username_locators:
            if await username_locator.count() == 1:
                await username_locator.click()
                await username_locator.fill(username)
                await asyncio.sleep(1)
                break

        # Focus and fill the password
        await self.page.get_by_placeholder("Password").click()
        await self.page.get_by_placeholder("Password").fill(password)
        await asyncio.sleep(1)

        # Click the "Sign in" or "Login" button. the id is consistent across the 3 portals
        # (state street is an input element instead of a button)
        await self.page.locator("#btSubmit").click()

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        has_otp = bool(await self.page.get_by_placeholder("enter the code").is_visible())
        return has_otp and (await self.page.get_by_placeholder("enter the code").is_visible())

    async def _send_otp(self) -> None:
        # Not used here as it is automatically sent, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.get_by_placeholder("enter the code").click()
        await asyncio.sleep(1)
        await self.page.get_by_placeholder("enter the code").fill(otp)
        await asyncio.sleep(1)
        if await self.page.get_by_label("Remember me on this device").count():
            await self.page.get_by_label("Remember me on this device").set_checked(True)
            await asyncio.sleep(1)
        await self.page.locator("div.button").filter(has_text="Verify").click()

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError

        await self.page.wait_for_load_state("networkidle")
        page_content = await self.page.content()

        # Try first pass
        success = (
            "Sign in" not in page_content and "Forgot password?" not in page_content and not await self._check_has_otp()
        )
        if not success:
            await asyncio.sleep(1)
            page_content = await self.page.content()
            success = (
                "Sign in" not in page_content
                and "Forgot password?" not in page_content
                and not await self._check_has_otp()
            )

        return success

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        m = json.loads(document_metadata)
        string_value = "".join(
            [
                m["document_name"],
                m["raw_document_type"],
                m["fund"],
                m["investor"],
                m["raw_document_date"],
                m["raw_published_date"],
                m["raw_due_date"],
            ]
        )
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324 # nosec

    async def _dismiss_accept_screen(self) -> None:
        has_accept_screen = True
        while has_accept_screen:
            has_accept_screen = False
            buttons = await self.page.get_by_role("button").all()
            for button in buttons:
                text_content = await button.text_content()
                if text_content.lower().strip() == "accept" and await button.is_visible():
                    await button.click()
                    await asyncio.sleep(5)
                    has_accept_screen = True
                    break

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        await asyncio.sleep(10)
        await self._dismiss_accept_screen()
        await self.page.get_by_text("Documents", exact=True).click()
        await asyncio.sleep(10)
        frames = [f for f in self.page.frames if "standalone-document-feature" in f.url]
        if len(frames) != 1:
            logger.exception(
                "Error attaching frame", length_frames=len(frames), frame_urls=[f.url for f in self.page.frames]
            )
            raise CouldNotFindHTMLElementError

        frame = frames[0]
        seen = 0
        soup = bs(markup=await frame.content(), features="html.parser")
        expected_page_length = self.__get_page_length(soup)
        expected_number_of_documents = self.__get_number_of_documents(soup)
        max_pages = expected_number_of_documents // expected_page_length + 1
        current_page = self.__get_current_page(soup)
        old_seen = -1
        old_page = -1
        # TODO: refactor this a bit
        while seen != expected_number_of_documents:
            soup = bs(markup=await frame.content(), features="html.parser")
            current_page = self.__get_current_page(soup)
            logger.info(
                "Current retrieval status",
                seen=seen,
                expected_number_of_documents=expected_number_of_documents,
                expected_page_length=expected_page_length,
            )
            logger.info("Moving Pages", old_page=old_page, current_page=current_page)
            if old_page == current_page:
                logger.error("Could not go to the next page")
                break
            documents = self.__parse_table(soup, expected_page_length, expected_number_of_documents, current_page)
            for document in documents:
                seen += 1
                doc_hash = await self.should_download_document(json.dumps(document))
                if document["is_unread"] or doc_hash is not None:
                    if doc_hash is None:
                        doc_hash = self._doc_hash(json.dumps(document))
                    logger.info("Downloading document", document=document)
                    yield (await self.__download_document(frame, document, doc_hash))
                else:
                    logger.warning("Skipping document", document=document)
            if old_seen == seen:
                raise CouldNotFindHTMLElementError
            await self.__go_to_next_page(frame, current_page, max_pages)
            old_page = current_page
            old_seen = seen

    async def __go_to_next_page(self, frame: Frame, current_page: int, max_pages: int) -> None:
        # Avoid clicking the weird tablet table below the main table
        # TODO: wait for the toast to disappear
        if current_page != max_pages:
            await (
                frame.locator(".k-grid:not(.document-grid-layout-tablet)")
                .get_by_title("Go to the next page")
                .first.click()
            )
        await asyncio.sleep(3)

    def __get_page_length(self, soup: bs) -> int:
        res = {int(span.find("option")["value"]) for span in soup.findAll("span", {"class": "k-pager-sizes"})}
        if len(res) != 1:
            logger.exception("Could not find page length", page_length=res)
            raise CouldNotFindHTMLElementError
        return res.pop()

    def __get_current_page(self, soup: bs) -> int:
        res = {int(div.find("input")["value"]) for div in soup.findAll("div", {"class": "k-pager"})}
        if len(res) != 1:
            logger.exception("Could not find current page", current_page=res)
            raise CouldNotFindHTMLElementError
        return res.pop()

    def __get_number_of_documents(self, soup: bs) -> int:
        res = set()
        pattern = re.compile(r"(\d+)(?=\s+items)")
        for div in soup.findAll("div", {"class": "k-pager-info"}):
            match = pattern.search(div.text)
            if match:
                res.add(match.group(1))
        if len(res) != 1:
            logger.exception("Could not find number of documents", number_of_documents=res)
            raise CouldNotFindHTMLElementError
        return int(res.pop())

    def __parse_table(
        self, soup: bs, expected_page_length: int, expected_number_of_documents: int, current_page: int
    ) -> dict[str, str | bool]:
        page_length = self.__get_page_length(soup)

        number_of_documents = self.__get_number_of_documents(soup)

        if number_of_documents != expected_number_of_documents:
            logger.exception(
                "Unexpected number of documents",
                number_of_documents=number_of_documents,
                expected_number_of_documents=expected_number_of_documents,
            )
            raise ValueError
        if page_length != expected_page_length:
            logger.exception(
                "Unexpected page length", page_length=page_length, expected_page_length=expected_page_length
            )
            raise ValueError

        # evaluate if we should count documents downloaded instead.
        # expected_table_length =  min(number_of_documents-seen, page_length)  # noqa: ERA001
        if expected_number_of_documents // page_length < current_page:
            expected_table_length = expected_number_of_documents % page_length
        else:
            expected_table_length = page_length
        res = [
            table
            for table in soup.select('table[role="presentation"].k-grid-table')
            if len(table.select("tr")) == expected_table_length
        ]

        if len(res) != 1:
            logger.exception(
                "Could not find table",
                len_tables=len(res),
                expected_table_length=expected_table_length,
                current_page=current_page,
                page_length=page_length,
            )
            raise CouldNotFindHTMLElementError
        table = res[0]

        documents = []
        for row in table.select("tr"):
            td = row.select("td")
            obj = {
                "checkbox_id": row.select("input", {"type": "checkbox"})[0].get("id"),
                "document_name": td[1].text.strip(),
                "raw_document_type": td[2].text.strip(),
                "fund": td[3].text.strip(),
                "investor": td[4].text.strip(),
                "raw_document_date": td[5].text.strip(),
                "raw_published_date": td[6].text.strip(),
                "raw_due_date": td[7].text.strip(),
                "is_unread": "unread-doc" in row.get("class"),
            }
            for date_field in ["document_date", "published_date", "due_date"]:
                with contextlib.suppress(parser.ParserError):
                    obj[date_field] = parser.parse(obj[f"raw_{date_field}"]).strftime("%Y-%m-%d")
            obj["document_type"] = self.__document_mapper(obj)
            documents.append(obj)
        if len(documents) != expected_table_length:
            logger.exception(
                "Unexpected Table Length", length_documents=len(documents), expected_table_length=expected_table_length
            )
            raise CouldNotFindHTMLElementError

        return documents

    def __document_mapper(self, document: dict[str, str | bool]) -> str:
        raw_type = document["raw_document_type"]
        document_name = document["document_name"].lower()

        document_map = {
            "Tax Documents": DocumentType.TAX,
            "Tax Information": DocumentType.TAX,
            "K1s": DocumentType.TAX,
            "Partnership Documents": DocumentType.LEGAL,
            "Legal Documents": DocumentType.LEGAL,
            "Legal Notices": DocumentType.LEGAL,
            "Other Correspondence": DocumentType.OTHER,
            "General Correspondence": DocumentType.OTHER,
            "General Notices": DocumentType.OTHER,
            "Annual Meeting Notices": DocumentType.OTHER,
            "Partner's Capital Statements": DocumentType.ACCOUNT_STATEMENT,
            "Capital Accounts": DocumentType.ACCOUNT_STATEMENT,
            "Quarterly Reports": DocumentType.ACCOUNT_STATEMENT,
            "Investment Reports": DocumentType.INVESTMENT_UPDATE,
            "Financial Statements": DocumentType.INVESTMENT_UPDATE,
            "Annual Reports": DocumentType.INVESTMENT_UPDATE,
            "Investment Memorandum": DocumentType.INVESTMENT_UPDATE,
            "Capital Calls": DocumentType.CAPITAL_CALL,
            "Call Notice": DocumentType.CAPITAL_CALL,
            "Distribution Notices": DocumentType.DISTRIBUTION_NOTICE,
            "Distributions": DocumentType.DISTRIBUTION_NOTICE,
        }

        if raw_type in document_map:
            return document_map[raw_type]

        if raw_type == "Capital Calls & Distributions":
            if "distribution" in document_name:
                return DocumentType.DISTRIBUTION_NOTICE
            if "call" in document_name:
                return DocumentType.CAPITAL_CALL

        return DocumentType.UNKNOWN

    async def __download_document(
        self, frame: Frame, document: dict[str, str | bool], doc_hash: DocumentHash
    ) -> RawDocumentTuple:
        checkbox_id = document["checkbox_id"]
        await frame.locator(f'[id="{checkbox_id}"]').check()
        await frame.locator("span").filter(has_text="Action").first.click()
        await asyncio.sleep(0.5)
        download = None
        accept_screen_detected = False
        try:
            async with self.page.expect_download(timeout=45000) as download_info:
                await frame.locator("span").filter(has_text="Download").first.click()
                try:
                    download = await asyncio.wait_for(download_info.value, 100)
                except (TimeoutError, CancelledError):
                    # If download doesn't start after a 10s timeout, check for accept screen
                    has_accept_screen = await self.page.locator("button.accept-all").count()
                    if has_accept_screen:
                        logger.info("Accept Screen detected", log_to_llm=True)
                        accept_screen_detected = True
                        # Purposely raise to exit this context
                        raise
                    msg = "Download timedout with no accept screen detected"
                    raise TimeoutError(msg) from AsyncTimeoutError
        except (TimeoutError, CancelledError):
            # Only try second attempt if accept screen was handled
            if accept_screen_detected:
                # Try a second download with a new context
                async with self.page.expect_download() as new_download_info:
                    logger.info("Clicking accept...", log_to_llm=True)
                    await self.page.locator("button.accept-all").click()
                    download = await new_download_info.value
                    logger.info("Downloaded file after closing accept screen", log_to_llm=True)
            else:
                # Re-raise the exception if it wasn't due to an accept screen
                raise
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = file_name.read_bytes()
        file_name.unlink()
        await frame.locator(f'[id="{checkbox_id}"]').uncheck()
        if "published_date" in document:
            date = datetime.strptime(document["published_date"], "%Y-%m-%d")  # noqa: DTZ007
        elif "document_date" in document:
            date = datetime.strptime(document["document_date"], "%Y-%m-%d")  # noqa: DTZ007
        elif "due_date" in document:
            date = datetime.strptime(document["due_date"], "%Y-%m-%d")  # noqa: DTZ007
        else:
            date = datetime.now(tz=datetime.UTC)
        return RawDocumentTuple(
            name=download.suggested_filename,
            date=date,
            doc_type=document["document_type"],
            content=content,
            raw_metadata=json.dumps(document),
            doc_hash=doc_hash,
        )
