import React, { useState, useEffect } from "react";
import type { BoxData } from "../types/pdfLabeler";

interface IndexedBoxesListProps {
  indexedBoxes: BoxData[];
  currentPageNumber: number;
  overlaysRef: React.MutableRefObject<{ [key: number]: HTMLDivElement }>;
  scale: number;
}

const IndexedBoxesList: React.FC<IndexedBoxesListProps> = ({
  indexedBoxes,
  currentPageNumber,
  overlaysRef,
  scale,
}) => {
  const [showIndexBoxes, setShowIndexBoxes] = useState<BoxData[]>([]);

  // Update box sizes when scale changes
  useEffect(() => {
    if (showIndexBoxes.length > 0) {
      // Re-render boxes with new scale without changing state
      clearAllIndexBoxes();
      indexedBoxes.forEach((boxData) => {
        const pageOverlay = overlaysRef.current[boxData.pageNumber];
        if (pageOverlay) {
          const boxElement = document.createElement("div");
          boxElement.className =
            "data-index-boxes absolute border-2 border-solid border-blue-400 bg-blue-900 bg-opacity-20 pointer-events-auto cursor-pointer z-[100] box-border";

          boxElement.style.left = `${boxData.boundingBox.x * scale}px`;
          boxElement.style.top = `${boxData.boundingBox.y * scale}px`;
          boxElement.style.width = `${boxData.boundingBox.width * scale}px`;
          boxElement.style.height = `${boxData.boundingBox.height * scale}px`;

          pageOverlay.appendChild(boxElement);
          boxData.element = boxElement;
        }
      });
    }
  }, [scale]);

  const handleShowClick = () => {
    // Don't allow toggling if global hide is active
    const isCurrentlyShowing = showIndexBoxes.length > 0;

    if (isCurrentlyShowing) {
      // Hide all index boxes
      setShowIndexBoxes([]);
      clearAllIndexBoxes();
    } else {
      // Show all index boxes
      showAllIndexBoxes();
      setShowIndexBoxes(indexedBoxes);
    }
  };

  const clearAllIndexBoxes = () => {
    const overlays = overlaysRef.current;
    for (const pageNumber in overlays) {
      const overlay = overlays[pageNumber];
      if (overlay) {
        overlay
          .querySelectorAll(".data-index-boxes")
          .forEach((box: Element) => {
            box.remove();
          });
      }
    }
  };

  const showAllIndexBoxes = () => {
    // First clear any existing boxes
    clearAllIndexBoxes();

    // Show boxes on their respective pages
    indexedBoxes.forEach((boxData) => {
      const pageOverlay = overlaysRef.current[boxData.pageNumber];
      if (pageOverlay) {
        const boxElement = document.createElement("div");
        boxElement.className =
          "data-index-boxes absolute border-2 border-solid border-blue-400 bg-blue-900 bg-opacity-20 pointer-events-auto cursor-pointer z-[100] box-border";

        boxElement.style.left = `${boxData.boundingBox.x * scale}px`;
        boxElement.style.top = `${boxData.boundingBox.y * scale}px`;
        boxElement.style.width = `${boxData.boundingBox.width * scale}px`;
        boxElement.style.height = `${boxData.boundingBox.height * scale}px`;

        pageOverlay.appendChild(boxElement);
        boxData.element = boxElement;
      }
    });
  };
  return (
    <>
      <div className="flex items-center gap-4 mb-4 mt-6">
        <h2 className="text-xl font-semibold text-white">Indexed Boxes</h2>
        <button
          onClick={handleShowClick}
          className={`text-xs px-2 py-1 rounded ${"bg-yellow-600 hover:bg-yellow-500 text-white"}`}
        >
          {showIndexBoxes.length > 0 ? "Hide All" : "Show All"}
        </button>
      </div>
      <div className="text-gray-300 space-y-2">
        {indexedBoxes.length === 0 ? (
          <p className="text-gray-300">No labeled boxes yet.</p>
        ) : (
          showIndexBoxes.map((boxData) => (
            <div
              key={boxData.id}
              data-box-id={boxData.id}
              data-page-number={boxData.pageNumber}
              className={`bg-gray-700 border border-green-600 rounded-lg p-3 mb-2 ${
                boxData.pageNumber !== currentPageNumber ? "opacity-50" : ""
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <h4 className="text-sm font-medium text-white">
                  {boxData.data?.replace("_", " ").toUpperCase()} page:{" "}
                  {boxData.pageNumber}
                </h4>
              </div>
              <div className="text-xs text-gray-300 mb-2">
                <div className="mb-1">
                  <strong>Coords:</strong>{" "}
                  <span>
                    x: {boxData.boundingBox.x}, y: {boxData.boundingBox.y}, w:{" "}
                    {boxData.boundingBox.width}, h: {boxData.boundingBox.height}
                  </span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </>
  );
};

export default IndexedBoxesList;
