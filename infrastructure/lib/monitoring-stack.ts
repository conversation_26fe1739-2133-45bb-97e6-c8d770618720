import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import * as cloudwatch from "aws-cdk-lib/aws-cloudwatch";
import * as sns from "aws-cdk-lib/aws-sns";
import * as cloudwatch_actions from "aws-cdk-lib/aws-cloudwatch-actions";
import * as events from "aws-cdk-lib/aws-events";
import * as targets from "aws-cdk-lib/aws-events-targets";
import * as subscriptions from "aws-cdk-lib/aws-sns-subscriptions";
import * as guardduty from "aws-cdk-lib/aws-guardduty";

import {
  SharedProps,
  SharedStack,
  StorageStackOutputs,
  SiteServiceStackOutputs,
} from "./schema";

export class MonitoringStack extends SharedStack {
  constructor(scope: Construct, id: string, props: SharedProps) {
    super(scope, id, props);

    const storageOutputs = StorageStackOutputs.fromCdkOutput(
      this,
      StorageStackOutputs,
    );
    const siteServiceOutputs = SiteServiceStackOutputs.fromCdkOutput(
      this,
      SiteServiceStackOutputs,
    );
    if (storageOutputs === undefined || siteServiceOutputs === undefined) {
      return;
    }
    const monitoringAlertsTopic = new sns.Topic(this, "MonitoringAlertsTopic", {
      topicName: "MonitoringAlertsTopic",
    });
    monitoringAlertsTopic.addSubscription(
      new subscriptions.EmailSubscription("<EMAIL>"),
    );
    // Enable GuardDuty in the AWS region, to detect security issues
    new guardduty.CfnDetector(this, "GuardDutyDetector", { enable: true });

    const guarddutyFindingsRule = new events.Rule(
      this,
      "GuardDutyFindingsRule",
      {
        description: "Rule to capture AWS GuardDuty findings",
        eventPattern: {
          source: ["aws.guardduty"],
          detailType: ["GuardDuty Finding"],
        },
      },
    );

    guarddutyFindingsRule.addTarget(
      new targets.SnsTopic(monitoringAlertsTopic),
    );

    // Create a CloudWatch alarm for each RDS instance ID
    for (const instanceId of storageOutputs.DBInstanceIds) {
      const cpuMetric = new cloudwatch.Metric({
        metricName: "CPUUtilization",
        namespace: "AWS/RDS",
        dimensionsMap: {
          DBInstanceIdentifier: instanceId,
        },
        period: cdk.Duration.seconds(300),
        statistic: "Average",
      });

      const cpuAlarm = new cloudwatch.Alarm(
        this,
        `RdsCpuUtilizationAlarm-${instanceId}`,
        {
          alarmName: `RDS_CPU_Utilization_Alarm_${instanceId}`,
          metric: cpuMetric,
          comparisonOperator:
            cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
          threshold: 80,
          evaluationPeriods: 1,
          alarmDescription: `Alarm when CPU utilization exceeds 80% for RDS instance ${instanceId}`,
        },
      );

      // Add the SNS topic as an alarm action
      cpuAlarm.addAlarmAction(
        new cloudwatch_actions.SnsAction(monitoringAlertsTopic),
      );
    }
    // Create FreeableMemory alarms
    for (const instanceId of storageOutputs.DBInstanceIds) {
      const freeableMemoryMetric = new cloudwatch.Metric({
        metricName: "FreeableMemory",
        namespace: "AWS/RDS",
        dimensionsMap: {
          DBInstanceIdentifier: instanceId,
        },
        period: cdk.Duration.seconds(300),
        statistic: "Average",
      });

      const freeableMemoryAlarm = new cloudwatch.Alarm(
        this,
        `RdsFreeableMemoryAlarm-${instanceId}`,
        {
          alarmName: `FreeableMemoryAlarm-${instanceId}`,
          metric: freeableMemoryMetric,
          comparisonOperator: cloudwatch.ComparisonOperator.LESS_THAN_THRESHOLD,
          threshold: 2 ** 30, // 1 GB
          evaluationPeriods: 1,
          alarmDescription: `Alarm when RDS instance ${instanceId} freeable memory is too low`,
        },
      );
      freeableMemoryAlarm.addAlarmAction(
        new cloudwatch_actions.SnsAction(monitoringAlertsTopic),
      );
    }

    // Create ReadIOPS alarms
    for (const instanceId of storageOutputs.DBInstanceIds) {
      const readIopsMetric = new cloudwatch.Metric({
        metricName: "ReadIOPS", // You can change this to another IO metric if desired
        namespace: "AWS/RDS",
        dimensionsMap: {
          DBInstanceIdentifier: instanceId,
        },
        period: cdk.Duration.seconds(300),
        statistic: "Average",
      });

      const ioAlarm = new cloudwatch.Alarm(this, `RdsIoAlarm-${instanceId}`, {
        alarmName: `RDS_IO_Alarm_${instanceId}`,
        metric: readIopsMetric,
        comparisonOperator:
          cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
        threshold: 100, // Adjust threshold as needed
        evaluationPeriods: 1,
        alarmDescription: `Alarm when RDS IO (ReadIOPS) exceeds 100 for RDS instance ${instanceId}`,
      });

      ioAlarm.addAlarmAction(
        new cloudwatch_actions.SnsAction(monitoringAlertsTopic),
      );
    }

    // FreeStorageSpace Alarms
    for (const instanceId of storageOutputs.DBInstanceIds) {
      const freeStorageSpaceMetric = new cloudwatch.Metric({
        metricName: "FreeStorageSpace",
        namespace: "AWS/RDS",
        dimensionsMap: {
          DBInstanceIdentifier: instanceId,
        },
        period: cdk.Duration.seconds(300),
        statistic: "Average",
      });

      const freeStorageSpaceAlarm = new cloudwatch.Alarm(
        this,
        `RdsFreeStorageSpaceAlarm-${instanceId}`,
        {
          alarmName: `FreeStorageSpaceAlarm-${instanceId}`,
          metric: freeStorageSpaceMetric,
          comparisonOperator: cloudwatch.ComparisonOperator.LESS_THAN_THRESHOLD,
          threshold: **********, // 1GB as an example, adjust as needed
          evaluationPeriods: 1,
          alarmDescription: `Alarm when RDS instance ${instanceId} free storage space is too low`,
        },
      );

      // Add action if desired
      // If you don't want to send notifications for this alarm, you can remove the line below.
      freeStorageSpaceAlarm.addAlarmAction(
        new cloudwatch_actions.SnsAction(monitoringAlertsTopic),
      );
    }
    // UnhealthyHostCount Alarm for the ALB
    const unhealthyHostCountMetric = new cloudwatch.Metric({
      metricName: "UnHealthyHostCount",
      namespace: "AWS/ApplicationELB",
      dimensionsMap: {
        LoadBalancer: siteServiceOutputs.LoadBalancerName,
      },
      period: cdk.Duration.seconds(60),
      statistic: "Average",
    });

    const unhealthyHostCountAlarm = new cloudwatch.Alarm(
      this,
      "UnhealthyHostCountAlarm",
      {
        alarmName: "UnhealthyHostCountAlarm",
        metric: unhealthyHostCountMetric,
        comparisonOperator:
          cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
        threshold: 1,
        evaluationPeriods: 1,
        alarmDescription:
          "Alarm when the number of unhealthy hosts is greater than 1",
      },
    );

    unhealthyHostCountAlarm.addAlarmAction(
      new cloudwatch_actions.SnsAction(monitoringAlertsTopic),
    );

    const latencyMetric = new cloudwatch.Metric({
      metricName: "Latency",
      namespace: "AWS/ApplicationELB",
      dimensionsMap: {
        LoadBalancer: siteServiceOutputs.LoadBalancerName,
      },
      period: cdk.Duration.seconds(60),
      statistic: "Average",
    });

    const latencyAlarm = new cloudwatch.Alarm(this, "LatencyAlarm", {
      alarmName: "SiteSe-ALBAE-Og9fH0u96Vjn_Latency_Alarm",
      metric: latencyMetric,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
      threshold: 0.5,
      evaluationPeriods: 1,
      alarmDescription:
        "Alarm when SiteSe-ALBAE-Og9fH0u96Vjn latency exceeds 0.5 seconds",
    });

    // Import or create the SNS Topic:

    // Add the SNS action to the alarm
    latencyAlarm.addAlarmAction(
      new cloudwatch_actions.SnsAction(monitoringAlertsTopic),
    );
    // Define the CloudWatch metric for ELB 5XX errors
    const elb5xxMetric = new cloudwatch.Metric({
      metricName: "HTTPCode_ELB_5XX_Count",
      namespace: "AWS/ApplicationELB",
      dimensionsMap: {
        LoadBalancer: siteServiceOutputs.LoadBalancerName,
      },
      period: cdk.Duration.seconds(60),
      statistic: "Sum",
    });

    // Create a CloudWatch alarm for 5XX errors
    const elb5xxAlarm = new cloudwatch.Alarm(this, "ELB5xxErrorsAlarm", {
      alarmName: "ELB_5XX_Errors_Alarm",
      metric: elb5xxMetric,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
      threshold: 1,
      evaluationPeriods: 1,
      alarmDescription: "Alarm when the load balancer returns 5XX errors",
      actionsEnabled: true,
    });

    // Add SNS action to the alarm
    elb5xxAlarm.addAlarmAction(
      new cloudwatch_actions.SnsAction(monitoringAlertsTopic),
    );
  }
}
