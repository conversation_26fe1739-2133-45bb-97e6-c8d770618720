import asyncio
import datetime
import hashlib
import json
import urllib.parse
from collections.abc import AsyncGenerator

import structlog
from bs4 import BeautifulSoup as bs  # noqa: N813
from playwright.async_api import Locator, expect
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class FISPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "fiscloudservices.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "Data Exchange Sungard"),
            ("<EMAIL>", "Apex"),
        ]

    @classmethod
    def email_otp_rules(cls) -> dict:
        return {
            "senderContains": ["fisclientservices.com"],
        }

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        login_email_locator = self.page.locator("input[id='email']")
        await login_email_locator.click()
        await asyncio.sleep(1)
        await login_email_locator.fill(username)
        await asyncio.sleep(1)
        login_pass_locator = self.page.locator("input[id='password']")
        await login_pass_locator.click()
        await asyncio.sleep(1)
        await login_pass_locator.fill(password)
        await asyncio.sleep(1)
        await self.page.get_by_text("Submit").click()
        await asyncio.sleep(3)

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return await self.page.get_by_label(text="One-time PIN").is_visible()

    async def _send_otp(self) -> None:
        # Not used here as it is automatically sent, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError
        otp_locator = self.page.locator("input[id='pinCode']")
        await otp_locator.click()
        await asyncio.sleep(1)
        await otp_locator.fill(otp)
        await asyncio.sleep(1)
        remember_me_locator = self.page.locator("span[id='remember-me']")
        await remember_me_locator.click()
        await asyncio.sleep(1)
        await self.page.get_by_text("Submit").click()
        await asyncio.sleep(3)
        await self.page.reload()
        await asyncio.sleep(3)

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return (
            sum(
                [
                    await self.page.locator("input[id='email']").count(),
                    await self.page.locator("input[id='password']").count(),
                    await self.page.locator("span[id='remember-me']").count(),
                    await self.page.locator("input[id='pinCode']").count(),
                    await self.page.get_by_label(text="One-time PIN").count(),
                ]
            )
            == 0
        )

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError
        k_window_locator = self.page.locator("div.k-widget.k-window.k-state-focused")
        if await k_window_locator.count() > 0:
            await self.page.keyboard.press("Escape")
            await asyncio.sleep(2)
        while True:
            try:
                # err
                await expect(self.page.locator("button.dx-cookie-banner-close.icon-Close:visible")).to_have_count(
                    0, timeout=3000
                )
                break
            except Exception:  # noqa: BLE001
                await self.page.locator("button.dx-cookie-banner-close.icon-Close:visible").first.click(force=True)

        async for doc in self._retrieve_all_files():
            yield doc

    async def _retrieve_all_files(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: PLR0915
        if self.page is None:
            raise ManagerNotInitializedError
        # Navigate to all files page
        nav_bar = self.page.locator("table.dx-navigationbar")
        find_files_tab = nav_bar.locator("td.dx-find-files-tab")
        await find_files_tab.click()
        await asyncio.sleep(1)
        await self.page.get_by_text("Files across Data Rooms").first.click()
        await asyncio.sleep(5)

        # Inside all files page
        panel_section_content = self.page.locator("div.dx-panel-section-content")
        grid_content = panel_section_content.locator("tr.dx-grid-content")
        table_locator = "tbody[role='rowgroup']"
        rowgroup = grid_content.locator(table_locator)
        seen_row_ids: set[str] = set()
        unseen_rows = await self._create_unseen_rows(
            table=table_locator, rows_identifier="tr[role='row']", seen=seen_row_ids, row_id_attribute="data-uid"
        )
        idx_count = 0
        while unseen_rows:
            row_id = unseen_rows.pop(0)
            try:
                row = rowgroup.locator(f"tr[role='row'][data-uid='{row_id}']")
                if await row.count() != 1 or not await row.is_visible():
                    logger.info("Row not visible or distinct", row_id=row_id)
                field = row.locator("td[data-field='DocumentEffectiveDate']")
                await field.click()
                await self.page.keyboard.press("ArrowDown")
                row_metadata = await self._get_row_metadata(row)
                metadata = json.dumps(row_metadata)
                viewed = row.locator("td[role='gridcell'][data-field='Viewed']")
                pdf_icon = viewed.locator("div.dx-file-type-pdf")
                is_pdf_row = await pdf_icon.count() > 0
                idx_count += 1
                if await self.should_download_document(metadata) and is_pdf_row:
                    file_cell = row.locator("td[role='gridcell'][data-field='Name']")
                    file_name = await file_cell.inner_text()
                    a_tag = file_cell.locator("a")
                    href = await a_tag.get_attribute("href")
                    base_url = "https://services.dataexchange.fiscloudservices.com"
                    full_url = urllib.parse.urljoin(base_url, href)
                    response = await self.page.request.get(full_url)
                    if response.ok:
                        doc_content = await response.body()
                        doc_category = await row.locator(
                            "td[role='gridcell'][data-field='DocumentCategories']"
                        ).inner_text()
                        doc_type = self._document_mapper(doc_category)
                        doc_effective_date = await row.locator(
                            "td[role='gridcell'][data-field='DocumentEffectiveDate']"
                        ).inner_text()
                        datetime_date = datetime.datetime.strptime(doc_effective_date[:-6], "%B %d, %Y")  # noqa: DTZ007
                        doc = RawDocumentTuple(
                            name=file_name,
                            date=datetime_date,
                            doc_type=doc_type,
                            content=doc_content,
                            raw_metadata=metadata,
                            doc_hash=self._doc_hash(metadata),
                        )
                        if doc is not None:
                            yield doc
                    else:
                        logger.error("Could not download document contents")
                seen_row_ids.add(row_id)
                unseen_rows = await self._create_unseen_rows(
                    table=table_locator,
                    rows_identifier="tr[role='row']",
                    seen=seen_row_ids,
                    row_id_attribute="data-uid",
                )
                logger.info("unseen_rows", len_unseen_rows=len(unseen_rows), seen=len(seen_row_ids))
            except PlaywrightTimeoutError:
                session_timeout_locator = self.page.get_by_role("button").filter(has_text="Extend Session")
                if await session_timeout_locator.count() > 0:
                    # click extend session and try the row_id agin
                    await session_timeout_locator.click()
                    unseen_rows.append(row_id)
                    seen_row_ids.discard(row_id)
                else:
                    logger.exception("Could not parse row_id %s content", row_id)
                    raise

    async def _get_row_metadata(self, row: Locator) -> dict[str, str]:
        row_metadata = {}
        for cell in await row.locator("td[role='gridcell']").all():
            # Want to skip data fields without any actual data
            if await cell.get_attribute("data-field") in ("Starred", "Viewed", "Checked"):
                continue
            data_field = await cell.get_attribute("data-field")
            if data_field is None:
                continue
            inner_text = await cell.inner_text()
            row_metadata[data_field] = inner_text

        return row_metadata

    async def _create_unseen_rows(
        self, table: str, rows_identifier: str, seen: set[str], row_id_attribute: str
    ) -> list[str]:
        if self.page is None:
            raise ManagerNotInitializedError
        soup = bs(markup=await self.page.content(), features="html.parser")
        table_locs = soup.select(table)
        if len(table_locs) == 0:
            raise ValueError
        rows = [row.get_attribute_list(row_id_attribute) for row in table_locs[0].select(rows_identifier)]
        return [attrs[0] for attrs in rows if len(attrs) > 0 and attrs[0] not in seen and attrs[0] is not None]

    def _document_mapper(self, raw_doc_info: str) -> DocumentType | str:
        mapping = {
            "Financial and Capital Statements": DocumentType.FINANCIAL_STATEMENTS,
            "Capital Calls": DocumentType.CAPITAL_CALL,
            "Tax Documents": DocumentType.TAX,
            "Legal": DocumentType.LEGAL,
            "Investor Updates": DocumentType.INVESTMENT_UPDATE,
            "Distributions": DocumentType.DISTRIBUTION_NOTICE,
            "General Notices": DocumentType.INVESTMENT_UPDATE,
        }

        for mapping_text in mapping:
            if mapping_text in raw_doc_info:
                return mapping.get(mapping_text, DocumentType.UNKNOWN)
        return DocumentType.UNKNOWN

    # There are 3 subportals to my knowledge thus far: Oria, Alta Point, and Haveli.
    # Currently, the implementation of this scrape makes use of an "All Files" page to scrape documents, but we may need
    # to change this implementation to navigate into each subportal and scrape documents from each one.
    # The Alta Point subportal has a very similar structure to the "All Files" page, and thus the _retrieve_all_files
    # method can be used or made slightly abstracted in order to be used for scraping both pages.
    # The Haveli and Oria subportals share a similar structure, wherein there are folders for funds, with each fund
    # containing subfolders for different document types,such as Capital Calls, Financial Statements, Legal, Tax, etc.
    # These folders will have their own nested folders to create a folder structure to hold the documents. For these
    # portals, a recursive scraping approach can be used to scrape each folder and traverse each level of nesting
    # embedded in the folder structure, with a way to navigate back to previous pages once a page has been scraped.
    # The below docstring contains some rough code to scrape the Oria subportal using this recursive approach.
    # However, there are some nuances in the portal that have not been properly accounted for in this working code.
    # Specifically, the handling of different types of containers which can contain document or folder content is one
    # example. This can serve as a strong starting point to build a working scrape from. Furthermore, much of this logic
    # can be reused for the Haveli portal scrape as these two portals are structured very similarly, with just a few
    # differences in the way page content containers are set up.
    """
    async def _retrieve_oria_capital(self) -> AsyncGenerator[RawDocumentTuple, None]:
        folders_container_outer = self.page.locator("td.layout-column.width-pixel")
        folders_content_outer = folders_container_outer.locator("div.k-listview-content")
        folder_items_outer = folders_content_outer.locator("div[role='listitem']")
        for fund in await folder_items_outer.all():
            # do this for each folder
            await fund.click()
            await asyncio.sleep(3)
            folders_container_inner = self.page.locator("td.layout-column.width-pixel")
            folders_content_inner = folders_container_inner.locator("div.k-listview-content")
            folder_items_inner = folders_content_inner.locator("div[role='listitem']")
            for fund_folder in await folder_items_inner.all():
                await fund_folder.click()
                await asyncio.sleep(2)

                async for doc in await self._retrieve_orial_capital_docs_recursively(effective_date=None):
                    yield doc

            await self._go_back_to_previous_folder()
            await self._go_back_to_previous_folder()

    async def _retrieve_orial_capital_docs_recursively(self, effective_date: str)
    -> AsyncGenerator[RawDocumentTuple, None]:
        files_outer_container = self.page.locator("td.layout-column.width-percentage")
        files_inner_container = files_outer_container.locator("td[data-section-type='InvestorAttachments']")
        container_header = files_inner_container.locator("tr.dx-panel-header.gradient")
        panel_sections = files_inner_container.locator("tr.dx-panel-sections")
        grid_content = panel_sections.locator("tr.dx-grid-content")
        rows_container = grid_content.locator("div.k-grid-content")
        rowgroup = rows_container.locator("tbody[role='rowgroup']")
        if not await self._is_main_page_content_empty():
            for row in await rowgroup.locator("tr[role='row']").all():
                a_inside_row = row.locator("a")
                if await container_header.inner_text() == "Specific Attachments":
                    row_metadata = self._get_row_metadata(row)
                    row_metadata["EffectiveDate"] = effective_date
                    metadata = json.dumps(row_metadata)
                    if await self.should_download_document(metadata):
                        filename = await row.locator("td[role='gridcell'][data-field='Name']").inner_text()
                        doc_name, doc_content = await self._download_external_link_doc(a_inside_row, filename)
                        page_folder_structure = await self._get_page_folder_structure()
                        doc_type = self._document_mapper(page_folder_structure)
                        datetime_date = datetime.datetime.strptime(doc_effective_date[:-6], "%B %d, %Y").date()
                        doc = RawDocumentTuple(
                            name=doc_name,
                            date=datetime_date,
                            doc_type=doc_type,
                            content=doc_content,
                            raw_metadata=metadata,
                            doc_hash=self._doc_hash(metadata)
                        )
                        if doc is not None:
                            yield doc
                else:
                    new_effective_date = await row.locator(
                        "td[role='gridcell'][data-field='EffectiveDate']"
                    ).inner_text()
                    await a_inside_row.click()
                    await asyncio.sleep(2)
                    async for doc in await self._retrieve_orial_capital_docs_recursively(new_effective_date):
                        yield doc

        await self._go_back_to_previous_folder()

    # If implementation needs to be changed in order to capture each subportal separately,
    # this helper function grabs the page folder structure as displayed in the Oria and Haveli Portals.
    async def _get_page_folder_structure(self) -> str:
        folder_texts = self.page.locator("div.dx-breadcrumb")
        folder_structure = ""
        for cell in await folder_texts.locator("div.dx-breadcrumb-node").all():
            folder_text = await cell.inner_text()
            folder_structure += folder_text + "/"

        return folder_structure[:-1]

    # If implementation needs to be changed to capture each subportal,
    # this helper function is used in the recursive approach in order to navigate to a previous page.
    async def _go_back_to_previous_folder(self) -> None:
        folder_texts_locator = self.page.locator("div.dx-breadcrumb")
        folder_texts_nodes = folder_texts_locator.locator("div.dx-breadcrumb-node")
        prev_folders = folder_texts_nodes.locator("a")
        prev_folders_count = await prev_folders.count()
        prev_folder = prev_folders.nth(prev_folders_count - 1) if prev_folders_count > 0 else None
        if prev_folder:
            await prev_folder.click()
            await asyncio.sleep(3)

    # This helper function can be used to download docs from the Atla Point and Haveli portals,
    # as there is a download button to download docs.
    async def _download_doc(self, download_button: Locator) -> tuple[str, bytes]:
        async with self.page.expect_download() as download_info:
            await download_button.click()
        download = await download_info.value
        file_name = Path(f"/tmp/{download.suggested_filename}")  # noqa: S108 # nosec
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.error("Unsupported file format", file_name_suffix=file_name.suffix)
            raise ValueError
        file_name.unlink()
        await asyncio.sleep(1)
        return download.suggested_filename, content

    # If implementation needs to expand to capture all subportals, this helper function will check if a page's main
    # content (either a container of folders or files) is empty, and thus we can exit this page without any scraping.
    async def _is_main_page_content_empty(self) -> bool:
        content_locator = self.page.locator("td.layout-column.hidden-width.width-percentage")
        return not await content_locator.get_attribute("style")

    # This function is used to download docs mainly from the Oria portal
    # (also used in the existing implementation to download from all files page) since there is no download button
    # for docs. Instead we need to open the docs in new page and capture the information there.
    async def _download_external_link_doc(self, download_link: Locator, filename: str) -> tuple[str, bytes]:
        async with self.page.context.expect_page() as new_page_info:
            await download_link.click()
        new_page = await new_page_info.value
        await new_page.wait_for_load_state()
        pdf_url = new_page.url

        async with httpx.AsyncClient() as client:
            response = await client.get(pdf_url)
        save_path = self.local_directory / filename
        save_path.write_bytes(response.content)

        pdf_path = Path(save_path)
        async with aiofiles.open(pdf_path, "rb") as pdf:
            pdf_bytes = await pdf.read()

        await new_page.close()

        return filename, pdf_bytes

    async def _download_doc(self, download_button: Locator) -> tuple[str, bytes]:
        async with self.page.expect_download() as download_info:
            await download_button.click()
        download = await download_info.value
        file_name = Path(f"/tmp/{download.suggested_filename}")  # noqa: S108 # nosec
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.error("Unsupported file format", file_name_suffix=file_name.suffix)
            raise ValueError
        file_name.unlink()
        await asyncio.sleep(1)
        return download.suggested_filename, content
    """
