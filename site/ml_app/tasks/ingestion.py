import structlog
from celery import Task, shared_task
from webapp.models.documents import RawDocument

from ml_app.core.ingestion.backfill import backfill_processed_doc
from ml_app.core.ingestion.config import INGESTOR

logger = structlog.get_logger(__name__)


@shared_task(bind=True, track_started=True)
def ingest_processed_doc(self: Task, *, raw_document_ids: list[str]) -> None:  # noqa: ARG001
    for raw_document_id in raw_document_ids:
        logger.info("Ingesting document", raw_document_id=raw_document_id)
        INGESTOR.ingest(raw_document_id)


@shared_task(bind=True, track_started=True)
def ingest_and_backfill_processed_doc(self: Task, *, raw_document_ids: list[str]) -> None:  # noqa: ARG001
    for raw_document_id in raw_document_ids:
        raw_doc = RawDocument.objects.get(pk=raw_document_id)
        if raw_doc.doc_chunks.count() == 0:
            logger.info("Ingesting document", raw_document_id=raw_document_id)
            INGESTOR.ingest(raw_document_id)
        logger.info("Backfilling document", raw_document_id=raw_document_id)
        backfill_processed_doc(raw_document_id)


@shared_task(bind=True, track_started=True)
def backfill_processed_docs(self: Task, *, raw_document_ids: list[str]) -> None:  # noqa: ARG001
    for raw_document_id in raw_document_ids:
        logger.info("Backfilling document", raw_document_id=raw_document_id)
        backfill_processed_doc(raw_document_id)
