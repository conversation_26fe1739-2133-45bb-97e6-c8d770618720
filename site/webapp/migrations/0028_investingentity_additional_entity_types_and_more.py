# Generated by Django 5.1.7 on 2025-04-04 19:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0027_smsmessage'),
    ]

    operations = [
        migrations.AddField(
            model_name='investingentity',
            name='additional_entity_types',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='investingentity',
            name='address',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='investingentity',
            name='dob',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='investingentity',
            name='email',
            field=models.EmailField(blank=True, max_length=254),
        ),
        migrations.AddField(
            model_name='investingentity',
            name='phone',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='investingentity',
            name='ssn_or_tin',
            field=models.Char<PERSON>ield(blank=True, max_length=50),
        ),
    ]
