import { Input } from "../../components/ui/input";
import { cn, getTypeColor, getTypeLabel } from "../../lib/utils";
import type {
  JSONSchema,
  NewField,
  ObjectJSONSchema,
  SchemaType,
} from "../../types/jsonSchema";
import {
  asObjectSchema,
  getSchemaDescription,
  isBooleanSchema,
  withObjectSchema,
} from "../../types/jsonSchema";
import React from "react";
import { ChevronDown, ChevronRight, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import TypeDropdown from "./TypeDropdown";
import TypeEditor from "../SchemaEditor/TypeEditor";

export interface SchemaPropertyEditorProps {
  name: string;
  schema: JSONSchema;
  required: boolean;
  onDelete: () => void;
  onNameChange: (newName: string) => void;
  onRequiredChange: (required: boolean) => void;
  onSchemaChange: (schema: ObjectJSONSchema) => void;
  depth?: number;
}

export const SchemaPropertyEditor: React.FC<SchemaPropertyEditorProps> = ({
  name,
  schema,
  required,
  onDelete,
  onNameChange,
  onRequiredChange,
  onSchemaChange,
  depth = 0,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingDesc, setIsEditingDesc] = useState(false);
  const [tempName, setTempName] = useState(name);
  const [tempDesc, setTempDesc] = useState(getSchemaDescription(schema));
  const type = withObjectSchema(
    schema,
    (s) => (s.type || "object") as SchemaType,
    "object" as SchemaType,
  );

  // Update temp values when props change
  useEffect(() => {
    setTempName(name);
    setTempDesc(getSchemaDescription(schema));
  }, [name, schema]);

  const handleNameSubmit = () => {
    const trimmedName = tempName.trim();
    if (trimmedName && trimmedName !== name) {
      onNameChange(trimmedName);
    } else {
      setTempName(name);
    }
    setIsEditingName(false);
  };

  const handleDescSubmit = () => {
    const trimmedDesc = tempDesc.trim();
    if (trimmedDesc !== getSchemaDescription(schema)) {
      onSchemaChange({
        ...asObjectSchema(schema),
        description: trimmedDesc || undefined,
      });
    } else {
      setTempDesc(getSchemaDescription(schema));
    }
    setIsEditingDesc(false);
  };

  // Handle schema changes, preserving description
  const handleSchemaUpdate = (updatedSchema: ObjectJSONSchema) => {
    const description = getSchemaDescription(schema);
    onSchemaChange({
      ...updatedSchema,
      description: description || undefined,
    });
  };

  return (
    <div
      className={cn(
        "mb-2 animate-in rounded-lg border transition-all duration-200",
        depth > 0 && "ml-0 sm:ml-4 border-l border-l-border/40",
      )}
    >
      <div className="relative json-field-row flex-col gap-2 group">
        <div className="flex items-start gap-2 flex-col sm:flex-row flex-grow min-w-0 w-full">
          {/* Expand/collapse button */}
          <div className="flex-shrink-0 flex items-center">
            <button
              type="button"
              className="text-white hover:text-foreground transition-colors"
              onClick={() => setExpanded(!expanded)}
              aria-label={expanded ? "Collapse" : "Expand"}
            >
              {expanded ? (
                <ChevronDown size={18} />
              ) : (
                <ChevronRight size={18} />
              )}
            </button>
          </div>

          {/* Property name and description */}
          <div className="flex flex-col gap-1 flex-grow min-w-0 w-full">
            <div className="flex items-center gap-2 min-w-0 flex-grow overflow-visible">
              {isEditingName ? (
                <Input
                  value={tempName}
                  onChange={(e) => setTempName(e.target.value)}
                  onBlur={handleNameSubmit}
                  onKeyDown={(e) => e.key === "Enter" && handleNameSubmit()}
                  className="h-8 text-sm font-medium min-w-[120px] max-w-full z-10"
                  autoFocus
                  onFocus={(e) => e.target.select()}
                />
              ) : (
                <button
                  type="button"
                  onClick={() => setIsEditingName(true)}
                  onKeyDown={(e) => e.key === "Enter" && setIsEditingName(true)}
                  className="json-field-label font-medium cursor-text px-2 py-0.5 -mx-0.5 rounded-sm hover:bg-gray-900/30 hover:shadow-sm hover:ring-1 hover:ring-ring/20 transition-all text-left truncate min-w-[80px] max-w-full"
                >
                  {name}
                </button>
              )}
            </div>
            <div className="flex items-center gap-2 min-w-0 flex-grow overflow-visible">
              {isEditingDesc ? (
                <Input
                  value={tempDesc}
                  onChange={(e) => setTempDesc(e.target.value)}
                  onBlur={handleDescSubmit}
                  onKeyDown={(e) => e.key === "Enter" && handleDescSubmit()}
                  placeholder="Add description..."
                  className="h-8 text-xs text-white italic flex-1 min-w-[150px] z-10"
                  autoFocus
                  onFocus={(e) => e.target.select()}
                />
              ) : tempDesc ? (
                <button
                  type="button"
                  onClick={() => setIsEditingDesc(true)}
                  onKeyDown={(e) => e.key === "Enter" && setIsEditingDesc(true)}
                  className="text-xs text-white italic cursor-text px-2 py-0.5 -mx-0.5 rounded-sm hover:bg-gray-900/30 hover:shadow-sm hover:ring-1 hover:ring-ring/20 transition-all text-left truncate flex-1 max-w-full mr-2"
                >
                  {tempDesc}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={() => setIsEditingDesc(true)}
                  onKeyDown={(e) => e.key === "Enter" && setIsEditingDesc(true)}
                  className="text-xs text-white/50 italic cursor-text px-2 py-0.5 -mx-0.5 rounded-sm hover:bg-gray-900/30 hover:shadow-sm hover:ring-1 hover:ring-ring/20 transition-all opacity-0 group-hover:opacity-100 text-left truncate flex-1 max-w-full mr-2"
                >
                  Add description...
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Type, Required, Delete */}
        <div className="flex flex-col gap-2 w-full mt-1">
          {/* Type Dropdown */}
          <div className="flex items-center gap-2 flex-grow">
            <TypeDropdown
              value={type}
              onChange={(newType) => {
                onSchemaChange({
                  ...asObjectSchema(schema),
                  type: newType,
                });
              }}
            />
          </div>
          {/* Required Button */}
          <div className="flex items-center gap-2 flex-grow">
            <button
              type="button"
              onClick={() => onRequiredChange(!required)}
              className={cn(
                "text-xs px-2 py-1 rounded-md font-medium min-w-[80px] text-center cursor-pointer hover:shadow-sm hover:ring-2 hover:ring-ring/30 active:scale-95 transition-all whitespace-nowrap",
                required ? "bg-red-50 text-red-500" : "bg-gray-900 text-white",
              )}
            >
              {required ? "Required" : "Optional"}
            </button>
          </div>
          {/* Delete button */}
          <div className="flex items-center gap-1 text-white">
            <button
              type="button"
              onClick={onDelete}
              className="p-1 rounded-md hover:bg-gray-900 hover:text-destructive transition-colors opacity-0 group-hover:opacity-100"
              aria-label="Delete field"
            >
              <X size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* Type-specific editor */}
      {expanded && (
        <div className="pt-1 pb-2 px-2 sm:px-3 animate-in">
          <TypeEditor
            schema={schema}
            onChange={handleSchemaUpdate}
            depth={depth + 1}
          />
        </div>
      )}
    </div>
  );
};

export default SchemaPropertyEditor;
