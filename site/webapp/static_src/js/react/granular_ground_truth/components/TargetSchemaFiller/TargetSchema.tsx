import React, { useEffect, useState } from "react";
import type { BoxData } from "js/react/granular_ground_truth/types/pdfLabeler";
import {
  getColumnName,
  JSONSchema,
} from "js/react/granular_ground_truth/types/jsonSchema";
import LabelSelectorForm from "./LabelSelectorForm";
import LabelBox from "./LabelBox";
import {
  Target,
  ChevronDown,
  ChevronRight,
  Check,
  Circle,
  X,
} from "lucide-react";
import TargetSchemaArray from "./TargetSchemaArray";
import TargetSchemaObject from "./TargetSchemaObject";
import TargetSchemaPrimative from "./TargetSchemaPrimative";
import { TargetSchemaProps } from "./TargetSchemaInterface";
import Ajv from "ajv";
import { removeIdFields } from "../../lib/schemaEditor";

const TargetSchema: React.FC<TargetSchemaProps> = ({
  schema,
  is_required,
  path,
  labeledBoxes,
  targetSchema,
  hideLabels,
  overlays,
  scale,
  currentPageNumber,
  onSaveTargetSchema,
  onDeleteBox,
  onLabelBox,
  extractTextFromBoundingBox,
}) => {
  if (typeof schema !== "object") {
    return <></>;
  }

  const [isCollapsed, setIsCollapsed] = useState(false);
  const [boxData, setBoxData] = useState<BoxData | null>(null);
  const boxDataRef = React.useRef<BoxData | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const onLabelBoxLocal = (boxes: BoxData[]) => {
    setBoxData(boxes[boxes.length - 1]);
    boxDataRef.current = boxes[boxes.length - 1];
    onLabelBox(boxes);
  };
  const onDeleteBoxLocal = (boxes: BoxData[]) => {
    if (onDeleteBox) {
      onDeleteBox(boxes);
    }
  };
  useEffect(() => {
    if (!labeledBoxes || labeledBoxes.length === 0) {
      setBoxData(null);
      boxDataRef.current = null;
      return;
    }

    const targetUri =
      typeof targetSchema === "object" && targetSchema.$id
        ? targetSchema.$id
        : null;

    const matchingBox = labeledBoxes.find(
      (box) => box.targetUri === targetUri && box.path === path,
    );

    if (matchingBox) {
      setBoxData(matchingBox || null);
      boxDataRef.current = matchingBox || null;
    }
  }, [labeledBoxes, targetSchema, path]);

  if (path === "$" && !targetSchema) {
    targetSchema = schema;
  }

  useEffect(() => {
    if (boxData && schema) {
      const ajv = new Ajv({ allErrors: true, strict: false });
      const cleanedSchema = removeIdFields(schema);
      const validate = ajv.compile(cleanedSchema);
      const valid = validate(boxData?.value);
      if (!valid && validate.errors) {
        setValidationErrors(
          validate.errors.map(
            (err) =>
              `${err.instancePath || err.schemaPath}: ${err.message || ""}`,
          ),
        );
      } else {
        setValidationErrors([]);
      }
    } else {
      setValidationErrors([]);
    }
  }, [boxData, schema]);

  const getStatus = () => {
    if (boxData?.isSaved && boxData?.isValid) {
      return (
        <div className="flex items-center gap-2" title="">
          <Check className="w-5 h-5 text-green-600" />
          <span className="text-sm text-gray-600 capitalize">Completed</span>
        </div>
      );
    }
    if (boxData?.isSaved && !boxData?.isValid) {
      return (
        <div className="flex items-center gap-2 relative group">
          <Check className="w-5 h-5 text-orange-500" />
          <span className="text-sm text-gray-600 capitalize">Not Valid</span>
          {validationErrors.length > 0 && (
            <div className="absolute left-1/2 top-full z-10 mt-2 w-64 -translate-x-1/2 rounded bg-gray-900 text-white text-xs p-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
              {validationErrors.map((err, idx) => (
                <div key={idx}>{err}</div>
              ))}
            </div>
          )}
        </div>
      );
    }
    if (!boxData?.isSaved && boxData?.isValid) {
      return (
        <div className="flex items-center gap-2" title="">
          <Circle className="w-5 h-5 text-orange-500" />
          <span className="text-sm text-gray-600 capitalize">Not Saved</span>
        </div>
      );
    }
    if (!boxData?.isSaved && !boxData?.isValid && is_required) {
      return (
        <div className="flex items-center gap-2 relative group">
          <X className="w-5 h-5 text-red-800" />
          <span className="text-sm text-gray-600 capitalize">Incomplete</span>
          {validationErrors.length > 0 && (
            <div className="absolute left-1/2 top-full z-10 mt-2 w-64 -translate-x-1/2 rounded bg-gray-900 text-white text-xs p-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
              {validationErrors.map((err, idx) => (
                <div key={idx}>{err}</div>
              ))}
            </div>
          )}
        </div>
      );
    }
    if (!boxData?.isSaved && !boxData?.isValid && !is_required) {
      return (
        <div className="flex items-center gap-2 relative group">
          <X className="w-5 h-5 text-red-400 opacity-50" />
          <span className="text-sm text-gray-600 capitalize">Incomplete</span>
          {validationErrors.length > 0 && (
            <div className="absolute left-1/2 top-full z-10 mt-2 w-64 -translate-x-1/2 rounded bg-gray-900 text-white text-xs p-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
              {validationErrors.map((err, idx) => (
                <div key={idx}>{err}</div>
              ))}
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="border border-gray-200 rounded-lg mb-4">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-gray-700 rounded-t-lg">
        <div className="flex items-center gap-3">
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="flex items-center justify-center w-6 h-6 rounded hover:bg-gray-200 transition-colors"
          >
            {isCollapsed ? (
              <ChevronRight className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </button>
          <h3 className="text-lg font-semibold text-gray-50">
            {getColumnName(schema)}
          </h3>
          {is_required && (
            <span className="text-red-500 text-sm font-medium">*</span>
          )}
        </div>

        {getStatus()}
      </div>

      {/* Content */}
      {
        <div className={`p-4 ${isCollapsed ? "hidden" : ""}`}>
          {schema.type === "array" ? (
            <TargetSchemaArray
              schema={schema}
              path={path}
              labeledBoxes={labeledBoxes}
              targetSchema={targetSchema}
              hideLabels={hideLabels}
              overlays={overlays}
              scale={scale}
              currentPageNumber={currentPageNumber}
              onLabelBox={onLabelBoxLocal}
              onDeleteBox={onDeleteBoxLocal}
              extractTextFromBoundingBox={extractTextFromBoundingBox}
              is_required={is_required}
            />
          ) : schema.type === "object" ? (
            <TargetSchemaObject
              schema={schema}
              path={path}
              labeledBoxes={labeledBoxes}
              targetSchema={targetSchema}
              hideLabels={hideLabels}
              overlays={overlays}
              scale={scale}
              currentPageNumber={currentPageNumber}
              onLabelBox={onLabelBoxLocal}
              onDeleteBox={onDeleteBoxLocal}
              extractTextFromBoundingBox={extractTextFromBoundingBox}
              is_required={is_required}
            />
          ) : (
            <TargetSchemaPrimative
              schema={schema}
              path={path}
              labeledBoxes={labeledBoxes}
              targetSchema={targetSchema}
              hideLabels={hideLabels}
              overlays={overlays}
              scale={scale}
              currentPageNumber={currentPageNumber}
              onLabelBox={onLabelBoxLocal}
              onDeleteBox={onDeleteBoxLocal}
              extractTextFromBoundingBox={extractTextFromBoundingBox}
              is_required={is_required}
            />
          )}

          {path === "$" && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
                onClick={() =>
                  onSaveTargetSchema && onSaveTargetSchema(boxData)
                }
              >
                Save
              </button>
            </div>
          )}
        </div>
      }
    </div>
  );
};

export default TargetSchema;
