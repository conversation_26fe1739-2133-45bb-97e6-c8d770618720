import re
from typing import Named<PERSON>uple

from bs4 import BeautifulSoup
from camelot.core import Table


class BoundingBox:
    def __init__(self, x0: float, y0: float, x1: float, y1: float) -> None:
        self.xmin = min(x0, x1)
        self.ymin = min(y0, y1)
        self.xmax = max(x0, x1)
        self.ymax = max(y0, y1)

    @classmethod
    def from_camelot_table(cls, table: Table) -> "BoundingBox|None":
        # Placeholder implementation
        bbox = table._bbox  # noqa: SLF001
        _, y_size = table.pdf_size
        if bbox is not None and isinstance(bbox, tuple) and len(bbox) == 4:  # noqa: PLR2004
            try:
                x0, y0, x1, y1 = bbox
                return BoundingBox(x0, max(y_size - y0, 0), x1, max(y_size - y1, 0))
            except ValueError:
                return None
        return None

    @classmethod
    def from_html_element(cls, element: str) -> "BoundingBox|None":
        soup = BeautifulSoup(element, "html.parser")
        bboxes = get_bounding_boxes(soup)
        return cls.from_bboxes(bboxes) if bboxes else None

    @classmethod
    def from_bboxes(cls, bboxes: list["BoundingBox"]) -> "BoundingBox|None":
        if not bboxes:
            return None
        x0 = bboxes[0].xmin
        y0 = bboxes[0].ymin
        x1 = bboxes[0].xmax
        y1 = bboxes[0].ymax
        for bbox in bboxes[1:]:
            x0 = min(x0, bbox.xmin)
            y0 = min(y0, bbox.ymin)
            x1 = max(x1, bbox.xmax)
            y1 = max(y1, bbox.ymax)
        return BoundingBox(x0, y0, x1, y1)


def parse_style(style: str) -> dict[str, float]:
    styles = {}
    for match in re.finditer(r"([a-zA-Z\-]+):\s*([\d.]+)px", style):
        key = match.group(1)
        val = float(match.group(2))
        styles[key] = val
    return styles


def get_bounding_boxes(soup: BeautifulSoup) -> list[BoundingBox]:
    bboxes = []
    for tag in soup.find_all(recursive=True):
        if not tag.has_attr("style"):
            continue
        styles = parse_style(tag["style"])
        top = styles.get("top")
        left = styles.get("left")
        height = styles.get("height")
        width = styles.get("width")
        font_size = styles.get("font-size")
        line_height = styles.get("line-height", font_size)
        text = tag.get_text(strip=True)

        if top is None or left is None:
            continue
        if (height is None or width is None) and (font_size is not None and line_height is not None):
            width = font_size * 0.5 * len(text)
            height = line_height
        if height is None:
            height = 0
        if width is None:
            width = 0

        bboxes.append(BoundingBox(left, top, left + width, top + height))
    return bboxes


class ParsedDocument(NamedTuple):
    text: str
    page_number: int
    format: str
    parser_name: str
    bounding_box: BoundingBox | None


class ParsedDocumentWithChunk(NamedTuple):
    parsed_document: ParsedDocument
    chunk: str
    chunk_idx: int
    chunker_name: str
    chunk_bounding_box: BoundingBox | None = None
