{% load static customtags %}
<div class="w-full flex flex-col lg:flex-row justify-between items-start lg:items-center px-4 lg:px-9 pt-6 lg:pt-10 gap-4 lg:gap-0">
    <div class="text-[#34336] font-bold font-nanum text-lg lg:text-[20px] leading-7">Onboarding Status</div>
    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full lg:w-auto">
        <!-- Portal Filter -->
        <div class="relative w-full sm:w-auto">
            <button id="dropdownSearchButton-portal"
                    type="button"
                    class="w-full sm:w-auto h-[37px] px-3 py-2 bg-white rounded-lg border border-[#d0d2bf] text-sm font-semibold flex items-center gap-2 justify-center sm:justify-start"
                    data-dropdown-toggle="dropdownSearch-portal">
                <img src="{% static 'assets/images/filter_black.svg' %}"
                     width="20"
                     height="21"
                     alt="Filter"
                     class="w-5 h-5">
                <span class="hidden sm:inline">Portal / Inbox Filter</span>
                <span class="sm:hidden">Portal Filter</span>
            </button>
            <div id="dropdownSearch-portal"
                 class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-full sm:w-70 mt-1 left-0 sm:left-auto"
                 data-dropdown>
                <ul class="h-48 px-3 py-2 overflow-y-auto text-sm text-gray-700">
                    {% for portal in portals %}
                        <li class="flex items-center px-2 py-1 rounded hover:bg-gray-100">
                            <input id="checkbox-item-portal-{{ portal.id }}"
                                   type="checkbox"
                                   name="portal"
                                   value="{{ portal.id }}"
                                   class="line_item_search w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                   hx-post="{% url 'line_item_search' %}"
                                   hx-trigger="change"
                                   hx-include=".line_item_search"
                                   hx-target="#portal_dashboard_table"
                                   hx-swap="outerHTML">
                            <label for="checkbox-item-portal-{{ portal.id }}"
                                   class=" ml-2 text-sm font-medium text-gray-900">{{ portal.name }}</label>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        <!-- Connection Status Filter -->
        <div class="relative w-full sm:w-auto">
            <button id="dropdownSearchButton-connection"
                    type="button"
                    class="w-full sm:w-auto h-[37px] px-3 py-2 bg-white rounded-lg border border-[#d0d2bf] text-sm font-semibold flex items-center gap-2 justify-center sm:justify-start"
                    data-dropdown-toggle="dropdownSearch-connection">
                <img src="{% static 'assets/images/filter_black.svg' %}"
                     alt="Filter"
                     width="20"
                     height="21"
                     class="w-5 h-5">
                <span class="hidden sm:inline">Connection Status</span>
                <span class="sm:hidden">Status</span>
            </button>
            <div id="dropdownSearch-connection"
                 class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-full sm:w-60 mt-1 left-0 sm:left-auto"
                 data-dropdown>
                <ul class="h-48 px-3 py-2 overflow-y-auto text-sm text-gray-700">
                    {% for status, label in ConnectionHealthStatus.items %}
                        <li class="flex items-center px-2 py-1 rounded hover:bg-gray-100">
                            <input id="checkbox-item-connection-{{ status }}"
                                   type="checkbox"
                                   name="connection_status"
                                   value="{{ status }}"
                                   class="line_item_search w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                   hx-post="{% url 'line_item_search' %}"
                                   hx-include=".line_item_search"
                                   hx-trigger="change"
                                   hx-target="#portal_dashboard_table"
                                   hx-swap="outerHTML">
                            <label for="checkbox-item-connection-{{ status }}"
                                   class="ml-2 text-sm font-medium text-gray-900">{{ label|capfirst }}</label>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>
