import asyncio
import hashlib
import json
import re
from collections.abc import As<PERSON><PERSON>enerator
from datetime import datetime
from pathlib import Path

import structlog
from playwright.async_api import Locator, expect
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import (
    DocumentHash,
    RawDocumentTuple,
    RetrievalManager,
)

logger = structlog.get_logger(__name__)


@register_strategy
class AltviaSolutionsPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "sharesecurely.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "Altvia Solutions, LLC"),
        ]

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError
        # Close the cookie notice modal
        await self.page.locator(".close-modal-button").click()
        await asyncio.sleep(2)
        # Focus and fill the email
        await self.page.get_by_placeholder("Email Address").count()
        await self.page.get_by_placeholder("Email Address").fill(username)
        await asyncio.sleep(2)
        # Focus and fill the password
        await self.page.get_by_placeholder("Password").count()
        await self.page.get_by_placeholder("Password").fill(password)
        await asyncio.sleep(2)
        await self.page.get_by_role("button", name="Sign in").click()
        await asyncio.sleep(5)

    async def _check_has_otp(self) -> bool:
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        await asyncio.sleep(10)
        return (
            await self.page.get_by_placeholder("Password").count()
            + await self.page.get_by_placeholder("Email Address").count()
            + await self.page.get_by_role("button", name="Sign in").count()
        ) == 0

    # choose a string here because its generic? that's how its saved in DB. so we can rerun posthoc
    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:  # noqa: C901, PLR0912, PLR0915
        if self.page is None:
            raise ManagerNotInitializedError
        await self.page.locator("a").filter(has=self.page.get_by_text("Documents")).click()
        await asyncio.sleep(5)

        footer_text = await self.page.locator(".MuiDataGrid-rowCount").inner_text()
        # Use regex to extract the total rows number
        # This pattern handles text while paginated "Total Rows: 25 of 143"
        # and when all rows are rendered "Total Rows: 143"
        match = re.search(r"Total Rows:.*?(\d+)(?:\s+of\s+(\d+))?", footer_text)
        total_rows = 0
        if match is None:
            raise CouldNotFindHTMLElementError
        # If we have "of X" format, return X (the second group) Otherwise return the first number
        total_rows = int(match.group(2)) if match.group(2) else int(match.group(1))
        # loop through all row_index
        row_index = 0
        row_retry = 0
        while row_index < total_rows:
            if row_retry > 3:  # noqa: PLR2004
                raise CouldNotFindHTMLElementError
            row_locator = self.page.locator(f'.MuiDataGrid-row[data-rowindex="{row_index}"]')

            try:
                # scroll to row index
                while await row_locator.count() == 0:
                    first_row_locator = self.page.locator(
                        "div.MuiDataGrid-virtualScrollerRenderZone div.MuiDataGrid-row"
                    ).first
                    first_row_index = await first_row_locator.get_attribute("data-rowindex")
                    last_row_locator = self.page.locator(
                        "div.MuiDataGrid-virtualScrollerRenderZone div.MuiDataGrid-row"
                    ).last
                    last_row_index = await last_row_locator.get_attribute("data-rowindex")
                    if last_row_index is None or first_row_index is None:
                        logger.error(
                            "can't find row index between first and last",
                            row_index=row_index,
                            first_row_index=first_row_index,
                            last_row_index=last_row_index,
                        )
                        break
                    if int(last_row_index) < row_index:
                        # scroll down
                        logger.info("scrolling down", last_row_index=last_row_index, looking_for_row_index=row_index)
                        await last_row_locator.scroll_into_view_if_needed()
                    elif int(first_row_index) > row_index:
                        # scroll up ? shouldn't happen but just in case
                        logger.info("scrolling up", first_row_index=first_row_index, lookking_for_row_index=row_index)
                        await first_row_locator.scroll_into_view_if_needed()
                    else:
                        logger.error(
                            "can't find row index between first and last",
                            row_index=row_index,
                            first_row_index=first_row_index,
                            last_row_index=last_row_index,
                        )
                        break
                    await self.__wait_for_progress_bar()

                if await row_locator.count() == 1:
                    await row_locator.scroll_into_view_if_needed()
                    await self.__wait_for_progress_bar()
                    (
                        doc_name,
                        content_type,
                        doc_categories,
                        parsed_doc_date,
                        parsed_shared_at_date,
                        doc_type,
                    ) = await self._get_document_metadata(row_locator)
                    metadata = json.dumps(
                        {
                            "Document Name": doc_name,
                            "Content Type": content_type,
                            "Categories": doc_categories,
                            "Document Date": parsed_doc_date.isoformat(),
                            "Shared Date": parsed_shared_at_date.isoformat(),
                        }
                    )
                    # TODO: revisit reading xlsx
                    if content_type not in ("mp4", "xlsx") and await self.should_download_document(metadata):
                        # click checkbox which shows the download button.
                        # the page reloads so the checkbox is unchecked after download
                        row_checkbox = row_locator.locator('[data-field="__check__"] input[type="checkbox"]')
                        await row_checkbox.click()
                        await asyncio.sleep(2)
                        doc = await self.__download_doc(
                            metadata,
                            parsed_doc_date,
                            doc_type,
                        )
                        await asyncio.sleep(2)
                        if doc is not None:
                            yield doc
            except Exception:
                logger.exception(
                    "Error in getting document, retrying...",
                    row_index=row_index,
                    row_locator=row_locator,
                    row_retry=row_retry,
                )
                await self.page.reload()
                row_retry += 1
                continue
            row_index += 1
            row_retry = 0

    async def _get_document_metadata(self, row_locator: Locator) -> tuple[str, str, list, datetime, datetime, str]:
        if self.page is None:
            raise ManagerNotInitializedError
        # col 1 is the checkbox
        # col 2
        doc_name = (await row_locator.locator('[data-field="name"]').inner_text()).strip()
        # col 3
        content_type = (await row_locator.locator('[data-field="content_type"]').inner_text()).strip()
        # col 4 is a button that expands the row showing category chips
        categories_expand_button = row_locator.locator('[data-field="__detail_panel_toggle__"] button')
        await categories_expand_button.click()
        await asyncio.sleep(2)
        doc_categories = [
            (await chip.inner_text()).strip()
            for chip in await self.page.locator(
                ".MuiDataGrid-detailPanels .MuiDataGrid-detailPanel .MuiChip-label"
            ).all()
        ]
        categories_collapse_button = self.page.locator(
            '[data-field="__detail_panel_toggle__"] button[aria-label="Close"]'
        )

        # the expanded row is not a child of the row locator. need to make sure only the relevant row is expanded
        categories_collapse_button_count = await categories_collapse_button.count()
        while categories_collapse_button_count >= 1:
            await categories_collapse_button.first.click(timeout=10000, force=True)
            try:
                await expect(categories_collapse_button).to_have_count(
                    categories_collapse_button_count - 1, timeout=5000
                )
            finally:
                categories_collapse_button_count = await categories_collapse_button.count()

        # col 5
        raw_date = (await row_locator.locator('[data-field="date"]').inner_text()).strip()
        parsed_doc_date = datetime.strptime(raw_date, "%b %d, %Y")  # noqa: DTZ007
        # col 6 is shared at time in the formats: "4 months ago | Nov 8, 2024 7:14 PM"
        # stripping the first part out since that's gonna change even when the doc doesn't
        raw_full_shared_at_date = await row_locator.locator('[data-field="shared_at"]').inner_text()
        _, raw_date_part = raw_full_shared_at_date.split(" | ")
        parsed_shared_at_date = datetime.strptime(raw_date_part, "%b %d, %Y %I:%M %p")  # noqa: DTZ007
        # col 7 is the "viewed" boolean, which I don't think we care about, downloading the doc toggles it to viewed
        return (
            doc_name,
            content_type,
            doc_categories,
            parsed_doc_date,
            parsed_shared_at_date,
            self.__document_mapper(doc_categories),
        )

    async def __download_doc(self, metadata: str, ref_date: datetime, doc_type: str) -> RawDocumentTuple | None:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        button_locator = self.page.locator("button").filter(has_text="Download")
        # This piece of shit fucking portal won't even start a 30mb download for >30 seconds
        # doing 60s for now
        try:
            async with self.page.expect_download(timeout=60000) as download_info:
                await button_locator.click()
        except TimeoutError:
            return None
        download = await download_info.value
        await asyncio.sleep(1)
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        # pdf, png, mp4. skipping mp4, xlsx
        if file_name.suffix.lower() == ".pdf" or file_name.suffix.lower() == ".png":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.error("Unsupported file format", file_name_suffic=file_name.suffix, file_name=file_name)
        file_name.unlink()
        if content is None:
            return None
        return RawDocumentTuple(
            name=download.suggested_filename,
            date=ref_date,
            doc_type=doc_type,
            content=content,
            raw_metadata=metadata,
            doc_hash=self._doc_hash(metadata),
        )

    def __document_mapper(self, doc_categories: list[str]) -> str:
        document_mapping = {
            "Tax Documents": DocumentType.TAX,
            "Legal Correspondence": DocumentType.LEGAL,
            "Capital Call": DocumentType.CAPITAL_CALL,
            "Distribution Notice": DocumentType.DISTRIBUTION_NOTICE,
            "General Correspondence": DocumentType.INVESTMENT_UPDATE,
            "Quarterly & Annual Reports": DocumentType.INVESTMENT_UPDATE,
        }
        for category in doc_categories:
            if category in document_mapping:
                return document_mapping[category]
        return DocumentType.OTHER if len(doc_categories) > 0 else DocumentType.UNKNOWN

    async def __wait_for_progress_bar(self, time_to_wait: float = 600000) -> None:
        progress_bar_locator = self.page.get_by_role("progressbar")
        try:
            # Wait for the progress bar to disappear
            await expect(progress_bar_locator).to_have_count(0, timeout=time_to_wait)
            logger.info("Progress bar disappeared")
        except PlaywrightTimeoutError:
            logger.exception("Progress bar didn't disappear after timeout %fms", time_to_wait)
            raise CouldNotFindHTMLElementError from PlaywrightTimeoutError
