import asyncio
import random
import time

import structlog
from asgiref.sync import sync_to_async
from celery import Task, shared_task
from celery.exceptions import Ignore, SoftTimeLimitExceeded
from django.conf import settings
from patchright.async_api import TimeoutError as PatchrightTimeoutError
from patchright.async_api import async_playwright as async_patchright
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from playwright.async_api import async_playwright
from webapp.models import BridgeUser, CanceledRetrievalError, MergedPortalCredential, Portal, Retrieval
from webapp.utils.slack import send_message_to_slack

from retrieval.core.exceptions import CouldNotFindHTMLElementError, LockCouldNotBeAcquiredError
from retrieval.core.registry import RetrievalRegistry
from retrieval.tasks.utils import aquire_login_portal_lock, singleton_task

logger = structlog.get_logger(__name__)


@shared_task(bind=True, track_started=True, time_limit=4 * 60 * 60 + 300, soft_time_limit=4 * 60 * 60)
def login_portal_task(self: Task, *, retrieval_id: str) -> None:
    try:
        retrieval = Retrieval.objects.get(id=retrieval_id)
        retrieval.task_id = str(self.request.id)
        retrieval.save()
        user = retrieval.created_by
        if user is None:
            logger.error("No user found for retrieval", retrieval_pk=retrieval.pk)
            retrieval.update_login_status(Retrieval.RetrievalStatus.CANCELED)
            raise ValueError
        manager_cls = RetrievalRegistry.get_retrieval_manager(
            user=user,
            name=retrieval.merged_portal_credential.portal.name,
            portal_login_url=retrieval.merged_portal_credential.portal.portal_login_url,
        )
        if manager_cls is None:
            logger.error("No manager found for retrieval", retrieval_pk=retrieval.pk)
            retrieval.update_login_status(Retrieval.RetrievalStatus.CANCELED)
            raise ValueError
        try:
            with aquire_login_portal_lock(retrieval.merged_portal_credential, manager_cls.__name__):
                if retrieval.retrieval_status not in {
                    Retrieval.RetrievalStatus.PENDING_SCHEDULE,
                    Retrieval.RetrievalStatus.NOT_STARTED,
                }:
                    self.update_state(
                        state="REVOKED",
                        meta={
                            "reason": "Retrieval is not in NOT_STARTED/PENDING_SCHEDULE state",
                            "retrieval_id": retrieval_id,
                        },
                    )
                    retrieval.update_login_status(Retrieval.RetrievalStatus.CANCELED)
                    raise Ignore
                retrieval.update_login_status(Retrieval.RetrievalStatus.SUBMITTED)
                retrieval.update_start_time()
                asyncio.run(async_login_portal_task(retrieval_id=str(retrieval.pk)))
        except LockCouldNotBeAcquiredError as e:
            self.update_state(
                state="REVOKED",
                meta={
                    "reason": "Lock could not be acquired for retrieval",
                    "retrieval_id": retrieval_id,
                },
            )
            # If the retrieval was manually triggered, we can mark it as canceled
            # If it came from a scheduled task, it will be retried later,
            # an implicit blocking lock
            if retrieval.retrieval_status == Retrieval.RetrievalStatus.NOT_STARTED:
                retrieval.update_login_status(Retrieval.RetrievalStatus.CANCELED)
            raise Ignore from e
    except SoftTimeLimitExceeded:
        logger.info("Max time limit reached for portal task", retrieval_pk=retrieval.pk)
        retrieval.update_login_status(Retrieval.RetrievalStatus.DOCUMENT_RETRIEVAL_EXCEEDED_MAX_TIME_LIMIT)


async def async_login_portal_task(retrieval_id: str) -> None:  # noqa: C901, PLR0912, PLR0915
    manager = None
    try:
        retrieval = await Retrieval.objects.select_related("created_by").aget(id=retrieval_id)
        user_id = retrieval.created_by.pk
        user = await BridgeUser.objects.aget(id=user_id)
        if user is None:
            logger.error("No user found for retrieval", retrieval_pk=retrieval.pk)
            raise ValueError
        manager_cls = await RetrievalRegistry.aget_retrieval_manager_for_retrieval(user, retrieval)
        needs_playwright = False
        needs_patchright = False
        if manager_cls is not None:
            needs_playwright = manager_cls.needs_playwright()
            needs_patchright = manager_cls.needs_patchright()
        if needs_patchright and needs_playwright:
            raise ValueError
        playwright_instance = None
        if needs_playwright:
            playwright_instance = await async_playwright().start()
        if needs_patchright:
            playwright_instance = await async_patchright().start()
        if manager_cls is not None:
            manager = manager_cls(playwright_instance=playwright_instance, retrieval=retrieval, user=user)
            if settings.DEBUG and not await user.ais_demo():
                logger.info(
                    "Running retrieval manager in debug mode",
                    retrieval_pk=retrieval_id,
                    manager_cls=manager_cls.__name__,
                )
                await asyncio.sleep(10)
                if random.uniform(0, 1) > 0.5:  # noqa: PLR2004, S311 # nosec
                    msg = "Simulating a timeout in debug mode"
                    logger.info(
                        msg,
                        retrieval_pk=retrieval_id,
                        manager_cls=manager_cls.__name__,
                    )
                    raise PlaywrightTimeoutError(msg)  # noqa: TRY301
                await manager.update_login_status(Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL)
                return
            try:
                await manager.async_init()
                await manager.login()
                await manager.retrieve()
            finally:
                await manager.cleanup()
        if manager_cls is None:
            logger.info("No manager found for retrieval", retrieval_pk=retrieval_id)

    except CanceledRetrievalError:
        logger.exception("Retrieval was canceled", retrieval_pk=retrieval_id)
        await update_login_status(retrieval_id, Retrieval.RetrievalStatus.CANCELED)
        raise
    except (PlaywrightTimeoutError, PatchrightTimeoutError, CouldNotFindHTMLElementError):
        await mark_retry(retrieval_id)
        logger.exception("Retrieval ran into a timeout", retrieval_pk=retrieval_id)
        raise
    except:
        logger.exception("Retrieval ran into unknown error", retrieval_pk=retrieval_id)
        await update_login_status(retrieval_id, Retrieval.RetrievalStatus.FAILED_PORTAL_DOWN)
        raise


@sync_to_async
def update_login_status(retrieval_id: str, status: Retrieval.RetrievalStatus) -> None:
    retrieval = Retrieval.objects.get(id=retrieval_id)
    retrieval.update_login_status(status)


@sync_to_async
def mark_retry(retrieval_id: str) -> None:
    failed_retrieval = Retrieval.objects.get(id=retrieval_id)
    should_slack = False
    if failed_retrieval.number_of_retries >= Retrieval.MAX_RETRIES:
        should_slack = True
        logger.error("Max retries reached for retrieval", retrieval_pk=failed_retrieval.pk)
    else:
        retry_with_new_retrieval(failed_retrieval, failed_retrieval.number_of_retries + 1)
    failed_retrieval.update_login_status(Retrieval.RetrievalStatus.FAILED_PORTAL_DOWN_RETRY, should_slack=should_slack)


@shared_task(bind=True, track_started=True)
@singleton_task()
def schedule_all_portal_extractions(self: Task) -> None:  # noqa: ARG001
    for manager_class in RetrievalRegistry.manager_interfaces:
        for email, portal_name in manager_class.known_user_portals():
            try:
                user = BridgeUser.objects.get(email=email)
                portal = Portal.objects.get(name=portal_name, organization=user.organization)
                merged_portal_credential = MergedPortalCredential.objects.for_user(user).filter(portal=portal).first()
                if merged_portal_credential is None or merged_portal_credential.created_by is None:
                    logger.info("No merged portal credential found", portal_name=portal_name, email=email)
                    continue
                manager_cls = RetrievalRegistry.get_retrieval_manager(
                    user=user, name=portal.name, portal_login_url=portal.portal_login_url
                )
                if manager_cls is None:
                    logger.error("No manager found", portal_name=portal_name, email=email)
                    continue
                if (
                    not Retrieval.objects.filter(
                        merged_portal_credential=merged_portal_credential,
                        retrieval_status=Retrieval.RetrievalStatus.PENDING_SCHEDULE,
                    ).exists()
                    and manager_cls is not None
                ):
                    Retrieval.create(
                        user=user,
                        merged_portal_credential=merged_portal_credential,
                        manager=manager_cls.__name__,
                        retrieval_status=Retrieval.RetrievalStatus.PENDING_SCHEDULE,
                    )
            except Exception:
                logger.exception("Error building schedule for portal extraction", portal_name=portal_name, email=email)


@shared_task(bind=True, track_started=True)
@singleton_task()
def execute_portal_extractions(self: Task) -> None:  # noqa: ARG001
    retrievals = Retrieval.objects.filter(
        retrieval_status=Retrieval.RetrievalStatus.PENDING_SCHEDULE,
    ).all()
    for retrieval in retrievals:
        # add a bit of delay to setup locks
        time.sleep(1)
        login_portal_task.apply_async(
            kwargs={
                "retrieval_id": str(retrieval.pk),
            },
            queue="medium",
        )


@shared_task(bind=True, track_started=True)
def rerun_retrieval_task(self: Task, failed_retrieval_id: str) -> None:  # noqa: ARG001
    """Handle rerun scrape logic asynchronously"""
    try:
        failed_retrieval = Retrieval.objects.get(id=failed_retrieval_id)
        retrieval = retry_with_new_retrieval(failed_retrieval)
        # Send success message to Slack
        blocks = [
            {
                "type": "header",
                "text": {"type": "plain_text", "text": ":large_yellow_circle: Retrying Retrieval", "emoji": True},
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*Failed Object ID:*\n<{failed_retrieval.admin_panel_link}|{failed_retrieval.pk}>",
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*New Object ID:*\n<{retrieval.admin_panel_link}|{retrieval.pk}>",
                    },
                ],
            },
            retrieval.retrieval_meta_data_slack_block,
        ]
        message = f"Successfully started retry for Retrieval <{retrieval.admin_panel_link}|{retrieval.pk}>"
        send_message_to_slack(channel="#portals", message=message, blocks=blocks)

    except Exception as e:
        logger.exception("rerun_retrieval_task failed", error=str(e), retrieval_id=failed_retrieval_id)
        # Send error message to Slack
        send_message_to_slack(
            channel="#portals", message=f"Failed to rerun scrape for retrieval {failed_retrieval_id}: {e!s}"
        )


def retry_with_new_retrieval(failed_retrieval: Retrieval, retry_count: int = 0) -> Retrieval:
    retrieval = Retrieval.create(
        user=failed_retrieval.created_by,
        merged_portal_credential=failed_retrieval.merged_portal_credential,
        number_of_retries=retry_count,
    )
    retrieval_id = str(retrieval.id)

    task = login_portal_task.apply_async(kwargs={"retrieval_id": retrieval_id})
    retrieval.task_id = task.id

    if failed_retrieval.check_point:
        starting_point = failed_retrieval.check_point
        logger.info("setting starting_point", starting_point=starting_point)
        retrieval.starting_point = starting_point

    retrieval.save()
    return retrieval


@shared_task(bind=True, track_started=True)
@singleton_task()
def singleton_noop_task(self: Task) -> None:  # noqa: ARG001
    time.sleep(60)


@shared_task(bind=True, track_started=True)
def noop_task(self: Task) -> None:  # noqa: ARG001
    time.sleep(60)
