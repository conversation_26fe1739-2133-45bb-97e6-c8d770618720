{% load static %}
<aside id="default-sidebar"
    class="fixed top-0 left-0 z-40 h-screen w-16 bg-primary-dark-olive transition-all duration-300"
    aria-label="Sidebar"
    {# djlint:off #}
       _="on click
       if target.closest('.nav-link') then exit end
       if my.classList.contains('w-16') then
         remove .w-16 from me
         add .w-64 to me
         remove .hidden from #big_logo
         add .hidden to #small_logo
         add .p-2 to #log_a_tag
         remove .p-1 from #log_a_tag
         remove .hidden from .sidebar-text in me
         if .main-content exists then
           add .transition-all to .main-content
           add .duration-700 to .main-content
           remove .ml-16 from .main-content
           add .ml-64 to .main-content
           add .lg:ml-64 to .main-content
           add .md:ml-32 to .main-content
           add .sm:ml-16 to .main-content
           add .gap-96 to .main-content
         end
       else
         add .w-16 to me
         remove .w-64 from me
         add .hidden to #big_logo
         remove .hidden from #small_logo
         remove .p-2 from #log_a_tag
         add .p-1 to #log_a_tag
         add .hidden to .sidebar-text in me
         if .main-content exists then
           add .transition-all to .main-content
           add .duration-700 to .main-content
           remove .ml-64 from .main-content
           remove .lg:ml-64 from .main-content
           remove .md:ml-32 from .main-content
           remove .sm:ml-16 from .main-content
           remove .gap-96 from .main-content
           add .ml-16 to .main-content
         end
       end
     on load
       set currentPath to window.location.pathname
       for link in .nav-link in me
         if link.getAttribute('href') is currentPath then
           add .bg-base-darker-olive-muted to link
         else
           remove .bg-base-darker-olive-muted from link
         end
       end
     on htmx:afterSwap
       set currentPath to window.location.pathname
       for link in .nav-link in me
         if link.getAttribute('href') is currentPath then
           add .bg-base-darker-olive-muted to link
         else
           remove .bg-base-darker-olive-muted from link
         end
       end"
    {# djlint:on #}>
    <div class="h-full px-3 py-4 overflow-y-auto flex flex-col justify-between">
        <div>
            <div id="log_a_tag" class="flex items-start mb-5 p-2">
                <img class="hidden"
                     id="big_logo"
                     src="{% static 'assets/images/logo.svg' %}"
                     width="129"
                     height="36"
                     alt="Bridge Logo" />
                <img id="small_logo"
                     src="{% static 'assets/images/Bridge_Logo_white.png' %}"
                     width="80"
                     height="80"
                     alt="Small Bridge Logo" />
            </div>
            <button hidden id="sidebar-toggle" class="mb-4 text-white focus:outline-none">
                <svg xmlns="http://www.w3.org/2000/svg"
                     class="w-6 h-6"
                     fill="none"
                     viewBox="0 0 24 24"
                     stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
                </svg>
            </button>
            <ul class="space-y-2 font-medium">
                <li>
                    <a href="{% url 'dashboard' %}"
                       class="nav-link flex items-center p-2 rounded-lg text-secondary-faded-green hover:bg-base-darker-olive-muted dark:hover:bg-base-darker-olive-muted group"
                       _="on click halt the event's bubbling">
                        <img alt="Vault Icon"
                             width="20"
                             height="20"
                             src="{% static 'assets/images/dashboard.svg' %}"
                             class="w-5 h-5">
                        <span class="ms-3 sidebar-text hidden">Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'vault' %}"
                       class="nav-link flex items-center p-2 rounded-lg text-secondary-faded-green hover:bg-base-darker-olive-muted dark:hover:bg-base-darker-olive-muted group"
                       _="on click halt the event's bubbling">
                        <img alt="Vault Icon"
                             width="20"
                             height="20"
                             src="{% static 'assets/images/vault.svg' %}"
                             class="w-5 h-5">
                        <span class="flex-1 ms-3 sidebar-text hidden whitespace-nowrap">Vault</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'link_dashboard' %}"
                       class="nav-link flex items-center p-2 rounded-lg text-secondary-faded-green hover:bg-base-darker-olive-muted dark:hover:bg-base-darker-olive-muted group"
                       _="on click halt the event's bubbling">
                        <img alt="Vault Icon"
                             width="20"
                             height="20"
                             src="{% static 'assets/images/link.svg' %}"
                             class="w-5 h-5">
                        <span class="flex-1 ms-3 sidebar-text hidden">Link</span>
                    </a>
                </li>
                {% if user.is_demo or user.organization.name == "Aspiriant" %}
                    <li>
                        <a href="{% url 'insight_home' %}"
                           hx-boost="false"
                           class="nav-link flex items-center p-2 rounded-lg text-secondary-faded-green hover:bg-base-darker-olive-muted dark:hover:bg-base-darker-olive-muted group"
                           _="on click halt the event's bubbling">
                            <img alt="Insights Icon"
                                 width="20"
                                 height="20"
                                 src="{% static 'assets/images/insight.svg' %}"
                                 class="w-5 h-5">
                            <span class="flex-1 ms-3 sidebar-text hidden whitespace-nowrap">Insights</span>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</aside>
