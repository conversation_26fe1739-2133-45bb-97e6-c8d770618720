import pytest
from django.core.exceptions import ValidationError
from django.db import models
from django.test import TestCase

from webapp.models.base import StateMachineChoices, StateMachineField


# Test state machine choices class
class TestStatus(StateMachineChoices):
    """Test state machine for testing purposes."""

    INITIAL = "initial", "Initial"
    IN_PROGRESS = "in_progress", "In Progress"
    COMPLETED = "completed", "Completed"
    FAILED = "failed", "Failed"

    @classmethod
    def valid_transitions(cls) -> dict[str, list[str]]:
        """Define valid transitions for testing."""
        return {
            cls.INITIAL: [cls.IN_PROGRESS],
            cls.IN_PROGRESS: [cls.COMPLETED, cls.FAILED],
            cls.COMPLETED: [],  # Terminal state
            cls.FAILED: [cls.INITIAL],  # Can restart from failure
        }


# Test model using the state machine field
class TestModel(models.Model):
    """Test model for testing the StateMachineField."""

    status = StateMachineField(
        state_choices=TestStatus,
        default=TestStatus.INITIAL,
    )

    # This is needed for testing without migrations
    class Meta:
        app_label = "webapp"
        # Use a unique database table name to avoid conflicts
        db_table = "test_state_machine_model"
        # Mark as managed to create the table during tests
        managed = True


@pytest.mark.django_db
class TestStateMachineChoices:
    """Tests for the StateMachineChoices class."""

    def test_validate_transition_same_state(self) -> None:
        """Test that transitions to the same state are always valid."""
        assert TestStatus.validate_transition(TestStatus.INITIAL, TestStatus.INITIAL) is True
        assert TestStatus.validate_transition(TestStatus.IN_PROGRESS, TestStatus.IN_PROGRESS) is True
        assert TestStatus.validate_transition(TestStatus.COMPLETED, TestStatus.COMPLETED) is True
        assert TestStatus.validate_transition(TestStatus.FAILED, TestStatus.FAILED) is True

    def test_validate_transition_valid(self) -> None:
        """Test that valid transitions are allowed."""
        assert TestStatus.validate_transition(TestStatus.INITIAL, TestStatus.IN_PROGRESS) is True
        assert TestStatus.validate_transition(TestStatus.IN_PROGRESS, TestStatus.COMPLETED) is True
        assert TestStatus.validate_transition(TestStatus.IN_PROGRESS, TestStatus.FAILED) is True
        assert TestStatus.validate_transition(TestStatus.FAILED, TestStatus.INITIAL) is True

    def test_validate_transition_invalid(self) -> None:
        """Test that invalid transitions are not allowed."""
        assert TestStatus.validate_transition(TestStatus.INITIAL, TestStatus.COMPLETED) is False
        assert TestStatus.validate_transition(TestStatus.INITIAL, TestStatus.FAILED) is False
        assert TestStatus.validate_transition(TestStatus.COMPLETED, TestStatus.IN_PROGRESS) is False
        assert TestStatus.validate_transition(TestStatus.COMPLETED, TestStatus.FAILED) is False
        assert TestStatus.validate_transition(TestStatus.FAILED, TestStatus.COMPLETED) is False

    def test_get_valid_next_states(self) -> None:
        """Test getting valid next states from a given state."""
        # Initial state can transition to itself or IN_PROGRESS
        assert set(TestStatus.get_valid_next_states(TestStatus.INITIAL)) == {TestStatus.INITIAL, TestStatus.IN_PROGRESS}

        # IN_PROGRESS can transition to itself, COMPLETED, or FAILED
        assert set(TestStatus.get_valid_next_states(TestStatus.IN_PROGRESS)) == {
            TestStatus.IN_PROGRESS,
            TestStatus.COMPLETED,
            TestStatus.FAILED,
        }

        # COMPLETED is a terminal state, can only transition to itself
        assert set(TestStatus.get_valid_next_states(TestStatus.COMPLETED)) == {TestStatus.COMPLETED}

        # FAILED can transition to itself or back to INITIAL
        assert set(TestStatus.get_valid_next_states(TestStatus.FAILED)) == {TestStatus.FAILED, TestStatus.INITIAL}


@pytest.mark.django_db
class TestStateMachineField(TestCase):
    """Tests for the StateMachineField class."""

    def setUp(self) -> None:
        """Set up the test environment."""
        # Create the test model table
        self.model = TestModel.objects.create()

    def test_default_value(self) -> None:
        """Test that the default value is set correctly."""
        assert self.model.status == TestStatus.INITIAL

    def test_valid_transition(self) -> None:
        """Test that valid transitions are allowed."""
        # INITIAL -> IN_PROGRESS (valid)
        self.model.status = TestStatus.IN_PROGRESS
        self.model.save()
        assert self.model.status == TestStatus.IN_PROGRESS

        # IN_PROGRESS -> COMPLETED (valid)
        self.model.status = TestStatus.COMPLETED
        self.model.save()
        assert self.model.status == TestStatus.COMPLETED

    def test_invalid_transition(self) -> None:
        """Test that invalid transitions raise ValidationError."""
        # INITIAL -> COMPLETED (invalid)
        self.model.status = TestStatus.COMPLETED
        with pytest.raises(ValidationError) as excinfo:
            self.model.save()

        # Check that the error message contains useful information
        error_msg = str(excinfo.value)
        assert "Invalid state transition" in error_msg
        assert TestStatus.INITIAL in error_msg
        assert TestStatus.COMPLETED in error_msg

    def test_complex_transition_path(self) -> None:
        """Test a more complex path of transitions."""
        # INITIAL -> IN_PROGRESS -> FAILED -> INITIAL -> IN_PROGRESS -> COMPLETED
        self.model.status = TestStatus.IN_PROGRESS
        self.model.save()

        self.model.status = TestStatus.FAILED
        self.model.save()

        self.model.status = TestStatus.INITIAL
        self.model.save()

        self.model.status = TestStatus.IN_PROGRESS
        self.model.save()

        self.model.status = TestStatus.COMPLETED
        self.model.save()

        assert self.model.status == TestStatus.COMPLETED

    def test_same_state_transition(self) -> None:
        """Test that transitioning to the same state is always allowed."""
        # INITIAL -> INITIAL (valid)
        self.model.status = TestStatus.INITIAL
        self.model.save()
        assert self.model.status == TestStatus.INITIAL

        # Change to IN_PROGRESS first
        self.model.status = TestStatus.IN_PROGRESS
        self.model.save()

        # IN_PROGRESS -> IN_PROGRESS (valid)
        self.model.status = TestStatus.IN_PROGRESS
        self.model.save()
        assert self.model.status == TestStatus.IN_PROGRESS


@pytest.mark.django_db
class TestStateMachineFieldEdgeCases(TestCase):
    """Tests for edge cases of the StateMachineField."""

    def test_new_instance_any_state(self) -> None:
        """Test that a new instance can be created with any state."""
        # New instances should be able to start in any state
        # This is because we're not validating transitions for new instances
        model = TestModel(status=TestStatus.COMPLETED)
        model.save()
        assert model.status == TestStatus.COMPLETED

        model = TestModel(status=TestStatus.FAILED)
        model.save()
        assert model.status == TestStatus.FAILED

    def test_invalid_state_value(self) -> None:
        """Test handling of invalid state values."""
        # Django's validation for choices happens during full_clean, not during save
        model = TestModel(status="invalid")

        # The error should be raised during validation, not during save
        with pytest.raises(ValidationError) as excinfo:
            model.full_clean()

        # Check that the error message mentions the invalid choice
        error_msg = str(excinfo.value)
        assert "is not a valid choice" in error_msg
