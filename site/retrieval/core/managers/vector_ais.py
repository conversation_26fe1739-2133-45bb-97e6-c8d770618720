import asyncio
import datetime
import hashlib
import json
from collections.abc import As<PERSON><PERSON><PERSON>ator
from pathlib import Path

import structlog
from playwright.async_api import Locator
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import CouldNotFindHTMLElementError, ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import Document<PERSON>ash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class VectorAISPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "vectorais.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [("<EMAIL>", "Valence (Vector AIS)")]

    @classmethod
    def email_otp_rules(cls) -> dict:
        return {
            "senderContains": ["valence.vectorais.com"],
        }

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError

        email_input_locator = self.page.locator("input[name='email']")
        password_input_locator = self.page.locator("input[name='password']")
        login_button_locator = self.page.locator("button[type='submit']")
        if await email_input_locator.count() == 1 and await email_input_locator.is_visible():
            await email_input_locator.click()
            await email_input_locator.fill(username)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        if await password_input_locator.count() == 1 and await password_input_locator.is_visible():
            await password_input_locator.click()
            await password_input_locator.fill(password)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        if await login_button_locator.count() == 1 and await login_button_locator.is_visible():
            await login_button_locator.click()
            await asyncio.sleep(5)
        else:
            raise CouldNotFindHTMLElementError

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError

        new_code_text_ct = await self.page.get_by_text("OTP Verification").count()
        otp_input_ct = await self.page.locator("input[name='otpCode']").count()
        return (new_code_text_ct + otp_input_ct) > 0

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:
        if self.page is None:
            raise ManagerNotInitializedError

        otp_input_locator = self.page.locator("input[name='otpCode']")
        if otp_input_locator and await otp_input_locator.count() == 1 and await otp_input_locator.is_visible():
            await otp_input_locator.click()
            await otp_input_locator.fill(otp)
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        """
        remember_me_locator = self.page.locator("input[type='checkbox'][name='dontAskOtp']")
        if await remember_me_locator.count() == 1 and await remember_me_locator.is_visible():
            await remember_me_locator.check()
            await asyncio.sleep(1)
        else:
            raise CouldNotFindHTMLElementError
        """
        verify_code_button_locator = self.page.locator("button[type='submit']")
        if await verify_code_button_locator.count() == 1 and await verify_code_button_locator.is_visible():
            await verify_code_button_locator.click()
            await asyncio.sleep(10)
        else:
            raise CouldNotFindHTMLElementError

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return await self.page.get_by_text("Dashboard").count() > 0

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        sidebar_locator = self.page.locator("div.sidebar-links")
        sidebar_link_group = sidebar_locator.locator("ul")
        sidebar_links = sidebar_link_group.locator("li")
        sidebar_links_count = await sidebar_links.count()

        for i in range(sidebar_links_count):
            sidebar_link = sidebar_links.nth(i)
            sidebar_link_title_raw = await sidebar_link.inner_text()
            sidebar_link_title_stripped = sidebar_link_title_raw.strip()
            if sidebar_link_title_stripped in ("Dashboard", "Team Members & Investors"):
                continue
            await sidebar_link.click()
            await asyncio.sleep(10)
            async for doc in self._download_page(sidebar_link_title_stripped):
                yield doc

    async def _download_page(self, page_title: str) -> AsyncGenerator[RawDocumentTuple, None]:
        await self.page.evaluate("document.body.style.zoom = '10%'")
        page_content_grid_locator = self.page.locator("div[role='grid'][tabulator-layout='fitData']")
        page_content_table_locator = page_content_grid_locator.locator("div.tabulator-tableholder")
        table_row_group = page_content_table_locator.locator("div[role='rowgroup']")
        if await table_row_group.count() == 0:
            return
        table_rows = table_row_group.locator("div[role='row']")
        table_rows_count = await table_rows.count()
        if table_rows_count == 0:
            return

        for i in range(table_rows_count):
            row = table_rows.nth(i)
            row_metadata = await self._get_row_metadata(row)
            row_metadata["page_title"] = page_title
            metadata = json.dumps(row_metadata)
            if await self.should_download_document(metadata):
                download_button = row.locator("button")
                await download_button.scroll_into_view_if_needed()
                await download_button.wait_for(state="visible")
                if (not await download_button.is_visible()) or (await download_button.count() != 1):
                    logger.info("Could not download document", row=row, row_metadata=row_metadata)
                    continue
                doc_name, doc_content = await self._download_document(download_button)
                if doc_content is None:
                    logger.info("Could not download documnet", row=row, row_metadata=row_metadata)
                    continue
                doc_type = self._document_mapper(page_title)
                if "created_at" not in row_metadata:
                    logger.info("Document does not have a created at date")
                datetime_date = datetime.datetime.strptime(row_metadata["created_at"], "%m/%d/%Y")  # noqa: DTZ007
                doc = RawDocumentTuple(
                    name=doc_name,
                    date=datetime_date,
                    doc_type=doc_type,
                    content=doc_content,
                    raw_metadata=metadata,
                    doc_hash=self._doc_hash(metadata),
                )
                if doc is not None:
                    yield doc

    async def _get_row_metadata(self, row: Locator) -> dict[str, str]:
        row_metadata = {}
        cells = row.locator("div[role='gridcell']")
        cells_count = await cells.count()
        for i in range(cells_count):
            cell = cells.nth(i)
            tabulator_field = await cell.get_attribute("tabulator-field")
            if tabulator_field:
                if tabulator_field == "select-all":
                    continue
                row_metadata[tabulator_field] = await cell.inner_text()
        return row_metadata

    async def _download_document(self, download_button: Locator) -> tuple[str, bytes]:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        async with self.page.expect_download() as download_info:
            await download_button.click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        if file_name.suffix.lower() == ".pdf":
            with Path.open(file_name, "rb") as pdf_file:
                content = pdf_file.read()
        else:
            logger.error("Unsupported file format", file_name_suffix=file_name.suffix)
        file_name.unlink()
        return download.suggested_filename, content

    def _document_mapper(self, document_type: str | None) -> DocumentType | str:
        if document_type is None:
            return DocumentType.UNKNOWN

        mapping = {
            "Capital Calls": DocumentType.CAPITAL_CALL,
            "Capital Account Statements": DocumentType.ACCOUNT_STATEMENT,
            "Investor Reporting": DocumentType.FINANCIAL_STATEMENTS,
            "Distributions": DocumentType.DISTRIBUTION_NOTICE,
            "Legal Documents": DocumentType.LEGAL,
            "Tax Documents": DocumentType.TAX,
            "Investor Communication": DocumentType.INVESTMENT_UPDATE,
        }

        return mapping.get(document_type, DocumentType.UNKNOWN)
