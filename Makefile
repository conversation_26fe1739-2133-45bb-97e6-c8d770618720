SHELL := /bin/bash

help:  ## Show this help message
	@echo "Usage: make [target]"
	@echo ""
	@echo "Available targets:"
	@grep -E '^[a-zA-Z_-]+:.*##' $(MAKEFILE_LIST) \
	| sort \
	| awk 'BEGIN {FS = ":.*?##"} {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}'

AWS_PROFILE      ?= default
SITE_INIT_ACTION ?= reset

mypy-check:  ## Run local mypy
	PYTHONPATH=site ENVIRONMENT=dev uv run mypy site ml

lint-fix-python:  ## Run local Python linter and fix
	uv run ruff check --fix .

lint-check-python:  ## Run local Python linters
	uv run ruff check .
	uv run bandit -c pyproject.toml -r .

format-fix-python:  ## Run local Python formatter
	uv run ruff format .

lint-fix-django:  ## Run local Django linter
	uv run djlint . --reformat

lint-check-django: ## Run local Django linter
	uv run djlint . --check
	uv run djlint . --lint

lint-check-js:  ## Run local JS linter
	npx eslint infrastructure site/webapp/static_src/js --ext .js,.jsx

format-fix-js:  ## Run local formatter
	npx prettier infrastructure --write
	npx prettier site/webapp/static_src/js --write

lint-fix-js:  ## Run local JS linter and autofix issues
	npx eslint infrastructure site/webapp/static_src/js --ext .js,.jsx --fix

lint-fix-all:  ## Run local Python linter and auto-fix issues
	./lint.py --fix
	#@$(MAKE) lint-fix-python
	#@$(MAKE) format-fix-python  # not standard yet
	#@$(MAKE) lint-fix-django
	#@$(MAKE) format-fix-js
	#@$(MAKE) lint-fix-js  # not standard yet

lint-check-all:  ## Run local linters
	./lint.py
	#@$(MAKE) lint-check-python
	#@$(MAKE) lint-check-django
	#@$(MAKE) lint-check-js

test-python:  ## Run local Python tests
	PYTHONPATH=site ENVIRONMENT=test uv run pytest

test-python-file:  ## Run specific local Python test file
	@[ -n "$(FILE)" ] || (echo "Provide a test file path"; exit 1)
	PYTHONPATH=site ENVIRONMENT=test uv run pytest ${FILE}

docker-up:  ## Start Docker containers with build (SITE_INIT_ACTION=$(SITE_INIT_ACTION), AWS_PROFILE=$(AWS_PROFILE))
	SITE_INIT_ACTION=$(SITE_INIT_ACTION) \
	AWS_PROFILE=$(AWS_PROFILE) \
	docker compose up --build

docker-down:  ## Stop and remove Docker containers
	docker compose down

docker-up-likeprod:  ## Start Docker containers in production-like mode
	SITE_INIT_ACTION=$(SITE_INIT_ACTION) \
	AWS_PROFILE=$(AWS_PROFILE) \
	docker compose -f docker-compose.yml -f docker-compose.likeprod.yml up --build

interactive-local:  ## Spawn an interactive shell on the local web container
	./deploy.py interactive local

psql:  ## Drop into the PostgreSQL container's psql
	docker exec -it bridge-python-db-1 bash -c "psql -U myuser mydatabase"

sync-dev:  ## Sync local dev environment using uv
	uv sync --dev

playwright-install:  ## Install Playwright dependencies (chromium, etc.) using uv
	uv run -m playwright install --with-deps chromium

UV_VERSION := $(shell grep "ghcr.io/astral-sh/uv" Dockerfile | awk -F: '{print $$2}' | awk '{print $$1}')

uv-update:  ## Update uv to the version used in Dockerfile
	uv self update $(UV_VERSION)

deploy-help:  ## Show deploy.py help (AWS_PROFILE=$(AWS_PROFILE))
	AWS_PROFILE=$(AWS_PROFILE) ./deploy.py --help

deploy-list:  ## List all deployments with deploy.py (AWS_PROFILE=$(AWS_PROFILE))
	AWS_PROFILE=$(AWS_PROFILE) ./deploy.py list

deploy-interactive:  ## Spawn an interactive shell in a remote container (SERVICE=SiteService)
	@[ -n "$(SERVICE)" ] || (echo "Please set SERVICE, e.g. make deploy-interactive SERVICE=SiteService"; exit 1)
	AWS_PROFILE=$(AWS_PROFILE) ./deploy.py interactive $(SERVICE)