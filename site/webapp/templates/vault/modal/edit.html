{% load static %}
<div id="edit-modal" class="relative bg-white rounded-xl w-[450px] p-6">
    <button type="button"
            _="on click trigger closeModal"
            class="absolute top-3 right-3 text-gray-400 hover:text-gray-800 transition">
        <img src="{% static 'assets/images/close.svg' %}"
             width="16"
             height="16"
             alt="Close">
    </button>
    <div id="form-messages" class="mb-4"></div>
    <form id="edit-form"
          class="space-y-4"
          hx-post="{% url 'vault_edit_processed_documents' %}"
          hx-trigger="submit"
          hx-on="closeModal: true">
        <input type="text"
               name="processed_doc_pk"
               value="{{ processed_doc.pk }}"
               class="hidden">
        <div class="flex items-center gap-2">
            <label for="line-item"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Line Item:</label>
            <select name="line_item"
                    id="line-item"
                    class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                    required
                    {% if processed_doc.has_multiple_investors %}disabled{% endif %}>
                <option value="">Select Line Item</option>
                {% for line_item in line_items %}
                    <option value="{{ line_item.id }}"
                            {% if line_item.id == processed_doc.line_item.id %}selected{% endif %}>
                        {{ line_item }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="flex items-center gap-2">
            <label for="posted-date"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Posted Date:</label>
            <input type="date"
                   id="posted-date"
                   name="posted_date"
                   value="{{ processed_doc.posted_date|date:'Y-m-d' }}"
                   class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                   required>
        </div>
        <div class="flex items-center gap-2">
            <label for="effective-date"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Effective Date:</label>
            <input type="date"
                   id="effective-date"
                   name="effective_date"
                   value="{{ processed_doc.effective_date|date:'Y-m-d' }}"
                   class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                   required>
        </div>
        <div class="flex items-center gap-2">
            <label for="document-type"
                   class="w-32 text-left text-sm text-[#4b554d] font-medium">Category:</label>
            <select name="document_type"
                    id="document-type"
                    class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                    required
                    hx-get="{% url 'get_valid_sub_document_types' %}"
                    hx-target="#sub-document-type-container"
                    hx-trigger="change"
                    hx-swap="outerHTML"
                    hx-include="[name='document_type']">
                <option value="">Select Document Type</option>
                {% for choice in DocumentType.choices %}
                    <option value="{{ choice.0 }}"
                            {% if choice.0 == processed_doc.document_type %}selected{% endif %}>{{ choice.1 }}</option>
                {% endfor %}
            </select>
        </div>
        <div id="sub-document-type-container">
            {% if valid_sub_document_types %}
                <div class="flex items-center gap-2">
                    <label for="sub-document-type"
                           class="w-32 text-left text-sm text-[#4b554d] font-medium">Sub Category:</label>
                    <select name="sub_document_type"
                            id="sub-document-type"
                            class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                            {% if processed_doc.document_type not in REQUIRED_SUB_TYPE_DOCUMENTS %}disabled{% endif %}>
                        <option value="">Select Sub Document Type</option>
                        {% for choice in valid_sub_document_types %}
                            <option value="{{ choice.value }}"
                                    {% if choice.value == processed_doc.sub_document_type %}selected{% endif %}>
                                {{ choice.label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            {% endif %}
        </div>
        <div>
            <button type="submit"
                    class="w-full px-4 py-2.5 bg-[#343d36] text-white rounded-lg hover:bg-[#424d44] transition-colors flex items-center justify-center gap-2">
                <span>Edit Documents</span>
            </button>
        </div>
    </form>
</div>
<script>
    document.body.addEventListener("closeModal", closeModal);

    function closeModal() {
        const modal = document.getElementById("edit-modal");
        if (modal) {
            modal.remove();
        }
    }
</script>
