import random
import time
from collections.abc import Callable, Generator
from contextlib import contextmanager
from functools import wraps
from typing import Any

import structlog
from celery import Signature, Task
from celery.exceptions import Ignore
from django.core.cache import cache
from redis_lock import Lock
from webapp.models.portal import MergedPortalCredential, MFAType

from retrieval.core.exceptions import LockCouldNotBeAcquiredError

logger = structlog.get_logger(__name__)


def celery_chain_ignoring_failures(task_list: list[Signature]) -> Signature[None] | None:
    """
    WARNING: have noticed high amount of memory usage when using this function.
    Need to replace for portal scheduling one day soon.
    """
    prev = None
    for each_task in reversed(task_list):
        if prev is None:
            prev = each_task
            continue

        prev = each_task.set(link=prev).set(link_error=prev)
    return prev


@contextmanager
def aquire_login_portal_lock(
    merged_portal_credential: MergedPortalCredential,
    portal_class: str,
    *,
    blocking: bool = False,
    timeout: int | None = None,
) -> Generator[None, None, None]:
    user = merged_portal_credential.created_by
    if user is not None and user.is_demo:
        yield
        return
    mpc_lock = None
    mpc_lock_aquired = False
    sms_lock = None
    sms_lock_aquired = False
    try:
        mpc_lock_id = f"portal-lock-mpc-{merged_portal_credential.pk}"
        sms_lock_id = None
        if merged_portal_credential.multi_factor_authentication.multi_factor_authentication_type == MFAType.SMS:
            # TODO: release this lock post login.
            sms_lock_id = f"portal-lock-sms-{merged_portal_credential.multi_factor_authentication.phone_number}"
        # Add some jitter to avoid race conditions
        time.sleep(random.uniform(0, 1))  # noqa: S311 # nosec

        if not hasattr(cache, "lock"):
            raise TypeError

        mpc_lock = cache.lock(mpc_lock_id, expire=60, auto_renewal=True)
        if sms_lock_id is not None:
            sms_lock = cache.lock(sms_lock_id, expire=60, auto_renewal=True)

        if not isinstance(mpc_lock, Lock) or (sms_lock is not None and not isinstance(sms_lock, Lock)):
            raise TypeError

        mpc_lock_aquired = mpc_lock.acquire(blocking=blocking, timeout=timeout)
        sms_lock_aquired = True if sms_lock is None else sms_lock.acquire(blocking=blocking, timeout=timeout)

        if mpc_lock_aquired and sms_lock_aquired:
            yield
        else:
            logger.error(
                "Failed to acquire locks for login portal task",
                portal_class=portal_class,
                sms_lock_id=sms_lock_id,
                mpc_lock_id=mpc_lock_id,
                mpc_lock_acquired=mpc_lock_aquired,
                sms_lock_acquired=sms_lock_aquired,
            )
            raise LockCouldNotBeAcquiredError
    finally:
        if sms_lock is not None and sms_lock_aquired:
            sms_lock.release()
        if mpc_lock is not None and mpc_lock_aquired:
            mpc_lock.release()


def singleton_task(*, blocking: bool = False, unique_kwargs: list[str] | None = None) -> Callable:
    """Decorator that ensures only one instance of a task runs at a time"""

    def _singleton_task(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self: Task, *args: list[str], **kwargs: str) -> Any:  # noqa: ANN401
            suffix = ""
            if unique_kwargs is not None:
                suffix = "-".join(str(kwargs.get(kw, "")) for kw in unique_kwargs if kw in kwargs)
                if suffix:
                    suffix = f"-{suffix}"
            lock_id = f"celery-singleton-{self.name}{suffix}"
            lock_acquired = False
            lock = None

            try:
                # Add some jitter to avoid race conditions
                time.sleep(random.uniform(0, 1))  # noqa: S311 # nosec

                if not hasattr(cache, "lock"):
                    raise TypeError

                lock = cache.lock(lock_id, expire=60, auto_renewal=True)

                if not isinstance(lock, Lock):
                    raise TypeError

                lock_acquired = lock.acquire(blocking=blocking)
                if lock_acquired:
                    return func(self, *args, **kwargs)

                logger.info(
                    "Task already running on another worker, marking as duplicate",
                    lock_id=lock_id,
                )

                self.update_state(
                    state="REVOKED",
                    meta={"reason": "Duplicate execution prevented", "lock_id": lock_id},
                )

                raise Ignore
            finally:
                # Only release the lock if we acquired it
                if lock_acquired and lock is not None:
                    # Keep the lock around to try to best effort not submit duplicate tasks
                    if not blocking:
                        time.sleep(5)
                    lock.release()

        return wrapper

    return _singleton_task
