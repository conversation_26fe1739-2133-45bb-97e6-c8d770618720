# Generated by Django 5.1.4 on 2024-12-18 14:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0003_alter_processeddocument_document_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='portal',
            name='portal_login_url',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='rawbulkupload',
            name='file_location',
            field=models.CharField(blank=True, max_length=1024),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='client_legal_name',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='entity_legal_name',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='investment_fund_legal_name',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='investment_managing_firm_name',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='multi_factor_authentication_details',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='multi_factor_authentication_type',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='portal_login_url',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='portal_name',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='receiving_email',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='rawbulkuploadrow',
            name='receiving_email_provider',
            field=models.TextField(blank=True),
        ),
    ]
