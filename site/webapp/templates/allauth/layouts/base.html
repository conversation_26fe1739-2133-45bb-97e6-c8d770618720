{% extends "base.html" %}
{% comment %}
    From: https://docs.allauth.org/en/dev/common/templates.html
    Source: https://codeberg.org/allauth/django-allauth/src/branch/main/allauth/templates/allauth/layouts/base.html
{% endcomment %}
{% load i18n %}
{% block title %}
    {% block head_title %}
    {% endblock head_title %}
{% endblock title %}
{% block webapp_content %}
    {% block extra_head %}
    {% endblock extra_head %}
    {% block body %}
        {% comment %} TODO: We need to do messages somehow {% endcomment %}
        {% comment %} {% if messages %}
            <div>
                <strong>{% trans "Messages:" %}</strong>
                <ul>
                    {% for message in messages %}<li>{{ message }}</li>{% endfor %}
                </ul>
            </div>
        {% endif %} {% endcomment %}
        {% if user.is_authenticated %}
            <div>
                <strong>{% trans "Menu:" %}</strong>
                <ul>
                    {% url 'account_email' as email_url_ %}
                    {% if email_url_ %}
                        <li>
                            <a href="{{ email_url_ }}">{% trans "Change Email" %}</a>
                        </li>
                    {% endif %}
                    {% url 'account_change_password' as change_password_url_ %}
                    {% if change_password_url_ %}
                        <li>
                            <a href="{{ change_password_url_ }}">{% trans "Change Password" %}</a>
                        </li>
                    {% endif %}
                    {% url 'socialaccount_connections' as connections_url_ %}
                    {% if connections_url_ %}
                        <li>
                            <a href="{{ connections_url_ }}">{% trans "Account Connections" %}</a>
                        </li>
                    {% endif %}
                    {% url 'mfa_index' as mfa_url_ %}
                    {% if mfa_url_ %}
                        <li>
                            <a href="{{ mfa_url_ }}">{% trans "Two-Factor Authentication" %}</a>
                        </li>
                    {% endif %}
                    {% url 'usersessions_list' as usersessions_list_url_ %}
                    {% if usersessions_list_url_ %}
                        <li>
                            <a href="{{ usersessions_list_url_ }}">{% trans "Sessions" %}</a>
                        </li>
                    {% endif %}
                    {% url 'account_logout' as logout_url_ %}
                    {% if logout_url_ %}
                        <li>
                            <a href="{{ logout_url_ }}">{% trans "Sign Out" %}</a>
                        </li>
                    {% endif %}
                    {% comment %} TODO: Should we encorporate this back? {% endcomment %}
                    {% comment %} {% url 'account_login' as login_url_ %}
                    {% if login_url_ %}
                        <li>
                            <a href="{{ login_url_ }}">{% trans "Sign In" %}</a>
                        </li>
                    {% endif %}
                    {% url 'account_signup' as signup_url_ %}
                    {% if signup_url_ %}
                        <li>
                            <a href="{{ signup_url_ }}">{% trans "Sign Up" %}</a>
                        </li>
                    {% endif %} {% endcomment %}
                </ul>
            </div>
        {% else %}
        {% endif %}
        {% block content %}
        {% endblock content %}
    {% endblock body %}
    {% block extra_body %}
    {% endblock extra_body %}
{% endblock webapp_content %}
