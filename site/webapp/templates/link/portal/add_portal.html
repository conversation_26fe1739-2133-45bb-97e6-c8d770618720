{% load customtags static widget_tweaks %}
<div id="modal-container-add-portal">
    <div class="flex items-center justify-between p-3 rounded-t dark:border-gray-600">
        <div class="p-2 bg-[#343d36] rounded-[10px] justify-center items-center inline-flex">
            <div class="relative flex-col justify-start items-start flex">
                <div class="relative">
                    <img src="{% static 'assets/images/fin_tag.svg' %}"
                         width="38"
                         height="20"
                         alt="closeButton">
                </div>
            </div>
        </div>
        <button _="on click trigger closeModal"
                type="button"
                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <img src="{% static 'assets/images/close.svg' %}"
                 width="16"
                 height="19"
                 alt="closeButton">
            <span class="sr-only">Close modal</span>
        </button>
    </div>
    <div class="opacity-75 text-[#343d36] px-3 pb-3 pt-4 text-sm font-normal font-inter leading-tight">
        In a separate tab, please navigate to the portal website, copy the exact portal URL, and paste it below.
    </div>
    <div class="opacity-75 text-[#343d36] px-3 pb-4 pt-4 text-sm font-normal font-inter leading-tight">
        Next, name the portal (i.e. Dynamo).
    </div>
    <div class="px-3 w-full flex-col justify-start items-start gap-5 inline-flex">
        <form class="w-full space-y-4"
              hx-post="{% url 'add_portal' %}"
              hx-target="#modal-container-add-portal"
              hx-swap="outerHTML"
              hx-on:after-request="handleFormResponse(event)">
            {% csrf_token %}
            {{ form.non_field_errors }}
            <div class="flex flex-col gap-2">
                <label for="{{ form.portal_url.id_for_label }}"
                       class="text-[#343d36] text-sm font-medium">{{ form.portal_url.label }}</label>
                {{ form.portal_url }}
                {% if form.portal_url.errors %}
                    <div class="text-red-500 text-sm">{{ form.portal_url.errors|join:", " }}</div>
                {% endif %}
            </div>
            <div class="flex flex-col gap-2">
                <label for="{{ form.portal_name.id_for_label }}"
                       class="text-[#343d36] text-sm font-medium">{{ form.portal_name.label }}</label>
                {% if is_edit %}
                    {{ form.portal_name|attr:"readonly" }}
                {% else %}
                    {{ form.portal_name }}
                {% endif %}
                {% if form.portal_name.errors %}
                    <div class="text-red-500 text-sm">{{ form.portal_name.errors|join:", " }}</div>
                {% endif %}
            </div>
            <div class="border-t border-[#eceee8] mt-4"></div>
            <button type="submit"
                    class="h-11 px-4 mt-4 w-full py-2.5 bg-[#924f34] rounded-lg border border-[#924f34] justify-center items-center gap-1.5 inline-flex">
                <div class="px-0.5 justify-center items-center flex">
                    <div class="text-white text-base font-semibold font-inter text-[16px] leading-normal">Done</div>
                </div>
            </button>
        </form>
    </div>
</div>
