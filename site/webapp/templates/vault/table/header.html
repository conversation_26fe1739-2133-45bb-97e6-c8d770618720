{% load customtags %}
{% load static %}
<div>
    <button class=" dropdown-btn flex text-left justify-between py-1 text-xs font-semibold text-base-darker-olive-muted font-inter rounded focus:ring-2 focus:ring-blue-300 focus:outline-none"
            type="button"
            id="dropdownSearchButton-{{ column_name }}"
            data-dropdown-toggle="dropdownSearch-{{ column_name }}"
            data-dropdown-placement="bottom-start"
            data-dropdown-ignore-click-outside-class="datepicker">
        <span>{{ title }}</span>
        <span id="filterIndicator-{{ column_name }}"
              class="hidden ml-1 text-red-500 font-bold">*</span>
        <img class="ml-1"
             width="16"
             height="16"
             alt="sort_arrows"
             src="{% static 'assets/images/table_header_arrow.svg' %}">
    </button>
    {% if dropdown_type == "date" %}
        <div id="dropdownSearch-{{ column_name }}"
             class="absolute z-10 hidden bg-white border border-gray-200 rounded shadow-md p-2">
            <button id="{{ column_name }}_sort"
                    class="flex items-center justify-between w-full pt-2"
                    name="{{ column_name }}_sort"
                    type="button"
                    onclick=" document.querySelectorAll('.sort-hidden-input').forEach(hiddenInput => { const columnName = hiddenInput.name.replace('_sort_hidden', ''); var sortButton = document.getElementById(`${columnName}_sort`); var span = sortButton.querySelector('span'); var leastRecentImg = sortButton.querySelector('img.sort-by-least-recent'); var mostRecentImg = sortButton.querySelector('img.sort-by-most-recent'); var hiddenInputId = hiddenInput.id; if (hiddenInputId.includes('{{ column_name }}')) { if (hiddenInput.value === 'None') { hiddenInput.value = 'False'; } else { hiddenInput.value = hiddenInput.value.toLowerCase() === 'true' ? 'False' : 'True'; } } else { hiddenInput.value = 'None'; } if (hiddenInput.value === 'True' || hiddenInput.value === 'None') { span.textContent = 'Sort by least recent'; leastRecentImg.classList.remove('hidden'); mostRecentImg.classList.add('hidden'); } else { span.textContent = 'Sort by most recent'; leastRecentImg.classList.add('hidden'); mostRecentImg.classList.remove('hidden'); } });  document.getElementById('{{ column_name }}_sort').dispatchEvent( new Event('sortDates', { bubbles: true, }) ); ">
                <span class="block text-sm font-medium text-gray-700 mb-1">Sort by least recent</span>
                <img class="sort-by-least-recent ml-1"
                     width="16"
                     height="16"
                     alt="up_arrow"
                     src="{% static 'assets/images/up_arrow.svg' %}">
                <img class="hidden sort-by-most-recent ml-1"
                     width="16"
                     height="16"
                     alt="down_arrow"
                     src="{% static 'assets/images/down_arrow.svg' %}">
            </button>
            <input class="vaultinputs sort-hidden-input"
                   id="{{ column_name }}_sort_hidden"
                   name="{{ column_name }}_sort_hidden"
                   type="hidden"
                   value="{{ sorted_by_most_recent }}">
            <div class="mb-3 pt-5">
                <label class="block text-sm font-medium text-gray-700 mb-1">From</label>
                <div class="flex items-center">
                    <input type="text"
                           id="{{ column_name }}_date_from"
                           name="{{ column_name }}_date_from"
                           value="{{ min_date }}"
                           data-default-date="{{ min_date }}"
                           class="vaultinputs bg-gray-50 border border-gray-300 text-xs w-full rounded focus:ring-blue-500 focus:border-blue-500 datepicker-input"
                           placeholder="Start date">
                    <img class="ml-1 cursor-pointer"
                         id="{{ column_name }}_reset_date_from"
                         width="16"
                         height="16"
                         alt="reset"
                         src="{% static 'assets/images/reset.svg' %}">
                </div>
            </div>
            <div class="mb-3 pt-5">
                <label class="block text-sm font-medium text-gray-700 mb-1">To</label>
                <div class="flex items-center">
                    <input type="text"
                           id="{{ column_name }}_date_to"
                           name="{{ column_name }}_date_to"
                           value="{{ max_date }}"
                           data-default-date="{{ max_date }}"
                           class="vaultinputs bg-gray-50 border border-gray-300 text-xs w-full rounded focus:ring-blue-500 focus:border-blue-500 datepicker-input"
                           placeholder="End date">
                    <img class="ml-1 cursor-pointer"
                         id="{{ column_name }}_reset_date_to"
                         width="16"
                         height="16"
                         alt="reset"
                         src="{% static 'assets/images/reset.svg' %}">
                </div>
            </div>
        </div>
    {% else %}
        <div id="dropdownSearch-{{ column_name }}"
             class="absolute z-10 hidden bg-white border border-gray-200 rounded shadow-md w-fit">
            <ul class="h-48 px-3 py-2 overflow-y-auto text-sm text-gray-700">
                <ul class="flex justify-between w-full max-w-xl mx-auto">
                    <li class="flex-1 text-center">
                        <a href="#select-all"
                           data-column="{{ column_name }}"
                           hx-post="{% url 'vault_header' %}"
                           hx-vals='{"select_all": true, "column_name": "{{ column_name }}"}'
                           hx-target="#dropdownSearch-{{ column_name }}"
                           hx-swap="outerHTML"
                           hx-select="#dropdownSearch-{{ column_name }}"
                           hx-include=".vaultinputs"
                           class="select-all-link vaultinputs block py-2 text-blue-600 hover:underline">Select All</a>
                    </li>
                    <li class="flex-1 text-center">
                        <a href="#clear"
                           data-column="{{ column_name }}"
                           hx-post="{% url 'vault_header' %}"
                           hx-vals='{"clear": true, "column_name": "{{ column_name }}"}'
                           hx-target="#dropdownSearch-{{ column_name }}"
                           hx-swap="outerHTML"
                           hx-select="#dropdownSearch-{{ column_name }}"
                           hx-include=".vaultinputs"
                           class="clear-link vaultinputs block py-2 text-blue-600 hover:underline">Clear</a>
                    </li>
                </ul>
                <ul hx-post="{% url 'vault_header' %}"
                    hx-vals='{"dropdown_filter": true, "column_name": "{{ column_name }}", "test123": "works"}'
                    hx-target="#dropdownSearch-{{ column_name }}"
                    hx-swap="outerHTML"
                    hx-select="#dropdownSearch-{{ column_name }}"
                    hx-include=".vaultinputs"
                    hx-trigger="change">
                    {% for value in column_vals %}
                        <li class="flex items-center px-2 py-1 rounded hover:bg-gray-100">
                            <div class="flex items-center ps-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                                <input type="checkbox"
                                       id="checkbox-item-{{ column_name }}-{{ value|slugify }}"
                                       name="{{ column_name }}"
                                       value="{{ value }}"
                                       {% if value in selected_vals %}checked{% endif %}
                                       class="vaultinputs vaultcheckbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                                <input type="hidden"
                                       name="{{ column_name }}-available"
                                       value="{{ value }}"
                                       class="vaultinputs">
                                <label class="ml-2 text-sm font-medium text-gray-900"
                                       for="checkbox-item-{{ column_name }}-{{ value|slugify }}">
                                    <span>
                                        {% if enum %}
                                            {% label_lookup label=value enum=enum %}
                                        {% else %}
                                            {{ value }}
                                        {% endif %}
                                    </span>
                                </label>
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            </ul>
        </div>
    {% endif %}
</div>
{{ column_name|json_script }}
<script>
    (function(column_name) {
        function sortDates() {

        }

        function loadHeader() {
            var date_from_elem = document.getElementById(`${column_name}_date_from`);
            var date_to_elem = document.getElementById(`${column_name}_date_to`);
            if (date_from_elem && date_to_elem) {
                var datepicker_from = new Datepicker(date_from_elem, {
                    format: 'yyyy-mm-dd',
                    autohide: true,
                    maxDate: date_to_elem.getAttribute("data-default-date"),
                    minDate: date_from_elem.getAttribute("data-default-date"),
                });
                date_from_elem.datepicker = datepicker_from;
                document.getElementById(`${column_name}_reset_date_from`).onclick = (e) => {
                    datepicker_from.setDate(date_from_elem.getAttribute("data-default-date"));
                };
                var datepicker_to = new Datepicker(date_to_elem, {
                    format: 'yyyy-mm-dd',
                    autohide: true,
                    maxDate: date_to_elem.getAttribute("data-default-date"),
                    minDate: date_from_elem.getAttribute("data-default-date"),
                });
                date_to_elem.datepicker = datepicker_to;
                document.getElementById(`${column_name}_reset_date_to`).onclick = (e) => {
                    datepicker_to.setDate(date_to_elem.getAttribute("data-default-date"));
                };
            }

            const selector = `div#dropdownSearch-${column_name} .vaultinputs`
            document.querySelectorAll(selector).forEach((input) => {
                const checkbox_changed_function = (e) => {
                    // TODO: clean up this code and ensure filter indicator works correctly in all scenarios
                    const filterIndicator = document.getElementById(`filterIndicator-${column_name}`);
                    if (input.checked) {
                        filterIndicator.classList.remove("hidden");
                    } else {
                        const allCheckboxes = document.querySelectorAll(selector);
                        const anyChecked = Array.from(allCheckboxes).some(checkbox => checkbox.checked);
                        (anyChecked);
                        if (!anyChecked) {
                            filterIndicator.classList.add("hidden");
                        } else {
                            filterIndicator.classList.remove("hidden");
                        }
                    }
                    document.addEventListener("click", function(e) {
                        const clear = e.target.classList.contains("clear-link");
                        if (clear) {
                            filterIndicator.classList.add("hidden");
                        }
                    });
                };
                const date_changed_function = (e) => {
                    const filterIndicator = document.getElementById(`filterIndicator-${column_name}`);
                    const isNotDefault = input.value !== input.getAttribute("data-default-date");
                    if (isNotDefault) {
                        filterIndicator.classList.remove("hidden");
                    } else {
                        const allInputs = document.querySelectorAll(selector);
                        const anyNotDefault = Array.from(allInputs).some(elem => elem.value !== elem.getAttribute("data-default-date"));
                        if (anyNotDefault) {
                            filterIndicator.classList.remove("hidden");
                        } else {
                            filterIndicator.classList.add("hidden");
                        }
                    }
                };

                input.addEventListener("change", checkbox_changed_function);
                input.addEventListener("change-update", checkbox_changed_function);
                input.addEventListener("changeDate", date_changed_function);
                input.addEventListener("changeDate-update", date_changed_function);
            });

            // TODO: swap from global event listener to listening for specific element actions
            document.addEventListener("click", function(e) {
                if (e.target.classList.contains("select-all-link")) {
                    const columnName = e.target.dataset.column;
                    document.querySelectorAll(`input[type="checkbox"][name="${columnName}"]`)
                        .forEach(cb => {
                            cb.checked = true;
                        });
                }

                if (e.target.classList.contains("clear-link")) {
                    const columnName = e.target.dataset.column;
                    document.querySelectorAll(`input[type="checkbox"][name="${columnName}"]`)
                        .forEach(cb => {
                            cb.checked = false;
                        });
                }
            });
        }
        document.addEventListener("DOMContentLoaded", loadHeader);
        document.addEventListener("htmx:afterSwap", loadHeader);
    })(JSON.parse(document.currentScript.previousElementSibling.textContent));
</script>
