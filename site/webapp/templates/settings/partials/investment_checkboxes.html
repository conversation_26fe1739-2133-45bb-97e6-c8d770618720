<ul class="p-3 space-y-1 text-sm text-gray-700 dark:text-gray-200"
    aria-labelledby="dropdownCheckboxButton-{{ user_id }}-investments">
    <li>
        <div class="flex items-center px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
            <input id="select-all-{{ user_id }}-investments"
                   type="checkbox"
                   value=""
                   class="flowbite-select-all-checkbox form-checkbox rounded-sm text-black border-black focus:ring-0 focus:outline-0 w-5 h-5 mr-2">
            <label for="select-all-{{ user_id }}-investments"
                   class="w-full ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">
                Select All Investments
            </label>
        </div>
    </li>
    <li>
        <hr class="my-1 border-gray-200 dark:border-gray-600">
    </li>
    {% for checkbox in form.investments %}
        <li>
            <div class="flex items-center px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                {{ checkbox.tag }}
                <label for="{{ checkbox.id_for_label }}"
                       class="w-full ms-2 text-sm font-medium text-gray-900 rounded dark:text-gray-300">
                    {{ checkbox.choice_label }}
                </label>
            </div>
        </li>
    {% endfor %}
</ul>
