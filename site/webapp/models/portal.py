import re
import time
from datetime import UTC, datetime, timedelta
from typing import TYPE_CHECKING

import pyotp
import structlog
from django.db import models
from django.utils.translation import gettext_lazy as _
from phonenumber_field.modelfields import <PERSON><PERSON><PERSON>berField

from webapp.models.emails import UserForwardingRule

logger = structlog.get_logger(__name__)

from webapp.models.core import AbstractBaseModel, SecretAbstractBaseModel
from webapp.models.emails import CustomerEmailCredential
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser, Organization

if TYPE_CHECKING:
    from webapp.models.line_item import LineItem


class UserLoginStatusCallToAction(models.TextChoices):
    NOT_STARTED = "ns", _("Not Started")
    PENDING = "pe", _("Pending")
    ACTION_REQUIRED = "ar", _("Action Required")
    SUCCESS = "su", _("Success")


class Portal(AbstractBaseModel):
    class PortalType(models.TextChoices):
        WEB_BASED = "wb", _("Web Based")
        EMAIL_BASED = "eb", _("Email Based")

    name = models.CharField(max_length=255)
    portal_login_url = models.TextField()
    portal_type = models.CharField(
        max_length=2,
        choices=PortalType.choices,
        default=PortalType.WEB_BASED,
    )
    merged_portal_credentials: "MergedPortalCredential"

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["merged_portal_credentials__line_items"]

    def __str__(self) -> str:
        if self.portal_type == self.PortalType.EMAIL_BASED:
            return f"Email Inbox for {self.name}"
        # Web Based
        return self.name

    def is_portal_supported(self, user: BridgeUser) -> bool:
        # HACK: prevents circular imports. I don't know the best way to factor this
        # On the one hand: I want our code implementation (hence static classes) to be the source of truth
        # On the other hand: I don't want to have to import the retrieval manager in the model
        # On the other hand: the Retrieval codebase probably just shouldn't know about models.
        from retrieval.core.registry import RetrievalRegistry

        return (
            RetrievalRegistry.get_retrieval_manager(user=user, name=self.name, portal_login_url=self.portal_login_url)
            is not None
        )

    @classmethod
    def get_portal_by_name(cls, name: str, organization: Organization) -> "Portal":
        name = name.strip()
        if not name:
            return None

        try:
            return Portal.objects.get(organization=organization, name__iexact=name)
        except Portal.DoesNotExist:
            portals = Portal.objects.filter(organization=organization, name__icontains=name)
            if portals.exists():
                return portals.first()
            simplified_name = re.sub(r"[^a-z0-9]", "", name.lower())
            for portal in Portal.objects.all():
                simplified_portal_name = re.sub(r"[^a-z0-9]", "", portal.name.lower())
                if simplified_name in simplified_portal_name or simplified_portal_name in simplified_name:
                    return portal
            return None


class OTPMethod(models.TextChoices):
    # Status for never having logged in w this credential
    COLD_LOGIN = "cl", _("Cold Login")

    # MITM stand for: man-in-the-middle
    # Login technique for second login
    LIVE_LOGIN = "ll", _("Live Login")
    MITM_SMS = "sm", _("SMS")
    MITM_EMAIL = "em", _("Email")
    MITM_AUTHENTICATOR = "ap", _("Authenticator")
    NOT_SUPPORTED = "ns", _("Not Supported")

    def display_name(self) -> str:
        # Access choices through the class, not the instance
        for choice in OTPMethod.choices:
            if self == choice[0]:
                return choice[1]
        return "Unknown"


class MFAType(models.TextChoices):
    SMS = "sm", _("SMS")
    EMAIL = "em", _("Email")
    AUTHENTICATOR = "ap", _("Authenticator")
    LIVE_LOGIN = "ll", _("Live Login")
    NONE = "no", _("No MFA")
    UNKNOWN = "un", _("Unknown")


class MFAStatus(models.TextChoices):
    NOT_STARTED = "ns", _("Not Started")
    SUCCESS = "su", _("Success")
    NOT_SUPPORTED = "no", _("Not Supported")
    FAILED = "fa", _("Failed")


class PortalCredential(SecretAbstractBaseModel):
    # TODO: delete?
    portal = models.ForeignKey(Portal, on_delete=models.CASCADE, related_name="portal_credentials")
    username = models.CharField(max_length=255)
    # Customer owned email(s) for the portal

    merged_portal_credential: "MergedPortalCredential"

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["merged_portal_credential__line_items"]

    def __str__(self) -> str:
        return f"Credential for {self.portal.name}"

    @property
    def is_complete(self) -> bool:
        return self.username != "" and self.has_secret()

    @classmethod
    def create(cls, user: BridgeUser, portal: Portal, username: str, password: str | None = None) -> "PortalCredential":
        portal_credential = cls.objects.create(
            user=user,
            portal=portal,
            username=username,
        )
        if password is not None:
            portal_credential.set_secret(password)
        portal_credential.save()
        return portal_credential


class PhoneType(models.TextChoices):
    APPLE = "ap", _("Apple")
    GOOGLE = "ad", _("Android")
    OTHER = "ot", _("Other")


class MultiFactorAuthentication(SecretAbstractBaseModel):
    multi_factor_authentication_type = models.CharField(
        max_length=2,
        choices=MFAType.choices,
        default=MFAType.UNKNOWN,
    )
    phone_number = PhoneNumberField(blank=True)
    phone_type = models.CharField(
        max_length=2,
        choices=PhoneType.choices,
        blank=True,
    )
    receiving_email: "CustomerEmailCredential" = models.ForeignKey(
        "CustomerEmailCredential",
        on_delete=models.CASCADE,
        related_name="multi_factor_authentications",
        blank=True,
        null=True,
    )  # type: ignore[assignable]

    merged_portal_credential: "MergedPortalCredential"

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["merged_portal_credential__line_items"]

    def __str__(self) -> str:
        from webapp.models.emails import CustomerEmailCredential

        mfa_type = str(MFAType(self.multi_factor_authentication_type).label)
        mfa_state = str(MFAStatus(self.status).label)
        if self.receiving_email is not None:
            mfa_email_int_status = str(
                CustomerEmailCredential.EmailIntegrationStatus(self.receiving_email.email_integration_status).label
            )
        else:
            mfa_email_int_status = "no email"
        return f"{mfa_type} {mfa_state} {mfa_email_int_status}"

    def get_otp(self) -> str | None:
        if self.multi_factor_authentication_type == MFAType.AUTHENTICATOR:
            otp_obj = pyotp.parse_uri(self.get_secret())
            totp = pyotp.TOTP(s=otp_obj.secret)
            # Get the time step interval (default is 30 seconds)
            interval = totp.interval
            # Calculate the time remaining until the next code
            time_remaining = interval - (int(time.time()) % interval)
            logger.info("Time remaining until next code", time_remaining=time_remaining)
            wait_time = 10
            if time_remaining < wait_time:
                new_wait_time = wait_time + 0.5
                logger.info("Sleeping to get a new OTP", wait_time=new_wait_time)
                time.sleep(new_wait_time)
            return totp.now()
        return None

    @property
    def authentication_details(self) -> str | None:
        if self.multi_factor_authentication_type == MFAType.EMAIL:
            return self.receiving_email.email
        if self.multi_factor_authentication_type == MFAType.SMS:
            return self.phone_number
        if self.multi_factor_authentication_type == MFAType.AUTHENTICATOR:
            return None
        return ""

    @property
    def status(self) -> str:  # noqa: PLR0911
        from webapp.models.emails import CustomerEmailCredential

        if (
            self.multi_factor_authentication_type == MFAType.EMAIL
            and self.receiving_email is not None
            and self.receiving_email.email_integration_status
            != CustomerEmailCredential.EmailIntegrationStatus.NOT_STARTED
        ):
            return MFAStatus.SUCCESS
        if self.multi_factor_authentication_type == MFAType.AUTHENTICATOR and self.has_secret():
            return MFAStatus.SUCCESS
        if self.multi_factor_authentication_type == MFAType.NONE:
            return MFAStatus.SUCCESS
        if self.multi_factor_authentication_type == MFAType.SMS and self.phone_number and self.phone_type:
            return MFAStatus.SUCCESS
        if self.multi_factor_authentication_type == MFAType.LIVE_LOGIN:
            try:
                line_items = self.merged_portal_credential.line_items
                if line_items:
                    line_item = line_items.first()
                    if (
                        line_item
                        and line_item.last_retrieval
                        and line_item.last_retrieval.retrieval_status
                        in [
                            Retrieval.RetrievalStatus.SUCCESS_LOGGED_IN,
                            Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
                        ]
                    ):
                        return MFAStatus.SUCCESS
            except Exception as e:
                logger.exception("Error checking live login status", error=e)
                return MFAStatus.FAILED
        return MFAStatus.NOT_STARTED

    @property
    def is_complete(self) -> bool:
        return self.status == MFAStatus.SUCCESS


class SMSMessage(AbstractBaseModel):
    """
    User Forwarded SMS Messages originally from a Portal
    Example: Portal Dynamo with phone number +1234567890 sends a message
    "Your code is 123456" to the user with phone number +1234567890
    """

    body = models.TextField()
    user_phone_number = PhoneNumberField(blank=True)
    portal_phone_number = PhoneNumberField(blank=True)
    portal = models.ForeignKey(Portal, null=True, on_delete=models.CASCADE, related_name="sms_messages")
    raw_payload = models.JSONField(default=dict)

    def extract_code_from_message(self) -> str | None:
        """
        Attempts to extract an OTP code from the message body.
        Looks for common patterns like 6-digit codes.
        Returns the code if found, None otherwise.
        """
        import re

        otp_patterns = [
            r"(\d{4}-\d{4})",  # 8 digit code with dash "1234-1234"
            r"\b\d{8}\b",  # 8 digit code
            r"\b\d{6}\b",  # 6 digit code
            r"code[:\s]+(\d+)",  # "code: 123456" or "code 123456"
            r"password[:\s]+(\d+)",  # "password: 123456"
            r"pin[:\s]+(\d+)",  # "pin: 123456"
            r"(\d{3}[- ]\d{3})",  # "123-456"
            r"\b\d{4}\b",  # 4 digit code
        ]

        possible_matches = []
        for pattern in otp_patterns:
            match = re.search(pattern, self.body, re.IGNORECASE)
            if match:
                # If the pattern has a capture group, return that, otherwise return the full match
                match = match.group(1) if len(match.groups()) > 0 else match.group(0)
                match = "".join(filter(str.isdigit, match))
                if not match:
                    continue
                possible_matches.append(match)

        ranked_possible_matches = sorted(
            possible_matches,
            key=lambda x: (-len(x), x),  # Sort by length first, then lexicographically
        )
        if len(ranked_possible_matches) > 0:
            # Return the longest match, which is likely the OTP code
            return ranked_possible_matches[0]
        return None

    def is_within_time_window(self, seconds: int = 600) -> bool:
        """
        Whether the message was sent within the last 10 minutes (600 seconds)
        """
        current_time = datetime.now(UTC)
        return self.created_at > current_time - timedelta(seconds=seconds)

    def _mark_as_forwarded(self) -> None:
        self.state = self.state.FORWARDED
        self.save()

    def _mark_as_invalid(self) -> None:
        self.state = self.state.INVALID
        self.save()

    def __str__(self) -> str:
        return f"{self.user_phone_number}: {self.body}"

    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["merged_portal_credential__line_items"]


class MergedPortalCredential(AbstractBaseModel):
    class OnboardingStatus(models.TextChoices):
        NOT_STARTED = "ns", _("Not Started")
        IN_PROCESS = "pr", _("Pending")
        COMPLETED = "co", _("Completed")

    class ConnectionHealthStatus(models.TextChoices):
        ACTION_REQUIRED = "ar", _("Action Required")
        PENDING = "pe", _("Pending")
        LINKED = "li", _("Linked")

    """
    The goal of this class is to start to disassociate the one to one nature of line items, credentials, and MFA.
    It is also to merge the notion of a credential and an MFA method.
    TODO: should we also merge in email credentials? Likely when we get to emails.
    currently its only a reference.
    """

    portal = models.ForeignKey(Portal, on_delete=models.CASCADE, related_name="merged_portal_credentials")
    portal_credential = models.OneToOneField(
        "PortalCredential", null=True, blank=True, on_delete=models.CASCADE, related_name="merged_portal_credential"
    )
    multi_factor_authentication = models.OneToOneField(
        "webapp.MultiFactorAuthentication",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name="merged_portal_credential",
    )
    user_forwarding_rule = models.ForeignKey(
        UserForwardingRule, null=True, blank=True, on_delete=models.CASCADE, related_name="merged_portal_credential"
    )
    last_retrieval: "Retrieval" = models.ForeignKey(
        "Retrieval", on_delete=models.SET_NULL, null=True, blank=True, related_name="latest_merged_portal_credentials"
    )
    last_user_login_validation_retrieval: "Retrieval" = models.ForeignKey(
        "Retrieval",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="latest_user_login_merged_portal_credentials",
    )
    is_backfilled = models.BooleanField(default=False)
    line_items: models.QuerySet["LineItem"]
    retrievals: models.QuerySet["Retrieval"]

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_items"]

    def __str__(self) -> str:
        if self.portal is not None:
            return f"MergedPortalCredential for {self.portal.name}"
        return "MergedPortalCredential for unknown portal (None)"

    @property
    def phone_number(self) -> str | None:
        if self.portal_credential is not None:
            return self.portal_credential.phone_number
        return None

    @property
    def username(self) -> str | None:
        if self.portal_credential is not None:
            return self.portal_credential.username
        return None

    @property
    def is_complete(self) -> bool:
        if self.user_forwarding_rule:
            return (
                self.user_forwarding_rule.receiving_email.email_integration_status
                != CustomerEmailCredential.EmailIntegrationStatus.NOT_STARTED
            )
        return (
            self.portal_credential is not None
            and self.portal_credential.is_complete
            and self.multi_factor_authentication is not None
            and self.multi_factor_authentication.is_complete
        )

    @property
    def is_partially_complete(self) -> bool:
        return (self.portal_credential is not None and self.portal_credential.is_complete) or (
            self.multi_factor_authentication is not None and self.multi_factor_authentication.is_complete
        )

    @property
    def multi_factor_authentication_type(self) -> MFAType | None:
        if self.multi_factor_authentication is not None:
            return self.multi_factor_authentication.multi_factor_authentication_type
        return None

    @property
    def is_pending_validation(self) -> bool:
        return (
            self.portal_credential_user_login_status == UserLoginStatusCallToAction.PENDING
            and self.multi_factor_authentication_user_login_status == UserLoginStatusCallToAction.PENDING
        )

    @property
    def is_validated(self) -> bool:
        return (
            self.portal_credential_user_login_status == UserLoginStatusCallToAction.SUCCESS
            and self.multi_factor_authentication_user_login_status == UserLoginStatusCallToAction.SUCCESS
        )

    @property
    def portal_user_login_status(self) -> UserLoginStatusCallToAction:
        if self.last_user_login_validation_retrieval is not None:
            retrieval_user_login_status = self.last_user_login_validation_retrieval.user_login_status
            # Implicitly if there's other issues, we assume successful credentials.
            if retrieval_user_login_status in {
                Retrieval.UserLoginStatus.NOT_STARTED,
                # We keep these in pending because we don't have anything for the user to do.
                Retrieval.UserLoginStatus.UNABLE_TO_LOGIN,
                Retrieval.UserLoginStatus.UNABLE_TO_REACH_LANDING_PAGE,
                Retrieval.UserLoginStatus.UNKNOWN_ERROR,
            }:
                return UserLoginStatusCallToAction.PENDING
            if retrieval_user_login_status in {
                Retrieval.UserLoginStatus.INVALID_LOGIN_URL,
            }:
                return UserLoginStatusCallToAction.ACTION_REQUIRED
            return UserLoginStatusCallToAction.SUCCESS
        return UserLoginStatusCallToAction.PENDING

    @property
    def portal_credential_user_login_status(self) -> UserLoginStatusCallToAction:
        # We check the portal URL here because we currently use the button for credentials to
        # redirect the user to the enter URL page.
        if self.portal_user_login_status not in {
            UserLoginStatusCallToAction.SUCCESS,
            UserLoginStatusCallToAction.PENDING,
        }:
            return self.portal_user_login_status

        if self.portal_credential is None or not self.portal_credential.is_complete:
            return UserLoginStatusCallToAction.NOT_STARTED
        if self.last_user_login_validation_retrieval is not None:
            retrieval_user_login_status = self.last_user_login_validation_retrieval.user_login_status
            # Implicitly if there's other issues, we assume successful credentials.
            if retrieval_user_login_status in {
                Retrieval.UserLoginStatus.NOT_STARTED,
                Retrieval.UserLoginStatus.INVALID_LOGIN_URL,
                # We keep these in pending because we don't have anything for the user to do.
                Retrieval.UserLoginStatus.UNABLE_TO_LOGIN,
                Retrieval.UserLoginStatus.UNABLE_TO_REACH_LANDING_PAGE,
                Retrieval.UserLoginStatus.UNKNOWN_ERROR,
            }:
                return UserLoginStatusCallToAction.PENDING
            if retrieval_user_login_status in {
                Retrieval.UserLoginStatus.INVALID_CREDENTIALS,
            }:
                return UserLoginStatusCallToAction.ACTION_REQUIRED
            return UserLoginStatusCallToAction.SUCCESS
        return UserLoginStatusCallToAction.PENDING

    @property
    def user_login_status_debug_str(self) -> str:
        """
        Returns a debug string for the user login status.
        """
        return (
            f"complete?: {self.is_complete}, "
            f"Portal Status: {self.portal_user_login_status}, "
            f"Cred Status: {self.portal_credential_user_login_status}, "
            f"complete?: {self.portal_credential.is_complete}, "
            f"MFA Status: {self.multi_factor_authentication_user_login_status}"
            f"complete?: {self.multi_factor_authentication.is_complete}, "
            f"last_ulv_retrieval status: {
                self.last_user_login_validation_retrieval.user_login_status
                if self.last_user_login_validation_retrieval
                else 'None'
            }"
        )

    @property
    def multi_factor_authentication_user_login_status(self) -> UserLoginStatusCallToAction:
        if self.multi_factor_authentication is None or not self.multi_factor_authentication.is_complete:
            return UserLoginStatusCallToAction.NOT_STARTED
        if self.last_user_login_validation_retrieval is not None:
            retrieval_user_login_status = self.last_user_login_validation_retrieval.user_login_status
            # Implicitly if there's other issues, we assume successful credentials.
            if retrieval_user_login_status in {
                Retrieval.UserLoginStatus.NOT_STARTED,
                Retrieval.UserLoginStatus.INVALID_LOGIN_URL,
                Retrieval.UserLoginStatus.INVALID_CREDENTIALS,
                # We keep these in pending because we don't have anything for the user to do.
                Retrieval.UserLoginStatus.UNABLE_TO_LOGIN,
                Retrieval.UserLoginStatus.UNABLE_TO_REACH_LANDING_PAGE,
                Retrieval.UserLoginStatus.UNKNOWN_ERROR,
            }:
                return UserLoginStatusCallToAction.PENDING
            if retrieval_user_login_status in {
                Retrieval.UserLoginStatus.INVALID_MFA_TYPE,
                Retrieval.UserLoginStatus.MFA_NOT_RECEIVED,
            }:
                return UserLoginStatusCallToAction.ACTION_REQUIRED
            return UserLoginStatusCallToAction.SUCCESS
        return UserLoginStatusCallToAction.PENDING

    def get_portal_url(self) -> str | None:
        if self.portal is not None:
            return self.portal.portal_login_url
        return None

    def get_mfa_secret(self) -> str | None:
        if self.multi_factor_authentication is not None and self.multi_factor_authentication.has_secret():
            try:
                return self.multi_factor_authentication.get_secret()
            except Exception:
                logger.exception("Cannot get mfa secret")
        return None

    def get_portal_secret(self) -> str | None:
        if self.portal_credential is not None and self.portal_credential.has_secret():
            try:
                return self.portal_credential.get_secret()
            except Exception:
                logger.exception("Cannot get portal secret")
        return None

    def get_otp(self) -> str | None:
        return self.multi_factor_authentication.get_otp()

    @property
    def onboarding_status(self) -> OnboardingStatus:
        if not self.is_complete and not self.is_partially_complete:
            return MergedPortalCredential.OnboardingStatus.NOT_STARTED

        if (not self.is_complete and self.is_partially_complete) or (self.is_pending_validation):
            return MergedPortalCredential.OnboardingStatus.IN_PROCESS

        # If is_complete is True and no retrieval is running (or no retrieval yet)
        # it implies the onboarding is done from the MPC's perspective.
        # Further status is then determined by connection_health_status.
        return MergedPortalCredential.OnboardingStatus.COMPLETED

    @property
    def connection_health_status(self) -> ConnectionHealthStatus:
        """
        Three States Across Portal Types:
        - Web Based:
        - Action Required: Catch All
        - Pending: Meant to be if there are not actions required
        - Complete: Meant to be if there are line items and/or documents
            but also handles future unlinkings

        - Email Based:
        - Action Required: Catch All
        - Pending: Email Integration is complete, but no line items or documents
        - Complete: Email Integration is complete, and there are line items and/or documents
        """
        if self.portal.portal_type == Portal.PortalType.WEB_BASED:
            if self.line_items and self.line_items.filter(is_visible=True).count() > 0 and self.is_validated:
                return MergedPortalCredential.ConnectionHealthStatus.LINKED

            if self.is_pending_validation or self.is_validated:
                return MergedPortalCredential.ConnectionHealthStatus.PENDING

            return MergedPortalCredential.ConnectionHealthStatus.ACTION_REQUIRED

        if self.portal.portal_type == Portal.PortalType.EMAIL_BASED:
            if (
                self.user_forwarding_rule
                and self.user_forwarding_rule.receiving_email
                and self.user_forwarding_rule.receiving_email.email_integration_status
                != CustomerEmailCredential.EmailIntegrationStatus.NOT_STARTED
            ):
                if self.line_items and self.line_items.filter(is_visible=True).count() > 0:
                    return MergedPortalCredential.ConnectionHealthStatus.LINKED
                return MergedPortalCredential.ConnectionHealthStatus.PENDING

            return MergedPortalCredential.ConnectionHealthStatus.ACTION_REQUIRED

        # This should never happen
        msg = f"Invalid portal type for merged portal credential: {self.portal.portal_type}"
        raise ValueError(msg)

    @property
    def latest_processed_docs_count(self) -> int:
        return sum([line_item.latest_processed_docs_count for line_item in self.line_items.all()])

    @classmethod
    def create(
        cls,
        user: BridgeUser,
        portal: Portal,
        portal_credential: PortalCredential | None = None,
        multi_factor_authentication: MultiFactorAuthentication | None = None,
        user_forwarding_rule: UserForwardingRule | None = None,
    ) -> "MergedPortalCredential":
        """
        Create a merged portal credential for a line item.
        """
        if user_forwarding_rule is None and (portal_credential is None and multi_factor_authentication is None):
            error_msg = "user_forwarding_rule or portal_credential and multi_factor_authentication needs to exist"
            raise ValueError(error_msg)
        if user_forwarding_rule:
            merged_creds = cls.objects.create(
                user=user,
                portal=portal,
                user_forwarding_rule=user_forwarding_rule,
            )
            merged_creds.save()
            return merged_creds
        merged_creds = cls.objects.create(
            user=user,
            portal=portal,
            portal_credential=portal_credential,
            multi_factor_authentication=multi_factor_authentication,
        )
        merged_creds.save()
        return merged_creds

    def update_merged_credential_portal_credential(self, portal_credential: PortalCredential) -> None:
        """
        Associate a portal credential with a merged credential.
        """
        from retrieval.tasks import universal_login_task

        old_portal_credential = self.portal_credential
        old_portal_credential.soft_delete()

        self.portal_credential = portal_credential
        self.save()
        self.refresh_from_db()
        if self.is_complete:
            user = self.created_by
            if user is None:
                logger.error("No user found for merged portal credential", merged_portal_credential_pk=self.pk)
                raise ValueError("No user found for merged portal credential")  # noqa: EM101
            retrieval = Retrieval.create(user, self, is_user_login_validation_retrieval=True)
            retrieval_id = str(retrieval.id)
            task = universal_login_task.apply_async(kwargs={"retrieval_id": retrieval_id})
            retrieval.task_id = task.id
            retrieval.save()

    def update_merged_credential_multi_factor_authentication(
        self, multi_factor_authentication: MultiFactorAuthentication
    ) -> None:
        """
        Associate an MFA with a merged credential.
        """
        from retrieval.tasks import universal_login_task

        old_multi_factor_authentication = self.multi_factor_authentication
        old_multi_factor_authentication.soft_delete()

        self.multi_factor_authentication = multi_factor_authentication
        self.save()
        self.refresh_from_db()
        if self.is_complete:
            user = self.created_by
            if user is None:
                logger.error("No user found for merged portal credential", merged_portal_credential_pk=self.pk)
                raise ValueError("No user found for merged portal credential")  # noqa: EM101
            retrieval = Retrieval.create(user, self, is_user_login_validation_retrieval=True)
            retrieval_id = str(retrieval.id)
            task = universal_login_task.apply_async(kwargs={"retrieval_id": retrieval_id})
            retrieval.task_id = task.id
            retrieval.save()

    @property
    def is_first_retrieval(self) -> bool:
        return self.last_retrieval == self.last_user_login_validation_retrieval and self.line_items.count() == 0
