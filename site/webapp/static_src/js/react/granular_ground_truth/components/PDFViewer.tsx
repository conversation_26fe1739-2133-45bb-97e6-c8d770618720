import React, { useEffect, useRef, useCallback } from "react";

interface PDFViewerProps {
  allPages: any[];
  scale: number;
  overlaysRef: React.MutableRefObject<{ [key: number]: HTMLDivElement }>;
  loading: boolean;
  setOverlayData: (pageNumber: number, data: any) => void;
  onCurrentPageChange: (pageNumber: number) => void;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  allPages,
  scale,
  overlaysRef,
  loading,
  setOverlayData,
  onCurrentPageChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const pageRefs = useRef<{ [key: number]: HTMLDivElement }>({});

  const handleScroll = useCallback(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const scrollTop = container.scrollTop;
    const containerHeight = container.clientHeight;
    const viewportCenter = scrollTop + containerHeight / 2;

    // Find which page is at the center of the viewport
    let currentPage = 1;
    for (let i = 1; i <= allPages.length; i++) {
      const pageElement = pageRefs.current[i];
      if (pageElement) {
        const pageTop = pageElement.offsetTop;
        const pageBottom = pageTop + pageElement.offsetHeight;

        if (viewportCenter >= pageTop && viewportCenter <= pageBottom) {
          currentPage = i;
          break;
        }
      }
    }

    onCurrentPageChange(currentPage);
  }, [allPages.length, onCurrentPageChange]);

  useEffect(() => {
    if (!containerRef.current || !allPages.length || loading) return;

    const renderAllPages = async () => {
      const container = containerRef.current!;
      container.innerHTML = ""; // Clear previous content

      for (let i = 0; i < allPages.length; i++) {
        const page = allPages[i];
        const pageNumber = i + 1;
        const viewport = page.getViewport({ scale });

        // Create page container
        const pageContainer = document.createElement("div");
        pageContainer.className = "relative mb-5 bg-white rounded shadow-lg";
        pageContainer.style.width = `${viewport.width}px`;
        pageContainer.style.height = `${viewport.height}px`;
        pageContainer.setAttribute("data-page-number", pageNumber.toString());

        // Create canvas
        const canvas = document.createElement("canvas");
        canvas.className = "block rounded";
        canvas.width = viewport.width;
        canvas.height = viewport.height;

        const context = canvas.getContext("2d");
        await page.render({
          canvasContext: context,
          viewport,
        }).promise;

        // Create overlay
        const overlay = document.createElement("div");
        overlay.setAttribute("data-pdf-overlay", "true");
        overlay.setAttribute("data-page-number", pageNumber.toString());
        overlay.className = "absolute top-0 left-0 pointer-events-none rounded";
        overlay.style.width = `${viewport.width}px`;
        overlay.style.height = `${viewport.height}px`;

        // Store overlay reference
        setOverlayData(pageNumber, overlay);

        // Add page number indicator
        const pageIndicator = document.createElement("div");
        pageIndicator.className =
          "absolute top-2 right-2 bg-gray-800 text-white px-2 py-1 rounded text-sm font-medium";
        pageIndicator.textContent = `Page ${pageNumber}`;

        pageContainer.appendChild(canvas);
        pageContainer.appendChild(overlay);
        pageContainer.appendChild(pageIndicator);

        container.appendChild(pageContainer);
        pageRefs.current[pageNumber] = pageContainer;
      }
    };
    renderAllPages();
  }, [allPages, scale, overlaysRef, loading]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener("scroll", handleScroll);
    // Initial call to set current page
    handleScroll();

    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-300">Loading PDF...</div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="pdf-viewer h-full overflow-y-auto"
      onScroll={handleScroll}
    />
  );
};

export default PDFViewer;
