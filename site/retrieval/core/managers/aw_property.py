import asyncio
import datetime
import hashlib
import json
from collections.abc import Async<PERSON>enerator
from zipfile import ZipFile

import structlog
from playwright.async_api import Locator
from webapp.models.documents import DocumentType

from retrieval.core.exceptions import ManagerNotInitializedError
from retrieval.core.registry import register_strategy
from retrieval.core.strategy import Document<PERSON>ash, RawDocumentTuple, RetrievalManager

logger = structlog.get_logger(__name__)


@register_strategy
class AWPropertyPortalManager(RetrievalManager):
    @classmethod
    def supports_portal(cls, portal_login_url: str) -> bool:
        logger.debug("Checking portal support", portal_login_url=portal_login_url, strategy_name=cls.__name__)
        return "awproperty.com" in portal_login_url

    @classmethod
    def known_user_portals(cls) -> list[tuple[str, str]]:
        return [
            ("<EMAIL>", "AW Property"),
        ]

    async def _enter_credentials(self, username: str | None, password: str | None) -> None:
        if self.page is None or username is None or password is None:
            raise ManagerNotInitializedError

        login_button_locators = self.page.get_by_text("Login")
        if await login_button_locators.count() > 0:
            login_button = login_button_locators.first
            await login_button.click()

        await asyncio.sleep(2)
        login_email_locator = self.page.locator("input[type='email']")
        await login_email_locator.click()
        await asyncio.sleep(1)
        await login_email_locator.fill(username)
        await asyncio.sleep(1)
        login_pass_locator = self.page.locator("input[type='password']")
        await login_pass_locator.click()
        await asyncio.sleep(1)
        await login_pass_locator.fill(password)
        await asyncio.sleep(1)
        login_button_locator = self.page.locator("button[type='submit']")
        await login_button_locator.click()

    async def _check_has_otp(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        return False

    async def _send_otp(self) -> None:
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _enter_otp(self, otp: str) -> None:  # noqa: ARG002
        # Not used here, but implemented for completeness.
        if self.page is None:
            raise ManagerNotInitializedError

    async def _check_login_successful(self) -> bool:
        if self.page is None:
            raise ManagerNotInitializedError
        res = await self.page.get_by_text("My Portfolio").count() > 0
        if res:
            return res
        await asyncio.sleep(10)
        return res

    def _doc_hash(self, document_metadata: str) -> DocumentHash:
        document = json.loads(document_metadata)
        keys = sorted(document.keys())
        string_value = "".join([str(document[k]) for k in keys])
        return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, self.__class__.__name__)  # noqa: S324, # nosec

    async def _retrieve(self) -> AsyncGenerator[RawDocumentTuple, None]:
        if self.page is None:
            raise ManagerNotInitializedError

        document_text_locators = self.page.get_by_text("Documents")
        if await document_text_locators.count() > 0:
            document_dropwdown_button = document_text_locators.first
            await document_dropwdown_button.click()
        await asyncio.sleep(1)

        dropdown_div_locator = self.page.locator("div.tippy-popper")
        document_button = dropdown_div_locator.get_by_text("Documents")
        await document_button.click()
        await asyncio.sleep(2)

        page_navigation = self.page.locator("nav[aria-label='pagination navigation']")
        page_tabs = page_navigation.locator("li:not(:has(svg))")
        page_tabs_inner_texts = await page_tabs.all_inner_texts()
        for page_tab_inner_txt in page_tabs_inner_texts:
            cur_page_button = page_tabs.locator(f"button:text-is('{page_tab_inner_txt}')")
            await cur_page_button.click()
            await asyncio.sleep(3)

            table = self.page.locator("table.MuiTable-root")
            table_body = table.locator("tbody.MuiTableBody-root")
            table_rows = table_body.locator("tr.MuiTableRow-root")
            for table_row in await table_rows.all():
                row_metadata = await self._get_row_metadata(table_row)
                metadata = json.dumps(row_metadata)
                cells = table_row.locator("td.MuiTableCell-root")
                checkbox = cells.locator("input[type='checkbox']")
                await checkbox.set_checked(True)
                download_button = self.page.locator("button:has(svg[data-icon='download'])")
                if await self.should_download_document(metadata) and not await download_button.is_disabled():
                    content = await self._download_document(download_button)
                    if content:
                        doc_type = self._document_mapper(row_metadata["Type"])
                        datetime_date = datetime.datetime.strptime(row_metadata["Date Added"], "%m/%d/%Y")  # noqa: DTZ007
                        doc = RawDocumentTuple(
                            name=row_metadata["Name"],
                            date=datetime_date,
                            doc_type=doc_type,
                            content=content,
                            raw_metadata=metadata,
                            doc_hash=self._doc_hash(metadata),
                        )
                        if doc is not None:
                            yield doc
                await checkbox.set_checked(False)

    async def _get_row_metadata(self, row: Locator) -> dict[str, str]:
        row_metadata = {}
        cells = row.locator("td.MuiTableCell-root")
        row_metadata["Name"] = await cells.nth(1).inner_text()
        row_metadata["Investment"] = await cells.nth(2).inner_text()
        row_metadata["Type"] = await cells.nth(3).inner_text()
        row_metadata["Profile"] = await cells.nth(4).inner_text()
        row_metadata["Date Added"] = await cells.nth(5).inner_text()
        return row_metadata

    async def _download_document(self, document_button: Locator) -> bytes:
        if self.page is None or self.local_directory is None:
            raise ManagerNotInitializedError
        async with self.page.expect_download() as download_info:
            await document_button.click()
        download = await download_info.value
        file_name = self.local_directory / download.suggested_filename
        await download.save_as(file_name)
        content = None
        zip_file_name = None
        with ZipFile(file_name, "r") as zf:
            zip_file_names = [zfn for zfn in zf.namelist() if not (zfn.endswith("/") or zfn.startswith("__MACOSX/"))]
            if len(zip_file_names) != 1:
                logger.info("Multiple files in zip", zip_file_names=zip_file_names)
                raise ValueError
            zip_file_name = zip_file_names[0]
            with zf.open(zip_file_name) as pdf_file:
                content = pdf_file.read()
        file_name.unlink()
        return content

    def _document_mapper(self, document_type: str | None) -> DocumentType | str:
        if document_type is None:
            return DocumentType.UNKNOWN

        mapping = {
            "Tax Documents": DocumentType.TAX,
            "Management Reports": DocumentType.INVESTMENT_UPDATE,
            "Financials": DocumentType.FINANCIAL_STATEMENTS,
            "Funding Notice": DocumentType.CAPITAL_CALL,
            "Closing Documents": DocumentType.OTHER,
        }

        return mapping.get(document_type, DocumentType.UNKNOWN)
