import json
from itertools import groupby
from operator import attrgetter

import structlog  # type: ignore  # noqa: PGH003
from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.db.models import Case, IntegerField, Value, When
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse

from webapp.models import Client, InvestingEntity, Investment
from webapp.models.portal import MergedPortalCredential, MFAStatus, MFAType, Portal, UserLoginStatusCallToAction
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser
from webapp.views.client_dashboard import QuerySet
from webapp.views.utils import (
    add_hx_trigger_header,
    get_line_item_from_url_params,
    get_merged_portal_credential_from_url_params,
)

logger = structlog.get_logger(__name__)


@login_required
def dashboard(request: HttpRequest) -> HttpResponse:
    template = "link/mpc_link_dashboard.html"
    context = get_all_portal_context(request.user)

    # TODO: Get triggers working for MPCS
    hx_trigger = request.GET.get("hx_trigger")
    hx_triggers = []
    if hx_trigger is not None:
        try:
            hx_trigger = json.loads(hx_trigger)
            for event_name, details in hx_trigger.items():
                details_str = ", ".join([f"{k}:'{v}'" for k, v in details.items()])
                hyperscript_event = f"{event_name}({details_str})"
                hx_triggers.append(hyperscript_event)
        except json.JSONDecodeError:
            hx_triggers.append(hx_trigger)

    context["hx_triggers"] = hx_triggers
    logger.info("hx_triggers", hx_triggers=hx_triggers)
    return TemplateResponse(request, template, context)


@login_required
def search_line_items(request: HttpRequest) -> HttpResponse:
    """Handles search queries for the line items table with optional portal and connection status filters."""
    # Get portal filters - handle both single and multiple values
    portal_ids = request.POST.getlist("portal") or [request.POST.get("portal", "").strip()]
    portal_ids = [pid for pid in portal_ids if pid.strip()]

    # Get connection status filters - handle both single and multiple values
    connection_statuses = request.POST.getlist("connection_status") or [
        request.POST.get("connection_status", "").strip()
    ]
    connection_statuses = [status for status in connection_statuses if status.strip()]

    # Get filtered context
    context = get_all_portal_context(request.user, portal_ids=portal_ids, connection_statuses=connection_statuses)

    return TemplateResponse(request, "link/portal/table.html", context)


@login_required
def manage_merged_portal_credential(request: HttpRequest) -> HttpResponse:
    template_name = "link/portal/merged_portal_credential.html"
    merged_portal_credential = get_merged_portal_credential_from_url_params(request)
    # mpc_row_hidden aims to maintain investment row state across htmx swaps
    mpc_row_hidden_key = f"mpc_{merged_portal_credential.pk}_hidden"
    mpc_row_hidden_state = request.POST.get(mpc_row_hidden_key, "true")
    line_items = (
        merged_portal_credential.line_items.for_user(request.user)
        .select_related("investment")
        .order_by("investment__legal_name")
    )

    # hidden_investment_groups aims to maintain line item row state across htmx swaps
    hidden_investment_groups = []

    for key, value in request.POST.items():
        if key.startswith("investment_group_") and key.endswith("_hidden") and value == "true":
            pk = key.replace("investment_group_", "").replace("_hidden", "")
            hidden_investment_groups.append(pk)

    line_items_by_investment = [
        {
            "investment": investment,
            "line_items": list(items),
            "investment_group_hidden_state": "true" if str(investment.pk) in hidden_investment_groups else "false",
        }
        for investment, items in groupby(line_items, key=attrgetter("investment"))
    ]

    context = {
        "merged_portal_credential": merged_portal_credential,
        "merged_portal_credential_with_line_items": {
            "merged_portal_credential": merged_portal_credential,
            "line_items_by_investment": line_items_by_investment,
            "total_line_items": sum(len(group["line_items"]) for group in line_items_by_investment),
        },
        "mpc_row_hidden_state": mpc_row_hidden_state,
        "MFAStatus": MFAStatus,
        "RetrievalStatus": Retrieval.RetrievalStatus,
        "OnboardingStatus": MergedPortalCredential.OnboardingStatus,
        "PortalType": Portal.PortalType,
        "MFAType": MFAType,
        "UserLoginStatusCallToAction": UserLoginStatusCallToAction,
    }
    return TemplateResponse(request, template_name, context)


@login_required
def aggregate_cards(request: HttpRequest) -> HttpResponse:
    template_name = "link/portal/aggregate_cards.html"
    context = get_all_portal_context(request.user)  # type: ignore[attr-defined]
    return TemplateResponse(request, template_name, context)


def get_all_portal_context(
    user: BridgeUser, portal_ids: list[str] | None = None, connection_statuses: list[str] | None = None
) -> dict:
    mpcs = (
        MergedPortalCredential.objects.for_user(user)
        .select_related(
            "user_forwarding_rule",
            "user_forwarding_rule__receiving_email",
            "last_retrieval",
            "last_user_login_validation_retrieval",
            "portal",
            "portal_credential",
            "multi_factor_authentication",
            "multi_factor_authentication__receiving_email",
        )
        .prefetch_related(
            "line_items",
            "line_items__investment",
            "line_items__investing_entity",
            "line_items__investing_entity__client",
        )
        .annotate(
            # TODO: add an annotation for line item count to speed this up.
            # connection_health_status counting blows up SQL query a bit
            portal_type_order=Case(
                When(portal__portal_type=Portal.PortalType.WEB_BASED, then=Value(0)),
                When(portal__portal_type=Portal.PortalType.EMAIL_BASED, then=Value(1)),
                default=Value(2),
                output_field=IntegerField(),
            )
        )
        .order_by("portal_type_order", "portal__name")
    )

    if portal_ids:
        mpcs = mpcs.filter(portal_id__in=portal_ids)

    # Convert queryset to list for property-based filtering
    mpcs_list = list(mpcs)

    # Apply connection status filter using the property
    if connection_statuses:
        mpcs_list = {mpc for mpc in mpcs_list if mpc.connection_health_status in connection_statuses}

    group_by = {}
    for mpc in mpcs_list:
        group_by.setdefault(mpc.onboarding_status, 0)
        group_by[mpc.onboarding_status] += 1
        group_by.setdefault(mpc.connection_health_status, 0)
        group_by[mpc.connection_health_status] += 1

    merged_portal_credentials_with_line_items = []

    for mpc in mpcs_list:
        line_items = mpc.line_items.for_user(user).select_related("investment").order_by("investment__legal_name")

        line_items_by_investment = [
            {"investment": investment, "line_items": list(items)}
            for investment, items in groupby(line_items, key=attrgetter("investment"))
        ]

        # Calculate total count
        total_line_items = sum(len(group["line_items"]) for group in line_items_by_investment)

        merged_portal_credentials_with_line_items.append(
            {
                "merged_portal_credential": mpc,
                "line_items_by_investment": line_items_by_investment,
                "total_line_items": total_line_items,
            }
        )

    # Calculate total line item count
    line_item_count = sum(mpc["total_line_items"] for mpc in merged_portal_credentials_with_line_items)

    # Maintains order for dropdown
    distinct_portals = [mpc.portal for mpc in mpcs_list]

    connection_status_labels = {
        MergedPortalCredential.ConnectionHealthStatus.ACTION_REQUIRED: (
            MergedPortalCredential.ConnectionHealthStatus.ACTION_REQUIRED.label
        ),
        MergedPortalCredential.ConnectionHealthStatus.PENDING: (
            MergedPortalCredential.ConnectionHealthStatus.PENDING.label
        ),
        MergedPortalCredential.ConnectionHealthStatus.LINKED: (
            MergedPortalCredential.ConnectionHealthStatus.LINKED.label
        ),
    }

    return {
        "merged_portal_credentials_with_line_items": merged_portal_credentials_with_line_items,
        # Card Counts
        "not_started_count": group_by.get(MergedPortalCredential.OnboardingStatus.NOT_STARTED, 0),
        "in_process_count": group_by.get(MergedPortalCredential.OnboardingStatus.IN_PROCESS, 0),
        "completed_count": group_by.get(MergedPortalCredential.OnboardingStatus.COMPLETED, 0),
        "pending_count": group_by.get(MergedPortalCredential.ConnectionHealthStatus.PENDING, 0),
        "linked_count": group_by.get(MergedPortalCredential.ConnectionHealthStatus.LINKED, 0),
        "action_required_count": group_by.get(MergedPortalCredential.ConnectionHealthStatus.ACTION_REQUIRED, 0),
        "line_item_count": line_item_count,
        "ConnectionHealthStatus": connection_status_labels,
        "portals": distinct_portals,
        "connection_statuses": MergedPortalCredential.ConnectionHealthStatus,
        "onboarding_statuses": MergedPortalCredential.OnboardingStatus,
        "MFAStatus": MFAStatus,
        "RetrievalStatus": Retrieval.RetrievalStatus,
        "OnboardingStatus": MergedPortalCredential.OnboardingStatus,
        "PortalType": Portal.PortalType,
        "MFAType": MFAType,
        "UserLoginStatusCallToAction": UserLoginStatusCallToAction,
    }


@login_required
def confirm_edit(request: HttpRequest) -> HttpResponse:
    mpc = get_merged_portal_credential_from_url_params(request)
    line_item = get_line_item_from_url_params(request)
    client_name = request.POST.get("client_name")
    entity_name = request.POST.get("entity_name")
    investment_name = request.POST.get("investment_name")
    modal_description_text_with_line_item = "No changes were deteched for line item:"
    if (
        line_item.investing_entity.client.legal_name != client_name
        or line_item.investing_entity.legal_name != entity_name
        or line_item.investment.legal_name != investment_name
    ):
        # line item changed
        with transaction.atomic():
            client, _ = Client.objects.update_or_create(user=request.user, legal_name=client_name)
            investing_entity, _ = InvestingEntity.objects.update_or_create(
                user=request.user,
                legal_name=entity_name,
                defaults={
                    "client": client,
                },
            )
            investment, _ = Investment.objects.update_or_create(
                user=request.user, legal_name=investment_name, create_defaults={"managing_firm_name": ""}
            )
            line_item.investing_entity = investing_entity
            line_item.investment = investment
            line_item.save()
        headers = add_hx_trigger_header({"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": mpc.pk}})
        modal_description_text_with_line_item = "Line item updated:"

    return TemplateResponse(
        request,
        "link/line_items/edit_line_item_end.html",
        {
            "mpc": mpc,
            "line_item": line_item,
            "modal_header_text": "Success",
            "modal_description_text_with_line_item": modal_description_text_with_line_item,
        },
        headers=headers,
    )


@login_required
def delete_line_item(request: HttpRequest) -> HttpResponse:
    mpc = get_merged_portal_credential_from_url_params(request)
    line_item = get_line_item_from_url_params(request)
    if request.POST.get("confirm") == "confirm":
        line_item.soft_delete()
        headers = add_hx_trigger_header({"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": mpc.pk}})
        return TemplateResponse(
            request,
            "link/line_items/edit_line_item_end.html",
            {
                "mpc": mpc,
                "line_item": line_item,
                "modal_header_icon": True,
                "modal_description_text_with_line_item": "Deleted line item:",
            },
            headers=headers,
        )

    return TemplateResponse(
        request,
        "link/line_items/delete_line_item_confirm.html",
        {
            "mpc": mpc,
            "line_item": line_item,
        },
    )


def get_all_line_item_field_for_user(user: BridgeUser, line_item_field: str) -> QuerySet | None:
    if line_item_field == "client":
        return Client.objects.for_user(user).order_by("legal_name")
    if line_item_field == "investing_entity":
        return InvestingEntity.objects.for_user(user).order_by("legal_name")
    if line_item_field == "investment":
        return Investment.objects.for_user(user).order_by("legal_name")
    return None


@login_required
def line_item_filtered_options(request: HttpRequest) -> HttpResponse:
    """Get filtered entities and investments based on selected client/entity."""
    line_item_field = request.GET.get("line_item_field")
    if line_item_field:
        query_set = get_all_line_item_field_for_user(request.user, line_item_field)
        query_filter = request.POST.get(line_item_field, "").strip()
        if query_filter and query_set:
            query_set = query_set.filter(legal_name__icontains=query_filter).distinct()
    return TemplateResponse(
        request,
        "line_item_search_option.html",
        {
            "drop_down_options": query_set or [],
            "input_name": line_item_field,
            "input_display_name": line_item_field.replace("_", " "),
        },
    )
