<div id="latest-updates-content"
     class="h-[478px] p-8 bg-base-beige flex-col justify-start items-start w-full">
    <div class="PaddingBottom3xl self-stretch h-[52px] pb-6 flex-col justify-start items-start gap-2.5 flex">
        <div class="SectionHeader h-40 flex-col justify-start items-start gap-5 flex">
            <div class="Content self-stretch justify-start items-start gap-4 inline-flex">
                <div class="DisplayMd text-[#343d36] text-xl text-[20px] font-bold font-nanum leading-7">Latest Updates</div>
            </div>
        </div>
    </div>
    <div class="TextContent self-stretch w-full flex-col justify-start items-start gap-4 flex">
        <div class="BodyXsMedium self-stretch w-full text-[#616863] text-sm font-medium font-inter leading-[21px]">
            <div class="border-separate py-4 w-full">
                {% if latest_updates %}
                    <div class="relative w-full">
                        <div class="overflow-auto max-h-[330px] w-full rounded-md border border-gray-200">
                            <table class="min-w-full w-full bg-white">
                                <thead class="bg-white sticky top-0 z-10">
                                    <tr class="border-b after:absolute after:inset-0 after:bg-white after:-z-10">
                                        <th class="px-3 py-2 text-left text-[#343D36] font-inter font-medium text-[14px]">Date Received</th>
                                        <th class="px-3 py-2 text-left text-[#343D36] font-inter font-medium text-[14px]">Entity</th>
                                        <th class="px-3 py-2 text-left text-[#343D36] font-inter font-medium text-[14px]">Investment</th>
                                        <th class="px-3 py-2 text-left text-[#343D36] font-inter font-medium text-[14px]">Notice Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for latest_update in latest_updates %}
                                        {% include "dashboard/latest_updates/row.html" %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% else %}
                    <p>No updates available</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
