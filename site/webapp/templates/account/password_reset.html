{% extends "account/base_entrance.html" %}
{% comment %}
    From: https://docs.allauth.org/en/dev/common/templates.html
    Source: https://codeberg.org/allauth/django-allauth/src/branch/main/allauth/templates/account/password_rest.html
{% endcomment %}
{% load widget_tweaks static allauth account %}
{% block head_title %}
    Forgot Password
{% endblock head_title %}
{% block extra_head %}
    {% include "components/bridge_header.html" %}
{% endblock extra_head %}
{% block content %}
    <main>
        <div class="Container h-[636px] w-full px-8 flex flex-col justify-center items-center">
            <div class="Content w-4/5 flex flex-col justify-center items-center gap-20">
                <div class="HeaderAndForm h-[298px] flex-col justify-start items-start gap-8 inline-flex">
                    <!-- Header -->
                    <div class="Header self-stretch h-[76px] flex-col justify-start items-center flex">
                        <div class="TextAndSupportingText self-stretch h-[76px] flex-col justify-start items-start gap-3 flex">
                            <div class="Text self-stretch text-[#343d36] text-4xl font-bold font-['Nanum Myeongjo'] leading-[44px]">
                                Forgot password?
                            </div>
                            <div class="Text text-center text-[#343d36] text-sm font-normal font-['Inter'] leading-tight">
                                No worries, we’ll send you reset instructions.
                            </div>
                        </div>
                    </div>
                    <!-- Form -->
                    <form method="post"
                          class="Content self-stretch h-[138px] rounded-xl flex-col justify-start items-start gap-6 flex">
                        {% csrf_token %}
                        <div class="Form self-stretch h-[70px] flex-col justify-start items-start gap-4 flex">
                            <!-- Email Input -->
                            <div class="InputWithLabel self-stretch h-[70px] flex-col justify-start items-start gap-1.5 flex">
                                <div class="LabelWrapper justify-start items-start gap-0.5 inline-flex">
                                    <label for="id_email"
                                           class="Label text-[#343d36] text-sm font-medium font-['Inter'] leading-tight">
                                        Email
                                    </label>
                                </div>
                                <div class="Input self-stretch bg-white rounded-lg shadow border border-[#eceee8] flex justify-between items-center gap-2">
                                    <input id="id_email"
                                           name="email"
                                           type="email"
                                           placeholder="Enter your email"
                                           required
                                           class="grow shrink h-11 flex border-none rounded-xl text-[#343d36] text-sm font-normal font-inter focus:outline-none focus:ring-0" />
                                </div>
                            </div>
                        </div>
                        <!-- Submit Button -->
                        <div class="Actions self-stretch h-11 flex-col justify-start items-start gap-4 flex">
                            <button type="submit"
                                    class="ButtonsButton self-stretch px-4 py-2.5 bg-[#924f34] rounded-lg shadow border border-[#924f34] justify-center items-center gap-1.5 inline-flex">
                                <div class="TextPadding px-0.5 justify-center items-center flex">
                                    <div class="Text text-white text-base font-semibold font-['Inter'] leading-normal">Reset password</div>
                                </div>
                            </button>
                        </div>
                    </form>
                    <!-- Back to Sign In -->
                    <div class="Row self-stretch justify-center items-start gap-1 inline-flex">
                        <a href="{% url 'account_login' %}"
                           class="ButtonsButton justify-center items-center gap-1.5 flex">
                            <div class="Frame1000003288 w-3.5 h-3.5 justify-center items-center gap-2.5 flex"></div>
                            <div class="Text text-center text-[#924f34] text-sm font-semibold font-['Inter'] leading-tight">Back to Sign in</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>
{% endblock content %}
