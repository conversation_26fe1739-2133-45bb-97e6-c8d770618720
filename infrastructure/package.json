{"name": "bridge-invest-cdk", "version": "0.1.0", "bin": {"bridge-invest": "bin/bridge-invest.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "cdk": "cdk"}, "devDependencies": {"@types/node": "24.1.0", "aws-cdk": "^2.1023.0", "prettier": "3.6.2", "ts-node": "^10.9.2", "typescript": "~5.9.2"}, "dependencies": {"@aws-sdk/client-ssm": "^3.859.0", "@aws-sdk/client-sts": "^3.859.0", "aws-cdk-lib": "^2.208.0", "constructs": "^10.0.0", "source-map-support": "^0.5.21"}}