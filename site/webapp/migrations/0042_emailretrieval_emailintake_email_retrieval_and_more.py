# Generated by Django 5.2.1 on 2025-05-27 14:44

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0041_alter_lineitempermission_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailRetrieval',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('sender_emails', models.JSONField(default=list)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('line_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_retrievals', to='webapp.lineitem')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s', to='webapp.organization')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
                'default_manager_name': 'objects',
            },
        ),
        migrations.AddField(
            model_name='emailintake',
            name='email_retrieval',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='email_intakes', to='webapp.emailretrieval'),
        ),
        migrations.CreateModel(
            name='UserForwardingRule',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('forwarding_rule', models.TextField(blank=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('from_email', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_forwarding_rule', to='webapp.customeremailcredential')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s', to='webapp.organization')),
                ('receiving_email', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_forwarding_rule', to='webapp.bridgeemail')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
                'default_manager_name': 'objects',
            },
        ),
        migrations.AddField(
            model_name='emailretrieval',
            name='user_forwarding_rule',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_retrievals', to='webapp.userforwardingrule'),
        ),
    ]
