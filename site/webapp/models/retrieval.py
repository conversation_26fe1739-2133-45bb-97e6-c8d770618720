import time
import typing
import urllib.parse
from pathlib import Path

import boto3
import structlog
from django.conf import settings
from django.db import models, transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from webapp.utils.slack import send_message_to_slack

logger = structlog.get_logger(__name__)

import datetime

from webapp.models.core import AbstractBaseModel
from webapp.models.documents import RawDocument
from webapp.models.user import BridgeUser


class NoPortalCredentialError(Exception):
    pass


class TimeoutForOTPTokenError(Exception):
    pass


class CanceledRetrievalError(Exception):
    pass


class RetrievalError(Exception):
    pass


BUCKET = settings.AWS_STORAGE_BUCKET_NAME
RETRIEVAL_LOGS_PATH = Path("retrieval_logs")
RETRIEVAL_LOG_FORMAT_VERISON = 1
RETRIEVAL_COOLDOWN_HOURS = 0.5  # 30 minutes cooldown

if typing.TYPE_CHECKING:
    from webapp.models.portal import MergedPortalCredential


class Retrieval(AbstractBaseModel):
    MAX_RETRIES = 3

    class RetrievalStatus(models.TextChoices):
        ## META STATES
        # Default state: retrievals in this state may have never gotten to the task queue
        NOT_STARTED = "ns", _("Not Started")

        # In progress state (managed by the caller)
        SUBMITTED = "su", _("Submitted")
        PENDING_CANCEL = "pc", _("Cancel Pending")

        ## LOGIN STATES
        # In progress state (managed by the task)
        PENDING_LOGIN = "lp", _("Login Pending")
        # Pending occurs after we are unblocked from an OTP
        PENDING_LOGIN_OTP = "2p", _("OTP Pending")
        # In progress state, but task blocked (managed by the task)
        BLOCKED_LOGIN_OTP = "2b", _("OTP Blocked")

        # Failure states (managed by the task)
        CANCELED = "ca", _("Canceled")
        FAILED_LOGIN_CREDENTIALS = "lf", _("Login Failed")
        FAILED_LOGIN_OTP = "2f", _("OTP Required and could not get OTP")
        FAILED_PORTAL_DOWN = "pd", _("Login Failed - Portal Down")
        FAILED_PORTAL_DOWN_RETRY = "fr", _("Portal Down - Retryable")

        # Scheduled state for retries or first time
        PENDING_SCHEDULE = "ps", _("Pending Scheduling")

        # Success states (managed by the task)
        SUCCESS_LOGGED_IN = "li", _("Successfully Logged In")

        ## DOCUMENT RETRIEVAL STATES
        # In progress state (managed by the task)
        PENDING_DOCUMENT_RETRIEVAL = "dr", _("Document Retrieval Pending")

        # Failure states
        FAILED_DOCUMENT_RETRIEVAL = "df", _("Document Retrieval Failed")

        # Success states
        SUCCESS_DOCUMENT_RETRIEVAL = "ds", _("Successfully Retrieved Documents")

        # Retrieval stopped due to max task time limit
        DOCUMENT_RETRIEVAL_EXCEEDED_MAX_TIME_LIMIT = "mt", _("Document Retrieval Exceeded Max Time Limit")

    class UserLoginStatus(models.TextChoices):
        # Default state: we have not ever checked the state for this retrieval.
        NOT_STARTED = "ns", _("Not Started")

        # Failure states
        INVALID_LOGIN_URL = "il", _("Invalid Login URL")
        INVALID_CREDENTIALS = "ic", _("Invalid Credentials")
        INVALID_MFA_TYPE = "im", _("Invalid MFA Type")
        MFA_NOT_RECEIVED = "mn", _("MFA Not Received")
        UNABLE_TO_LOGIN = "ul", _("Unable to Login")
        UNABLE_TO_REACH_LANDING_PAGE = "up", _("Unable to Reach Landing Page")
        UNKNOWN_ERROR = "ue", _("Unknown Error")

        # Success state
        SUCCESS_LOGGED_IN = "sl", _("Successfully Logged In")

    merged_portal_credential: "MergedPortalCredential" = models.ForeignKey(
        "MergedPortalCredential", on_delete=models.SET_NULL, related_name="retrievals", null=True
    )

    # This many to many field represents, at the time of retrieval,
    # which line items do we expect to be retrieved.
    # This expectation is defined by the line items the user associated with their MPC.
    line_items = models.ManyToManyField(
        "LineItem",
        related_name="retrievals",
        blank=True,
    )
    task_id = models.CharField(max_length=255, blank=True)
    s3_format_version = models.IntegerField(default=RETRIEVAL_LOG_FORMAT_VERISON)
    s3_key = models.CharField(max_length=1024, blank=True)
    s3_bucket = models.CharField(max_length=255, blank=True)
    exists_in_s3 = models.BooleanField(default=False)
    # TODO: should this should go here? We blank it out after use.
    token_otp = models.CharField(blank=True, max_length=255)
    manager = models.CharField(max_length=255, blank=True)
    is_backfill = models.BooleanField(default=False)
    check_point = models.JSONField(default=dict, blank=True)
    starting_point = models.JSONField(default=dict, blank=True)

    start_time = models.DateTimeField(null=True, blank=True)
    number_documents_retrieved = models.IntegerField(default=0)
    number_documents_skipped = models.IntegerField(default=0)
    number_of_retries = models.IntegerField(default=0)

    # should we keep a history of login_status for one retrieval?
    retrieval_status = models.CharField(
        max_length=2,
        choices=RetrievalStatus.choices,
        default=RetrievalStatus.NOT_STARTED,
    )
    user_login_status = models.CharField(
        max_length=2,
        choices=UserLoginStatus.choices,
        default=UserLoginStatus.NOT_STARTED,
    )
    raw_documents: models.QuerySet[RawDocument]

    # Permissions
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return ["line_items"]

    def __str__(self) -> str:
        return f"Retrieval {self.id} for {self.merged_portal_credential}"

    @property
    def is_retrieval_running(self) -> bool:
        # TODO: Need to refactor if document retrieval not supported for portal
        return self.retrieval_status in {
            Retrieval.RetrievalStatus.NOT_STARTED,
            Retrieval.RetrievalStatus.SUBMITTED,
            Retrieval.RetrievalStatus.PENDING_CANCEL,
            Retrieval.RetrievalStatus.PENDING_SCHEDULE,
            Retrieval.RetrievalStatus.PENDING_LOGIN,
            Retrieval.RetrievalStatus.PENDING_LOGIN_OTP,
            Retrieval.RetrievalStatus.BLOCKED_LOGIN_OTP,
            Retrieval.RetrievalStatus.SUCCESS_LOGGED_IN,
            Retrieval.RetrievalStatus.PENDING_DOCUMENT_RETRIEVAL,
        }

    @property
    def is_retrieval_successfully_completed(self) -> bool:
        return self.retrieval_status in {
            Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL,
        }

    @property
    def is_retrieval_retryable(self) -> bool:
        self.refresh_from_db()
        return self.retrieval_status in {
            Retrieval.RetrievalStatus.FAILED_LOGIN_CREDENTIALS,
            Retrieval.RetrievalStatus.FAILED_LOGIN_OTP,
            Retrieval.RetrievalStatus.FAILED_PORTAL_DOWN,
            Retrieval.RetrievalStatus.CANCELED,
        }

    @property
    def is_email_mfa(self) -> bool:
        from webapp.models.portal import MFAType

        self.refresh_from_db()
        if self.merged_portal_credential is None:
            raise RetrievalError
        return self.merged_portal_credential.multi_factor_authentication_type == MFAType.EMAIL

    @property
    def is_sms_mfa(self) -> bool:
        from webapp.models.portal import MFAType

        self.refresh_from_db()
        if self.merged_portal_credential is None:
            raise RetrievalError
        return self.merged_portal_credential.multi_factor_authentication_type == MFAType.SMS

    @property
    def next_available_time(self) -> datetime.datetime:
        return self.created_at + datetime.timedelta(hours=RETRIEVAL_COOLDOWN_HOURS)

    @property
    def external_retrieval_status(self) -> str:
        if self.is_retrieval_running:
            return "Active retrieval"
        if self.is_retrieval_successfully_completed:
            return "Successfully retrieved documents"
        return "Bridge review"

    @property
    def admin_panel_link(self) -> str:
        return f"https://{settings.DOMAIN_NAME}/admin/webapp/retrieval/{self.pk}/change"

    @property
    def retrieval_meta_data_slack_block(self) -> dict:
        return {
            "type": "section",
            "fields": [
                {
                    "type": "mrkdwn",
                    "text": f"*Merged Portal Credential:*\n{self.merged_portal_credential.portal}",
                },
                {"type": "mrkdwn", "text": f"*Organization:*\n{self.organization}"},
            ],
        }

    @classmethod
    def create(
        cls,
        user: BridgeUser,
        merged_portal_credential: "MergedPortalCredential",
        *,
        manager: str | None = None,
        is_user_login_validation_retrieval: bool = False,
        **kwargs: typing.Any,  # noqa: ANN401
    ) -> "Retrieval":
        from retrieval.core.registry import RetrievalRegistry

        with transaction.atomic():
            # Get the manager from the registry if not provided
            if manager is None:
                manager_cls = RetrievalRegistry.get_retrieval_manager(
                    user=user,
                    name=merged_portal_credential.portal.name,
                    portal_login_url=merged_portal_credential.portal.portal_login_url,
                )
                manager = "" if manager_cls is None else manager_cls.__name__

            retrieval = Retrieval.objects.create(
                merged_portal_credential=merged_portal_credential,
                user=user,
                manager=manager,
                **kwargs,
            )
            merged_portal_credential.last_retrieval = retrieval
            if is_user_login_validation_retrieval:
                merged_portal_credential.last_user_login_validation_retrieval = retrieval
            merged_portal_credential.save()
            retrieval.line_items.set(merged_portal_credential.line_items.all())
            retrieval.save()
            retrieval.line_items.update(last_retrieval=retrieval)
            return retrieval

    def get_retrieval_s3_path(self) -> tuple[str, Path]:
        if not self.exists_in_s3:
            self.exists_in_s3 = True
            self.s3_bucket = BUCKET
            self.s3_key = str(RETRIEVAL_LOGS_PATH / str(self.pk))
            self.s3_format_version = RETRIEVAL_LOG_FORMAT_VERISON
            self.save()
        return self.s3_bucket, Path(self.s3_key)

    def try_get_otp(self) -> str | None:
        return self.merged_portal_credential.get_otp()

    def increment_documents_retrieved(self) -> None:
        self.refresh_from_db()
        self.number_documents_retrieved += 1
        self.save()

    def increment_documents_skipped(self) -> None:
        self.refresh_from_db()
        self.number_documents_skipped += 1
        self.save()

    def update_login_status(
        self, login_status: RetrievalStatus, *, should_slack: bool = True, should_raise: bool = True
    ) -> None:
        self.refresh_from_db()
        if should_raise and self.retrieval_status == Retrieval.RetrievalStatus.PENDING_CANCEL:
            self.retrieval_status = Retrieval.RetrievalStatus.CANCELED
            self.save()
            raise CanceledRetrievalError

        # listing all the statuses here, if it returns an empty string don't send the slack message
        header_emoji = {
            Retrieval.RetrievalStatus.NOT_STARTED: "",
            Retrieval.RetrievalStatus.SUBMITTED: "",
            Retrieval.RetrievalStatus.PENDING_CANCEL: "",
            Retrieval.RetrievalStatus.PENDING_LOGIN: "",
            Retrieval.RetrievalStatus.PENDING_LOGIN_OTP: "",
            Retrieval.RetrievalStatus.BLOCKED_LOGIN_OTP: "",
            Retrieval.RetrievalStatus.CANCELED: ":x:",
            Retrieval.RetrievalStatus.FAILED_LOGIN_CREDENTIALS: ":x:",
            Retrieval.RetrievalStatus.FAILED_LOGIN_OTP: ":no_entry_sign:",
            Retrieval.RetrievalStatus.FAILED_PORTAL_DOWN: ":x:",
            Retrieval.RetrievalStatus.FAILED_PORTAL_DOWN_RETRY: "",
            Retrieval.RetrievalStatus.PENDING_SCHEDULE: "",
            Retrieval.RetrievalStatus.SUCCESS_LOGGED_IN: "",
            Retrieval.RetrievalStatus.PENDING_DOCUMENT_RETRIEVAL: "",
            Retrieval.RetrievalStatus.FAILED_DOCUMENT_RETRIEVAL: ":x:",
            Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL: "",
        }[login_status]

        if header_emoji and should_slack:
            login_status_label = str(Retrieval.RetrievalStatus(login_status).label)
            blocks = [
                {
                    "type": "header",
                    "text": {"type": "plain_text", "text": f"{header_emoji} {login_status_label}", "emoji": True},
                },
                {
                    "type": "section",
                    "text": {"type": "mrkdwn", "text": f"*Object ID:*\n<{self.admin_panel_link}|{self.pk}>"},
                    "accessory": {
                        "type": "button",
                        "text": {"type": "plain_text", "text": "Retry"},
                        "value": f"{self.pk}",
                        "action_id": "rerun_scrape",
                    },
                },
                self.retrieval_meta_data_slack_block,
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Doc Retrieved / Skipped:*\n{self.number_documents_retrieved} / {self.number_documents_skipped}",  # noqa: E501
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Time Taken:*\n{
                                (
                                    self.updated_at
                                    - (self.start_time if self.start_time is not None else self.created_at)
                                ).seconds
                            }s",
                        },
                    ],
                },
            ]

            message = f"{self.merged_portal_credential.portal} {Retrieval.RetrievalStatus(login_status).label} Retrieval <{self.admin_panel_link}|{self.pk}> for {self.organization}"  # noqa: E501
            send_message_to_slack(channel="#portals", message=message, blocks=blocks)

        self.retrieval_status = login_status
        self.save()

    def update_user_login_status(self, user_login_status: UserLoginStatus) -> None:
        self.refresh_from_db()
        self.user_login_status = user_login_status
        self.save()

    def update_token_otp(self, token_otp: str) -> None:
        self.token_otp = token_otp
        self.retrieval_status = Retrieval.RetrievalStatus.PENDING_LOGIN_OTP
        self.save()

    def update_start_time(self) -> None:
        self.start_time = timezone.now()
        self.save()

    # this must be sync
    def wait_for_token_otp(self, timeout: int = 300) -> str:
        start_time = time.time()
        while time.time() - start_time < timeout:
            self.refresh_from_db()
            if self.retrieval_status == Retrieval.RetrievalStatus.PENDING_CANCEL:
                self.retrieval_status = Retrieval.RetrievalStatus.CANCELED
                self.save()
                raise CanceledRetrievalError
            token = self.token_otp
            if token:
                return token
            time.sleep(0.1)
        self.update_login_status(Retrieval.RetrievalStatus.FAILED_LOGIN_OTP)
        raise TimeoutForOTPTokenError

    def get_presigned_video_url(self) -> str | None:
        if not self.exists_in_s3 or not self.s3_bucket or not self.s3_key:
            return None

        folder_path = str(Path(self.s3_key) / "video")
        s3 = boto3.client("s3")
        try:
            for obj in s3.list_objects_v2(Bucket=self.s3_bucket, Prefix=folder_path).get("Contents", []):
                key = Path(obj.get("Key", ""))
                if key.suffix == ".webm":
                    return s3.generate_presigned_url(
                        ClientMethod="get_object",
                        Params={
                            "Bucket": self.s3_bucket,
                            "Key": str(key),
                            "ResponseContentType": "application/octet-stream",
                            "ResponseContentDisposition": f'filename="{urllib.parse.quote(key.name)}"',
                        },
                        ExpiresIn=3600 * 24,  # 24 hour in seconds, increase if needed
                    )
        except Exception:
            logger.exception("Could not make presigned url", bucket=self.s3_bucket, folder=self.s3_key)
        return None

    def get_cloud_watch_url(self) -> str | None:
        start = (self.created_at - timezone.timedelta(hours=1)).strftime("%Y-%m-%dT%H")
        end = (self.updated_at + timezone.timedelta(hours=3)).strftime("%Y-%m-%dT%H")
        return (
            "https://180294215839.us-east-1.console.aws.amazon.com/cloudwatch/home"
            "?region=us-east-1#logsV2:logs-insights"
            f"$3FqueryDetail$3D~(end~'{end}*3a00*3a00.000Z~start~'{start}*3a00*3a00.000Z~timeType~'ABSOLUTE~tz~'UTC"
            "~editorString~'fields*20*40"
            "timestamp*2c*20retrieval_pk*2c*20logger*2c*20manager*2c*20level*2c*20event*2c*20action*2c*20"
            "tag*2c*20retrieval_status*2c*20skipped_documents*2c*20seen_documents*2c*20"
            "created_documents*2c*20processed_documents*0a*7c*20"
            f"filter*20retrieval_pk*3d*22{self.pk}*22*0a*7c*20"
            "sort*20*40timestamp*20asc*0a*7c*20"
            "limit*2010000~queryId~'90c3c4ce-d76f-41a1-8cc4-76434bf9f5cd~"
            "source~(~'SiteServiceStack-SiteLogGroupCAF00FD1-089b3gbkQJBW~"
            "'CeleryServiceStack-CeleryLogGroup0C29692E-9UPbQUAwrr0Y)"
            "~lang~'CWLI)"
        )
