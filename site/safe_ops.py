#!/usr/bin/env -S uv run
import os
from collections.abc import Iterator
from contextlib import contextmanager
from pathlib import Path

import boto3
import click
import django
import psycopg
import structlog
from django.conf import settings
from django.core.management import call_command
from django.utils import timezone
from psycopg import sql

logger = structlog.get_logger(__name__)

django.setup()


@contextmanager
def get_cursor(database_entry: str = "default") -> Iterator[psycopg.cursor.Cursor]:
    db = settings.DATABASES[database_entry]
    conn, cursor = None, None
    try:
        conn = psycopg.connect(
            dbname=db["NAME"],
            user=db["USER"],
            password=db["PASSWORD"],
            host=db["HOST"],
            port=db["PORT"],
        )
        conn.autocommit = True
        cursor = conn.cursor()
        yield cursor
    except Exception:
        logger.exception("Database connection error")
        raise
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def terminate_connections(database_entry: str = "default") -> None:
    db = settings.DATABASES[database_entry]
    dbname = db["NAME"]
    with get_cursor(database_entry) as cursor:
        # Terminate all connections to the specified database
        terminate_query = sql.SQL("""
            SELECT pg_terminate_backend(pg_stat_activity.pid)
            FROM pg_stat_activity
            WHERE pg_stat_activity.datname = %s
                AND pid <> pg_backend_pid();
            """)
        cursor.execute(terminate_query, (dbname,))
    click.echo(f"All connections to the database '{dbname}' have been terminated.")


def dump_database(
    postgres_backup_folder: Path = Path("postgres_dumps"),
    postgres_dump_local_folder: Path = Path("site"),
    postgres_dump_latest: str = "db-latest.json",
) -> None:
    file_name = f"db-{timezone.now().strftime('%Y-%m-%d_%H-%M-%S')}.json"
    local_file_name = str(postgres_dump_local_folder / file_name)
    call_command("dumpdata", output=local_file_name)
    boto3.client("s3").upload_file(
        local_file_name, settings.AWS_STORAGE_BUCKET_NAME, str(postgres_backup_folder / file_name)
    )
    boto3.client("s3").copy_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        CopySource={"Bucket": settings.AWS_STORAGE_BUCKET_NAME, "Key": str(postgres_backup_folder / file_name)},
        Key=str(postgres_backup_folder / postgres_dump_latest),
    )
    Path(local_file_name).unlink()
    click.echo("Database dumped successfully.")


@click.command()
@click.option("--migrate", is_flag=True, default=False, help="Run migrations")
@click.option("--create-superuser", is_flag=True, default=False, help="Create a superuser")
@click.option("--run-in-prod", is_flag=True, default=False, help="Explicitly pass to run in production")
@click.option("--collect-static", is_flag=True, default=False, help="Collet static files")
@click.option("--terminate-db-sessions", is_flag=True, default=False, help="Terminates existing DB sessions")
@click.option("--interactive", is_flag=True, default=False, help="Run in interactive mode")
@click.option("--dump", is_flag=True, default=False, help="Dump the database")
@click.option("--dry-run", is_flag=True, default=False, help="Truncate all tables in the database")
def manage(  # noqa: C901, PLR0912, PLR0913, PLR0915
    *,
    migrate: bool,
    create_superuser: bool,
    run_in_prod: bool,
    collect_static: bool,
    terminate_db_sessions: bool,
    interactive: bool,
    dump: bool,
    dry_run: bool,
) -> None:
    """Script to manage database operations and superuser creation."""
    click.echo("Running management script...")
    if not run_in_prod and not settings.DEBUG:
        click.echo(
            "This script should only be run in development mode. Use --run-in-prod to run in production. Doing dry run"
        )
        dry_run = True

    click.echo("Running script.")
    if terminate_db_sessions and not dry_run:
        terminate_connections()
    elif terminate_db_sessions and dry_run:
        click.echo("Would have terminated all database sessions.")
    else:
        click.echo("Database sessions not terminated. Use --terminate-db-sessions flag to terminate all sessions.")

    if migrate and not dry_run:
        call_command("makemigrations", interactive=interactive)
        click.echo("Migrations created.")

        call_command("migrate", interactive=interactive)
        click.echo("Migrations applied.")

        call_command("createcachetable")
        click.echo("Cache table created.")
    elif migrate and dry_run:
        click.echo("Would have run 'makemigrations' command.")
        click.echo("Would have run 'migrate' command.")
        click.echo("Would have run 'createcachetable' command.")
    else:
        click.echo("Migrations not created or applied. Use --migrate flag to run migrations.")

    if dump and not dry_run:
        dump_database()
    elif dump and dry_run:
        click.echo("Would have dumped the database.")
    else:
        click.echo("Database not dumped. Use --dump flag to dump the database.")

    if collect_static and not dry_run:
        call_command("collectstatic", interactive=interactive)
        click.echo("Static files collected.")
    elif collect_static and dry_run:
        click.echo("Would have run 'collectstatic' command.")
    else:
        click.echo("Static files not collected. Use --collectstatic flag to collect static files.")

    if create_superuser and not dry_run:
        from webapp.models.user import BridgeUser, Organization, Role

        bridge_organization, _ = Organization.objects.update_or_create(name="Bridge")
        demo_org, _ = Organization.objects.update_or_create(name="Demo")
        manager_role, _ = Role.objects.update_or_create(name=Role.RoleName.MANAGER)
        viewer_role, _ = Role.objects.update_or_create(name=Role.RoleName.VIEWER)

        call_command(
            "createsuperuser",
            interactive=interactive,
            username="<EMAIL>",
            email="<EMAIL>",
            first_name="Bridge",
            last_name="Admin",
            organization=bridge_organization.pk,
            roles=[manager_role.pk],
            contact_number="4042178500",
        )
        BridgeUser.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            first_name="Tom",
            last_name="Jones",
            password=os.environ["DJANGO_SUPERUSER_PASSWORD"],
            organization=demo_org.pk,
            roles=[manager_role.pk],
            contact_number="+14042178501",
        )
        click.echo("Superuser created successfully.")
    elif create_superuser and dry_run:
        click.echo("Would have created a superuser and demo user.")
    else:
        click.echo("Superuser not created. Use --create-superuser flag to create a superuser.")


if __name__ == "__main__":
    manage()
