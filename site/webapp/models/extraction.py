import typing
from urllib.parse import urlparse

import structlog
from django.contrib.postgres.indexes import GinIndex
from django.contrib.postgres.search import Search<PERSON><PERSON><PERSON>ield
from django.db import models
from pgvector.django import HnswIndex, Vector<PERSON>ield

from webapp.models.core import AbstractBaseModel
from webapp.models.documents import RawDocument
from webapp.models.user import BridgeUser
from webapp.utils.json_schema import (
    build_schema_reference_map,
    check_dag_and_topological_sort,
    create_resolved_schema,
    json_paths_to_dict,
    rename_properties_by_id,
    unresolve_refs,
    update_json_schema_refs,
    validate_with_details,
)

logger = structlog.get_logger(__name__)


class ExtractionSchema(AbstractBaseModel):
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return []

    # TODO: deleting a schema does not delete other shit
    uri = models.CharField(max_length=4096, unique=True)
    column_name = models.CharField(max_length=2048)
    title = models.CharField(max_length=4096)
    json_type = models.CharField(max_length=256, default="object")
    major_version = models.IntegerField(default=1)
    minor_version = models.IntegerField(default=0)
    json_schema = models.JSONField(default=dict, blank=True)

    def __str__(self) -> str:
        return f"{self.uri}"

    @classmethod
    def update_or_create(cls, user: BridgeUser | None, json_schema: dict) -> list["ExtractionSchema"]:  # noqa: C901
        if user is None or not isinstance(user, BridgeUser):
            raise ValueError
        normalized = unresolve_refs(json_schema)
        normalized = rename_properties_by_id(normalized, id_key="$ref")
        res = []
        id2rs = {v["$id"]: v for v in normalized["definitions"].values()}
        old_schemas = ExtractionSchema.objects.active().order_by("uri").values_list("json_schema", flat=True).all()
        for v in old_schemas:
            if v["$id"] not in id2rs:
                id2rs[v["$id"]] = v
        ref_map = build_schema_reference_map(id2rs.values())
        li = check_dag_and_topological_sort(ref_map)
        for uri in li:
            latest_uri = cls.get_latest_schemas_uri(uri)
            if latest_uri is not None and latest_uri != uri:
                logger.error("Schema URI mismatch", expected=uri, actual=latest_uri)
                raise ValueError
        completed = set()
        while li:
            ref = li.pop(0)
            value = id2rs.get(ref)
            if "$id" not in value or not isinstance(value["$id"], str):
                logger.error(
                    "Invalid schema definition: missing or invalid $id",
                    json_schema=value,
                    completed=completed,
                    remaining=len(li),
                )
                raise ValueError
            schema, updated = cls._update_or_create_helper(user=user, json_schema=value)
            res.append((schema, updated))
            completed.add(ref)
            completed.add(schema.uri)
            if updated:
                logger.info("Update", old_ref=ref, new_ref=schema.uri)
                old_li = li.copy()
                new_definitions = update_json_schema_refs(list(id2rs.values()), ref, schema.uri)
                logger.info("Updated schema refs", old_id2rs=id2rs.keys())
                id2rs = {v["$id"]: v for v in new_definitions}
                ref_map = build_schema_reference_map(new_definitions)
                li = check_dag_and_topological_sort(ref_map)
                li = [v for v in li if v not in completed]
                logger.info(
                    "Updated definitions after schema update",
                    old_li=old_li,
                    new_li=li,
                    remaining=len(li),
                    completed=completed,
                )
        for schema, updated in res:
            if updated:
                logger.info(
                    "Updated schema",
                    uri=schema.uri,
                    column_name=schema.column_name,
                    major_version=schema.major_version,
                    minor_version=schema.minor_version,
                )
            else:
                logger.info("Schema unchanged", uri=schema.uri, column_name=schema.column_name)
        return res

    @classmethod
    def _update_or_create_helper(cls, user: BridgeUser, json_schema: dict) -> tuple["ExtractionSchema", bool]:
        # TODO: do we always want to strip definitions before inserting?
        json_schema.pop("definitions", None)
        uri = json_schema.get("$id")
        if uri is None or not isinstance(uri, str):
            msg = "Invalid schema: missing or invalid $id"
            logger.error(msg, json_schema=json_schema)
            raise ValueError(msg)
        parse_uri = urlparse(uri)
        if len(parse_uri.path.split("/")) < 3:  # noqa: PLR2004
            msg = "Invalid schema URI: must have at least 3 segments"
            logger.error(msg, uri=uri, json_schema=json_schema)
            raise ValueError(msg)
        column_name = parse_uri.path.split("/")[-1]
        prev_obj = ExtractionSchema.get_latest_version_from_column_name(column_name)
        if prev_obj is not None:
            if prev_obj.is_same_json_schema(json_schema):
                logger.info("Schema unchanged, skipping update", uri=uri, column_name=column_name)
                return prev_obj, False
            major_version = prev_obj.major_version
            minor_version = prev_obj.minor_version + 1
            prev_obj.soft_delete()
        else:
            major_version = 1
            minor_version = 0
            if ExtractionSchema.objects.filter(column_name=column_name).exists():
                msg = "ExtractionSchema with this column name already exists"
                logger.error(msg, column_name=column_name, uri=uri)
                raise ValueError(msg)
        uri = f"bridge://v{major_version}.{minor_version}{parse_uri.path}"
        json_schema["$id"] = uri
        json_schema["title"] = parse_uri.path
        return ExtractionSchema.objects.create(
            user=user,
            uri=uri,
            column_name=column_name,
            json_type=json_schema.get("type"),
            title=json_schema.get("title"),
            major_version=major_version,
            minor_version=minor_version,
            json_schema=json_schema,
        ), prev_obj is not None

    def get_latest_version(self) -> "ExtractionSchema|None":
        return ExtractionSchema.get_latest_version_from_column_name(self.column_name)

    @classmethod
    def get_latest_version_from_column_name(cls, column_name: str) -> "ExtractionSchema|None":
        latest = (
            ExtractionSchema.objects.active()
            .filter(column_name=column_name)
            .order_by("-major_version", "-minor_version")
        )
        if latest.count() == 0:
            return None
        if latest.count() > 1:
            msg = "Multiple latest versions found"
            logger.error(msg, column_name=column_name)
            raise ValueError(msg)
        if latest is None:
            raise ValueError
        return latest.first()

    def is_same(self, other: "ExtractionSchema|None") -> bool:
        if other is None:
            return False
        return self.is_same_json_schema(other.json_schema) and self.column_name == other.column_name

    def is_same_json_schema(self, json_schema: dict) -> bool:
        prev_copy = json_schema.copy()
        new_copy = self.json_schema.copy()
        prev_copy.pop("title", None)
        new_copy.pop("title", None)
        prev_copy.pop("$id", None)
        new_copy.pop("$id", None)
        return prev_copy == new_copy

    @classmethod
    def get_resolved_schemas(cls) -> dict[str, dict]:
        # TODO: just sort by path.
        li_schemas = ExtractionSchema.objects.active().order_by("uri").values_list("json_schema", flat=True).all()
        resolved_schemas = [create_resolved_schema(li_schemas, li_schema["$id"]) for li_schema in li_schemas]
        return {rs["$id"]: rs for rs in resolved_schemas}

    @classmethod
    def get_latest_schemas_uri(cls, uri: str) -> str | None:
        column_name = urlparse(uri).path.split("/")[-1]
        schema = ExtractionSchema.get_latest_version_from_column_name(column_name)
        if schema is None:
            return None
        return schema.uri


class RawExtraction(AbstractBaseModel):
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return []

    raw_document = models.ForeignKey("webapp.RawDocument", on_delete=models.CASCADE, related_name="raw_extractions")
    processed_document = models.ForeignKey(
        "webapp.ProcessedDocument", on_delete=models.CASCADE, related_name="raw_extractions", blank=True, null=True
    )
    doc_chunks = models.ManyToManyField(
        "webapp.DocChunk",
        related_name="raw_extractions",
        blank=True,
    )
    is_ground_truth = models.BooleanField(default=False, help_text="Is this extraction a ground truth for training?")
    # extraction_source_name
    extraction_source_name = models.CharField(max_length=256, blank=True, null=True)  # noqa: DJ001
    original_extraction_schema = models.ForeignKey(
        ExtractionSchema, on_delete=models.CASCADE, related_name="original_extraction_schemas"
    )
    last_valid_extraction_schema = models.ForeignKey(
        ExtractionSchema, on_delete=models.CASCADE, related_name="last_valid_extraction_schemas"
    )
    current_extraction_schema = models.ForeignKey(
        ExtractionSchema, on_delete=models.CASCADE, related_name="current_extraction_schemas"
    )
    is_current_extraction_schema_validated = models.BooleanField(default=False)

    original_target_schema = models.ForeignKey(
        ExtractionSchema, on_delete=models.CASCADE, related_name="original_target_raw_extractions"
    )
    last_valid_target_schema = models.ForeignKey(
        ExtractionSchema,
        on_delete=models.SET_NULL,
        related_name="last_valid_target_raw_extractions",
        null=True,
        blank=True,
    )
    current_target_schema = models.ForeignKey(
        ExtractionSchema, on_delete=models.CASCADE, related_name="current_target_raw_extractions"
    )
    is_current_target_schema_validated = models.BooleanField(default=False)

    target_json_path = models.TextField(blank=True, null=True)  # noqa: DJ001
    data = models.JSONField(default=dict, blank=True)

    def __str__(self) -> str:
        return f"{self.data} - {self.current_extraction_schema} ({self.raw_document})"

    @classmethod
    def create(  # noqa: PLR0913
        cls,
        raw_doc: RawDocument,
        uri: str,
        target_uri: str,
        target_json_path: str,
        extraction_source: str,
        chunks: list["DocChunk"],
        data: typing.Any,  # noqa: ANN401
        *,
        is_ground_truth: bool,
    ) -> "RawExtraction":
        user = raw_doc.created_by
        if user is None or not isinstance(user, BridgeUser):
            raise ValueError

        latest_uri = ExtractionSchema.get_latest_schemas_uri(uri)
        if latest_uri is None:
            raise ValueError
        extraction_schema = ExtractionSchema.objects.active().filter(uri=latest_uri).first()
        if extraction_schema is None:
            raise ValueError

        target_latest_uri = ExtractionSchema.get_latest_schemas_uri(target_uri)
        if target_latest_uri is None:
            raise ValueError
        target_extraction_schema = ExtractionSchema.objects.active().filter(uri=target_latest_uri).first()
        if target_extraction_schema is None:
            raise ValueError

        resolved_schemas = ExtractionSchema.get_resolved_schemas()
        validate_ok, err = validate_with_details(data, resolved_schemas[latest_uri])
        if not validate_ok:
            logger.error(
                "Schema validation failed", raw_document_id=raw_doc.id, uri=uri, latest_uri=latest_uri, errors=err
            )
            raise ValueError

        target_validate_ok, err = validate_with_details(data, resolved_schemas[target_latest_uri])

        # TODO: add JSON paths
        # TODO: don't just delete old data.
        raw_doc.raw_extractions.filter(
            current_extraction_schema__column_name=extraction_schema.column_name,
            current_target_schema__column_name=target_extraction_schema.column_name,
            target_json_path=target_json_path,
        ).delete()
        extract = RawExtraction.objects.create(
            user=user,
            raw_document=raw_doc,
            original_extraction_schema=extraction_schema,
            current_extraction_schema=extraction_schema,
            last_valid_extraction_schema=extraction_schema,
            original_target_schema=target_extraction_schema,
            current_target_schema=target_extraction_schema,
            last_valid_target_schema=target_extraction_schema if target_validate_ok else None,
            is_ground_truth=is_ground_truth,
            is_current_extraction_schema_validated=True,
            is_current_target_schema_validated=target_validate_ok,
            data=data,
            extraction_source_name=extraction_source,
            target_json_path=target_json_path,
        )
        extract.doc_chunks.add(*chunks)
        return extract

    def upgrade_schema(self) -> bool:
        latest_schema = self.current_extraction_schema.get_latest_version()
        if latest_schema is None:
            self.is_current_extraction_schema_validated = False
            self.save()
            return False
        validate_extraction_schema = self.validate_extraction_schema()
        if validate_extraction_schema:
            self.last_valid_extraction_schema = latest_schema
        self.is_current_extraction_schema_validated = validate_extraction_schema
        self.current_extraction_schema = latest_schema
        self.save()
        return validate_extraction_schema and self.upgrade_target_schema()

    def upgrade_target_schema(self) -> bool:
        latest_schema = self.current_target_schema.get_latest_version()
        if latest_schema is None:
            self.is_current_target_schema_validated = False
            self.save()
            return False
        self.current_target_schema = latest_schema
        validated = self.validate_target_schema()
        if validated:
            self.last_valid_target_schema = latest_schema
        self.is_current_target_schema_validated = validated
        self.save()
        return validated

    def validate_extraction_schema(self) -> bool:
        latest_uri = ExtractionSchema.get_latest_schemas_uri(self.current_extraction_schema.uri)
        if latest_uri is None:
            raise ValueError
        resolved_schemas = ExtractionSchema.get_resolved_schemas()
        validate_ok, err = validate_with_details(self.data, resolved_schemas[latest_uri])
        if not validate_ok:
            logger.error(
                "Schema validation failed",
                raw_document_id=self.raw_document.id,
                uri=self.current_extraction_schema.uri,
                latest_uri=latest_uri,
                errors=err,
            )
        return validate_ok

    def validate_target_schema(self) -> bool:
        raw_paths = [
            (raw_extraction.target_json_path, raw_extraction.data)
            for raw_extraction in self.raw_document.raw_extractions.exclude(target_json_path="$")
        ]
        backend_data = json_paths_to_dict(raw_paths)
        target_latest_uri = ExtractionSchema.get_latest_schemas_uri(self.current_target_schema.uri)
        if target_latest_uri is None:
            raise ValueError
        target_extraction_schema = ExtractionSchema.objects.active().filter(uri=target_latest_uri).first()
        if target_extraction_schema is None:
            raise ValueError

        resolved_schemas = ExtractionSchema.get_resolved_schemas()
        validate_ok, err = validate_with_details(backend_data, resolved_schemas[target_latest_uri])
        if not validate_ok:
            logger.error(
                "Schema validation failed",
                raw_document_id=self.raw_document.id,
                uri=self.current_extraction_schema.uri,
                latest_uri=target_latest_uri,
                errors=err,
            )
        return validate_ok


class DocChunk(AbstractBaseModel):
    @classmethod
    def get_line_item_paths(cls) -> list[str]:
        return []

    raw_document = models.ForeignKey(RawDocument, on_delete=models.CASCADE, related_name="doc_chunks")
    content = models.TextField(blank=True, null=True)  # noqa: DJ001
    content_format = models.CharField(max_length=256, default="text")
    chunker_name = models.CharField(max_length=256)
    parser_name = models.CharField(max_length=256)
    ingestion_id = models.UUIDField()

    page_number = models.IntegerField(default=0)
    bbox_ymin = models.FloatField(default=0.0)
    bbox_xmin = models.FloatField(default=0.0)
    bbox_ymax = models.FloatField(default=0.0)
    bbox_xmax = models.FloatField(default=0.0)
    chunk_idx = models.IntegerField(default=-1)
    chunk_length = models.IntegerField(default=0)
    has_bounding_box = models.BooleanField(default=False)
    embedding_titan_text_1024 = VectorField(dimensions=1024)  # Specify the dimension of your vectors
    search_vector = SearchVectorField(null=True)

    class Meta:
        indexes: typing.ClassVar = [
            HnswIndex(
                name="embedding_tt1024_cosine_idx",
                fields=["embedding_titan_text_1024"],
                m=16,
                ef_construction=64,
                opclasses=["vector_cosine_ops"],
            ),
            GinIndex(fields=["search_vector"]),
        ]
