{% load static customtags widget_tweaks %}
<div id="modal-container-{{ merged_portal_credential.pk }}">
    <!-- Modal content -->
    <div class="relative">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-5 rounded-t dark:border-gray-600">
            <div class="text-[#616863] text-sm font-normal font-inter leading-[21px]">
                Portal: <span class="pl-2">{{ merged_portal_credential.portal.name }}</span>
            </div>
            <button _="on click trigger closeModal"
                    type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                <img src="{% static 'assets/images/close.svg' %}"
                     width="16"
                     height="19"
                     alt="closeButton">
                <span class="sr-only">Close modal</span>
            </button>
        </div>
        <!-- Modal body -->
        <div class="px-5 pb-4">
            <div class="text-[#343d36] text-lg font-semibold font-inter leading-[27px] pb-4">
                Setup Local SMS Device Automation for the Portal:
            </div>
            <div class="text-[#343d36] text-lg font-semibold font-inter leading-[27px] pb-4 text-center flex justify-center items-center gap-2">
                <span>{{ merged_portal_credential.portal.name }}</span>
                <button onclick="copyToClipboard('{{ merged_portal_credential.portal.name }}')"
                        class="p-2 rounded-md hover:bg-gray-100"
                        title="Copy to clipboard">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         class="h-5 w-5"
                         fill="none"
                         viewBox="0 0 24 24"
                         stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                    </svg>
                </button>
            </div>
            <div class="mt-2 text-sm text-gray-600 italic">
                <a href="https://docs.google.com/document/d/1oNWvi0I92x6LkXh_iRP6oRbJOJ8SXqfgmpdgRRFY6dQ/edit?usp=sharing"
                   target="_blank"
                   rel="noopener noreferrer"
                   aria-label="View SMS Multi-Factor Authentication Setup Instructions">
                    Click <span class="text-blue-500 underline">here</span> for detailed instructions
                </a>
            </div>
        </div>
        <!-- Modal footer -->
        <div class="flex items-center p-4 border-gray-200 rounded-b dark:border-gray-600">
            <button hx-get="{% url 'multi_factor_authentication_end_screen' %}{% urlparams merged_portal_credential_pk=merged_portal_credential.pk %}"
                    hx-target="#modal-container-{{ merged_portal_credential.pk }}"
                    hx-swap="outerHTML"
                    class="Buttons self-stretch h-11 w-full px-4 py-2 bg-[#924f34] rounded-lg shadow-inner justify-center items-center gap-1.5 inline-flex">
                <div class="text-white text-base font-semibold font-inter leading-normal">Continue</div>
            </button>
        </div>
    </div>
</div>
<script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text);
    }
</script>
