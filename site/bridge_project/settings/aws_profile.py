import json
import os

import structlog

from .dev import *  # noqa: F403
from .utils import generate_aws_configuration, get_secret

logger = structlog.get_logger(__name__)

logger.info("OVERRIDING FOR PROFILE LOCALLY SETTINGS", aws_profile=os.environ["AWS_PROFILE"])
aws_config, config = generate_aws_configuration(os.environ["AWS_PROFILE"])

if config.ConfigStack is None:
    logger.error("ConfigStack not found in config")
    raise ValueError

if config.ConfigStack.DjangoSecretArn is None:
    logger.error("DjangoSecretArn not found in config")
    raise ValueError

if config.StorageStack is None:
    logger.error("StorageStack not found in config")
    raise ValueError

if config.ConfigStack.SlackSecretArn is None:
    logger.error("SlackSecretArn not found in config")
    raise ValueError

# Secret Key
SECRET_KEY = json.loads(get_secret(config.ConfigStack.DjangoSecretArn)).get("password")

# Slack Secrets
SLACK_OAUTH_KEY = json.loads(get_secret(config.ConfigStack.SlackSecretArn)).get("OAUTH_TOKEN")
SLACK_SIGNING_SECRET = json.loads(get_secret(config.ConfigStack.SlackSecretArn)).get("SIGNING_SECRET")

# set up domain for email forwarding
DOMAIN_NAME = config.ConfigStack.DomainName

# AWS Bucket
AWS_STORAGE_BUCKET_NAME = config.ConfigStack.UserBucketName
SFTP_BUCKET_NAME = config.ConfigStack.SftpBucketName

DEFAULT_FROM_EMAIL = config.StorageStack.EmailSendAddress

is_prod_secret = os.getenv("USE_PROD_OAUTH_SECRET", None) is not None

# MSFT OAUTH for email plug-in integration, use development secret.
if is_prod_secret and config.ConfigStack.MsftEmailOauthSecretArn is not None:
    OAUTH_SECRET = json.loads(get_secret(config.ConfigStack.MsftEmailOauthSecretArn))
else:
    OAUTH_SECRET = json.loads(get_secret("Development-Application-MsftEmailOauth"))
