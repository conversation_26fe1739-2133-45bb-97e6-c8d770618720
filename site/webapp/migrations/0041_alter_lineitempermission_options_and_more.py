# Generated by Django 5.2.1 on 2025-05-23 18:02

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0040_alter_rawdocument_email_intake'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='lineitempermission',
            options={'default_manager_name': 'objects'},
        ),
        migrations.AlterUniqueTogether(
            name='lineitempermission',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='lineitempermission',
            name='batch_id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, help_text='Identifier for a batch of permission changes updated at the same time.'),
        ),
    ]
