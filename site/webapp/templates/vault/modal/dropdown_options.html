{% load static %}
<div id="dropdowns-container" class="flex flex-col gap-4">
    <div class="flex items-center gap-2">
        <label for="client"
               class="w-32 text-left text-sm text-[#4b554d] font-medium">Client:</label>
        <select name="client"
                id="client"
                class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                required
                hx-get="{% url 'vault_filtered_options' %}"
                hx-target="#dropdowns-container"
                hx-swap="outerHTML"
                hx-trigger="change"
                hx-include="[name='client'],[name='entity'],[name='investment']">
            <option value="">Select Client</option>
            {% for client in clients %}
                <option value="{{ client.id }}"
                        {% if selected_client_id == client.id %}selected{% endif %}>{{ client.legal_name }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="flex items-center gap-2">
        <label for="entity"
               class="w-32 text-left text-sm text-[#4b554d] font-medium">Investing Entity:</label>
        <select name="entity"
                id="entity"
                class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                required
                hx-get="{% url 'vault_filtered_options' %}"
                hx-target="#dropdowns-container"
                hx-trigger="change"
                hx-include="[name='client'],[name='entity'],[name='investment']">
            <option value="">Select Investing Entity</option>
            {% for investing_entity in investing_entities %}
                <option value="{{ investing_entity.id }}"
                        {% if selected_entity_id == investing_entity.id %}selected{% endif %}>
                    {{ investing_entity.legal_name }}
                </option>
            {% endfor %}
        </select>
    </div>
    <div class="flex items-center gap-2">
        <label for="investment"
               class="w-32 text-left text-sm text-[#4b554d] font-medium">Investment:</label>
        <select name="investment"
                id="investment"
                class="w-full px-4 py-2.5 bg-[#f7f7f5] border border-[#eceee8] rounded-lg text-sm text-[#4b554d]"
                required
                hx-get="{% url 'vault_filtered_options' %}"
                hx-target="#dropdowns-container"
                hx-trigger="change"
                hx-include="[name='client'],[name='entity'],[name='investment']">
            <option value="">Select Investment</option>
            {% for investment in investments %}
                <option value="{{ investment.id }}"
                        {% if selected_investment_id == investment.id %}selected{% endif %}>
                    {{ investment.legal_name }}
                </option>
            {% endfor %}
        </select>
    </div>
</div>
