from enum import Enum

import structlog
from phonenumbers import NumberParseException

from webapp.models import BridgeUser
from webapp.models.portal import MergedPortalCredential, MultiFactorAuthentication, Portal, SMSMessage
from webapp.models.retrieval import Retrieval

logger = structlog.get_logger(__name__)


class InvalidMessageError(Exception):
    """
    Exception raised when a message is invalid.
    """


class SMSErrorMessages(Enum):
    """
    Common error messages for SMS validation and processing.
    """

    INVALID_CODE = "Message does not contain a code"
    INVALID_PHONE = "Phone number is not valid"
    INVALID_FORMAT = "Invalid message format"
    RATE_LIMIT_EXCEEDED = "Rate limit exceeded for this number"
    DELIVERY_FAILED = "Message delivery failed"
    INVALID_LENGTH = "Message exceeds maximum length"
    BLOCKED_NUMBER = "Number is blocked or unavailable"
    MISSING_REQUIRED = "Required message components missing"
    SYSTEM_ERROR = "System error processing message"


class SMSRepository:
    """
    Abstraction for SMS message receiving and querying.

    When used as a class, this repository can be used to receive and process SMS messages.

    When used as an instance, this repository can be used to query and process SMS messages for a specific user.
    """

    user_phone_number: str | None = None
    user: BridgeUser | None = None

    def __init__(self, user: BridgeUser = None, user_phone_number: str | None = None) -> None:
        """
        Initialize the SMS repository.
        user_phone_number is deprecated, use user instead.
        """
        if user_phone_number:
            if not self._validate_phone_number(user_phone_number):
                raise InvalidMessageError(SMSErrorMessages.INVALID_PHONE.value)
            self.user_phone_number = self.normalize_phone_number(user_phone_number)
        else:
            self.user_phone_number = None

        if user:
            self.user = user

    @classmethod
    def _validate_phone_number(cls, phone_number: str) -> bool:
        """
        Validate the phone number.
        """
        from phonenumber_field.phonenumber import PhoneNumber

        try:
            phone_number = PhoneNumber.from_string(phone_number)
            return phone_number.is_valid()
        except NumberParseException:
            return False
        except ValueError:
            return False
        except TypeError:
            return False

    @classmethod
    def _validate_email(cls, email: str | None) -> bool:
        """
        Validate the email.
        """
        if email is None:
            return False
        return BridgeUser.objects.filter(email=email.lower()).exists()

    @classmethod
    def _divine_portal(cls, portal_name: str | None, user: BridgeUser | None, body: str | None) -> Portal | None:
        """
        Divine the portal from the portal name.

        Performs a soft match on portal name, handling differences in case or simplified versions.
        """
        if portal_name and user:
            logger.info("Identifying portal by name for SMS Integration", portal_name=portal_name, user=user.email)
            return Portal.get_portal_by_name(portal_name, user.organization)
        if body and user:
            logger.info("Identifying portal by body for SMS Integration", body=body, user=user.email)
            extracted_portal_name = cls.extract_portal_name_from_message(body)
            if extracted_portal_name:
                return Portal.get_portal_by_name(extracted_portal_name, user.organization)
        if user and user.organization:
            logger.info(
                "Identifying portal by organization for SMS Integration",
                portal_name=portal_name,
                organization=user.organization,
            )
            portals = Portal.objects.filter(organization=user.organization)
            if portals.count() == 1:
                return portals.first()
            logger.warning(
                "Multiple portals found with name or organization for SMS Integration",
                portal_name=portal_name,
                user=user.email,
                organization=user.organization,
                number_of_portals=len(portals),
            )
        logger.warning(
            "No portal found with name or organization for SMS Integration",
            portal_name=portal_name,
            user=user.email if user else None,
            organization=user.organization if user else None,
        )
        return None

    @classmethod
    def normalize_phone_number(cls, phone_number: str) -> str:
        """
        Normalize the phone number.
        """
        from phonenumber_field.phonenumber import PhoneNumber

        try:
            phone_number = PhoneNumber.from_string(phone_number)
        except NumberParseException:
            logger.warning("Phone number %s is not valid", phone_number)
            return phone_number
        except ValueError:
            logger.warning("Phone number %s is not valid", phone_number)
            return phone_number
        else:
            return phone_number.as_e164

    @classmethod
    def _divine_user(cls, email: str | None, user_phone_number: str | None) -> BridgeUser | None:
        """
        Divine the user from the email or phone number.
        """
        logger.info(
            "Divining user for SMS Integration",
            email=email,
            user_phone_number=user_phone_number,
            ve=cls._validate_email(email),
        )

        if email and cls._validate_email(email):
            logger.info("Identifying user by email for SMS Integration", email=email)
            return BridgeUser.objects.get(email=email.lower())

        mfa = (
            MultiFactorAuthentication.objects.active()
            .filter(phone_number=user_phone_number, merged_portal_credential__isnull=False)
            .first()
        )
        if (
            user_phone_number
            and cls._validate_phone_number(user_phone_number)
            and mfa is not None
            and mfa.merged_portal_credential is not None
            and mfa.merged_portal_credential.created_by is not None
        ):
            logger.info("Identifying user by phone number for SMS Integration", user_phone_number=user_phone_number)
            return mfa.merged_portal_credential.created_by

        logger.warning(
            "No user found with email or phone number for SMS Integration",
            email=email,
            user_phone_number=user_phone_number,
        )
        return None

    @classmethod
    def receive_message_as_webhook(
        cls, body: str, user_phone_number: str, portal_phone_number: str, email: str, portal_name: str
    ) -> SMSMessage:
        """
        Receive an SMS message as a webhook and save it to the database.
        """
        logger.info(
            "Received Forwarded SMS from Portal",
            user_phone_number=user_phone_number,
            portal_phone_number=portal_phone_number,
            portal_name=portal_name,
            email=email,
            body=body,
        )

        user = cls._divine_user(email, user_phone_number)
        if not user:
            logger.warning("Bridge user not found with this email", email=email)
            raise InvalidMessageError(SMSErrorMessages.INVALID_PHONE.value)

        portal = cls._divine_portal(portal_name, user, body)
        if not portal:
            logger.warning("Portal not found", user=user.email, portal_name=portal_name)

        raw_payload = {
            "user_phone_number": user_phone_number,
            "portal_phone_number": portal_phone_number,
            "email": email,
            "portal_name": portal_name,
            "body": body,
        }

        message = SMSMessage.objects.create(
            user=user,
            user_phone_number=user_phone_number,
            portal_phone_number=portal_phone_number,
            body=body,
            portal=portal,
            raw_payload=raw_payload,
        )
        message.save()
        cls._notify_otp_received(message)
        return message

    @classmethod
    def _notify_otp_received(cls, message: SMSMessage) -> None:
        """
        Notify open retrievals that an OTP has been received.
        """
        code = message.extract_code_from_message()
        user = message.created_by
        portal = message.portal

        if not portal:
            # TODO: this is unsustainable, but handles a lack of match, could we grab only in progress retrievals?
            merged_portal_credentials = MergedPortalCredential.objects.for_user(user)
            logger.info(
                "SMS Integration: Found all user's line items without portal",
                user=user.email,
                number_of_mpcs=len(merged_portal_credentials),
            )
        else:
            merged_portal_credentials = MergedPortalCredential.objects.for_user(user)
            if portal:
                merged_portal_credentials = merged_portal_credentials.filter(portal=portal)
            logger.info(
                "SMS Integration: Found user's line items for a portal",
                portal=portal.name,
                user=user.email,
                number_of_mpcs=len(merged_portal_credentials),
            )

        logger.info(
            "Attempting to notify Retrievals of OTP Receipt",
            user=user.email,
            message_id=message.id,
            number_of_mpcs=len(merged_portal_credentials),
            code=code,
        )

        possible_retrievals = []
        for merged_portal_credential in merged_portal_credentials:
            latest_retrieval = merged_portal_credential.last_retrieval
            if (
                latest_retrieval
                and latest_retrieval.retrieval_status
                in [
                    Retrieval.RetrievalStatus.PENDING_LOGIN,
                    Retrieval.RetrievalStatus.BLOCKED_LOGIN_OTP,
                ]
                and latest_retrieval.is_sms_mfa
            ):
                logger.info(
                    "Updated retrieval with SMS Integration OTP",
                    merged_portal_credential_id=merged_portal_credential.id,
                    retrieval_id=latest_retrieval.id,
                    retrieval_status=latest_retrieval.retrieval_status,
                )
                possible_retrievals.append(latest_retrieval)
            else:
                logger.warning(
                    "Retrieval is not in a valid state to receive an OTP or is not an SMS mfa",
                    merged_portal_credential_id=merged_portal_credential.id,
                    retrieval_id=latest_retrieval.id,
                    retrieval_status=latest_retrieval.retrieval_status if latest_retrieval else None,
                    is_sms_mfa=latest_retrieval.is_sms_mfa if latest_retrieval else None,
                )
        if len(possible_retrievals) == 0:
            logger.error(
                "No valid retrievals found for OTP notification",
                user=user.email,
                message_id=message.id,
                code=code,
            )
        elif len(possible_retrievals) == 1:
            retrieval = possible_retrievals[0]
            retrieval.update_token_otp(code)
            logger.info(
                "Updated retrieval with OTP",
                user=user.email,
                message_id=message.id,
                code=code,
                retrieval_id=retrieval.id,
            )
        elif len(possible_retrievals) > 1:
            logger.error(
                "Multiple retrievals found for OTP notification, SMS lock should prevent this",
                user=user.email,
                message_id=message.id,
                code=code,
                number_of_retrievals=len(possible_retrievals),
            )

    def get_most_recent_messages(self, portal_filter: str | None = None, limit: int = 10) -> list[SMSMessage]:
        """
        Get the most recent messages for a user.
        """
        if self.user:
            queryset = SMSMessage.objects.filter(user=self.user)
        else:
            queryset = SMSMessage.objects.filter(user_phone_number=self.user_phone_number)
        if portal_filter:
            queryset = queryset.filter(portal__name__icontains=portal_filter)
        queryset = queryset.order_by("-created_at")[:limit]
        return list(queryset)

    def get_most_recent_code(self) -> str | None:
        """
        Get the most recent message for a user, if it is a valid code.
        """
        candidate = SMSMessage.objects.filter(user_phone_number=self.user_phone_number).order_by("-created_at").first()
        if candidate and candidate.is_within_time_window():
            code = candidate.extract_code_from_message()
            if code:
                return code
            logger.warning(
                "Most recent message is not a valid code",
                user_phone_number=self.user_phone_number,
                user=self.user.email if self.user else None,
                sms_message_id=candidate.id,
            )
            return None
        logger.warning(
            "Most recent message is expired.",
            user_phone_number=self.user_phone_number,
            user=self.user.email if self.user else None,
            sms_message_id=candidate.id,
        )
        return None

    @classmethod
    def extract_portal_name_from_message(cls, message_body: str) -> str | None:
        """
        Extract portal name from common SMS message formats.
        """
        lower_body = message_body.lower()

        known_portal_mappings = {
            "goldman sachs": "Goldman Sachs",
            "gs": "Goldman Sachs",
            "intralinks": "Intralinks",
            "twilio": "Twilio",
            "digital data exchange": "Digital Data Exchange",
        }

        # Known keyword-based extraction
        for keyword, portal in known_portal_mappings.items():
            if keyword in lower_body:
                return portal

        # Pattern-based extraction
        if " One Time PIN" in message_body:
            return message_body.split(" One Time PIN")[0].strip()

        if "Your " in message_body and " verification code" in message_body:
            return message_body.split("Your ")[1].split(" verification")[0].strip()

        if " login code:" in message_body:
            return message_body.split(" login")[0].strip()

        return None
