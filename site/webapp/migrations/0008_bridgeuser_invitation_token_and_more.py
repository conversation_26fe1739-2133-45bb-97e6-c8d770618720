# Generated by Django 5.1.4 on 2025-01-14 16:01

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('webapp', '0007_alter_bridgeuser_contact_number'),
    ]

    operations = [
        migrations.AddField(
            model_name='bridgeuser',
            name='invitation_token',
            field=models.UUIDField(blank=True, default=None, null=True),
        ),
        migrations.AlterField(
            model_name='bridgeuser',
            name='contact_number',
            field=models.CharField(blank=True, default='', max_length=15, validators=[django.core.validators.RegexValidator(message='Enter a valid phone number.', regex='^\\+?1?\\d{9,15}$')]),
        ),
    ]
