from allauth.account.models import EmailAddress
from django.http import HttpRe<PERSON>, HttpResponse, HttpResponseNotAllowed
from django.shortcuts import redirect
from django.template.response import TemplateResponse
from django.utils.timezone import now

from webapp.decorators import unauthenticated_required
from webapp.forms import RegistrationForm
from webapp.models.user import BridgeUser


@unauthenticated_required(redirect_url="dashboard")
def registration(request: HttpRequest, token: str) -> HttpResponse:
    try:
        # Fetch the user associated with the token
        user = BridgeUser.objects.get(invitation_token=token)
        is_valid_token = True
    except BridgeUser.DoesNotExist:
        user = None
        is_valid_token = False

    # Allow only GET and POST methods
    if request.method not in ["GET", "POST"]:
        return HttpResponseNotAllowed(["GET", "POST"])

    if request.method == "POST" and is_valid_token:
        form = RegistrationForm(request.POST, instance=user)

        # Check if email is already registered
        email = form.data.get("email", "")
        if is_email_already_registered(email, exclude_user_id=user.id):
            form.add_error("email", "This email is already registered.")

        if form.is_valid():
            form.save()

            # Mark the email as verified
            EmailAddress.objects.update_or_create(
                user=user,
                email=user.email,
                defaults={"verified": True, "primary": True},
            )

            # Invalidate the token and activate the user
            user.invitation_token = None
            user.is_active = True
            user.tos_accepted_at = now().date()

            user.save()

            return redirect("account_login")  # Redirect on success

        # Render the template with form errors
        context = {
            "title": "User Registration",
            "description": "Complete your registration.",
            "is_valid_token": is_valid_token,
            "form": form,
        }
        template = "user/registration.html"
        return TemplateResponse(request, template, context)

    # Handle GET requests
    form = RegistrationForm(instance=user) if is_valid_token else None
    context = {
        "title": "User Registration",
        "description": (
            "Complete your registration."
            if is_valid_token
            else "This invitation link is invalid or has already been used."
        ),
        "is_valid_token": is_valid_token,
        "form": form,
    }
    template = "user/registration.html"
    return TemplateResponse(request, template, context)


def is_email_already_registered(email: str, exclude_user_id: str | None = None) -> bool:
    """
    Check if an email is already registered.

    Args:
        email (str): The email address to check.
        exclude_user_id (str, optional): A user ID to exclude from the check (useful for updates).

    Returns:
        bool: True if the email exists, False otherwise.

    """
    query = BridgeUser.objects.filter(email=email)
    if exclude_user_id:
        query = query.exclude(id=exclude_user_id)
    return query.exists()
