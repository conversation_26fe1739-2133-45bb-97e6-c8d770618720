#!/usr/bin/env npx ts-node
import { readFileSync } from "fs";
import { join } from "path";
import { SSMClient, GetParametersCommand } from "@aws-sdk/client-ssm";
import { STSClient, GetCallerIdentityCommand } from "@aws-sdk/client-sts";

interface SsmContextKey {
  account: string;
  region: string;
  parameterName: string;
  cachedValue: string;
}

async function main() {
  // Load cdk.context.json
  const contextPath = join(process.cwd(), "cdk.context.json");
  let contextData: Record<string, any>;
  try {
    contextData = JSON.parse(readFileSync(contextPath, "utf-8"));
  } catch (error) {
    console.error("Error reading cdk.context.json:", error);
    process.exit(1);
  }

  // Extract SSM context keys
  const ssmKeys = extractSsmContextKeys(contextData);

  if (ssmKeys.length === 0) {
    console.log("No SSM parameters found in cdk.context.json.");
    return;
  }

  // Group keys by account, then by region
  const accountRegionMap: Record<string, Record<string, SsmContextKey[]>> = {};
  for (const key of ssmKeys) {
    if (!accountRegionMap[key.account]) {
      accountRegionMap[key.account] = {};
    }
    if (!accountRegionMap[key.account][key.region]) {
      accountRegionMap[key.account][key.region] = [];
    }
    accountRegionMap[key.account][key.region].push(key);
  }

  // Determine the AWS account of the currently used credentials
  const stsClient = new STSClient({
    region: process.env.AWS_REGION || "us-east-1",
  });
  let currentAccount: string | undefined;
  try {
    const identity = await stsClient.send(new GetCallerIdentityCommand({}));
    currentAccount = identity.Account;
    console.log(`Current AWS Account: ${currentAccount}`);
  } catch (err) {
    console.error("Error determining current AWS Account via STS:", err);
    process.exit(1);
  }

  if (!currentAccount) {
    console.error("Unable to determine AWS account from STS.");
    process.exit(1);
  }

  let differencesFound = false;

  // Iterate over each account group
  for (const account of Object.keys(accountRegionMap)) {
    // If the current credentials do not match the account from the keys, skip
    if (account !== currentAccount) {
      console.log(
        `Skipping parameters for account ${account} as current credentials belong to ${currentAccount}.`,
      );
      continue;
    }

    // Process the regions for this account
    for (const region of Object.keys(accountRegionMap[account])) {
      const regionKeys = accountRegionMap[account][region];
      const ssmClient = new SSMClient({ region });
      const parametersToFetch = regionKeys.map((k) => k.parameterName);

      // SSM GetParameters only allows a max of 10 parameters at a time.
      const batchedParams: string[][] = [];
      for (let i = 0; i < parametersToFetch.length; i += 10) {
        batchedParams.push(parametersToFetch.slice(i, i + 10));
      }

      const fetchedParams: Record<string, string | undefined> = {};

      for (const batch of batchedParams) {
        const command = new GetParametersCommand({
          Names: batch,
          WithDecryption: true,
        });
        const response = await ssmClient.send(command);
        (response.Parameters || []).forEach((p) => {
          if (p.Name && p.Value !== undefined) {
            fetchedParams[p.Name] = p.Value;
          }
        });
      }

      // Compare each parameter
      for (const key of regionKeys) {
        const actualValue = fetchedParams[key.parameterName];
        const cachedValue = key.cachedValue;
        if (actualValue === undefined) {
          console.log(
            `Parameter not found in SSM: ${key.parameterName} (Region: ${region})`,
          );
          differencesFound = true;
        } else if (actualValue !== cachedValue) {
          console.log(
            `Difference found for parameter ${key.parameterName} (Region: ${region}):`,
          );
          console.log(`  Cached value: ${cachedValue}`);
          console.log(`  Actual value: ${actualValue}`);
          differencesFound = true;
        }
      }
    }
  }

  if (!differencesFound) {
    console.log(
      "No differences found between cached SSM values and actual SSM values.",
    );
  }
}

/**
 * Extracts all SSM parameter references from the context data.
 * CDK typically stores these keys in a format like:
 * "ssm:account=************:parameterName=/my/param:region=us-east-1": "cachedValue"
 *
 * We will parse these keys to extract the account, parameterName, and region.
 */
function extractSsmContextKeys(
  contextData: Record<string, any>,
): SsmContextKey[] {
  const ssmKeys: SsmContextKey[] = [];

  for (const key of Object.keys(contextData)) {
    // Check if the key starts with 'ssm:'
    // A typical pattern: ssm:account=************:parameterName=/myparam:region=us-east-1
    if (key.startsWith("ssm:")) {
      const parts = key.split(":");
      // parts might look like ["ssm","account=************","parameterName=/my/param","region=us-east-1"]
      const accountPart = parts.find((p) => p.startsWith("account="));
      const parameterPart = parts.find((p) => p.startsWith("parameterName="));
      const regionPart = parts.find((p) => p.startsWith("region="));

      if (!accountPart || !parameterPart || !regionPart) {
        continue; // doesn't match expected pattern
      }

      const account = accountPart.split("=")[1];
      const parameterName = parameterPart.split("=")[1];
      const region = regionPart.split("=")[1];

      const cachedValue = contextData[key];
      ssmKeys.push({
        account,
        region,
        parameterName,
        cachedValue,
      });
    }
  }

  return ssmKeys;
}

main().catch((err) => {
  console.error("Error running comparison:", err);
  process.exit(1);
});
