import base64
import io
from typing import Any

import cv2
import numpy as np
import pyotp
import pyqrcode
import structlog
from django import forms
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse
from phonenumber_field.formfields import PhoneNumberField

from webapp.models.emails import CustomerEmailCredential
from webapp.models.portal import (
    MergedPortalCredential,
    MFAStatus,
    MFAType,
    MultiFactorAuthentication,
    PhoneType,
)
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser
from webapp.views.retrieval import trigger_task
from webapp.views.utils import (
    add_hx_trigger_header,
    get_merged_portal_credential_from_url_params,
)

logger = structlog.get_logger(__name__)


def render_end_screen(
    *, request: HttpRequest, merged_portal_credential: MergedPortalCredential, success: bool
) -> HttpResponse:
    headers = add_hx_trigger_header(
        {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}}
    )

    return TemplateResponse(
        request,
        "link/multi_factor_authentication/end_screen.html",
        {
            "merged_portal_credential": merged_portal_credential,
            "success": success,
        },
        headers=headers,
    )


# TODO: Clean this up when we break up the flow
class MFAImageUploadForm(forms.Form):
    authentication_options = forms.ChoiceField(
        label="Auth method",
        choices=[
            ("", "Select"),
            *((choice[0], choice[1]) for choice in MFAType.choices if choice[0] != MFAType.UNKNOWN),
        ],
        required=True,
        widget=forms.Select(),
    )
    qr_image_file = forms.FileField(
        label="QR code image",
        required=False,
        widget=forms.FileInput(),
    )
    email = forms.EmailField(
        label="Email",
        required=False,
        widget=forms.TextInput(),
    )
    email_provider = forms.ChoiceField(
        choices=CustomerEmailCredential.EmailProvider.choices,
        label="Email provider",
        required=False,
        widget=forms.Select(),
    )
    phone_number = PhoneNumberField(
        label="Phone number",
        required=False,
        widget=forms.TextInput(),
    )
    phone_type = forms.ChoiceField(
        label="Phone type",
        choices=PhoneType.choices,
        required=False,
        widget=forms.Select(),
    )

    def clean(self) -> dict[str, Any]:
        cleaned_data = super().clean()
        auth_type = cleaned_data.get("authentication_options")

        # First check if required fields are present
        if auth_type == MFAType.SMS:
            phone_number = cleaned_data.get("phone_number")
            phone_type = cleaned_data.get("phone_type")

            # Only add required field error if there are no field-level errors
            if not phone_number and "phone_number" not in self.errors:
                msg = "Phone number is required for SMS authentication."
                self.add_error("phone_number", msg)
            if not phone_type and "phone_type" not in self.errors:
                msg = "Phone type is required for SMS authentication."
                self.add_error("phone_type", msg)

        elif auth_type == MFAType.EMAIL:
            email = cleaned_data.get("email")
            email_provider = cleaned_data.get("email_provider")

            if not email and "email" not in self.errors:
                msg = "Email is required for email authentication."
                self.add_error("email", msg)
            if not email_provider and "email_provider" not in self.errors:
                msg = "Email provider is required for email authentication."
                self.add_error("email_provider", msg)

        elif auth_type == MFAType.AUTHENTICATOR:
            if not cleaned_data.get("qr_image_file") and "qr_image_file" not in self.errors:
                msg = "QR code image is required for authenticator authentication."
                self.add_error("qr_image_file", msg)

        return cleaned_data


class MFALiveLoginOTPForm(forms.Form):
    one_time_password = forms.CharField(max_length=12, required=True)


def get_default_mfa_form(merged_portal_credential: MergedPortalCredential) -> MFAImageUploadForm:
    email = None
    email_provider = None
    mfa = merged_portal_credential.multi_factor_authentication
    if mfa.receiving_email:
        email = mfa.receiving_email.email
        email_provider = mfa.receiving_email.email_provider_customer_managed

    return MFAImageUploadForm(
        initial={
            "authentication_options": mfa.multi_factor_authentication_type,
            "email": email,
            "email_provider": email_provider,
            "phone_number": mfa.phone_number,
            "phone_type": mfa.phone_type,
        }
    )


def save_mfa_form_authenticator(
    user: BridgeUser, form: MFAImageUploadForm
) -> tuple[MultiFactorAuthentication | None, str | None, pyotp.OTP | None]:
    # HACK: I'm making this a relative import because it sometimes gets picked up by testing and requires non-python
    # dependencies to be installed. Until we run tests inside of docker in CI/CD, let's keep as a relative import
    from qreader import QReader

    try:
        image_file = form.cleaned_data["qr_image_file"]
        qreader = QReader()
        np_image = np.asarray(bytearray(image_file.read()), dtype="uint8")
        image = cv2.imdecode(np_image, cv2.COLOR_BGR2RGB)
        decoded_text_list = qreader.detect_and_decode(image=image)
        if len(decoded_text_list) == 1:
            decoded_text = decoded_text_list[0]
            otp = pyotp.parse_uri(decoded_text)
            file_in_memory = io.BytesIO()
            url = pyqrcode.create(otp.provisioning_uri())
            url.png(file_in_memory, scale=8)
            file_in_memory.seek(0)
            qr_code_base64 = base64.b64encode(file_in_memory.read()).decode("utf-8")
            mfa = MultiFactorAuthentication.objects.create(
                user=user,
                multi_factor_authentication_type=MFAType.AUTHENTICATOR,
            )
            mfa.set_secret(decoded_text)
            return mfa, qr_code_base64, otp
    except Exception:
        logger.exception("There was some error uploading QR code image")
    return None, None, None


def save_mfa_form_email(user: BridgeUser, form: MFAImageUploadForm) -> tuple[MultiFactorAuthentication, bool]:
    email_provider = form.cleaned_data["email_provider"]
    email = form.cleaned_data["email"]
    cec, _ = CustomerEmailCredential.objects.update_or_create(
        user=user,
        email=email,
        defaults={
            "email_provider_raw": CustomerEmailCredential.EmailProvider(email_provider).label,
            "email_provider_customer_managed": email_provider,
            "email_provider_bridge_managed": email_provider,
        },
    )
    mfa = MultiFactorAuthentication.objects.create(
        user=user,
        multi_factor_authentication_type=MFAType.EMAIL,
        receiving_email=cec,
    )
    return mfa, True


def save_mfa_form_live_login(user: BridgeUser) -> tuple[MultiFactorAuthentication, bool]:
    mfa = MultiFactorAuthentication.objects.create(
        user=user,
        multi_factor_authentication_type=MFAType.LIVE_LOGIN,
    )
    return mfa, True


def save_mfa_form_sms(user: BridgeUser, form: MFAImageUploadForm) -> tuple[MultiFactorAuthentication, bool]:
    mfa = MultiFactorAuthentication.objects.create(
        user=user,
        multi_factor_authentication_type=MFAType.SMS,
        phone_number=form.cleaned_data["phone_number"],
        phone_type=form.cleaned_data["phone_type"],
    )
    return mfa, True


def save_mfa_form_none(user: BridgeUser) -> tuple[MultiFactorAuthentication, bool]:
    mfa = MultiFactorAuthentication.objects.create(
        user=user,
        multi_factor_authentication_type=MFAType.NONE,
    )
    return mfa, True


@login_required
def modal(request: HttpRequest) -> HttpResponse:  # noqa: C901, PLR0911, PLR0912
    merged_portal_credential = get_merged_portal_credential_from_url_params(request)
    form = get_default_mfa_form(merged_portal_credential)

    if request.method == "GET":
        template = "modal.html"
        default_initial_template = "link/multi_factor_authentication/welcome_screen.html"
        modal_include_template = request.GET.get("modal_include_template", default_initial_template)
        if modal_include_template == "link/multi_factor_authentication/enter_otp.html":
            form = MFALiveLoginOTPForm()
        headers = add_hx_trigger_header(
            {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}}
        )
        return TemplateResponse(
            request,
            template,
            {
                "merged_portal_credential": merged_portal_credential,
                "portal_name": merged_portal_credential.portal.name,
                "modal_include_template": modal_include_template,
                "form": form,
                "MFAType": MFAType,
                "MFAStatus": MFAStatus,
            },
            headers=headers,
        )  # type: ignore[arg-type]
    # TODO: Rewrite this logic, maybe a helper function for routing--too many returns
    if request.method == "POST":
        form = MFAImageUploadForm(request.POST, request.FILES)
        headers = add_hx_trigger_header(
            {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}}
        )

        if not form.is_valid():
            # Return a response that will close the modal and show the form errors
            return TemplateResponse(
                request,
                "link/multi_factor_authentication/welcome_screen.html",
                {
                    "merged_portal_credential": merged_portal_credential,
                    "portal_name": merged_portal_credential.portal.name,
                    "form": form,
                    "MFAType": MFAType,
                    "MFAStatus": MFAStatus,
                },
                headers=headers,
            )

        auth_type: MFAType = form.cleaned_data["authentication_options"]
        success = False
        context: dict[str, Any] = {
            "merged_portal_credential": merged_portal_credential,
        }
        if auth_type == MFAType.AUTHENTICATOR:
            updated_mfa, qr_code_base64, otp = save_mfa_form_authenticator(request.user, form)
            logger.info("save_mfa_form_authenticator", updated_mfa=updated_mfa, qr_code_base64=qr_code_base64, otp=otp)
            context["success"] = False
            if otp is not None:
                context["otp_qr_code_base64"] = qr_code_base64
                context["otp"] = otp
                context["success"] = True
            if context["success"]:
                merged_portal_credential.update_merged_credential_multi_factor_authentication(updated_mfa)
                return TemplateResponse(
                    request, "link/multi_factor_authentication/authenticator_details.html", context, headers=headers
                )

        elif auth_type == MFAType.LIVE_LOGIN:
            updated_mfa, success = save_mfa_form_live_login(request.user)
            merged_portal_credential.update_merged_credential_multi_factor_authentication(updated_mfa)
            context["success"] = success

            # TODO: uncomment when ready merged_portal_credential.portal.is_portal_supported(request.user)
            is_this_portal_supported = False
            if request.user.is_demo:
                is_this_portal_supported = merged_portal_credential.portal.is_portal_supported(request.user)
            context["is_portal_supported"] = is_this_portal_supported

            return TemplateResponse(
                request, "link/multi_factor_authentication/portal_support.html", context, headers=headers
            )

        elif auth_type == MFAType.SMS:
            updated_mfa, success = save_mfa_form_sms(request.user, form)
            merged_portal_credential.update_merged_credential_multi_factor_authentication(updated_mfa)
            context["merged_portal_credential"] = merged_portal_credential
            return TemplateResponse(
                request, "link/multi_factor_authentication/setup_sms_mfa.html", context, headers=headers
            )
        elif auth_type == MFAType.EMAIL:
            updated_mfa, success = save_mfa_form_email(request.user, form)
            merged_portal_credential.update_merged_credential_multi_factor_authentication(updated_mfa)
            merged_portal_credential.save()
            headers = add_hx_trigger_header(
                {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}}
            )
            modal_include_template = "link/emails/email_modal.html"
            form_include_template = "link/emails/begin_email_integration.html"
            description_text = (
                "Select your preferred method for setting up email integration with Bridge."
                if updated_mfa.receiving_email.email_provider_bridge_managed != "ns"
                else "We only support a few providers right now. Please message support if you would like to see more."
            )
            return TemplateResponse(
                request,
                modal_include_template,
                {
                    "mpc": merged_portal_credential,
                    "modal_sub_header_text": "Begin Email Integration",
                    "modal_header_icon": True,
                    "modal_description_text": description_text,
                    "form_include_template": form_include_template,
                    "receiving_email": merged_portal_credential.multi_factor_authentication.receiving_email,
                },
            )
        elif auth_type == MFAType.NONE:
            updated_mfa, success = save_mfa_form_none(request.user)

        if updated_mfa:
            merged_portal_credential.update_merged_credential_multi_factor_authentication(updated_mfa)

        return render_end_screen(request=request, merged_portal_credential=merged_portal_credential, success=success)

    return HttpResponse(status=405)


@login_required
def establish_connection(request: HttpRequest) -> HttpResponse:
    merged_portal_credential = get_merged_portal_credential_from_url_params(request)
    context = {
        "merged_portal_credential": merged_portal_credential,
    }
    headers = add_hx_trigger_header(
        {"changeMergedPortalCredentialRow": {"merged_portal_credential_pk": merged_portal_credential.pk}}
    )

    if request.method == "GET":
        trigger_task(request.user, merged_portal_credential)
        return TemplateResponse(
            request,
            "link/multi_factor_authentication/establish_connection.html",
            context,
            headers=headers,
        )

    return HttpResponse(status=405)


@login_required
def enter_otp(request: HttpRequest) -> HttpResponse:
    merged_portal_credential = get_merged_portal_credential_from_url_params(request)
    context = {
        "merged_portal_credential": merged_portal_credential,
        "RetrievalStatus": Retrieval.RetrievalStatus,
    }
    if request.method == "GET":
        context["form"] = MFALiveLoginOTPForm()
        template = "link/multi_factor_authentication/enter_otp.html"

        return TemplateResponse(request, template, context)

    if request.method == "POST":
        form = MFALiveLoginOTPForm(request.POST)

        if not form.is_valid():
            return HttpResponse("Invalid form data", status=400)
        # TODO: I assume this needs to be a check against the status of the retrieval
        is_otp_successful = True
        # TODO: Univeral Login needs to update this to have live login work.
        if not is_otp_successful:
            context["error"] = "Invalid OTP. Please input the correct OTP."
            return TemplateResponse(request, "link/multi_factor_authentication/otp_screen.html", context)

        one_time_password = form.cleaned_data["one_time_password"]
        merged_portal_credential.last_retrieval.update_token_otp(one_time_password)

        return render_end_screen(request=request, merged_portal_credential=merged_portal_credential, success=True)

    return HttpResponse(status=405)


@login_required
def end_screen(request: HttpRequest) -> HttpResponse:
    merged_portal_credential = get_merged_portal_credential_from_url_params(request)
    # defaults to successful if not specified
    success = request.GET.get("success", "true").lower() == "true"

    return render_end_screen(request=request, merged_portal_credential=merged_portal_credential, success=success)


@login_required
def check_retrieval_status(request: HttpRequest) -> HttpResponse:
    if request.method == "GET":
        merged_portal_credential = get_merged_portal_credential_from_url_params(request)

        retrieval = merged_portal_credential.last_retrieval
        retrieval_status = retrieval.retrieval_status if retrieval else Retrieval.RetrievalStatus.NOT_STARTED
        context = {
            "merged_portal_credential": merged_portal_credential,
            "retrieval_status": retrieval_status,
            "RetrievalStatus": Retrieval.RetrievalStatus,
        }
        return TemplateResponse(
            request,
            "link/multi_factor_authentication/check_retrieval_status.html",
            context,
        )
    return HttpResponse(status=405)
