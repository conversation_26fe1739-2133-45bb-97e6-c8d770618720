import json
import os
from socket import gethostbyname, gethostname

import requests
import structlog

from . import base
from .base import *  # noqa: F403
from .logging_utils import configure_logging
from .utils import generate_aws_configuration, get_secret

LOGGING = configure_logging("json_formatter", "INFO", None)
logger = structlog.get_logger(__name__)

logger.info("OVERRIDING PROD SETTINGS")

DEBUG = False

aws_config, config = generate_aws_configuration(os.environ["ENVIRONMENT"])

if config.ConfigStack is None:
    logger.error("ConfigStack not found in config")
    raise ValueError

if config.ConfigStack.DjangoSecretArn is None:
    logger.error("DjangoSecretArn not found in config")
    raise ValueError

if config.StorageStack is None:
    logger.error("StorageStack not found in config")
    raise ValueError

if config.ConfigStack.MsftEmailOauthSecretArn is None:
    logger.error("MsftEmailOauthSecretArn not found in config")
    raise ValueError

if config.ConfigStack.SlackSecretArn is None:
    logger.error("SlackSecretArn not found in config")
    raise ValueError

# Secret Key
SECRET_KEY = json.loads(get_secret(config.ConfigStack.DjangoSecretArn)).get("password")

# Slack Secrets
SLACK_OAUTH_KEY = json.loads(get_secret(config.ConfigStack.SlackSecretArn)).get("OAUTH_TOKEN")
SLACK_SIGNING_SECRET = json.loads(get_secret(config.ConfigStack.SlackSecretArn)).get("SIGNING_SECRET")

# Allowed Hosts
DOMAIN_NAME = config.ConfigStack.DomainName
ALLOWED_HOSTS = [config.ConfigStack.DomainName, gethostbyname(gethostname())]
metadata_uri = os.environ.get("ECS_CONTAINER_METADATA_URI", None)
if metadata_uri is not None:
    container_metadata = requests.get(metadata_uri, timeout=10).json()
    ALLOWED_HOSTS.append(container_metadata["Networks"][0]["IPv4Addresses"][0])
else:
    logger.error("Could not get container metadata")
CSRF_TRUSTED_ORIGINS = [f"https://{host}" for host in ALLOWED_HOSTS]
logger.info("ALLOWED_HOSTS", allowed_hosts=ALLOWED_HOSTS)

base.MIDDLEWARE.append("django_structlog.middlewares.RequestMiddleware")

# AWS Bucket
AWS_STORAGE_BUCKET_NAME = config.ConfigStack.UserBucketName
SFTP_BUCKET_NAME = config.ConfigStack.SftpBucketName

DEFAULT_FROM_EMAIL = config.StorageStack.EmailSendAddress

# Database
credentials = aws_config.DATABASE_CREDENTIALS
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": credentials["dbname"],
        "USER": credentials["username"],
        "PASSWORD": credentials["password"],
        "HOST": credentials["host"],
        "PORT": credentials["port"],
    },
}
# Use redis as a cache, currently use redis as a cache to use locking features.
CACHES = {
    "default": {
        "BACKEND": "redis_lock.django_cache.RedisCache",
        "LOCATION": aws_config.REDIS_CONN_URI,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {
                "ssl_cert_reqs": None,  # Don't verify SSL certificates
            },
        },
        "KEY_PREFIX": "django_cache",
        "TIMEOUT": 600,
    }
}
# Celery
CELERY_BROKER_URL = aws_config.REDIS_CONN_URI
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True
CELERY_REDIS_BACKEND_USE_SSL = {
    "ssl_cert_reqs": None,
}

# MSFT OAUTH for email plug-in integration
OAUTH_SECRET = json.loads(get_secret(config.ConfigStack.MsftEmailOauthSecretArn))
