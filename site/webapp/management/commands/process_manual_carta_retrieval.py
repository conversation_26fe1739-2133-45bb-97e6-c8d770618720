import hashlib
import json
from argparse import Argument<PERSON><PERSON><PERSON>
from pathlib import Path
from zipfile import ZipFile

import boto3
import structlog
from dateutil import parser
from django.conf import settings
from django.core.management.base import BaseCommand
from django.utils import timezone
from django_extensions.management.utils import signalcommand
from retrieval.core.strategy import DocumentHash, RawDocumentTuple, save_document

from webapp.models.documents import DocumentType
from webapp.models.portal import MergedPortalCredential, Portal
from webapp.models.retrieval import Retrieval
from webapp.models.user import BridgeUser

logger = structlog.get_logger(__name__)


def download_from_s3(s3: boto3.client, zip_file: Path) -> tuple[Path, Path]:
    logger.info("Processing files from S3")
    zip_bucket = zip_file.parts[1]
    bucket_name = zip_bucket
    zip_file_key = str(Path(*zip_file.parts[2:]))
    try:
        s3.head_object(Bucket=bucket_name, Key=zip_file_key)
    except Exception:
        logger.exception(
            "Files do not exist in the S3 bucket",
            bucket_name=bucket_name,
            zip_file_key=zip_file_key,
        )
        raise

    dump_local_folder = Path("./site")
    zip_file_name = str(dump_local_folder / zip_file.parts[-1])
    s3.download_file(bucket_name, zip_file_key, zip_file_name)
    local_zip_file_path = Path(zip_file_name)
    logger.info("Downloaded files", local_zip_file_path=local_zip_file_path)
    return local_zip_file_path


def upload_to_s3(s3: boto3.client, zip_file: Path) -> tuple[Path, Path]:
    logger.info("Uploading to S3! First checking files' validity")
    _ = process_files(str(zip_file))  # raises if invalid
    manual_retrieval_folder = Path("manual_retrieval") / str(timezone.now().strftime("%Y-%m-%d_%H-%M-%S"))
    zip_s3_file = manual_retrieval_folder / zip_file.name.replace(" ", "_")
    s3.upload_file(zip_file, settings.AWS_STORAGE_BUCKET_NAME, str(zip_s3_file))
    logger.info(
        "We have successfully uploaded these files to S3. Processing is only allowed if the files are valid and in s3"
    )
    return zip_s3_file


class Command(BaseCommand):
    help = "Processes Excel and ZIP files to check if files from the Document Path exist in the ZIP file."

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument(
            "-z",
            "--zip",
            type=str,
            required=True,
            help="Path to the ZIP file",
        )
        parser.add_argument(
            "-u",
            "--user",
            type=str,
            help="User Email",
        )
        parser.add_argument(
            "-p",
            "--portal",
            type=str,
            help="Portal Name",
        )

    @signalcommand
    def handle(self, *args: list[str], **kwargs: dict) -> None:  # noqa: ARG002
        s3 = boto3.client("s3")
        zip_file = Path(kwargs.get("zip"))
        local_zip_file_path = None
        try:
            if Path(zip_file).suffix != ".zip":
                logger.error("ZIP file must have a .zip extension")
                raise ValueError

            if zip_file.parts[0] == "s3:":
                local_zip_file_path = download_from_s3(s3, zip_file)

            elif zip_file.exists():
                zip_s3_file = upload_to_s3(s3, zip_file)
                logger.info("Please run the following command:")
                logger.info(
                    './site/manage.py process_manual_carta_retrieval -z "s3://%s/%s"',
                    settings.AWS_STORAGE_BUCKET_NAME,
                    zip_s3_file,
                )
                return

            if not (local_zip_file_path is not None and local_zip_file_path.exists()):
                logger.error(
                    "Both files must be downloaded locally from s3 to be processed",
                    zip_file=local_zip_file_path,
                    local_zip_file_path_exists=local_zip_file_path.exists(),
                )
                raise ValueError

            # In summary, at this point in the code, we have validated that the zip files exist in S3,
            # Can be processed, and are downloaded locally.
            logger.info("Processing files Zip", local_zip_file_path=local_zip_file_path)

            user = kwargs.get("user")
            portal_name = kwargs.get("portal")
            if user is None or portal_name is None:
                logger.error("User and Portal name are required to proceed")
                return
            user = BridgeUser.objects.get(email=user)
            portal = Portal.objects.get(name=portal_name, organization=user.organization)
            merged_portal_credential = MergedPortalCredential.objects.for_user(user).filter(portal=portal).all()
            if len(merged_portal_credential) == 0:
                logger.error("Could not find a MergedPortalCredential for the user and portal")
                return
            if len(merged_portal_credential) > 1:
                logger.error("Found multiple MergedPortalCredentials for the user and portal")
                return

            merged_portal_credential = merged_portal_credential[0]

            retrieval = Retrieval.create(
                user=user,
                merged_portal_credential=merged_portal_credential,
                s3_format_version=-1,
                s3_key="/".join(zip_file.parts[2:-1]),
                s3_bucket=zip_file.parts[1],
                manager="ManualRetrievalV1",
                exists_in_s3=True,
            )
            _ = process_files(zip_file=str(local_zip_file_path), retrieval=retrieval, user=user)
            retrieval.update_login_status(Retrieval.RetrievalStatus.SUCCESS_DOCUMENT_RETRIEVAL)

        finally:
            logger.info("Cleaning up")
            if local_zip_file_path:
                Path.unlink(local_zip_file_path)


def process_files(  # noqa: C901, PLR0912, PLR0915
    zip_file: str, retrieval: Retrieval | None = None, user: BridgeUser | None = None
) -> list[RawDocumentTuple]:
    logger.info("Starting the processing of files")
    docs_with_content = []
    with ZipFile(zip_file, "r") as zf:
        for zfn in zf.namelist():
            if zfn.endswith("/") or "__MACOSX/" in zfn:
                continue
            zfn_path = Path(zfn)
            if zfn_path.suffix.lower() != ".pdf":
                logger.error("Non PDF file found in ZIP", zip_file_name=zfn)
                continue
            if len(zfn_path.parts) != 8:  # noqa: PLR2004
                logger.error("Unexpected path length", zip_file_name=zfn)
                continue
            document_path = zfn
            name = zfn_path.parts[7]
            raw_posted_date = zfn_path.parts[6]
            posted_date = parser.parse(raw_posted_date)
            raw_doc_type = zfn_path.parts[5]
            doc_type = map_to_bridge_doc_type(raw_doc_type)
            if doc_type == DocumentType.UNKNOWN:
                logger.error("Unknown document type", zip_file_name=zfn)
            carta_fund_name = zfn_path.parts[4]
            carta_zip_file_name = zfn_path.parts[3]
            fund_name = zfn_path.parts[2]
            investing_entity = zfn_path.parts[1]
            organization = zfn_path.parts[0]
            metadata_d = {
                "File Name": name,
                "Raw Posted Date": raw_posted_date,
                "Raw Document Type": raw_doc_type,
                "Carta Fund Name": carta_fund_name,
                "Carta Zip File Name": carta_zip_file_name,
                "Fund Name": fund_name,
                "Investing Entity": investing_entity,
                "Organization": organization,
            }
            doc_hash = generate_document_hash(metadata_d)
            raw_metadata = json.dumps(metadata_d, default=str)
            if retrieval is not None and user is not None:
                with zf.open(document_path) as pdf_file:
                    doc = RawDocumentTuple(
                        name=name,
                        date=posted_date,
                        doc_type=doc_type,
                        content=pdf_file.read(),
                        raw_metadata=raw_metadata,
                        doc_hash=doc_hash,
                    )
                    save_document(user, doc, retrieval=retrieval)
                    retrieval.increment_documents_retrieved()
            else:
                docs_with_content.append(
                    RawDocumentTuple(
                        name=name,
                        date=posted_date,
                        doc_type=doc_type,
                        content=None,
                        raw_metadata=raw_metadata,
                        doc_hash=doc_hash,
                    )
                )
    if user is not None and retrieval is not None:
        return []

    logger.info("Found documents with content", length_doc_with_content=len(docs_with_content))

    dupes = {}
    for doc in docs_with_content:
        if doc.doc_hash not in dupes:
            dupes[doc.doc_hash] = []
        dupes[doc.doc_hash].append(doc)

    has_duplicates = False
    for doc_hash, docs in dupes.items():
        if len(docs) > 1:
            logger.error("Duplicate document", doc_hash=doc_hash, length_docs=len(docs))
            for doc in docs:
                logger.error("Duplicate Document", doc_name=doc.name)
            logger.info("------------------------")
            has_duplicates = True
    if has_duplicates:
        logger.error("Please fix the duplicates in the Excel file")
        raise ValueError
    logger.info("All checks passed", length_docs_with_content=len(docs_with_content))
    return docs_with_content


def map_to_bridge_doc_type(doc_type: str) -> str:
    dt = doc_type.lower().strip()
    return (
        {
            "tax - schedule k-1": DocumentType.TAX,
            "general": DocumentType.OTHER,
            "annual meeting": DocumentType.INVESTMENT_UPDATE,
            "capital account statements": DocumentType.ACCOUNT_STATEMENT,
            "tax": DocumentType.TAX,
            "capital calls": DocumentType.CAPITAL_CALL,
            "annual and quarterly report": DocumentType.INVESTMENT_UPDATE,
            "distributions": DocumentType.DISTRIBUTION_NOTICE,
            "tax - estimate": DocumentType.TAX,
            "legal": DocumentType.LEGAL,
            "legal - subscription agreement": DocumentType.LEGAL,
        }
    ).get(dt, DocumentType.UNKNOWN)


def generate_document_hash(document: dict[str, str]) -> DocumentHash:
    keys = sorted([k for k in document if k is not None])
    string_value = "".join(str([document[k] for k in keys]))
    return DocumentHash(hashlib.md5(string_value.encode()).hexdigest(), 1, "ManualCartaRetrieval")  # noqa: S324 # nosec
