from argparse import Argument<PERSON><PERSON><PERSON>
from pathlib import Path

import boto3
import structlog
from django.conf import settings
from django.core.management.base import BaseCommand
from django_extensions.management.utils import signalcommand
from email_webhook.tasks.email_parse import (
    extract_documents_from_email,
    get_email_data,
)

from webapp.models import EmailIntake

EMAIL_DOCUMENTS_PATH = Path("email_documents")
BUCKET = settings.AWS_STORAGE_BUCKET_NAME
# ./site/manage.py backfill_emails --domain "ezralow.com"
logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Back filling email documents from s3 to email intake for a given domain"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("-d", "--domain", type=str)

    @signalcommand
    def handle(self, *args: list[str], **kwargs: str) -> None:  # noqa: ARG002
        domain = kwargs.get("domain")
        if domain is None:
            logger.info("email domain is None, must be set, exiting")
            return

        s3 = boto3.client("s3")
        paginator = s3.get_paginator("list_objects_v2")
        page_iterator = paginator.paginate(Bucket=BUCKET, Prefix="inbound_emails")
        for page in page_iterator:
            for obj in page["Contents"]:
                if "Key" in obj:
                    email_s3_key = obj["Key"]
                    if EmailIntake.objects.filter(s3_key=email_s3_key, s3_bucket=BUCKET).exists():
                        logger.debug("Email intake already exists, skipping", s3_key=email_s3_key, s3_bucket=BUCKET)
                        continue
                    (email_msg, email_subject, to_email, from_email, all_emails, parsed_date, text) = get_email_data(
                        email_s3_key
                    )
                    if any(domain in s for s in all_emails) and not to_email.startswith("mfa+"):
                        raw_docs_created = extract_documents_from_email(email_s3_key)
                        logger.info("Raw documents created", raw_docs_created=raw_docs_created)
